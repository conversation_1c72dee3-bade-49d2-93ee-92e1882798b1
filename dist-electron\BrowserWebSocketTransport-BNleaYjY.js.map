{"version": 3, "file": "BrowserWebSocketTransport-BNleaYjY.js", "sources": ["../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/common/BrowserWebSocketTransport.js"], "sourcesContent": ["/**\n * @internal\n */\nexport class BrowserWebSocketTransport {\n    static create(url) {\n        return new Promise((resolve, reject) => {\n            const ws = new WebSocket(url);\n            ws.addEventListener('open', () => {\n                return resolve(new BrowserWebSocketTransport(ws));\n            });\n            ws.addEventListener('error', reject);\n        });\n    }\n    #ws;\n    onmessage;\n    onclose;\n    constructor(ws) {\n        this.#ws = ws;\n        this.#ws.addEventListener('message', event => {\n            if (this.onmessage) {\n                this.onmessage.call(null, event.data);\n            }\n        });\n        this.#ws.addEventListener('close', () => {\n            if (this.onclose) {\n                this.onclose.call(null);\n            }\n        });\n        // Silently ignore all errors - we don't know what to do with them.\n        this.#ws.addEventListener('error', () => { });\n    }\n    send(message) {\n        this.#ws.send(message);\n    }\n    close() {\n        this.#ws.close();\n    }\n}\n//# sourceMappingURL=BrowserWebSocketTransport.js.map"], "names": [], "mappings": ";;;;;;;;;;AAAA;AAGO,MAAM,6BAAN,MAAM,2BAA0B;AAAA,EAanC,YAAY,IAAI;AAHhB;AACA;AACA;AAEI,uBAAK,KAAM;AACX,uBAAK,KAAI,iBAAiB,WAAW,WAAS;AAC1C,UAAI,KAAK,WAAW;AAChB,aAAK,UAAU,KAAK,MAAM,MAAM,IAAI;AAAA,MACpD;AAAA,IACA,CAAS;AACD,uBAAK,KAAI,iBAAiB,SAAS,MAAM;AACrC,UAAI,KAAK,SAAS;AACd,aAAK,QAAQ,KAAK,IAAI;AAAA,MACtC;AAAA,IACA,CAAS;AAED,uBAAK,KAAI,iBAAiB,SAAS,MAAM;AAAA,IAAA,CAAG;AAAA,EACpD;AAAA,EA1BI,OAAO,OAAO,KAAK;AACf,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,YAAM,KAAK,IAAI,UAAU,GAAG;AAC5B,SAAG,iBAAiB,QAAQ,MAAM;AAC9B,eAAO,QAAQ,IAAI,2BAA0B,EAAE,CAAC;AAAA,MAChE,CAAa;AACD,SAAG,iBAAiB,SAAS,MAAM;AAAA,IAC/C,CAAS;AAAA,EACT;AAAA,EAmBI,KAAK,SAAS;AACV,uBAAK,KAAI,KAAK,OAAO;AAAA,EAC7B;AAAA,EACI,QAAQ;AACJ,uBAAK,KAAI,MAAO;AAAA,EACxB;AACA;AAxBI;AAVG,IAAM,4BAAN;", "x_google_ignoreList": [0]}
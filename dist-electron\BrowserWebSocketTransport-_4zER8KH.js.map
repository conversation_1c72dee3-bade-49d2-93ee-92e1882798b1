{"version": 3, "file": "BrowserWebSocketTransport-_4zER8KH.js", "sources": ["../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/common/BrowserWebSocketTransport.js"], "sourcesContent": ["/**\n * @internal\n */\nexport class BrowserWebSocketTransport {\n    static create(url) {\n        return new Promise((resolve, reject) => {\n            const ws = new WebSocket(url);\n            ws.addEventListener('open', () => {\n                return resolve(new BrowserWebSocketTransport(ws));\n            });\n            ws.addEventListener('error', reject);\n        });\n    }\n    #ws;\n    onmessage;\n    onclose;\n    constructor(ws) {\n        this.#ws = ws;\n        this.#ws.addEventListener('message', event => {\n            if (this.onmessage) {\n                this.onmessage.call(null, event.data);\n            }\n        });\n        this.#ws.addEventListener('close', () => {\n            if (this.onclose) {\n                this.onclose.call(null);\n            }\n        });\n        // Silently ignore all errors - we don't know what to do with them.\n        this.#ws.addEventListener('error', () => { });\n    }\n    send(message) {\n        this.#ws.send(message);\n    }\n    close() {\n        this.#ws.close();\n    }\n}\n//# sourceMappingURL=BrowserWebSocketTransport.js.map"], "names": [], "mappings": "AAGO,MAAM,0BAA0B;AAAA,EACnC,OAAO,OAAO,KAAK;AACf,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,YAAM,KAAK,IAAI,UAAU,GAAG;AAC5B,SAAG,iBAAiB,QAAQ,MAAM;AAC9B,eAAO,QAAQ,IAAI,0BAA0B,EAAE,CAAC;AAAA,MAChE,CAAa;AACD,SAAG,iBAAiB,SAAS,MAAM;AAAA,IAC/C,CAAS;AAAA,EACT;AAAA,EACI;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,IAAI;AACZ,SAAK,MAAM;AACX,SAAK,IAAI,iBAAiB,WAAW,WAAS;AAC1C,UAAI,KAAK,WAAW;AAChB,aAAK,UAAU,KAAK,MAAM,MAAM,IAAI;AAAA,MACpD;AAAA,IACA,CAAS;AACD,SAAK,IAAI,iBAAiB,SAAS,MAAM;AACrC,UAAI,KAAK,SAAS;AACd,aAAK,QAAQ,KAAK,IAAI;AAAA,MACtC;AAAA,IACA,CAAS;AAED,SAAK,IAAI,iBAAiB,SAAS,MAAM;AAAA,IAAA,CAAG;AAAA,EACpD;AAAA,EACI,KAAK,SAAS;AACV,SAAK,IAAI,KAAK,OAAO;AAAA,EAC7B;AAAA,EACI,QAAQ;AACJ,SAAK,IAAI,MAAO;AAAA,EACxB;AACA;", "x_google_ignoreList": [0]}
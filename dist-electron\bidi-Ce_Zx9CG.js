import { d as debugError, P as PuppeteerURL, J as <PERSON><PERSON><PERSON><PERSON><PERSON>, U as UnsupportedOperation, E as <PERSON>ement<PERSON>and<PERSON>, t as throwIfDisposed, i as isPlainObject, a as isRegExp, b as isDate, L as LazyArg, c as EventEmitter, p as protocol, s as scriptInjector, g as getSourceUrlComment, e as getSourcePuppeteerURLIfAvailable, f as isString, h as stringifyFunction, j as disposeSymbol, S as SOURCE_URL_REGEX, C as CDPSession, D as Deferred, T as TargetCloseError, k as CallbackRegistry, l as assert, m as debug, B as BidiMapper, n as DisposableStack, o as inertIfDisposed, q as BrowserContext, r as Dialog, u as interpolateFunction, W as Wr, v as ProtocolError, w as TimeoutError, R as Realm$1, x as withSourcePuppeteerURLIfNone, y as UTILITY_WORLD_NAME, z as Ee, N as NETWORK_IDLE_TIME, F, A as k, G as Fe, H as timeout, I as me, K as be, M as fromEmitterEvent, O as Pe, Q as we, V as throwIfDetached, X as Frame, Y as Mouse, Z as MouseButton, _ as Touchscreen, $ as Keyboard, a0 as HTTPRequest, a1 as HTTPResponse, a2 as EventSubscription, a3 as NetworkManagerEvent, a4 as Page, a5 as FrameTree, a6 as Accessibility, a7 as Tracing, a8 as Coverage, a9 as EmulationManager$1, aa as ConsoleMessage, ab as validateDialogType, ac as parsePDFOptions, ad as isErrorLike, ae as evaluationString, af as Target, ag as TargetType, ah as Browser$1 } from "./main-BWWd-5rQ.js";
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
class BidiDeserializer {
  static deserializeNumber(value) {
    switch (value) {
      case "-0":
        return -0;
      case "NaN":
        return NaN;
      case "Infinity":
        return Infinity;
      case "-Infinity":
        return -Infinity;
      default:
        return value;
    }
  }
  static deserializeLocalValue(result) {
    switch (result.type) {
      case "array":
        return result.value?.map((value) => {
          return BidiDeserializer.deserializeLocalValue(value);
        });
      case "set":
        return result.value?.reduce((acc, value) => {
          return acc.add(BidiDeserializer.deserializeLocalValue(value));
        }, /* @__PURE__ */ new Set());
      case "object":
        return result.value?.reduce((acc, tuple) => {
          const { key, value } = BidiDeserializer.deserializeTuple(tuple);
          acc[key] = value;
          return acc;
        }, {});
      case "map":
        return result.value?.reduce((acc, tuple) => {
          const { key, value } = BidiDeserializer.deserializeTuple(tuple);
          return acc.set(key, value);
        }, /* @__PURE__ */ new Map());
      case "promise":
        return {};
      case "regexp":
        return new RegExp(result.value.pattern, result.value.flags);
      case "date":
        return new Date(result.value);
      case "undefined":
        return void 0;
      case "null":
        return null;
      case "number":
        return BidiDeserializer.deserializeNumber(result.value);
      case "bigint":
        return BigInt(result.value);
      case "boolean":
        return Boolean(result.value);
      case "string":
        return result.value;
    }
    debugError(`Deserialization of type ${result.type} not supported.`);
    return void 0;
  }
  static deserializeTuple([serializedKey, serializedValue]) {
    const key = typeof serializedKey === "string" ? serializedKey : BidiDeserializer.deserializeLocalValue(serializedKey);
    const value = BidiDeserializer.deserializeLocalValue(serializedValue);
    return { key, value };
  }
  static deserialize(result) {
    if (!result) {
      debugError("Service did not produce a result.");
      return void 0;
    }
    return BidiDeserializer.deserializeLocalValue(result);
  }
}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
async function releaseReference(client, remoteReference) {
  if (!remoteReference.handle) {
    return;
  }
  await client.connection.send("script.disown", {
    target: client.target,
    handles: [remoteReference.handle]
  }).catch((error) => {
    debugError(error);
  });
}
function createEvaluationError(details) {
  if (details.exception.type !== "error") {
    return BidiDeserializer.deserialize(details.exception);
  }
  const [name = "", ...parts] = details.text.split(": ");
  const message = parts.join(": ");
  const error = new Error(message);
  error.name = name;
  const stackLines = [];
  if (details.stackTrace && stackLines.length < Error.stackTraceLimit) {
    for (const frame of details.stackTrace.callFrames.reverse()) {
      if (PuppeteerURL.isPuppeteerURL(frame.url) && frame.url !== PuppeteerURL.INTERNAL_URL) {
        const url = PuppeteerURL.parse(frame.url);
        stackLines.unshift(`    at ${frame.functionName || url.functionName} (${url.functionName} at ${url.siteString}, <anonymous>:${frame.lineNumber}:${frame.columnNumber})`);
      } else {
        stackLines.push(`    at ${frame.functionName || "<anonymous>"} (${frame.url}:${frame.lineNumber}:${frame.columnNumber})`);
      }
      if (stackLines.length >= Error.stackTraceLimit) {
        break;
      }
    }
  }
  error.stack = [details.text, ...stackLines].join("\n");
  return error;
}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
class BidiJSHandle extends JSHandle {
  #disposed = false;
  #sandbox;
  #remoteValue;
  constructor(sandbox, remoteValue) {
    super();
    this.#sandbox = sandbox;
    this.#remoteValue = remoteValue;
  }
  context() {
    return this.realm.environment.context();
  }
  get realm() {
    return this.#sandbox;
  }
  get disposed() {
    return this.#disposed;
  }
  async jsonValue() {
    return await this.evaluate((value) => {
      return value;
    });
  }
  asElement() {
    return null;
  }
  async dispose() {
    if (this.#disposed) {
      return;
    }
    this.#disposed = true;
    if ("handle" in this.#remoteValue) {
      await releaseReference(this.context(), this.#remoteValue);
    }
  }
  get isPrimitiveValue() {
    switch (this.#remoteValue.type) {
      case "string":
      case "number":
      case "bigint":
      case "boolean":
      case "undefined":
      case "null":
        return true;
      default:
        return false;
    }
  }
  toString() {
    if (this.isPrimitiveValue) {
      return "JSHandle:" + BidiDeserializer.deserialize(this.#remoteValue);
    }
    return "JSHandle@" + this.#remoteValue.type;
  }
  get id() {
    return "handle" in this.#remoteValue ? this.#remoteValue.handle : void 0;
  }
  remoteValue() {
    return this.#remoteValue;
  }
  remoteObject() {
    throw new UnsupportedOperation("Not available in WebDriver BiDi");
  }
}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
var __runInitializers$9 = function(thisArg, initializers, value) {
  var useValue = arguments.length > 2;
  for (var i = 0; i < initializers.length; i++) {
    value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
  }
  return useValue ? value : void 0;
};
var __esDecorate$9 = function(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
  function accept(f) {
    if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected");
    return f;
  }
  var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
  var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
  var _, done = false;
  for (var i = decorators.length - 1; i >= 0; i--) {
    var context = {};
    for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
    for (var p in contextIn.access) context.access[p] = contextIn.access[p];
    context.addInitializer = function(f) {
      if (done) throw new TypeError("Cannot add initializers after decoration has completed");
      extraInitializers.push(accept(f || null));
    };
    var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
    if (kind === "accessor") {
      if (result === void 0) continue;
      if (result === null || typeof result !== "object") throw new TypeError("Object expected");
      if (_ = accept(result.get)) descriptor.get = _;
      if (_ = accept(result.set)) descriptor.set = _;
      if (_ = accept(result.init)) initializers.unshift(_);
    } else if (_ = accept(result)) {
      if (kind === "field") initializers.unshift(_);
      else descriptor[key] = _;
    }
  }
  if (target) Object.defineProperty(target, contextIn.name, descriptor);
  done = true;
};
var __addDisposableResource$2 = function(env, value, async) {
  if (value !== null && value !== void 0) {
    if (typeof value !== "object" && typeof value !== "function") throw new TypeError("Object expected.");
    var dispose;
    if (async) {
      if (!Symbol.asyncDispose) throw new TypeError("Symbol.asyncDispose is not defined.");
      dispose = value[Symbol.asyncDispose];
    }
    if (dispose === void 0) {
      if (!Symbol.dispose) throw new TypeError("Symbol.dispose is not defined.");
      dispose = value[Symbol.dispose];
    }
    if (typeof dispose !== "function") throw new TypeError("Object not disposable.");
    env.stack.push({ value, dispose, async });
  } else if (async) {
    env.stack.push({ async: true });
  }
  return value;
};
var __disposeResources$2 = /* @__PURE__ */ function(SuppressedError2) {
  return function(env) {
    function fail(e) {
      env.error = env.hasError ? new SuppressedError2(e, env.error, "An error was suppressed during disposal.") : e;
      env.hasError = true;
    }
    function next() {
      while (env.stack.length) {
        var rec = env.stack.pop();
        try {
          var result = rec.dispose && rec.dispose.call(rec.value);
          if (rec.async) return Promise.resolve(result).then(next, function(e) {
            fail(e);
            return next();
          });
        } catch (e) {
          fail(e);
        }
      }
      if (env.hasError) throw env.error;
    }
    return next();
  };
}(typeof SuppressedError === "function" ? SuppressedError : function(error, suppressed, message) {
  var e = new Error(message);
  return e.name = "SuppressedError", e.error = error, e.suppressed = suppressed, e;
});
let BidiElementHandle = (() => {
  var _a;
  let _classSuper = ElementHandle;
  let _instanceExtraInitializers = [];
  let _autofill_decorators;
  let _contentFrame_decorators;
  return class BidiElementHandle extends _classSuper {
    static {
      const _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;
      _autofill_decorators = [throwIfDisposed()];
      _contentFrame_decorators = [throwIfDisposed(), (_a = ElementHandle).bindIsolatedHandle.bind(_a)];
      __esDecorate$9(this, null, _autofill_decorators, { kind: "method", name: "autofill", static: false, private: false, access: { has: (obj) => "autofill" in obj, get: (obj) => obj.autofill }, metadata: _metadata }, null, _instanceExtraInitializers);
      __esDecorate$9(this, null, _contentFrame_decorators, { kind: "method", name: "contentFrame", static: false, private: false, access: { has: (obj) => "contentFrame" in obj, get: (obj) => obj.contentFrame }, metadata: _metadata }, null, _instanceExtraInitializers);
      if (_metadata) Object.defineProperty(this, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
    }
    constructor(sandbox, remoteValue) {
      super(new BidiJSHandle(sandbox, remoteValue));
      __runInitializers$9(this, _instanceExtraInitializers);
    }
    get realm() {
      return this.handle.realm;
    }
    get frame() {
      return this.realm.environment;
    }
    context() {
      return this.handle.context();
    }
    get isPrimitiveValue() {
      return this.handle.isPrimitiveValue;
    }
    remoteValue() {
      return this.handle.remoteValue();
    }
    async autofill(data) {
      const client = this.frame.client;
      const nodeInfo = await client.send("DOM.describeNode", {
        objectId: this.handle.id
      });
      const fieldId = nodeInfo.node.backendNodeId;
      const frameId = this.frame._id;
      await client.send("Autofill.trigger", {
        fieldId,
        frameId,
        card: data.creditCard
      });
    }
    async contentFrame() {
      const env_1 = { stack: [], error: void 0, hasError: false };
      try {
        const handle = __addDisposableResource$2(env_1, await this.evaluateHandle((element) => {
          if (element instanceof HTMLIFrameElement) {
            return element.contentWindow;
          }
          return;
        }), false);
        const value = handle.remoteValue();
        if (value.type === "window") {
          return this.frame.page().frame(value.value.context);
        }
        return null;
      } catch (e_1) {
        env_1.error = e_1;
        env_1.hasError = true;
      } finally {
        __disposeResources$2(env_1);
      }
    }
    uploadFile() {
      throw new UnsupportedOperation();
    }
  };
})();
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
class UnserializableError extends Error {
}
class BidiSerializer {
  static serializeNumber(arg) {
    let value;
    if (Object.is(arg, -0)) {
      value = "-0";
    } else if (Object.is(arg, Infinity)) {
      value = "Infinity";
    } else if (Object.is(arg, -Infinity)) {
      value = "-Infinity";
    } else if (Object.is(arg, NaN)) {
      value = "NaN";
    } else {
      value = arg;
    }
    return {
      type: "number",
      value
    };
  }
  static serializeObject(arg) {
    if (arg === null) {
      return {
        type: "null"
      };
    } else if (Array.isArray(arg)) {
      const parsedArray = arg.map((subArg) => {
        return BidiSerializer.serializeRemoteValue(subArg);
      });
      return {
        type: "array",
        value: parsedArray
      };
    } else if (isPlainObject(arg)) {
      try {
        JSON.stringify(arg);
      } catch (error) {
        if (error instanceof TypeError && error.message.startsWith("Converting circular structure to JSON")) {
          error.message += " Recursive objects are not allowed.";
        }
        throw error;
      }
      const parsedObject = [];
      for (const key in arg) {
        parsedObject.push([
          BidiSerializer.serializeRemoteValue(key),
          BidiSerializer.serializeRemoteValue(arg[key])
        ]);
      }
      return {
        type: "object",
        value: parsedObject
      };
    } else if (isRegExp(arg)) {
      return {
        type: "regexp",
        value: {
          pattern: arg.source,
          flags: arg.flags
        }
      };
    } else if (isDate(arg)) {
      return {
        type: "date",
        value: arg.toISOString()
      };
    }
    throw new UnserializableError("Custom object sterilization not possible. Use plain objects instead.");
  }
  static serializeRemoteValue(arg) {
    switch (typeof arg) {
      case "symbol":
      case "function":
        throw new UnserializableError(`Unable to serializable ${typeof arg}`);
      case "object":
        return BidiSerializer.serializeObject(arg);
      case "undefined":
        return {
          type: "undefined"
        };
      case "number":
        return BidiSerializer.serializeNumber(arg);
      case "bigint":
        return {
          type: "bigint",
          value: arg.toString()
        };
      case "string":
        return {
          type: "string",
          value: arg
        };
      case "boolean":
        return {
          type: "boolean",
          value: arg
        };
    }
  }
  static async serialize(sandbox, arg) {
    if (arg instanceof LazyArg) {
      arg = await arg.get(sandbox.realm);
    }
    const objectHandle = arg && (arg instanceof BidiJSHandle || arg instanceof BidiElementHandle) ? arg : null;
    if (objectHandle) {
      if (objectHandle.realm.environment.context() !== sandbox.environment.context()) {
        throw new Error("JSHandles can be evaluated only in the context they were created!");
      }
      if (objectHandle.disposed) {
        throw new Error("JSHandle is disposed!");
      }
      return objectHandle.remoteValue();
    }
    return BidiSerializer.serializeRemoteValue(arg);
  }
}
class BidiRealm extends EventEmitter {
  connection;
  #id;
  #sandbox;
  constructor(connection) {
    super();
    this.connection = connection;
  }
  get target() {
    return {
      context: this.#sandbox.environment._id,
      sandbox: this.#sandbox.name
    };
  }
  handleRealmDestroyed = async (params) => {
    if (params.realm === this.#id) {
      this.internalPuppeteerUtil = void 0;
      this.#sandbox.environment.clearDocumentHandle();
    }
  };
  handleRealmCreated = (params) => {
    if (params.type === "window" && params.context === this.#sandbox.environment._id && params.sandbox === this.#sandbox.name) {
      this.#id = params.realm;
      void this.#sandbox.taskManager.rerunAll();
    }
  };
  setSandbox(sandbox) {
    this.#sandbox = sandbox;
    this.connection.on(protocol.ChromiumBidi.Script.EventNames.RealmCreated, this.handleRealmCreated);
    this.connection.on(protocol.ChromiumBidi.Script.EventNames.RealmDestroyed, this.handleRealmDestroyed);
  }
  internalPuppeteerUtil;
  get puppeteerUtil() {
    const promise = Promise.resolve();
    scriptInjector.inject((script) => {
      if (this.internalPuppeteerUtil) {
        void this.internalPuppeteerUtil.then((handle) => {
          void handle.dispose();
        });
      }
      this.internalPuppeteerUtil = promise.then(() => {
        return this.evaluateHandle(script);
      });
    }, !this.internalPuppeteerUtil);
    return this.internalPuppeteerUtil;
  }
  async evaluateHandle(pageFunction, ...args) {
    return await this.#evaluate(false, pageFunction, ...args);
  }
  async evaluate(pageFunction, ...args) {
    return await this.#evaluate(true, pageFunction, ...args);
  }
  async #evaluate(returnByValue, pageFunction, ...args) {
    const sourceUrlComment = getSourceUrlComment(getSourcePuppeteerURLIfAvailable(pageFunction)?.toString() ?? PuppeteerURL.INTERNAL_URL);
    const sandbox = this.#sandbox;
    let responsePromise;
    const resultOwnership = returnByValue ? "none" : "root";
    const serializationOptions = returnByValue ? {} : {
      maxObjectDepth: 0,
      maxDomDepth: 0
    };
    if (isString(pageFunction)) {
      const expression = SOURCE_URL_REGEX.test(pageFunction) ? pageFunction : `${pageFunction}
${sourceUrlComment}
`;
      responsePromise = this.connection.send("script.evaluate", {
        expression,
        target: this.target,
        resultOwnership,
        awaitPromise: true,
        userActivation: true,
        serializationOptions
      });
    } else {
      let functionDeclaration = stringifyFunction(pageFunction);
      functionDeclaration = SOURCE_URL_REGEX.test(functionDeclaration) ? functionDeclaration : `${functionDeclaration}
${sourceUrlComment}
`;
      responsePromise = this.connection.send("script.callFunction", {
        functionDeclaration,
        arguments: args.length ? await Promise.all(args.map((arg) => {
          return BidiSerializer.serialize(sandbox, arg);
        })) : [],
        target: this.target,
        resultOwnership,
        awaitPromise: true,
        userActivation: true,
        serializationOptions
      });
    }
    const { result } = await responsePromise;
    if ("type" in result && result.type === "exception") {
      throw createEvaluationError(result.exceptionDetails);
    }
    return returnByValue ? BidiDeserializer.deserialize(result.result) : createBidiHandle(sandbox, result.result);
  }
  [disposeSymbol]() {
    this.connection.off(protocol.ChromiumBidi.Script.EventNames.RealmCreated, this.handleRealmCreated);
    this.connection.off(protocol.ChromiumBidi.Script.EventNames.RealmDestroyed, this.handleRealmDestroyed);
  }
}
function createBidiHandle(sandbox, result) {
  if (result.type === "node" || result.type === "window") {
    return new BidiElementHandle(sandbox, result);
  }
  return new BidiJSHandle(sandbox, result);
}
const cdpSessions = /* @__PURE__ */ new Map();
class CdpSessionWrapper extends CDPSession {
  #context;
  #sessionId = Deferred.create();
  #detached = false;
  constructor(context, sessionId) {
    super();
    this.#context = context;
    if (!this.#context.supportsCdp()) {
      return;
    }
    if (sessionId) {
      this.#sessionId.resolve(sessionId);
      cdpSessions.set(sessionId, this);
    } else {
      context.connection.send("cdp.getSession", {
        context: context.id
      }).then((session) => {
        this.#sessionId.resolve(session.result.session);
        cdpSessions.set(session.result.session, this);
      }).catch((err) => {
        this.#sessionId.reject(err);
      });
    }
  }
  connection() {
    return void 0;
  }
  async send(method, ...paramArgs) {
    if (!this.#context.supportsCdp()) {
      throw new UnsupportedOperation("CDP support is required for this feature. The current browser does not support CDP.");
    }
    if (this.#detached) {
      throw new TargetCloseError(`Protocol error (${method}): Session closed. Most likely the page has been closed.`);
    }
    const session = await this.#sessionId.valueOrThrow();
    const { result } = await this.#context.connection.send("cdp.sendCommand", {
      method,
      params: paramArgs[0],
      session
    });
    return result.result;
  }
  async detach() {
    cdpSessions.delete(this.id());
    if (!this.#detached && this.#context.supportsCdp()) {
      await this.#context.cdpSession.send("Target.detachFromTarget", {
        sessionId: this.id()
      });
    }
    this.#detached = true;
  }
  id() {
    const val = this.#sessionId.value();
    return val instanceof Error || val === void 0 ? "" : val;
  }
}
var BrowsingContextEvent;
(function(BrowsingContextEvent2) {
  BrowsingContextEvent2.Created = Symbol("BrowsingContext.created");
  BrowsingContextEvent2.Destroyed = Symbol("BrowsingContext.destroyed");
})(BrowsingContextEvent || (BrowsingContextEvent = {}));
let BrowsingContext$1 = class BrowsingContext extends BidiRealm {
  #id;
  #url;
  #cdpSession;
  #parent;
  #browserName = "";
  constructor(connection, info, browserName) {
    super(connection);
    this.#id = info.context;
    this.#url = info.url;
    this.#parent = info.parent;
    this.#browserName = browserName;
    this.#cdpSession = new CdpSessionWrapper(this, void 0);
    this.on("browsingContext.domContentLoaded", this.#updateUrl.bind(this));
    this.on("browsingContext.fragmentNavigated", this.#updateUrl.bind(this));
    this.on("browsingContext.load", this.#updateUrl.bind(this));
  }
  supportsCdp() {
    return !this.#browserName.toLowerCase().includes("firefox");
  }
  #updateUrl(info) {
    this.#url = info.url;
  }
  createRealmForSandbox() {
    return new BidiRealm(this.connection);
  }
  get url() {
    return this.#url;
  }
  get id() {
    return this.#id;
  }
  get parent() {
    return this.#parent;
  }
  get cdpSession() {
    return this.#cdpSession;
  }
  async sendCdpCommand(method, ...paramArgs) {
    return await this.#cdpSession.send(method, ...paramArgs);
  }
  dispose() {
    this.removeAllListeners();
    this.connection.unregisterBrowsingContexts(this.#id);
    void this.#cdpSession.detach().catch(debugError);
  }
};
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
const debugProtocolSend = debug("puppeteer:webDriverBiDi:SEND ►");
const debugProtocolReceive = debug("puppeteer:webDriverBiDi:RECV ◀");
class BidiConnection extends EventEmitter {
  #url;
  #transport;
  #delay;
  #timeout = 0;
  #closed = false;
  #callbacks = new CallbackRegistry();
  #browsingContexts = /* @__PURE__ */ new Map();
  #emitters = [];
  constructor(url, transport, delay = 0, timeout2) {
    super();
    this.#url = url;
    this.#delay = delay;
    this.#timeout = timeout2 ?? 18e4;
    this.#transport = transport;
    this.#transport.onmessage = this.onMessage.bind(this);
    this.#transport.onclose = this.unbind.bind(this);
  }
  get closed() {
    return this.#closed;
  }
  get url() {
    return this.#url;
  }
  pipeTo(emitter) {
    this.#emitters.push(emitter);
  }
  emit(type, event) {
    for (const emitter of this.#emitters) {
      emitter.emit(type, event);
    }
    return super.emit(type, event);
  }
  send(method, params) {
    assert(!this.#closed, "Protocol error: Connection closed.");
    return this.#callbacks.create(method, this.#timeout, (id) => {
      const stringifiedMessage = JSON.stringify({
        id,
        method,
        params
      });
      debugProtocolSend(stringifiedMessage);
      this.#transport.send(stringifiedMessage);
    });
  }
  /**
   * @internal
   */
  async onMessage(message) {
    if (this.#delay) {
      await new Promise((f) => {
        return setTimeout(f, this.#delay);
      });
    }
    debugProtocolReceive(message);
    const object = JSON.parse(message);
    if ("type" in object) {
      switch (object.type) {
        case "success":
          this.#callbacks.resolve(object.id, object);
          return;
        case "error":
          if (object.id === null) {
            break;
          }
          this.#callbacks.reject(object.id, createProtocolError(object), object.message);
          return;
        case "event":
          if (isCdpEvent(object)) {
            cdpSessions.get(object.params.session)?.emit(object.params.event, object.params.params);
            return;
          }
          this.#maybeEmitOnContext(object);
          this.emit(object.method, object.params);
          return;
      }
    }
    if ("id" in object) {
      this.#callbacks.reject(object.id, `Protocol Error. Message is not in BiDi protocol format: '${message}'`, object.message);
    }
    debugError(object);
  }
  #maybeEmitOnContext(event) {
    let context;
    if ("context" in event.params && event.params.context !== null) {
      context = this.#browsingContexts.get(event.params.context);
    } else if ("source" in event.params && event.params.source.context !== void 0) {
      context = this.#browsingContexts.get(event.params.source.context);
    }
    context?.emit(event.method, event.params);
  }
  registerBrowsingContexts(context) {
    this.#browsingContexts.set(context.id, context);
  }
  getBrowsingContext(contextId) {
    const currentContext = this.#browsingContexts.get(contextId);
    if (!currentContext) {
      throw new Error(`BrowsingContext ${contextId} does not exist.`);
    }
    return currentContext;
  }
  getTopLevelContext(contextId) {
    let currentContext = this.#browsingContexts.get(contextId);
    if (!currentContext) {
      throw new Error(`BrowsingContext ${contextId} does not exist.`);
    }
    while (currentContext.parent) {
      contextId = currentContext.parent;
      currentContext = this.#browsingContexts.get(contextId);
      if (!currentContext) {
        throw new Error(`BrowsingContext ${contextId} does not exist.`);
      }
    }
    return currentContext;
  }
  unregisterBrowsingContexts(id) {
    this.#browsingContexts.delete(id);
  }
  /**
   * Unbinds the connection, but keeps the transport open. Useful when the transport will
   * be reused by other connection e.g. with different protocol.
   * @internal
   */
  unbind() {
    if (this.#closed) {
      return;
    }
    this.#closed = true;
    this.#transport.onmessage = () => {
    };
    this.#transport.onclose = () => {
    };
    this.#browsingContexts.clear();
    this.#callbacks.clear();
  }
  /**
   * Unbinds the connection and closes the transport.
   */
  dispose() {
    this.unbind();
    this.#transport.close();
  }
  getPendingProtocolErrors() {
    return this.#callbacks.getPendingProtocolErrors();
  }
}
function createProtocolError(object) {
  let message = `${object.error} ${object.message}`;
  if (object.stacktrace) {
    message += ` ${object.stacktrace}`;
  }
  return message;
}
function isCdpEvent(event) {
  return event.method.startsWith("cdp.");
}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
const bidiServerLogger = (prefix, ...args) => {
  debug(`bidi:${prefix}`)(args);
};
async function connectBidiOverCdp(cdp, options) {
  const transportBiDi = new NoOpTransport();
  const cdpConnectionAdapter = new CdpConnectionAdapter(cdp);
  const pptrTransport = {
    send(message) {
      transportBiDi.emitMessage(JSON.parse(message));
    },
    close() {
      bidiServer.close();
      cdpConnectionAdapter.close();
      cdp.dispose();
    },
    onmessage(_message) {
    }
  };
  transportBiDi.on("bidiResponse", (message) => {
    pptrTransport.onmessage(JSON.stringify(message));
  });
  const pptrBiDiConnection = new BidiConnection(cdp.url(), pptrTransport);
  const bidiServer = await BidiMapper.BidiServer.createAndStart(
    transportBiDi,
    cdpConnectionAdapter,
    // TODO: most likely need a little bit of refactoring
    cdpConnectionAdapter.browserClient(),
    "",
    options,
    void 0,
    bidiServerLogger
  );
  return pptrBiDiConnection;
}
class CdpConnectionAdapter {
  #cdp;
  #adapters = /* @__PURE__ */ new Map();
  #browserCdpConnection;
  constructor(cdp) {
    this.#cdp = cdp;
    this.#browserCdpConnection = new CDPClientAdapter(cdp);
  }
  browserClient() {
    return this.#browserCdpConnection;
  }
  getCdpClient(id) {
    const session = this.#cdp.session(id);
    if (!session) {
      throw new Error(`Unknown CDP session with id ${id}`);
    }
    if (!this.#adapters.has(session)) {
      const adapter = new CDPClientAdapter(session, id, this.#browserCdpConnection);
      this.#adapters.set(session, adapter);
      return adapter;
    }
    return this.#adapters.get(session);
  }
  close() {
    this.#browserCdpConnection.close();
    for (const adapter of this.#adapters.values()) {
      adapter.close();
    }
  }
}
class CDPClientAdapter extends BidiMapper.EventEmitter {
  #closed = false;
  #client;
  sessionId = void 0;
  #browserClient;
  constructor(client, sessionId, browserClient) {
    super();
    this.#client = client;
    this.sessionId = sessionId;
    this.#browserClient = browserClient;
    this.#client.on("*", this.#forwardMessage);
  }
  browserClient() {
    return this.#browserClient;
  }
  #forwardMessage = (method, event) => {
    this.emit(method, event);
  };
  async sendCommand(method, ...params) {
    if (this.#closed) {
      return;
    }
    try {
      return await this.#client.send(method, ...params);
    } catch (err) {
      if (this.#closed) {
        return;
      }
      throw err;
    }
  }
  close() {
    this.#client.off("*", this.#forwardMessage);
    this.#closed = true;
  }
  isCloseError(error) {
    return error instanceof TargetCloseError;
  }
}
class NoOpTransport extends BidiMapper.EventEmitter {
  #onMessage = async (_m) => {
    return;
  };
  emitMessage(message) {
    void this.#onMessage(message);
  }
  setOnMessage(onMessage) {
    this.#onMessage = onMessage;
  }
  async sendMessage(message) {
    this.emit("bidiResponse", message);
  }
  close() {
    this.#onMessage = async (_m) => {
      return;
    };
  }
}
/**
 * @license
 * Copyright 2024 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
var __runInitializers$8 = function(thisArg, initializers, value) {
  var useValue = arguments.length > 2;
  for (var i = 0; i < initializers.length; i++) {
    value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
  }
  return useValue ? value : void 0;
};
var __esDecorate$8 = function(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
  function accept(f) {
    if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected");
    return f;
  }
  var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
  var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
  var _, done = false;
  for (var i = decorators.length - 1; i >= 0; i--) {
    var context = {};
    for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
    for (var p in contextIn.access) context.access[p] = contextIn.access[p];
    context.addInitializer = function(f) {
      if (done) throw new TypeError("Cannot add initializers after decoration has completed");
      extraInitializers.push(accept(f || null));
    };
    var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
    if (kind === "accessor") {
      if (result === void 0) continue;
      if (result === null || typeof result !== "object") throw new TypeError("Object expected");
      if (_ = accept(result.get)) descriptor.get = _;
      if (_ = accept(result.set)) descriptor.set = _;
      if (_ = accept(result.init)) initializers.unshift(_);
    } else if (_ = accept(result)) {
      if (kind === "field") initializers.unshift(_);
      else descriptor[key] = _;
    }
  }
  if (target) Object.defineProperty(target, contextIn.name, descriptor);
  done = true;
};
let Navigation = (() => {
  let _classSuper = EventEmitter;
  let _instanceExtraInitializers = [];
  let _dispose_decorators;
  return class Navigation2 extends _classSuper {
    static {
      const _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;
      __esDecorate$8(this, null, _dispose_decorators, { kind: "method", name: "dispose", static: false, private: false, access: { has: (obj) => "dispose" in obj, get: (obj) => obj.dispose }, metadata: _metadata }, null, _instanceExtraInitializers);
      if (_metadata) Object.defineProperty(this, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
    }
    static from(context) {
      const navigation = new Navigation2(context);
      navigation.#initialize();
      return navigation;
    }
    // keep-sorted start
    #request = (__runInitializers$8(this, _instanceExtraInitializers), void 0);
    #browsingContext;
    #disposables = new DisposableStack();
    #id = new Deferred();
    // keep-sorted end
    constructor(context) {
      super();
      this.#browsingContext = context;
    }
    #initialize() {
      const browsingContextEmitter = this.#disposables.use(new EventEmitter(this.#browsingContext));
      browsingContextEmitter.once("closed", () => {
        this.emit("failed", {
          url: this.#browsingContext.url,
          timestamp: /* @__PURE__ */ new Date()
        });
        this.dispose();
      });
      this.#browsingContext.on("request", ({ request }) => {
        if (request.navigation === this.#id.value()) {
          this.#request = request;
          this.emit("request", request);
        }
      });
      const sessionEmitter = this.#disposables.use(new EventEmitter(this.#session));
      for (const eventName of [
        "browsingContext.domContentLoaded",
        "browsingContext.load"
      ]) {
        sessionEmitter.on(eventName, (info) => {
          if (info.context !== this.#browsingContext.id) {
            return;
          }
          if (!info.navigation) {
            return;
          }
          if (!this.#id.resolved()) {
            this.#id.resolve(info.navigation);
          }
        });
      }
      for (const [eventName, event] of [
        ["browsingContext.fragmentNavigated", "fragment"],
        ["browsingContext.navigationFailed", "failed"],
        ["browsingContext.navigationAborted", "aborted"]
      ]) {
        sessionEmitter.on(eventName, (info) => {
          if (info.context !== this.#browsingContext.id) {
            return;
          }
          if (!info.navigation) {
            return;
          }
          if (!this.#id.resolved()) {
            this.#id.resolve(info.navigation);
          }
          if (this.#id.value() !== info.navigation) {
            return;
          }
          this.emit(event, {
            url: info.url,
            timestamp: new Date(info.timestamp)
          });
          this.dispose();
        });
      }
    }
    // keep-sorted start block=yes
    get #session() {
      return this.#browsingContext.userContext.browser.session;
    }
    get disposed() {
      return this.#disposables.disposed;
    }
    get request() {
      return this.#request;
    }
    // keep-sorted end
    dispose() {
      this[disposeSymbol]();
    }
    [(_dispose_decorators = [inertIfDisposed], disposeSymbol)]() {
      this.#disposables.dispose();
      super[disposeSymbol]();
    }
  };
})();
/**
 * @license
 * Copyright 2024 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
var __runInitializers$7 = function(thisArg, initializers, value) {
  var useValue = arguments.length > 2;
  for (var i = 0; i < initializers.length; i++) {
    value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
  }
  return useValue ? value : void 0;
};
var __esDecorate$7 = function(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
  function accept(f) {
    if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected");
    return f;
  }
  var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
  var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
  var _, done = false;
  for (var i = decorators.length - 1; i >= 0; i--) {
    var context = {};
    for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
    for (var p in contextIn.access) context.access[p] = contextIn.access[p];
    context.addInitializer = function(f) {
      if (done) throw new TypeError("Cannot add initializers after decoration has completed");
      extraInitializers.push(accept(f || null));
    };
    var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
    if (kind === "accessor") {
      if (result === void 0) continue;
      if (result === null || typeof result !== "object") throw new TypeError("Object expected");
      if (_ = accept(result.get)) descriptor.get = _;
      if (_ = accept(result.set)) descriptor.set = _;
      if (_ = accept(result.init)) initializers.unshift(_);
    } else if (_ = accept(result)) {
      if (kind === "field") initializers.unshift(_);
      else descriptor[key] = _;
    }
  }
  if (target) Object.defineProperty(target, contextIn.name, descriptor);
  done = true;
};
let Realm = (() => {
  let _classSuper = EventEmitter;
  let _instanceExtraInitializers = [];
  let _dispose_decorators;
  let _disown_decorators;
  let _callFunction_decorators;
  let _evaluate_decorators;
  return class Realm extends _classSuper {
    static {
      const _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;
      __esDecorate$7(this, null, _dispose_decorators, { kind: "method", name: "dispose", static: false, private: false, access: { has: (obj) => "dispose" in obj, get: (obj) => obj.dispose }, metadata: _metadata }, null, _instanceExtraInitializers);
      __esDecorate$7(this, null, _disown_decorators, { kind: "method", name: "disown", static: false, private: false, access: { has: (obj) => "disown" in obj, get: (obj) => obj.disown }, metadata: _metadata }, null, _instanceExtraInitializers);
      __esDecorate$7(this, null, _callFunction_decorators, { kind: "method", name: "callFunction", static: false, private: false, access: { has: (obj) => "callFunction" in obj, get: (obj) => obj.callFunction }, metadata: _metadata }, null, _instanceExtraInitializers);
      __esDecorate$7(this, null, _evaluate_decorators, { kind: "method", name: "evaluate", static: false, private: false, access: { has: (obj) => "evaluate" in obj, get: (obj) => obj.evaluate }, metadata: _metadata }, null, _instanceExtraInitializers);
      if (_metadata) Object.defineProperty(this, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
    }
    // keep-sorted start
    #reason = (__runInitializers$7(this, _instanceExtraInitializers), void 0);
    disposables = new DisposableStack();
    id;
    origin;
    // keep-sorted end
    constructor(id, origin) {
      super();
      this.id = id;
      this.origin = origin;
    }
    initialize() {
      const sessionEmitter = this.disposables.use(new EventEmitter(this.session));
      sessionEmitter.on("script.realmDestroyed", (info) => {
        if (info.realm !== this.id) {
          return;
        }
        this.dispose("Realm already destroyed.");
      });
    }
    // keep-sorted start block=yes
    get disposed() {
      return this.#reason !== void 0;
    }
    get target() {
      return { realm: this.id };
    }
    // keep-sorted end
    dispose(reason) {
      this.#reason = reason;
      this[disposeSymbol]();
    }
    async disown(handles) {
      await this.session.send("script.disown", {
        target: this.target,
        handles
      });
    }
    async callFunction(functionDeclaration, awaitPromise, options = {}) {
      const { result } = await this.session.send("script.callFunction", {
        functionDeclaration,
        awaitPromise,
        target: this.target,
        ...options
      });
      return result;
    }
    async evaluate(expression, awaitPromise, options = {}) {
      const { result } = await this.session.send("script.evaluate", {
        expression,
        awaitPromise,
        target: this.target,
        ...options
      });
      return result;
    }
    [(_dispose_decorators = [inertIfDisposed], _disown_decorators = [throwIfDisposed((realm) => {
      return realm.#reason;
    })], _callFunction_decorators = [throwIfDisposed((realm) => {
      return realm.#reason;
    })], _evaluate_decorators = [throwIfDisposed((realm) => {
      return realm.#reason;
    })], disposeSymbol)]() {
      this.#reason ??= "Realm already destroyed, probably because all associated browsing contexts closed.";
      this.emit("destroyed", { reason: this.#reason });
      this.disposables.dispose();
      super[disposeSymbol]();
    }
  };
})();
class WindowRealm extends Realm {
  static from(context, sandbox) {
    const realm = new WindowRealm(context, sandbox);
    realm.initialize();
    return realm;
  }
  // keep-sorted start
  browsingContext;
  sandbox;
  // keep-sorted end
  #workers = {
    dedicated: /* @__PURE__ */ new Map(),
    shared: /* @__PURE__ */ new Map()
  };
  constructor(context, sandbox) {
    super("", "");
    this.browsingContext = context;
    this.sandbox = sandbox;
  }
  initialize() {
    super.initialize();
    const sessionEmitter = this.disposables.use(new EventEmitter(this.session));
    sessionEmitter.on("script.realmCreated", (info) => {
      if (info.type !== "window") {
        return;
      }
      this.id = info.realm;
      this.origin = info.origin;
    });
    sessionEmitter.on("script.realmCreated", (info) => {
      if (info.type !== "dedicated-worker") {
        return;
      }
      if (!info.owners.includes(this.id)) {
        return;
      }
      const realm = DedicatedWorkerRealm.from(this, info.realm, info.origin);
      this.#workers.dedicated.set(realm.id, realm);
      const realmEmitter = this.disposables.use(new EventEmitter(realm));
      realmEmitter.once("destroyed", () => {
        realmEmitter.removeAllListeners();
        this.#workers.dedicated.delete(realm.id);
      });
      this.emit("worker", realm);
    });
    this.browsingContext.userContext.browser.on("sharedworker", ({ realm }) => {
      if (!realm.owners.has(this)) {
        return;
      }
      this.#workers.shared.set(realm.id, realm);
      const realmEmitter = this.disposables.use(new EventEmitter(realm));
      realmEmitter.once("destroyed", () => {
        realmEmitter.removeAllListeners();
        this.#workers.shared.delete(realm.id);
      });
      this.emit("sharedworker", realm);
    });
  }
  get session() {
    return this.browsingContext.userContext.browser.session;
  }
  get target() {
    return { context: this.browsingContext.id, sandbox: this.sandbox };
  }
}
class DedicatedWorkerRealm extends Realm {
  static from(owner, id, origin) {
    const realm = new DedicatedWorkerRealm(owner, id, origin);
    realm.initialize();
    return realm;
  }
  // keep-sorted start
  #workers = /* @__PURE__ */ new Map();
  owners;
  // keep-sorted end
  constructor(owner, id, origin) {
    super(id, origin);
    this.owners = /* @__PURE__ */ new Set([owner]);
  }
  initialize() {
    super.initialize();
    const sessionEmitter = this.disposables.use(new EventEmitter(this.session));
    sessionEmitter.on("script.realmCreated", (info) => {
      if (info.type !== "dedicated-worker") {
        return;
      }
      if (!info.owners.includes(this.id)) {
        return;
      }
      const realm = DedicatedWorkerRealm.from(this, info.realm, info.origin);
      this.#workers.set(realm.id, realm);
      const realmEmitter = this.disposables.use(new EventEmitter(realm));
      realmEmitter.once("destroyed", () => {
        this.#workers.delete(realm.id);
      });
      this.emit("worker", realm);
    });
  }
  get session() {
    return this.owners.values().next().value.session;
  }
}
/**
 * @license
 * Copyright 2024 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
var __runInitializers$6 = function(thisArg, initializers, value) {
  var useValue = arguments.length > 2;
  for (var i = 0; i < initializers.length; i++) {
    value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
  }
  return useValue ? value : void 0;
};
var __esDecorate$6 = function(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
  function accept(f) {
    if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected");
    return f;
  }
  var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
  var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
  var _, done = false;
  for (var i = decorators.length - 1; i >= 0; i--) {
    var context = {};
    for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
    for (var p in contextIn.access) context.access[p] = contextIn.access[p];
    context.addInitializer = function(f) {
      if (done) throw new TypeError("Cannot add initializers after decoration has completed");
      extraInitializers.push(accept(f || null));
    };
    var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
    if (kind === "accessor") {
      if (result === void 0) continue;
      if (result === null || typeof result !== "object") throw new TypeError("Object expected");
      if (_ = accept(result.get)) descriptor.get = _;
      if (_ = accept(result.set)) descriptor.set = _;
      if (_ = accept(result.init)) initializers.unshift(_);
    } else if (_ = accept(result)) {
      if (kind === "field") initializers.unshift(_);
      else descriptor[key] = _;
    }
  }
  if (target) Object.defineProperty(target, contextIn.name, descriptor);
  done = true;
};
let Request = (() => {
  let _classSuper = EventEmitter;
  let _instanceExtraInitializers = [];
  let _dispose_decorators;
  return class Request2 extends _classSuper {
    static {
      const _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;
      __esDecorate$6(this, null, _dispose_decorators, { kind: "method", name: "dispose", static: false, private: false, access: { has: (obj) => "dispose" in obj, get: (obj) => obj.dispose }, metadata: _metadata }, null, _instanceExtraInitializers);
      if (_metadata) Object.defineProperty(this, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
    }
    static from(browsingContext, event) {
      const request = new Request2(browsingContext, event);
      request.#initialize();
      return request;
    }
    // keep-sorted start
    #error = (__runInitializers$6(this, _instanceExtraInitializers), void 0);
    #redirect;
    #response;
    #browsingContext;
    #disposables = new DisposableStack();
    #event;
    // keep-sorted end
    constructor(browsingContext, event) {
      super();
      this.#browsingContext = browsingContext;
      this.#event = event;
    }
    #initialize() {
      const browsingContextEmitter = this.#disposables.use(new EventEmitter(this.#browsingContext));
      browsingContextEmitter.once("closed", ({ reason }) => {
        this.#error = reason;
        this.emit("error", this.#error);
        this.dispose();
      });
      const sessionEmitter = this.#disposables.use(new EventEmitter(this.#session));
      sessionEmitter.on("network.beforeRequestSent", (event) => {
        if (event.context !== this.#browsingContext.id) {
          return;
        }
        if (event.request.request !== this.id) {
          return;
        }
        this.#redirect = Request2.from(this.#browsingContext, event);
        this.emit("redirect", this.#redirect);
        this.dispose();
      });
      sessionEmitter.on("network.fetchError", (event) => {
        if (event.context !== this.#browsingContext.id) {
          return;
        }
        if (event.request.request !== this.id) {
          return;
        }
        this.#error = event.errorText;
        this.emit("error", this.#error);
        this.dispose();
      });
      sessionEmitter.on("network.responseCompleted", (event) => {
        if (event.context !== this.#browsingContext.id) {
          return;
        }
        if (event.request.request !== this.id) {
          return;
        }
        this.#response = event.response;
        this.emit("success", this.#response);
        this.dispose();
      });
    }
    // keep-sorted start block=yes
    get #session() {
      return this.#browsingContext.userContext.browser.session;
    }
    get disposed() {
      return this.#disposables.disposed;
    }
    get error() {
      return this.#error;
    }
    get headers() {
      return this.#event.request.headers;
    }
    get id() {
      return this.#event.request.request;
    }
    get initiator() {
      return this.#event.initiator;
    }
    get method() {
      return this.#event.request.method;
    }
    get navigation() {
      return this.#event.navigation ?? void 0;
    }
    get redirect() {
      return this.redirect;
    }
    get response() {
      return this.#response;
    }
    get url() {
      return this.#event.request.url;
    }
    // keep-sorted end
    dispose() {
      this[disposeSymbol]();
    }
    [(_dispose_decorators = [inertIfDisposed], disposeSymbol)]() {
      this.#disposables.dispose();
      super[disposeSymbol]();
    }
  };
})();
/**
 * @license
 * Copyright 2024 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
var __runInitializers$5 = function(thisArg, initializers, value) {
  var useValue = arguments.length > 2;
  for (var i = 0; i < initializers.length; i++) {
    value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
  }
  return useValue ? value : void 0;
};
var __esDecorate$5 = function(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
  function accept(f) {
    if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected");
    return f;
  }
  var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
  var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
  var _, done = false;
  for (var i = decorators.length - 1; i >= 0; i--) {
    var context = {};
    for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
    for (var p in contextIn.access) context.access[p] = contextIn.access[p];
    context.addInitializer = function(f) {
      if (done) throw new TypeError("Cannot add initializers after decoration has completed");
      extraInitializers.push(accept(f || null));
    };
    var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
    if (kind === "accessor") {
      if (result === void 0) continue;
      if (result === null || typeof result !== "object") throw new TypeError("Object expected");
      if (_ = accept(result.get)) descriptor.get = _;
      if (_ = accept(result.set)) descriptor.set = _;
      if (_ = accept(result.init)) initializers.unshift(_);
    } else if (_ = accept(result)) {
      if (kind === "field") initializers.unshift(_);
      else descriptor[key] = _;
    }
  }
  if (target) Object.defineProperty(target, contextIn.name, descriptor);
  done = true;
};
let UserPrompt = (() => {
  let _classSuper = EventEmitter;
  let _instanceExtraInitializers = [];
  let _dispose_decorators;
  let _handle_decorators;
  return class UserPrompt2 extends _classSuper {
    static {
      const _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;
      __esDecorate$5(this, null, _dispose_decorators, { kind: "method", name: "dispose", static: false, private: false, access: { has: (obj) => "dispose" in obj, get: (obj) => obj.dispose }, metadata: _metadata }, null, _instanceExtraInitializers);
      __esDecorate$5(this, null, _handle_decorators, { kind: "method", name: "handle", static: false, private: false, access: { has: (obj) => "handle" in obj, get: (obj) => obj.handle }, metadata: _metadata }, null, _instanceExtraInitializers);
      if (_metadata) Object.defineProperty(this, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
    }
    static from(browsingContext, info) {
      const userPrompt = new UserPrompt2(browsingContext, info);
      userPrompt.#initialize();
      return userPrompt;
    }
    // keep-sorted start
    #reason = (__runInitializers$5(this, _instanceExtraInitializers), void 0);
    #result;
    #disposables = new DisposableStack();
    browsingContext;
    info;
    // keep-sorted end
    constructor(context, info) {
      super();
      this.browsingContext = context;
      this.info = info;
    }
    #initialize() {
      const browserContextEmitter = this.#disposables.use(new EventEmitter(this.browsingContext));
      browserContextEmitter.once("closed", ({ reason }) => {
        this.dispose(`User prompt already closed: ${reason}`);
      });
      const sessionEmitter = this.#disposables.use(new EventEmitter(this.#session));
      sessionEmitter.on("browsingContext.userPromptClosed", (parameters) => {
        if (parameters.context !== this.browsingContext.id) {
          return;
        }
        this.#result = parameters;
        this.emit("handled", parameters);
        this.dispose("User prompt already handled.");
      });
    }
    // keep-sorted start block=yes
    get #session() {
      return this.browsingContext.userContext.browser.session;
    }
    get closed() {
      return this.#reason !== void 0;
    }
    get disposed() {
      return this.closed;
    }
    get handled() {
      return this.#result !== void 0;
    }
    get result() {
      return this.#result;
    }
    // keep-sorted end
    dispose(reason) {
      this.#reason = reason;
      this[disposeSymbol]();
    }
    async handle(options = {}) {
      await this.#session.send("browsingContext.handleUserPrompt", {
        ...options,
        context: this.info.context
      });
      return this.#result;
    }
    [(_dispose_decorators = [inertIfDisposed], _handle_decorators = [throwIfDisposed((prompt) => {
      return prompt.#reason;
    })], disposeSymbol)]() {
      this.#reason ??= "User prompt already closed, probably because the associated browsing context was destroyed.";
      this.emit("closed", { reason: this.#reason });
      this.#disposables.dispose();
      super[disposeSymbol]();
    }
  };
})();
/**
 * @license
 * Copyright 2024 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
var __runInitializers$4 = function(thisArg, initializers, value) {
  var useValue = arguments.length > 2;
  for (var i = 0; i < initializers.length; i++) {
    value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
  }
  return useValue ? value : void 0;
};
var __esDecorate$4 = function(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
  function accept(f) {
    if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected");
    return f;
  }
  var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
  var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
  var _, done = false;
  for (var i = decorators.length - 1; i >= 0; i--) {
    var context = {};
    for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
    for (var p in contextIn.access) context.access[p] = contextIn.access[p];
    context.addInitializer = function(f) {
      if (done) throw new TypeError("Cannot add initializers after decoration has completed");
      extraInitializers.push(accept(f || null));
    };
    var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
    if (kind === "accessor") {
      if (result === void 0) continue;
      if (result === null || typeof result !== "object") throw new TypeError("Object expected");
      if (_ = accept(result.get)) descriptor.get = _;
      if (_ = accept(result.set)) descriptor.set = _;
      if (_ = accept(result.init)) initializers.unshift(_);
    } else if (_ = accept(result)) {
      if (kind === "field") initializers.unshift(_);
      else descriptor[key] = _;
    }
  }
  if (target) Object.defineProperty(target, contextIn.name, descriptor);
  done = true;
};
let BrowsingContext2 = (() => {
  let _classSuper = EventEmitter;
  let _instanceExtraInitializers = [];
  let _dispose_decorators;
  let _activate_decorators;
  let _captureScreenshot_decorators;
  let _close_decorators;
  let _traverseHistory_decorators;
  let _navigate_decorators;
  let _reload_decorators;
  let _print_decorators;
  let _handleUserPrompt_decorators;
  let _setViewport_decorators;
  let _performActions_decorators;
  let _releaseActions_decorators;
  let _createWindowRealm_decorators;
  let _addPreloadScript_decorators;
  let _removePreloadScript_decorators;
  return class BrowsingContext3 extends _classSuper {
    static {
      const _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;
      __esDecorate$4(this, null, _dispose_decorators, { kind: "method", name: "dispose", static: false, private: false, access: { has: (obj) => "dispose" in obj, get: (obj) => obj.dispose }, metadata: _metadata }, null, _instanceExtraInitializers);
      __esDecorate$4(this, null, _activate_decorators, { kind: "method", name: "activate", static: false, private: false, access: { has: (obj) => "activate" in obj, get: (obj) => obj.activate }, metadata: _metadata }, null, _instanceExtraInitializers);
      __esDecorate$4(this, null, _captureScreenshot_decorators, { kind: "method", name: "captureScreenshot", static: false, private: false, access: { has: (obj) => "captureScreenshot" in obj, get: (obj) => obj.captureScreenshot }, metadata: _metadata }, null, _instanceExtraInitializers);
      __esDecorate$4(this, null, _close_decorators, { kind: "method", name: "close", static: false, private: false, access: { has: (obj) => "close" in obj, get: (obj) => obj.close }, metadata: _metadata }, null, _instanceExtraInitializers);
      __esDecorate$4(this, null, _traverseHistory_decorators, { kind: "method", name: "traverseHistory", static: false, private: false, access: { has: (obj) => "traverseHistory" in obj, get: (obj) => obj.traverseHistory }, metadata: _metadata }, null, _instanceExtraInitializers);
      __esDecorate$4(this, null, _navigate_decorators, { kind: "method", name: "navigate", static: false, private: false, access: { has: (obj) => "navigate" in obj, get: (obj) => obj.navigate }, metadata: _metadata }, null, _instanceExtraInitializers);
      __esDecorate$4(this, null, _reload_decorators, { kind: "method", name: "reload", static: false, private: false, access: { has: (obj) => "reload" in obj, get: (obj) => obj.reload }, metadata: _metadata }, null, _instanceExtraInitializers);
      __esDecorate$4(this, null, _print_decorators, { kind: "method", name: "print", static: false, private: false, access: { has: (obj) => "print" in obj, get: (obj) => obj.print }, metadata: _metadata }, null, _instanceExtraInitializers);
      __esDecorate$4(this, null, _handleUserPrompt_decorators, { kind: "method", name: "handleUserPrompt", static: false, private: false, access: { has: (obj) => "handleUserPrompt" in obj, get: (obj) => obj.handleUserPrompt }, metadata: _metadata }, null, _instanceExtraInitializers);
      __esDecorate$4(this, null, _setViewport_decorators, { kind: "method", name: "setViewport", static: false, private: false, access: { has: (obj) => "setViewport" in obj, get: (obj) => obj.setViewport }, metadata: _metadata }, null, _instanceExtraInitializers);
      __esDecorate$4(this, null, _performActions_decorators, { kind: "method", name: "performActions", static: false, private: false, access: { has: (obj) => "performActions" in obj, get: (obj) => obj.performActions }, metadata: _metadata }, null, _instanceExtraInitializers);
      __esDecorate$4(this, null, _releaseActions_decorators, { kind: "method", name: "releaseActions", static: false, private: false, access: { has: (obj) => "releaseActions" in obj, get: (obj) => obj.releaseActions }, metadata: _metadata }, null, _instanceExtraInitializers);
      __esDecorate$4(this, null, _createWindowRealm_decorators, { kind: "method", name: "createWindowRealm", static: false, private: false, access: { has: (obj) => "createWindowRealm" in obj, get: (obj) => obj.createWindowRealm }, metadata: _metadata }, null, _instanceExtraInitializers);
      __esDecorate$4(this, null, _addPreloadScript_decorators, { kind: "method", name: "addPreloadScript", static: false, private: false, access: { has: (obj) => "addPreloadScript" in obj, get: (obj) => obj.addPreloadScript }, metadata: _metadata }, null, _instanceExtraInitializers);
      __esDecorate$4(this, null, _removePreloadScript_decorators, { kind: "method", name: "removePreloadScript", static: false, private: false, access: { has: (obj) => "removePreloadScript" in obj, get: (obj) => obj.removePreloadScript }, metadata: _metadata }, null, _instanceExtraInitializers);
      if (_metadata) Object.defineProperty(this, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
    }
    static from(userContext, parent, id, url) {
      const browsingContext = new BrowsingContext3(userContext, parent, id, url);
      browsingContext.#initialize();
      return browsingContext;
    }
    // keep-sorted start
    #navigation = (__runInitializers$4(this, _instanceExtraInitializers), void 0);
    #reason;
    #url;
    #children = /* @__PURE__ */ new Map();
    #disposables = new DisposableStack();
    #realms = /* @__PURE__ */ new Map();
    #requests = /* @__PURE__ */ new Map();
    defaultRealm;
    id;
    parent;
    userContext;
    // keep-sorted end
    constructor(context, parent, id, url) {
      super();
      this.#url = url;
      this.id = id;
      this.parent = parent;
      this.userContext = context;
      this.defaultRealm = WindowRealm.from(this);
    }
    #initialize() {
      const userContextEmitter = this.#disposables.use(new EventEmitter(this.userContext));
      userContextEmitter.once("closed", ({ reason }) => {
        this.dispose(`Browsing context already closed: ${reason}`);
      });
      const sessionEmitter = this.#disposables.use(new EventEmitter(this.#session));
      sessionEmitter.on("browsingContext.contextCreated", (info) => {
        if (info.parent !== this.id) {
          return;
        }
        const browsingContext = BrowsingContext3.from(this.userContext, this, info.context, info.url);
        this.#children.set(info.context, browsingContext);
        const browsingContextEmitter = this.#disposables.use(new EventEmitter(browsingContext));
        browsingContextEmitter.once("closed", () => {
          browsingContextEmitter.removeAllListeners();
          this.#children.delete(browsingContext.id);
        });
        this.emit("browsingcontext", { browsingContext });
      });
      sessionEmitter.on("browsingContext.contextDestroyed", (info) => {
        if (info.context !== this.id) {
          return;
        }
        this.dispose("Browsing context already closed.");
      });
      sessionEmitter.on("browsingContext.domContentLoaded", (info) => {
        if (info.context !== this.id) {
          return;
        }
        this.#url = info.url;
        this.emit("DOMContentLoaded", void 0);
      });
      sessionEmitter.on("browsingContext.load", (info) => {
        if (info.context !== this.id) {
          return;
        }
        this.#url = info.url;
        this.emit("load", void 0);
      });
      sessionEmitter.on("browsingContext.navigationStarted", (info) => {
        if (info.context !== this.id) {
          return;
        }
        this.#url = info.url;
        this.#requests.clear();
        this.#navigation = Navigation.from(this);
        const navigationEmitter = this.#disposables.use(new EventEmitter(this.#navigation));
        for (const eventName of ["fragment", "failed", "aborted"]) {
          navigationEmitter.once(eventName, ({ url }) => {
            navigationEmitter[disposeSymbol]();
            this.#url = url;
          });
        }
        this.emit("navigation", { navigation: this.#navigation });
      });
      sessionEmitter.on("network.beforeRequestSent", (event) => {
        if (event.context !== this.id) {
          return;
        }
        if (this.#requests.has(event.request.request)) {
          return;
        }
        const request = Request.from(this, event);
        this.#requests.set(request.id, request);
        this.emit("request", { request });
      });
      sessionEmitter.on("log.entryAdded", (entry) => {
        if (entry.source.context !== this.id) {
          return;
        }
        this.emit("log", { entry });
      });
      sessionEmitter.on("browsingContext.userPromptOpened", (info) => {
        if (info.context !== this.id) {
          return;
        }
        const userPrompt = UserPrompt.from(this, info);
        this.emit("userprompt", { userPrompt });
      });
    }
    // keep-sorted start block=yes
    get #session() {
      return this.userContext.browser.session;
    }
    get children() {
      return this.#children.values();
    }
    get closed() {
      return this.#reason !== void 0;
    }
    get disposed() {
      return this.closed;
    }
    get realms() {
      return this.#realms.values();
    }
    get top() {
      let context = this;
      for (let { parent } = context; parent; { parent } = context) {
        context = parent;
      }
      return context;
    }
    get url() {
      return this.#url;
    }
    // keep-sorted end
    dispose(reason) {
      this.#reason = reason;
      this[disposeSymbol]();
    }
    async activate() {
      await this.#session.send("browsingContext.activate", {
        context: this.id
      });
    }
    async captureScreenshot(options = {}) {
      const { result: { data } } = await this.#session.send("browsingContext.captureScreenshot", {
        context: this.id,
        ...options
      });
      return data;
    }
    async close(promptUnload) {
      await Promise.all([...this.#children.values()].map(async (child) => {
        await child.close(promptUnload);
      }));
      await this.#session.send("browsingContext.close", {
        context: this.id,
        promptUnload
      });
    }
    async traverseHistory(delta) {
      await this.#session.send("browsingContext.traverseHistory", {
        context: this.id,
        delta
      });
    }
    async navigate(url, wait) {
      await this.#session.send("browsingContext.navigate", {
        context: this.id,
        url,
        wait
      });
      return await new Promise((resolve) => {
        this.once("navigation", ({ navigation }) => {
          resolve(navigation);
        });
      });
    }
    async reload(options = {}) {
      await this.#session.send("browsingContext.reload", {
        context: this.id,
        ...options
      });
      return await new Promise((resolve) => {
        this.once("navigation", ({ navigation }) => {
          resolve(navigation);
        });
      });
    }
    async print(options = {}) {
      const { result: { data } } = await this.#session.send("browsingContext.print", {
        context: this.id,
        ...options
      });
      return data;
    }
    async handleUserPrompt(options = {}) {
      await this.#session.send("browsingContext.handleUserPrompt", {
        context: this.id,
        ...options
      });
    }
    async setViewport(options = {}) {
      await this.#session.send("browsingContext.setViewport", {
        context: this.id,
        ...options
      });
    }
    async performActions(actions) {
      await this.#session.send("input.performActions", {
        context: this.id,
        actions
      });
    }
    async releaseActions() {
      await this.#session.send("input.releaseActions", {
        context: this.id
      });
    }
    createWindowRealm(sandbox) {
      return WindowRealm.from(this, sandbox);
    }
    async addPreloadScript(functionDeclaration, options = {}) {
      return await this.userContext.browser.addPreloadScript(functionDeclaration, {
        ...options,
        contexts: [this, ...options.contexts ?? []]
      });
    }
    async removePreloadScript(script) {
      await this.userContext.browser.removePreloadScript(script);
    }
    [(_dispose_decorators = [inertIfDisposed], _activate_decorators = [throwIfDisposed((context) => {
      return context.#reason;
    })], _captureScreenshot_decorators = [throwIfDisposed((context) => {
      return context.#reason;
    })], _close_decorators = [throwIfDisposed((context) => {
      return context.#reason;
    })], _traverseHistory_decorators = [throwIfDisposed((context) => {
      return context.#reason;
    })], _navigate_decorators = [throwIfDisposed((context) => {
      return context.#reason;
    })], _reload_decorators = [throwIfDisposed((context) => {
      return context.#reason;
    })], _print_decorators = [throwIfDisposed((context) => {
      return context.#reason;
    })], _handleUserPrompt_decorators = [throwIfDisposed((context) => {
      return context.#reason;
    })], _setViewport_decorators = [throwIfDisposed((context) => {
      return context.#reason;
    })], _performActions_decorators = [throwIfDisposed((context) => {
      return context.#reason;
    })], _releaseActions_decorators = [throwIfDisposed((context) => {
      return context.#reason;
    })], _createWindowRealm_decorators = [throwIfDisposed((context) => {
      return context.#reason;
    })], _addPreloadScript_decorators = [throwIfDisposed((context) => {
      return context.#reason;
    })], _removePreloadScript_decorators = [throwIfDisposed((context) => {
      return context.#reason;
    })], disposeSymbol)]() {
      this.#reason ??= "Browsing context already closed, probably because the user context closed.";
      this.emit("closed", { reason: this.#reason });
      this.#disposables.dispose();
      super[disposeSymbol]();
    }
  };
})();
/**
 * @license
 * Copyright 2024 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
var __runInitializers$3 = function(thisArg, initializers, value) {
  var useValue = arguments.length > 2;
  for (var i = 0; i < initializers.length; i++) {
    value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
  }
  return useValue ? value : void 0;
};
var __esDecorate$3 = function(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
  function accept(f) {
    if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected");
    return f;
  }
  var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
  var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
  var _, done = false;
  for (var i = decorators.length - 1; i >= 0; i--) {
    var context = {};
    for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
    for (var p in contextIn.access) context.access[p] = contextIn.access[p];
    context.addInitializer = function(f) {
      if (done) throw new TypeError("Cannot add initializers after decoration has completed");
      extraInitializers.push(accept(f || null));
    };
    var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
    if (kind === "accessor") {
      if (result === void 0) continue;
      if (result === null || typeof result !== "object") throw new TypeError("Object expected");
      if (_ = accept(result.get)) descriptor.get = _;
      if (_ = accept(result.set)) descriptor.set = _;
      if (_ = accept(result.init)) initializers.unshift(_);
    } else if (_ = accept(result)) {
      if (kind === "field") initializers.unshift(_);
      else descriptor[key] = _;
    }
  }
  if (target) Object.defineProperty(target, contextIn.name, descriptor);
  done = true;
};
let UserContext = (() => {
  let _classSuper = EventEmitter;
  let _instanceExtraInitializers = [];
  let _dispose_decorators;
  let _createBrowsingContext_decorators;
  let _remove_decorators;
  return class UserContext2 extends _classSuper {
    static {
      const _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;
      __esDecorate$3(this, null, _dispose_decorators, { kind: "method", name: "dispose", static: false, private: false, access: { has: (obj) => "dispose" in obj, get: (obj) => obj.dispose }, metadata: _metadata }, null, _instanceExtraInitializers);
      __esDecorate$3(this, null, _createBrowsingContext_decorators, { kind: "method", name: "createBrowsingContext", static: false, private: false, access: { has: (obj) => "createBrowsingContext" in obj, get: (obj) => obj.createBrowsingContext }, metadata: _metadata }, null, _instanceExtraInitializers);
      __esDecorate$3(this, null, _remove_decorators, { kind: "method", name: "remove", static: false, private: false, access: { has: (obj) => "remove" in obj, get: (obj) => obj.remove }, metadata: _metadata }, null, _instanceExtraInitializers);
      if (_metadata) Object.defineProperty(this, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
    }
    static DEFAULT = "default";
    static create(browser, id) {
      const context = new UserContext2(browser, id);
      context.#initialize();
      return context;
    }
    // keep-sorted start
    #reason = (__runInitializers$3(this, _instanceExtraInitializers), void 0);
    // Note these are only top-level contexts.
    #browsingContexts = /* @__PURE__ */ new Map();
    #disposables = new DisposableStack();
    #id;
    browser;
    // keep-sorted end
    constructor(browser, id) {
      super();
      this.#id = id;
      this.browser = browser;
    }
    #initialize() {
      const browserEmitter = this.#disposables.use(new EventEmitter(this.browser));
      browserEmitter.once("closed", ({ reason }) => {
        this.dispose(`User context already closed: ${reason}`);
      });
      const sessionEmitter = this.#disposables.use(new EventEmitter(this.#session));
      sessionEmitter.on("browsingContext.contextCreated", (info) => {
        if (info.parent) {
          return;
        }
        if (info.userContext !== this.#id) {
          return;
        }
        const browsingContext = BrowsingContext2.from(this, void 0, info.context, info.url);
        this.#browsingContexts.set(browsingContext.id, browsingContext);
        const browsingContextEmitter = this.#disposables.use(new EventEmitter(browsingContext));
        browsingContextEmitter.on("closed", () => {
          browsingContextEmitter.removeAllListeners();
          this.#browsingContexts.delete(browsingContext.id);
        });
        this.emit("browsingcontext", { browsingContext });
      });
    }
    // keep-sorted start block=yes
    get #session() {
      return this.browser.session;
    }
    get browsingContexts() {
      return this.#browsingContexts.values();
    }
    get closed() {
      return this.#reason !== void 0;
    }
    get disposed() {
      return this.closed;
    }
    get id() {
      return this.#id;
    }
    // keep-sorted end
    dispose(reason) {
      this.#reason = reason;
      this[disposeSymbol]();
    }
    async createBrowsingContext(type, options = {}) {
      const { result: { context: contextId } } = await this.#session.send("browsingContext.create", {
        type,
        ...options,
        referenceContext: options.referenceContext?.id,
        userContext: this.#id
      });
      const browsingContext = this.#browsingContexts.get(contextId);
      assert(browsingContext, "The WebDriver BiDi implementation is failing to create a browsing context correctly.");
      return browsingContext;
    }
    async remove() {
      try {
        await this.#session.send("browser.removeUserContext", {
          userContext: this.#id
        });
      } finally {
        this.dispose("User context already closed.");
      }
    }
    [(_dispose_decorators = [inertIfDisposed], _createBrowsingContext_decorators = [throwIfDisposed((context) => {
      return context.#reason;
    })], _remove_decorators = [throwIfDisposed((context) => {
      return context.#reason;
    })], disposeSymbol)]() {
      this.#reason ??= "User context already closed, probably because the browser disconnected/closed.";
      this.emit("closed", { reason: this.#reason });
      this.#disposables.dispose();
      super[disposeSymbol]();
    }
  };
})();
/**
 * @license
 * Copyright 2022 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
class BidiBrowserContext extends BrowserContext {
  #browser;
  #connection;
  #defaultViewport;
  #userContext;
  constructor(browser, userContext, options) {
    super();
    this.#browser = browser;
    this.#userContext = userContext;
    this.#connection = this.#browser.connection;
    this.#defaultViewport = options.defaultViewport;
  }
  targets() {
    return this.#browser.targets().filter((target) => {
      return target.browserContext() === this;
    });
  }
  waitForTarget(predicate, options = {}) {
    return this.#browser.waitForTarget((target) => {
      return target.browserContext() === this && predicate(target);
    }, options);
  }
  get connection() {
    return this.#connection;
  }
  async newPage() {
    const { result } = await this.#connection.send("browsingContext.create", {
      type: "tab",
      userContext: this.#userContext.id
    });
    const target = this.#browser._getTargetById(result.context);
    target._setBrowserContext(this);
    const page = await target.page();
    if (!page) {
      throw new Error("Page is not found");
    }
    if (this.#defaultViewport) {
      try {
        await page.setViewport(this.#defaultViewport);
      } catch {
      }
    }
    return page;
  }
  async close() {
    if (!this.isIncognito()) {
      throw new Error("Default context cannot be closed!");
    }
    try {
      await this.#userContext.remove();
    } catch (error) {
      debugError(error);
    }
  }
  browser() {
    return this.#browser;
  }
  async pages() {
    const results = await Promise.all([...this.targets()].map((t) => {
      return t.page();
    }));
    return results.filter((p) => {
      return p !== null;
    });
  }
  isIncognito() {
    return this.#userContext.id !== UserContext.DEFAULT;
  }
  overridePermissions() {
    throw new UnsupportedOperation();
  }
  clearPermissionOverrides() {
    throw new UnsupportedOperation();
  }
  get id() {
    if (this.#userContext.id === "default") {
      return void 0;
    }
    return this.#userContext.id;
  }
}
/**
 * @license
 * Copyright 2024 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
var __runInitializers$2 = function(thisArg, initializers, value) {
  var useValue = arguments.length > 2;
  for (var i = 0; i < initializers.length; i++) {
    value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
  }
  return useValue ? value : void 0;
};
var __esDecorate$2 = function(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
  function accept(f) {
    if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected");
    return f;
  }
  var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
  var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
  var _, done = false;
  for (var i = decorators.length - 1; i >= 0; i--) {
    var context = {};
    for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
    for (var p in contextIn.access) context.access[p] = contextIn.access[p];
    context.addInitializer = function(f) {
      if (done) throw new TypeError("Cannot add initializers after decoration has completed");
      extraInitializers.push(accept(f || null));
    };
    var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
    if (kind === "accessor") {
      if (result === void 0) continue;
      if (result === null || typeof result !== "object") throw new TypeError("Object expected");
      if (_ = accept(result.get)) descriptor.get = _;
      if (_ = accept(result.set)) descriptor.set = _;
      if (_ = accept(result.init)) initializers.unshift(_);
    } else if (_ = accept(result)) {
      if (kind === "field") initializers.unshift(_);
      else descriptor[key] = _;
    }
  }
  if (target) Object.defineProperty(target, contextIn.name, descriptor);
  done = true;
};
var __addDisposableResource$1 = function(env, value, async) {
  if (value !== null && value !== void 0) {
    if (typeof value !== "object" && typeof value !== "function") throw new TypeError("Object expected.");
    var dispose;
    if (async) {
      if (!Symbol.asyncDispose) throw new TypeError("Symbol.asyncDispose is not defined.");
      dispose = value[Symbol.asyncDispose];
    }
    if (dispose === void 0) {
      if (!Symbol.dispose) throw new TypeError("Symbol.dispose is not defined.");
      dispose = value[Symbol.dispose];
    }
    if (typeof dispose !== "function") throw new TypeError("Object not disposable.");
    env.stack.push({ value, dispose, async });
  } else if (async) {
    env.stack.push({ async: true });
  }
  return value;
};
var __disposeResources$1 = /* @__PURE__ */ function(SuppressedError2) {
  return function(env) {
    function fail(e) {
      env.error = env.hasError ? new SuppressedError2(e, env.error, "An error was suppressed during disposal.") : e;
      env.hasError = true;
    }
    function next() {
      while (env.stack.length) {
        var rec = env.stack.pop();
        try {
          var result = rec.dispose && rec.dispose.call(rec.value);
          if (rec.async) return Promise.resolve(result).then(next, function(e) {
            fail(e);
            return next();
          });
        } catch (e) {
          fail(e);
        }
      }
      if (env.hasError) throw env.error;
    }
    return next();
  };
}(typeof SuppressedError === "function" ? SuppressedError : function(error, suppressed, message) {
  var e = new Error(message);
  return e.name = "SuppressedError", e.error = error, e.suppressed = suppressed, e;
});
let Browser = (() => {
  let _classSuper = EventEmitter;
  let _instanceExtraInitializers = [];
  let _dispose_decorators;
  let _close_decorators;
  let _addPreloadScript_decorators;
  let _removePreloadScript_decorators;
  let _createUserContext_decorators;
  return class Browser2 extends _classSuper {
    static {
      const _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;
      __esDecorate$2(this, null, _dispose_decorators, { kind: "method", name: "dispose", static: false, private: false, access: { has: (obj) => "dispose" in obj, get: (obj) => obj.dispose }, metadata: _metadata }, null, _instanceExtraInitializers);
      __esDecorate$2(this, null, _close_decorators, { kind: "method", name: "close", static: false, private: false, access: { has: (obj) => "close" in obj, get: (obj) => obj.close }, metadata: _metadata }, null, _instanceExtraInitializers);
      __esDecorate$2(this, null, _addPreloadScript_decorators, { kind: "method", name: "addPreloadScript", static: false, private: false, access: { has: (obj) => "addPreloadScript" in obj, get: (obj) => obj.addPreloadScript }, metadata: _metadata }, null, _instanceExtraInitializers);
      __esDecorate$2(this, null, _removePreloadScript_decorators, { kind: "method", name: "removePreloadScript", static: false, private: false, access: { has: (obj) => "removePreloadScript" in obj, get: (obj) => obj.removePreloadScript }, metadata: _metadata }, null, _instanceExtraInitializers);
      __esDecorate$2(this, null, _createUserContext_decorators, { kind: "method", name: "createUserContext", static: false, private: false, access: { has: (obj) => "createUserContext" in obj, get: (obj) => obj.createUserContext }, metadata: _metadata }, null, _instanceExtraInitializers);
      if (_metadata) Object.defineProperty(this, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
    }
    static async from(session) {
      const browser = new Browser2(session);
      await browser.#initialize();
      return browser;
    }
    // keep-sorted start
    #closed = (__runInitializers$2(this, _instanceExtraInitializers), false);
    #reason;
    #disposables = new DisposableStack();
    #userContexts = /* @__PURE__ */ new Map();
    session;
    // keep-sorted end
    constructor(session) {
      super();
      this.session = session;
      this.#userContexts.set(UserContext.DEFAULT, UserContext.create(this, UserContext.DEFAULT));
    }
    async #initialize() {
      const sessionEmitter = this.#disposables.use(new EventEmitter(this.session));
      sessionEmitter.once("ended", ({ reason }) => {
        this.dispose(reason);
      });
      sessionEmitter.on("script.realmCreated", (info) => {
        if (info.type === "shared-worker") ;
      });
      await this.#syncUserContexts();
      await this.#syncBrowsingContexts();
    }
    async #syncUserContexts() {
      const { result: { userContexts } } = await this.session.send("browser.getUserContexts", {});
      for (const context of userContexts) {
        if (context.userContext === UserContext.DEFAULT) {
          continue;
        }
        this.#userContexts.set(context.userContext, UserContext.create(this, context.userContext));
      }
    }
    async #syncBrowsingContexts() {
      const contextIds = /* @__PURE__ */ new Set();
      let contexts;
      {
        const env_1 = { stack: [], error: void 0, hasError: false };
        try {
          const sessionEmitter = __addDisposableResource$1(env_1, new EventEmitter(this.session), false);
          sessionEmitter.on("browsingContext.contextCreated", (info) => {
            contextIds.add(info.context);
          });
          sessionEmitter.on("browsingContext.contextDestroyed", (info) => {
            contextIds.delete(info.context);
          });
          const { result } = await this.session.send("browsingContext.getTree", {});
          contexts = result.contexts;
        } catch (e_1) {
          env_1.error = e_1;
          env_1.hasError = true;
        } finally {
          __disposeResources$1(env_1);
        }
      }
      for (const info of contexts) {
        if (contextIds.has(info.context)) {
          this.session.emit("browsingContext.contextCreated", info);
        }
        if (info.children) {
          contexts.push(...info.children);
        }
      }
    }
    // keep-sorted start block=yes
    get closed() {
      return this.#closed;
    }
    get defaultUserContext() {
      return this.#userContexts.get(UserContext.DEFAULT);
    }
    get disconnected() {
      return this.#reason !== void 0;
    }
    get disposed() {
      return this.disconnected;
    }
    get userContexts() {
      return this.#userContexts.values();
    }
    // keep-sorted end
    dispose(reason, closed = false) {
      this.#closed = closed;
      this.#reason = reason;
      this[disposeSymbol]();
    }
    async close() {
      try {
        await this.session.send("browser.close", {});
      } finally {
        this.dispose("Browser already closed.", true);
      }
    }
    async addPreloadScript(functionDeclaration, options = {}) {
      const { result: { script } } = await this.session.send("script.addPreloadScript", {
        functionDeclaration,
        ...options,
        contexts: options.contexts?.map((context) => {
          return context.id;
        })
      });
      return script;
    }
    async removePreloadScript(script) {
      await this.session.send("script.removePreloadScript", {
        script
      });
    }
    async createUserContext() {
      const { result: { userContext: context } } = await this.session.send("browser.createUserContext", {});
      const userContext = UserContext.create(this, context);
      this.#userContexts.set(userContext.id, userContext);
      const userContextEmitter = this.#disposables.use(new EventEmitter(userContext));
      userContextEmitter.once("closed", () => {
        userContextEmitter.removeAllListeners();
        this.#userContexts.delete(context);
      });
      return userContext;
    }
    [(_dispose_decorators = [inertIfDisposed], _close_decorators = [throwIfDisposed((browser) => {
      return browser.#reason;
    })], _addPreloadScript_decorators = [throwIfDisposed((browser) => {
      return browser.#reason;
    })], _removePreloadScript_decorators = [throwIfDisposed((browser) => {
      return browser.#reason;
    })], _createUserContext_decorators = [throwIfDisposed((browser) => {
      return browser.#reason;
    })], disposeSymbol)]() {
      this.#reason ??= "Browser was disconnected, probably because the session ended.";
      if (this.closed) {
        this.emit("closed", { reason: this.#reason });
      }
      this.emit("disconnected", { reason: this.#reason });
      this.#disposables.dispose();
      super[disposeSymbol]();
    }
  };
})();
/**
 * @license
 * Copyright 2024 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
var __runInitializers$1 = function(thisArg, initializers, value) {
  var useValue = arguments.length > 2;
  for (var i = 0; i < initializers.length; i++) {
    value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
  }
  return useValue ? value : void 0;
};
var __esDecorate$1 = function(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
  function accept(f) {
    if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected");
    return f;
  }
  var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
  var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
  var _, done = false;
  for (var i = decorators.length - 1; i >= 0; i--) {
    var context = {};
    for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
    for (var p in contextIn.access) context.access[p] = contextIn.access[p];
    context.addInitializer = function(f) {
      if (done) throw new TypeError("Cannot add initializers after decoration has completed");
      extraInitializers.push(accept(f || null));
    };
    var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
    if (kind === "accessor") {
      if (result === void 0) continue;
      if (result === null || typeof result !== "object") throw new TypeError("Object expected");
      if (_ = accept(result.get)) descriptor.get = _;
      if (_ = accept(result.set)) descriptor.set = _;
      if (_ = accept(result.init)) initializers.unshift(_);
    } else if (_ = accept(result)) {
      if (kind === "field") initializers.unshift(_);
      else descriptor[key] = _;
    }
  }
  if (target) Object.defineProperty(target, contextIn.name, descriptor);
  done = true;
};
let Session = (() => {
  let _classSuper = EventEmitter;
  let _instanceExtraInitializers = [];
  let _dispose_decorators;
  let _send_decorators;
  let _subscribe_decorators;
  let _end_decorators;
  return class Session2 extends _classSuper {
    static {
      const _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;
      __esDecorate$1(this, null, _dispose_decorators, { kind: "method", name: "dispose", static: false, private: false, access: { has: (obj) => "dispose" in obj, get: (obj) => obj.dispose }, metadata: _metadata }, null, _instanceExtraInitializers);
      __esDecorate$1(this, null, _send_decorators, { kind: "method", name: "send", static: false, private: false, access: { has: (obj) => "send" in obj, get: (obj) => obj.send }, metadata: _metadata }, null, _instanceExtraInitializers);
      __esDecorate$1(this, null, _subscribe_decorators, { kind: "method", name: "subscribe", static: false, private: false, access: { has: (obj) => "subscribe" in obj, get: (obj) => obj.subscribe }, metadata: _metadata }, null, _instanceExtraInitializers);
      __esDecorate$1(this, null, _end_decorators, { kind: "method", name: "end", static: false, private: false, access: { has: (obj) => "end" in obj, get: (obj) => obj.end }, metadata: _metadata }, null, _instanceExtraInitializers);
      if (_metadata) Object.defineProperty(this, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
    }
    static async from(connection, capabilities) {
      let result;
      try {
        result = (await connection.send("session.new", {
          capabilities
        })).result;
      } catch (err) {
        debugError(err);
        result = {
          sessionId: "",
          capabilities: {
            acceptInsecureCerts: false,
            browserName: "",
            browserVersion: "",
            platformName: "",
            setWindowRect: false,
            webSocketUrl: ""
          }
        };
      }
      const session = new Session2(connection, result);
      await session.#initialize();
      return session;
    }
    // keep-sorted start
    #reason = (__runInitializers$1(this, _instanceExtraInitializers), void 0);
    #disposables = new DisposableStack();
    #info;
    browser;
    connection;
    // keep-sorted end
    constructor(connection, info) {
      super();
      this.#info = info;
      this.connection = connection;
    }
    async #initialize() {
      this.connection.pipeTo(this);
      this.browser = await Browser.from(this);
      const browserEmitter = this.#disposables.use(this.browser);
      browserEmitter.once("closed", ({ reason }) => {
        this.dispose(reason);
      });
    }
    // keep-sorted start block=yes
    get capabilities() {
      return this.#info.capabilities;
    }
    get disposed() {
      return this.ended;
    }
    get ended() {
      return this.#reason !== void 0;
    }
    get id() {
      return this.#info.sessionId;
    }
    // keep-sorted end
    dispose(reason) {
      this.#reason = reason;
      this[disposeSymbol]();
    }
    pipeTo(emitter) {
      this.connection.pipeTo(emitter);
    }
    /**
     * Currently, there is a 1:1 relationship between the session and the
     * session. In the future, we might support multiple sessions and in that
     * case we always needs to make sure that the session for the right session
     * object is used, so we implement this method here, although it's not defined
     * in the spec.
     */
    async send(method, params) {
      return await this.connection.send(method, params);
    }
    async subscribe(events) {
      await this.send("session.subscribe", {
        events
      });
    }
    async end() {
      try {
        await this.send("session.end", {});
      } finally {
        this.dispose(`Session already ended.`);
      }
    }
    [(_dispose_decorators = [inertIfDisposed], _send_decorators = [throwIfDisposed((session) => {
      return session.#reason;
    })], _subscribe_decorators = [throwIfDisposed((session) => {
      return session.#reason;
    })], _end_decorators = [throwIfDisposed((session) => {
      return session.#reason;
    })], disposeSymbol)]() {
      this.#reason ??= "Session already destroyed, probably because the connection broke.";
      this.emit("ended", { reason: this.#reason });
      this.#disposables.dispose();
      super[disposeSymbol]();
    }
  };
})();
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
class BidiDialog extends Dialog {
  #context;
  /**
   * @internal
   */
  constructor(context, type, message, defaultValue) {
    super(type, message, defaultValue);
    this.#context = context;
  }
  /**
   * @internal
   */
  async handle(options) {
    await this.#context.connection.send("browsingContext.handleUserPrompt", {
      context: this.#context.id,
      accept: options.accept,
      userText: options.text
    });
  }
}
class EmulationManager {
  #browsingContext;
  constructor(browsingContext) {
    this.#browsingContext = browsingContext;
  }
  async emulateViewport(viewport) {
    await this.#browsingContext.connection.send("browsingContext.setViewport", {
      context: this.#browsingContext.id,
      viewport: viewport.width && viewport.height ? {
        width: viewport.width,
        height: viewport.height
      } : null,
      devicePixelRatio: viewport.deviceScaleFactor ? viewport.deviceScaleFactor : null
    });
  }
}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
class ExposeableFunction {
  #frame;
  name;
  #apply;
  #channels;
  #callerInfos = /* @__PURE__ */ new Map();
  #preloadScriptId;
  constructor(frame, name, apply) {
    this.#frame = frame;
    this.name = name;
    this.#apply = apply;
    this.#channels = {
      args: `__puppeteer__${this.#frame._id}_page_exposeFunction_${this.name}_args`,
      resolve: `__puppeteer__${this.#frame._id}_page_exposeFunction_${this.name}_resolve`,
      reject: `__puppeteer__${this.#frame._id}_page_exposeFunction_${this.name}_reject`
    };
  }
  async expose() {
    const connection = this.#connection;
    const channelArguments = this.#channelArguments;
    connection.on(protocol.ChromiumBidi.Script.EventNames.Message, this.#handleArgumentsMessage);
    connection.on(protocol.ChromiumBidi.Script.EventNames.Message, this.#handleResolveMessage);
    connection.on(protocol.ChromiumBidi.Script.EventNames.Message, this.#handleRejectMessage);
    const functionDeclaration = stringifyFunction(interpolateFunction((sendArgs, sendResolve, sendReject) => {
      let id = 0;
      Object.assign(globalThis, {
        [PLACEHOLDER("name")]: function(...args) {
          return new Promise((resolve, reject) => {
            sendArgs([id, args]);
            sendResolve([id, resolve]);
            sendReject([id, reject]);
            ++id;
          });
        }
      });
    }, { name: JSON.stringify(this.name) }));
    const { result } = await connection.send("script.addPreloadScript", {
      functionDeclaration,
      arguments: channelArguments,
      contexts: [this.#frame.page().mainFrame()._id]
    });
    this.#preloadScriptId = result.script;
    await Promise.all(this.#frame.page().frames().map(async (frame) => {
      return await connection.send("script.callFunction", {
        functionDeclaration,
        arguments: channelArguments,
        awaitPromise: false,
        target: frame.mainRealm().realm.target
      });
    }));
  }
  #handleArgumentsMessage = async (params) => {
    if (params.channel !== this.#channels.args) {
      return;
    }
    const connection = this.#connection;
    const { callbacks, remoteValue } = this.#getCallbacksAndRemoteValue(params);
    const args = remoteValue.value?.[1];
    assert(args);
    try {
      const result = await this.#apply(...BidiDeserializer.deserialize(args));
      await connection.send("script.callFunction", {
        functionDeclaration: stringifyFunction(([_, resolve], result2) => {
          resolve(result2);
        }),
        arguments: [
          await callbacks.resolve.valueOrThrow(),
          BidiSerializer.serializeRemoteValue(result)
        ],
        awaitPromise: false,
        target: {
          realm: params.source.realm
        }
      });
    } catch (error) {
      try {
        if (error instanceof Error) {
          await connection.send("script.callFunction", {
            functionDeclaration: stringifyFunction(([_, reject], name, message, stack) => {
              const error2 = new Error(message);
              error2.name = name;
              if (stack) {
                error2.stack = stack;
              }
              reject(error2);
            }),
            arguments: [
              await callbacks.reject.valueOrThrow(),
              BidiSerializer.serializeRemoteValue(error.name),
              BidiSerializer.serializeRemoteValue(error.message),
              BidiSerializer.serializeRemoteValue(error.stack)
            ],
            awaitPromise: false,
            target: {
              realm: params.source.realm
            }
          });
        } else {
          await connection.send("script.callFunction", {
            functionDeclaration: stringifyFunction(([_, reject], error2) => {
              reject(error2);
            }),
            arguments: [
              await callbacks.reject.valueOrThrow(),
              BidiSerializer.serializeRemoteValue(error)
            ],
            awaitPromise: false,
            target: {
              realm: params.source.realm
            }
          });
        }
      } catch (error2) {
        debugError(error2);
      }
    }
  };
  get #connection() {
    return this.#frame.context().connection;
  }
  get #channelArguments() {
    return [
      {
        type: "channel",
        value: {
          channel: this.#channels.args,
          ownership: "root"
        }
      },
      {
        type: "channel",
        value: {
          channel: this.#channels.resolve,
          ownership: "root"
        }
      },
      {
        type: "channel",
        value: {
          channel: this.#channels.reject,
          ownership: "root"
        }
      }
    ];
  }
  #handleResolveMessage = (params) => {
    if (params.channel !== this.#channels.resolve) {
      return;
    }
    const { callbacks, remoteValue } = this.#getCallbacksAndRemoteValue(params);
    callbacks.resolve.resolve(remoteValue);
  };
  #handleRejectMessage = (params) => {
    if (params.channel !== this.#channels.reject) {
      return;
    }
    const { callbacks, remoteValue } = this.#getCallbacksAndRemoteValue(params);
    callbacks.reject.resolve(remoteValue);
  };
  #getCallbacksAndRemoteValue(params) {
    const { data, source } = params;
    assert(data.type === "array");
    assert(data.value);
    const callerIdRemote = data.value[0];
    assert(callerIdRemote);
    assert(callerIdRemote.type === "number");
    assert(typeof callerIdRemote.value === "number");
    let bindingMap = this.#callerInfos.get(source.realm);
    if (!bindingMap) {
      bindingMap = /* @__PURE__ */ new Map();
      this.#callerInfos.set(source.realm, bindingMap);
    }
    const callerId = callerIdRemote.value;
    let callbacks = bindingMap.get(callerId);
    if (!callbacks) {
      callbacks = {
        resolve: new Deferred(),
        reject: new Deferred()
      };
      bindingMap.set(callerId, callbacks);
    }
    return { callbacks, remoteValue: data };
  }
  [Symbol.dispose]() {
    void this[Symbol.asyncDispose]().catch(debugError);
  }
  async [Symbol.asyncDispose]() {
    if (this.#preloadScriptId) {
      await this.#connection.send("script.removePreloadScript", {
        script: this.#preloadScriptId
      });
    }
  }
}
function getBiDiLifeCycles(event) {
  if (Array.isArray(event)) {
    const pageLifeCycle = event.some((lifeCycle) => {
      return lifeCycle !== "domcontentloaded";
    }) ? "load" : "domcontentloaded";
    const networkLifeCycle = event.reduce((acc, lifeCycle) => {
      if (lifeCycle === "networkidle0") {
        return lifeCycle;
      } else if (acc !== "networkidle0" && lifeCycle === "networkidle2") {
        return lifeCycle;
      }
      return acc;
    }, null);
    return [pageLifeCycle, networkLifeCycle];
  }
  if (event === "networkidle0" || event === "networkidle2") {
    return ["load", event];
  }
  return [event, null];
}
const lifeCycleToReadinessState = /* @__PURE__ */ new Map([
  [
    "load",
    "complete"
    /* Bidi.BrowsingContext.ReadinessState.Complete */
  ],
  [
    "domcontentloaded",
    "interactive"
    /* Bidi.BrowsingContext.ReadinessState.Interactive */
  ]
]);
function getBiDiReadinessState(event) {
  const lifeCycles = getBiDiLifeCycles(event);
  const readiness = lifeCycleToReadinessState.get(lifeCycles[0]);
  return [readiness, lifeCycles[1]];
}
const lifeCycleToSubscribedEvent = /* @__PURE__ */ new Map([
  ["load", "browsingContext.load"],
  ["domcontentloaded", "browsingContext.domContentLoaded"]
]);
function getBiDiLifecycleEvent(event) {
  const lifeCycles = getBiDiLifeCycles(event);
  const bidiEvent = lifeCycleToSubscribedEvent.get(lifeCycles[0]);
  return [bidiEvent, lifeCycles[1]];
}
function rewriteNavigationError(message, ms) {
  return Wr((error) => {
    if (error instanceof ProtocolError) {
      error.message += ` at ${message}`;
    } else if (error instanceof TimeoutError) {
      error.message = `Navigation timeout of ${ms} ms exceeded`;
    }
    throw error;
  });
}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
const MAIN_SANDBOX = Symbol("mainSandbox");
const PUPPETEER_SANDBOX = Symbol("puppeteerSandbox");
class Sandbox extends Realm$1 {
  name;
  realm;
  #frame;
  constructor(name, frame, realm, timeoutSettings) {
    super(timeoutSettings);
    this.name = name;
    this.realm = realm;
    this.#frame = frame;
    this.realm.setSandbox(this);
  }
  get environment() {
    return this.#frame;
  }
  async evaluateHandle(pageFunction, ...args) {
    pageFunction = withSourcePuppeteerURLIfNone(this.evaluateHandle.name, pageFunction);
    return await this.realm.evaluateHandle(pageFunction, ...args);
  }
  async evaluate(pageFunction, ...args) {
    pageFunction = withSourcePuppeteerURLIfNone(this.evaluate.name, pageFunction);
    return await this.realm.evaluate(pageFunction, ...args);
  }
  async adoptHandle(handle) {
    return await this.evaluateHandle((node) => {
      return node;
    }, handle);
  }
  async transferHandle(handle) {
    if (handle.realm === this) {
      return handle;
    }
    const transferredHandle = await this.evaluateHandle((node) => {
      return node;
    }, handle);
    await handle.dispose();
    return transferredHandle;
  }
  async adoptBackendNode(backendNodeId) {
    const { object } = await this.environment.client.send("DOM.resolveNode", {
      backendNodeId
    });
    return new BidiElementHandle(this, {
      handle: object.objectId,
      type: "node"
    });
  }
}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
var __runInitializers = function(thisArg, initializers, value) {
  var useValue = arguments.length > 2;
  for (var i = 0; i < initializers.length; i++) {
    value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
  }
  return useValue ? value : void 0;
};
var __esDecorate = function(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
  function accept(f) {
    if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected");
    return f;
  }
  var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
  var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
  var _, done = false;
  for (var i = decorators.length - 1; i >= 0; i--) {
    var context = {};
    for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
    for (var p in contextIn.access) context.access[p] = contextIn.access[p];
    context.addInitializer = function(f) {
      if (done) throw new TypeError("Cannot add initializers after decoration has completed");
      extraInitializers.push(accept(f || null));
    };
    var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
    if (kind === "accessor") {
      if (result === void 0) continue;
      if (result === null || typeof result !== "object") throw new TypeError("Object expected");
      if (_ = accept(result.get)) descriptor.get = _;
      if (_ = accept(result.set)) descriptor.set = _;
      if (_ = accept(result.init)) initializers.unshift(_);
    } else if (_ = accept(result)) {
      if (kind === "field") initializers.unshift(_);
      else descriptor[key] = _;
    }
  }
  if (target) Object.defineProperty(target, contextIn.name, descriptor);
  done = true;
};
let BidiFrame = (() => {
  let _classSuper = Frame;
  let _instanceExtraInitializers = [];
  let _goto_decorators;
  let _setContent_decorators;
  let _waitForNavigation_decorators;
  return class BidiFrame extends _classSuper {
    static {
      const _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;
      __esDecorate(this, null, _goto_decorators, { kind: "method", name: "goto", static: false, private: false, access: { has: (obj) => "goto" in obj, get: (obj) => obj.goto }, metadata: _metadata }, null, _instanceExtraInitializers);
      __esDecorate(this, null, _setContent_decorators, { kind: "method", name: "setContent", static: false, private: false, access: { has: (obj) => "setContent" in obj, get: (obj) => obj.setContent }, metadata: _metadata }, null, _instanceExtraInitializers);
      __esDecorate(this, null, _waitForNavigation_decorators, { kind: "method", name: "waitForNavigation", static: false, private: false, access: { has: (obj) => "waitForNavigation" in obj, get: (obj) => obj.waitForNavigation }, metadata: _metadata }, null, _instanceExtraInitializers);
      if (_metadata) Object.defineProperty(this, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
    }
    #page = (__runInitializers(this, _instanceExtraInitializers), void 0);
    #context;
    #timeoutSettings;
    #abortDeferred = Deferred.create();
    #disposed = false;
    sandboxes;
    _id;
    constructor(page, context, timeoutSettings, parentId) {
      super();
      this.#page = page;
      this.#context = context;
      this.#timeoutSettings = timeoutSettings;
      this._id = this.#context.id;
      this._parentId = parentId ?? void 0;
      this.sandboxes = {
        [MAIN_SANDBOX]: new Sandbox(void 0, this, context, timeoutSettings),
        [PUPPETEER_SANDBOX]: new Sandbox(UTILITY_WORLD_NAME, this, context.createRealmForSandbox(), timeoutSettings)
      };
    }
    get client() {
      return this.context().cdpSession;
    }
    mainRealm() {
      return this.sandboxes[MAIN_SANDBOX];
    }
    isolatedRealm() {
      return this.sandboxes[PUPPETEER_SANDBOX];
    }
    page() {
      return this.#page;
    }
    isOOPFrame() {
      throw new UnsupportedOperation();
    }
    url() {
      return this.#context.url;
    }
    parentFrame() {
      return this.#page.frame(this._parentId ?? "");
    }
    childFrames() {
      return this.#page.childFrames(this.#context.id);
    }
    async goto(url, options = {}) {
      const { waitUntil = "load", timeout: ms = this.#timeoutSettings.navigationTimeout() } = options;
      const [readiness, networkIdle] = getBiDiReadinessState(waitUntil);
      const result$ = Ee(F(this.#context.connection.send("browsingContext.navigate", {
        context: this.#context.id,
        url,
        wait: readiness
      })), ...networkIdle !== null ? [
        this.#page.waitForNetworkIdle$({
          timeout: ms,
          concurrency: networkIdle === "networkidle2" ? 2 : 0,
          idleTime: NETWORK_IDLE_TIME
        })
      ] : []).pipe(k(([{ result: result2 }]) => {
        return result2;
      }), Fe(timeout(ms), F(this.#abortDeferred.valueOrThrow())), rewriteNavigationError(url, ms));
      const result = await me(result$);
      return this.#page.getNavigationResponse(result.navigation);
    }
    async setContent(html, options = {}) {
      const { waitUntil = "load", timeout: ms = this.#timeoutSettings.navigationTimeout() } = options;
      const [waitEvent, networkIdle] = getBiDiLifecycleEvent(waitUntil);
      const result$ = Ee(be([
        fromEmitterEvent(this.#context, waitEvent).pipe(Pe()),
        F(this.setFrameContent(html))
      ]).pipe(k(() => {
        return null;
      })), ...networkIdle !== null ? [
        this.#page.waitForNetworkIdle$({
          timeout: ms,
          concurrency: networkIdle === "networkidle2" ? 2 : 0,
          idleTime: NETWORK_IDLE_TIME
        })
      ] : []).pipe(Fe(timeout(ms), F(this.#abortDeferred.valueOrThrow())), rewriteNavigationError("setContent", ms));
      await me(result$);
    }
    context() {
      return this.#context;
    }
    async waitForNavigation(options = {}) {
      const { waitUntil = "load", timeout: ms = this.#timeoutSettings.navigationTimeout() } = options;
      const [waitUntilEvent, networkIdle] = getBiDiLifecycleEvent(waitUntil);
      const navigation$ = we(be([
        fromEmitterEvent(this.#context, protocol.ChromiumBidi.BrowsingContext.EventNames.NavigationStarted).pipe(Pe()),
        fromEmitterEvent(this.#context, waitUntilEvent).pipe(Pe())
      ]), fromEmitterEvent(this.#context, protocol.ChromiumBidi.BrowsingContext.EventNames.FragmentNavigated)).pipe(k((result2) => {
        if (Array.isArray(result2)) {
          return { result: result2[1] };
        }
        return { result: result2 };
      }));
      const result$ = Ee(navigation$, ...networkIdle !== null ? [
        this.#page.waitForNetworkIdle$({
          timeout: ms,
          concurrency: networkIdle === "networkidle2" ? 2 : 0,
          idleTime: NETWORK_IDLE_TIME
        })
      ] : []).pipe(k(([{ result: result2 }]) => {
        return result2;
      }), Fe(timeout(ms), F(this.#abortDeferred.valueOrThrow())));
      const result = await me(result$);
      return this.#page.getNavigationResponse(result.navigation);
    }
    waitForDevicePrompt() {
      throw new UnsupportedOperation();
    }
    get detached() {
      return this.#disposed;
    }
    [(_goto_decorators = [throwIfDetached], _setContent_decorators = [throwIfDetached], _waitForNavigation_decorators = [throwIfDetached], disposeSymbol)]() {
      if (this.#disposed) {
        return;
      }
      this.#disposed = true;
      this.#abortDeferred.reject(new Error("Frame detached"));
      this.#context.dispose();
      this.sandboxes[MAIN_SANDBOX][disposeSymbol]();
      this.sandboxes[PUPPETEER_SANDBOX][disposeSymbol]();
    }
    #exposedFunctions = /* @__PURE__ */ new Map();
    async exposeFunction(name, apply) {
      if (this.#exposedFunctions.has(name)) {
        throw new Error(`Failed to add page binding with name ${name}: globalThis['${name}'] already exists!`);
      }
      const exposeable = new ExposeableFunction(this, name, apply);
      this.#exposedFunctions.set(name, exposeable);
      try {
        await exposeable.expose();
      } catch (error) {
        this.#exposedFunctions.delete(name);
        throw error;
      }
    }
    waitForSelector(selector, options) {
      if (selector.startsWith("aria")) {
        throw new UnsupportedOperation("ARIA selector is not supported for BiDi!");
      }
      return super.waitForSelector(selector, options);
    }
  };
})();
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
var SourceActionsType;
(function(SourceActionsType2) {
  SourceActionsType2["None"] = "none";
  SourceActionsType2["Key"] = "key";
  SourceActionsType2["Pointer"] = "pointer";
  SourceActionsType2["Wheel"] = "wheel";
})(SourceActionsType || (SourceActionsType = {}));
var ActionType;
(function(ActionType2) {
  ActionType2["Pause"] = "pause";
  ActionType2["KeyDown"] = "keyDown";
  ActionType2["KeyUp"] = "keyUp";
  ActionType2["PointerUp"] = "pointerUp";
  ActionType2["PointerDown"] = "pointerDown";
  ActionType2["PointerMove"] = "pointerMove";
  ActionType2["Scroll"] = "scroll";
})(ActionType || (ActionType = {}));
const getBidiKeyValue = (key) => {
  switch (key) {
    case "\r":
    case "\n":
      key = "Enter";
      break;
  }
  if ([...key].length === 1) {
    return key;
  }
  switch (key) {
    case "Cancel":
      return "";
    case "Help":
      return "";
    case "Backspace":
      return "";
    case "Tab":
      return "";
    case "Clear":
      return "";
    case "Enter":
      return "";
    case "Shift":
    case "ShiftLeft":
      return "";
    case "Control":
    case "ControlLeft":
      return "";
    case "Alt":
    case "AltLeft":
      return "";
    case "Pause":
      return "";
    case "Escape":
      return "";
    case "PageUp":
      return "";
    case "PageDown":
      return "";
    case "End":
      return "";
    case "Home":
      return "";
    case "ArrowLeft":
      return "";
    case "ArrowUp":
      return "";
    case "ArrowRight":
      return "";
    case "ArrowDown":
      return "";
    case "Insert":
      return "";
    case "Delete":
      return "";
    case "NumpadEqual":
      return "";
    case "Numpad0":
      return "";
    case "Numpad1":
      return "";
    case "Numpad2":
      return "";
    case "Numpad3":
      return "";
    case "Numpad4":
      return "";
    case "Numpad5":
      return "";
    case "Numpad6":
      return "";
    case "Numpad7":
      return "";
    case "Numpad8":
      return "";
    case "Numpad9":
      return "";
    case "NumpadMultiply":
      return "";
    case "NumpadAdd":
      return "";
    case "NumpadSubtract":
      return "";
    case "NumpadDecimal":
      return "";
    case "NumpadDivide":
      return "";
    case "F1":
      return "";
    case "F2":
      return "";
    case "F3":
      return "";
    case "F4":
      return "";
    case "F5":
      return "";
    case "F6":
      return "";
    case "F7":
      return "";
    case "F8":
      return "";
    case "F9":
      return "";
    case "F10":
      return "";
    case "F11":
      return "";
    case "F12":
      return "";
    case "Meta":
    case "MetaLeft":
      return "";
    case "ShiftRight":
      return "";
    case "ControlRight":
      return "";
    case "AltRight":
      return "";
    case "MetaRight":
      return "";
    case "Digit0":
      return "0";
    case "Digit1":
      return "1";
    case "Digit2":
      return "2";
    case "Digit3":
      return "3";
    case "Digit4":
      return "4";
    case "Digit5":
      return "5";
    case "Digit6":
      return "6";
    case "Digit7":
      return "7";
    case "Digit8":
      return "8";
    case "Digit9":
      return "9";
    case "KeyA":
      return "a";
    case "KeyB":
      return "b";
    case "KeyC":
      return "c";
    case "KeyD":
      return "d";
    case "KeyE":
      return "e";
    case "KeyF":
      return "f";
    case "KeyG":
      return "g";
    case "KeyH":
      return "h";
    case "KeyI":
      return "i";
    case "KeyJ":
      return "j";
    case "KeyK":
      return "k";
    case "KeyL":
      return "l";
    case "KeyM":
      return "m";
    case "KeyN":
      return "n";
    case "KeyO":
      return "o";
    case "KeyP":
      return "p";
    case "KeyQ":
      return "q";
    case "KeyR":
      return "r";
    case "KeyS":
      return "s";
    case "KeyT":
      return "t";
    case "KeyU":
      return "u";
    case "KeyV":
      return "v";
    case "KeyW":
      return "w";
    case "KeyX":
      return "x";
    case "KeyY":
      return "y";
    case "KeyZ":
      return "z";
    case "Semicolon":
      return ";";
    case "Equal":
      return "=";
    case "Comma":
      return ",";
    case "Minus":
      return "-";
    case "Period":
      return ".";
    case "Slash":
      return "/";
    case "Backquote":
      return "`";
    case "BracketLeft":
      return "[";
    case "Backslash":
      return "\\";
    case "BracketRight":
      return "]";
    case "Quote":
      return '"';
    default:
      throw new Error(`Unknown key: "${key}"`);
  }
};
class BidiKeyboard extends Keyboard {
  #page;
  constructor(page) {
    super();
    this.#page = page;
  }
  async down(key, _options) {
    await this.#page.connection.send("input.performActions", {
      context: this.#page.mainFrame()._id,
      actions: [
        {
          type: SourceActionsType.Key,
          id: "__puppeteer_keyboard",
          actions: [
            {
              type: ActionType.KeyDown,
              value: getBidiKeyValue(key)
            }
          ]
        }
      ]
    });
  }
  async up(key) {
    await this.#page.connection.send("input.performActions", {
      context: this.#page.mainFrame()._id,
      actions: [
        {
          type: SourceActionsType.Key,
          id: "__puppeteer_keyboard",
          actions: [
            {
              type: ActionType.KeyUp,
              value: getBidiKeyValue(key)
            }
          ]
        }
      ]
    });
  }
  async press(key, options = {}) {
    const { delay = 0 } = options;
    const actions = [
      {
        type: ActionType.KeyDown,
        value: getBidiKeyValue(key)
      }
    ];
    if (delay > 0) {
      actions.push({
        type: ActionType.Pause,
        duration: delay
      });
    }
    actions.push({
      type: ActionType.KeyUp,
      value: getBidiKeyValue(key)
    });
    await this.#page.connection.send("input.performActions", {
      context: this.#page.mainFrame()._id,
      actions: [
        {
          type: SourceActionsType.Key,
          id: "__puppeteer_keyboard",
          actions
        }
      ]
    });
  }
  async type(text, options = {}) {
    const { delay = 0 } = options;
    const values = [...text].map(getBidiKeyValue);
    const actions = [];
    if (delay <= 0) {
      for (const value of values) {
        actions.push({
          type: ActionType.KeyDown,
          value
        }, {
          type: ActionType.KeyUp,
          value
        });
      }
    } else {
      for (const value of values) {
        actions.push({
          type: ActionType.KeyDown,
          value
        }, {
          type: ActionType.Pause,
          duration: delay
        }, {
          type: ActionType.KeyUp,
          value
        });
      }
    }
    await this.#page.connection.send("input.performActions", {
      context: this.#page.mainFrame()._id,
      actions: [
        {
          type: SourceActionsType.Key,
          id: "__puppeteer_keyboard",
          actions
        }
      ]
    });
  }
  async sendCharacter(char) {
    if ([...char].length > 1) {
      throw new Error("Cannot send more than 1 character.");
    }
    const frame = await this.#page.focusedFrame();
    await frame.isolatedRealm().evaluate(async (char2) => {
      document.execCommand("insertText", false, char2);
    }, char);
  }
}
const getBidiButton = (button) => {
  switch (button) {
    case MouseButton.Left:
      return 0;
    case MouseButton.Middle:
      return 1;
    case MouseButton.Right:
      return 2;
    case MouseButton.Back:
      return 3;
    case MouseButton.Forward:
      return 4;
  }
};
class BidiMouse extends Mouse {
  #context;
  #lastMovePoint = { x: 0, y: 0 };
  constructor(context) {
    super();
    this.#context = context;
  }
  async reset() {
    this.#lastMovePoint = { x: 0, y: 0 };
    await this.#context.connection.send("input.releaseActions", {
      context: this.#context.id
    });
  }
  async move(x, y, options = {}) {
    const from = this.#lastMovePoint;
    const to = {
      x: Math.round(x),
      y: Math.round(y)
    };
    const actions = [];
    const steps = options.steps ?? 0;
    for (let i = 0; i < steps; ++i) {
      actions.push({
        type: ActionType.PointerMove,
        x: from.x + (to.x - from.x) * (i / steps),
        y: from.y + (to.y - from.y) * (i / steps),
        origin: options.origin
      });
    }
    actions.push({
      type: ActionType.PointerMove,
      ...to,
      origin: options.origin
    });
    this.#lastMovePoint = to;
    await this.#context.connection.send("input.performActions", {
      context: this.#context.id,
      actions: [
        {
          type: SourceActionsType.Pointer,
          id: "__puppeteer_mouse",
          actions
        }
      ]
    });
  }
  async down(options = {}) {
    await this.#context.connection.send("input.performActions", {
      context: this.#context.id,
      actions: [
        {
          type: SourceActionsType.Pointer,
          id: "__puppeteer_mouse",
          actions: [
            {
              type: ActionType.PointerDown,
              button: getBidiButton(options.button ?? MouseButton.Left)
            }
          ]
        }
      ]
    });
  }
  async up(options = {}) {
    await this.#context.connection.send("input.performActions", {
      context: this.#context.id,
      actions: [
        {
          type: SourceActionsType.Pointer,
          id: "__puppeteer_mouse",
          actions: [
            {
              type: ActionType.PointerUp,
              button: getBidiButton(options.button ?? MouseButton.Left)
            }
          ]
        }
      ]
    });
  }
  async click(x, y, options = {}) {
    const actions = [
      {
        type: ActionType.PointerMove,
        x: Math.round(x),
        y: Math.round(y),
        origin: options.origin
      }
    ];
    const pointerDownAction = {
      type: ActionType.PointerDown,
      button: getBidiButton(options.button ?? MouseButton.Left)
    };
    const pointerUpAction = {
      type: ActionType.PointerUp,
      button: pointerDownAction.button
    };
    for (let i = 1; i < (options.count ?? 1); ++i) {
      actions.push(pointerDownAction, pointerUpAction);
    }
    actions.push(pointerDownAction);
    if (options.delay) {
      actions.push({
        type: ActionType.Pause,
        duration: options.delay
      });
    }
    actions.push(pointerUpAction);
    await this.#context.connection.send("input.performActions", {
      context: this.#context.id,
      actions: [
        {
          type: SourceActionsType.Pointer,
          id: "__puppeteer_mouse",
          actions
        }
      ]
    });
  }
  async wheel(options = {}) {
    await this.#context.connection.send("input.performActions", {
      context: this.#context.id,
      actions: [
        {
          type: SourceActionsType.Wheel,
          id: "__puppeteer_wheel",
          actions: [
            {
              type: ActionType.Scroll,
              ...this.#lastMovePoint ?? {
                x: 0,
                y: 0
              },
              deltaX: options.deltaX ?? 0,
              deltaY: options.deltaY ?? 0
            }
          ]
        }
      ]
    });
  }
  drag() {
    throw new UnsupportedOperation();
  }
  dragOver() {
    throw new UnsupportedOperation();
  }
  dragEnter() {
    throw new UnsupportedOperation();
  }
  drop() {
    throw new UnsupportedOperation();
  }
  dragAndDrop() {
    throw new UnsupportedOperation();
  }
}
class BidiTouchscreen extends Touchscreen {
  #context;
  constructor(context) {
    super();
    this.#context = context;
  }
  async touchStart(x, y, options = {}) {
    await this.#context.connection.send("input.performActions", {
      context: this.#context.id,
      actions: [
        {
          type: SourceActionsType.Pointer,
          id: "__puppeteer_finger",
          parameters: {
            pointerType: "touch"
          },
          actions: [
            {
              type: ActionType.PointerMove,
              x: Math.round(x),
              y: Math.round(y),
              origin: options.origin
            },
            {
              type: ActionType.PointerDown,
              button: 0
            }
          ]
        }
      ]
    });
  }
  async touchMove(x, y, options = {}) {
    await this.#context.connection.send("input.performActions", {
      context: this.#context.id,
      actions: [
        {
          type: SourceActionsType.Pointer,
          id: "__puppeteer_finger",
          parameters: {
            pointerType: "touch"
          },
          actions: [
            {
              type: ActionType.PointerMove,
              x: Math.round(x),
              y: Math.round(y),
              origin: options.origin
            }
          ]
        }
      ]
    });
  }
  async touchEnd() {
    await this.#context.connection.send("input.performActions", {
      context: this.#context.id,
      actions: [
        {
          type: SourceActionsType.Pointer,
          id: "__puppeteer_finger",
          parameters: {
            pointerType: "touch"
          },
          actions: [
            {
              type: ActionType.PointerUp,
              button: 0
            }
          ]
        }
      ]
    });
  }
}
class BidiHTTPRequest extends HTTPRequest {
  _response = null;
  _redirectChain;
  _navigationId;
  #url;
  #resourceType;
  #method;
  #postData;
  #headers = {};
  #initiator;
  #frame;
  constructor(event, frame, redirectChain = []) {
    super();
    this.#url = event.request.url;
    this.#resourceType = event.initiator.type.toLowerCase();
    this.#method = event.request.method;
    this.#postData = void 0;
    this.#initiator = event.initiator;
    this.#frame = frame;
    this._requestId = event.request.request;
    this._redirectChain = redirectChain;
    this._navigationId = event.navigation;
    for (const header of event.request.headers) {
      if (header.value.type === "string") {
        this.#headers[header.name.toLowerCase()] = header.value.value;
      }
    }
  }
  get client() {
    throw new UnsupportedOperation();
  }
  url() {
    return this.#url;
  }
  resourceType() {
    return this.#resourceType;
  }
  method() {
    return this.#method;
  }
  postData() {
    return this.#postData;
  }
  hasPostData() {
    return this.#postData !== void 0;
  }
  async fetchPostData() {
    return this.#postData;
  }
  headers() {
    return this.#headers;
  }
  response() {
    return this._response;
  }
  isNavigationRequest() {
    return Boolean(this._navigationId);
  }
  initiator() {
    return this.#initiator;
  }
  redirectChain() {
    return this._redirectChain.slice();
  }
  enqueueInterceptAction(pendingHandler) {
    void pendingHandler();
  }
  frame() {
    return this.#frame;
  }
  continueRequestOverrides() {
    throw new UnsupportedOperation();
  }
  continue(_overrides = {}) {
    throw new UnsupportedOperation();
  }
  responseForRequest() {
    throw new UnsupportedOperation();
  }
  abortErrorReason() {
    throw new UnsupportedOperation();
  }
  interceptResolutionState() {
    throw new UnsupportedOperation();
  }
  isInterceptResolutionHandled() {
    throw new UnsupportedOperation();
  }
  finalizeInterceptions() {
    throw new UnsupportedOperation();
  }
  abort() {
    throw new UnsupportedOperation();
  }
  respond(_response, _priority) {
    throw new UnsupportedOperation();
  }
  failure() {
    throw new UnsupportedOperation();
  }
}
class BidiHTTPResponse extends HTTPResponse {
  #request;
  #remoteAddress;
  #status;
  #statusText;
  #url;
  #fromCache;
  #headers = {};
  #timings;
  constructor(request, { response }) {
    super();
    this.#request = request;
    this.#remoteAddress = {
      ip: "",
      port: -1
    };
    this.#url = response.url;
    this.#fromCache = response.fromCache;
    this.#status = response.status;
    this.#statusText = response.statusText;
    this.#timings = null;
    for (const header of response.headers || []) {
      if (header.value.type === "string") {
        this.#headers[header.name.toLowerCase()] = header.value.value;
      }
    }
  }
  remoteAddress() {
    return this.#remoteAddress;
  }
  url() {
    return this.#url;
  }
  status() {
    return this.#status;
  }
  statusText() {
    return this.#statusText;
  }
  headers() {
    return this.#headers;
  }
  request() {
    return this.#request;
  }
  fromCache() {
    return this.#fromCache;
  }
  timing() {
    return this.#timings;
  }
  frame() {
    return this.#request.frame();
  }
  fromServiceWorker() {
    return false;
  }
  securityDetails() {
    throw new UnsupportedOperation();
  }
  buffer() {
    throw new UnsupportedOperation();
  }
}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
class BidiNetworkManager extends EventEmitter {
  #connection;
  #page;
  #subscriptions = new DisposableStack();
  #requestMap = /* @__PURE__ */ new Map();
  #navigationMap = /* @__PURE__ */ new Map();
  constructor(connection, page) {
    super();
    this.#connection = connection;
    this.#page = page;
    this.#subscriptions.use(new EventSubscription(this.#connection, "network.beforeRequestSent", this.#onBeforeRequestSent.bind(this)));
    this.#subscriptions.use(new EventSubscription(this.#connection, "network.responseStarted", this.#onResponseStarted.bind(this)));
    this.#subscriptions.use(new EventSubscription(this.#connection, "network.responseCompleted", this.#onResponseCompleted.bind(this)));
    this.#subscriptions.use(new EventSubscription(this.#connection, "network.fetchError", this.#onFetchError.bind(this)));
  }
  #onBeforeRequestSent(event) {
    const frame = this.#page.frame(event.context ?? "");
    if (!frame) {
      return;
    }
    const request = this.#requestMap.get(event.request.request);
    let upsertRequest;
    if (request) {
      request._redirectChain.push(request);
      upsertRequest = new BidiHTTPRequest(event, frame, request._redirectChain);
    } else {
      upsertRequest = new BidiHTTPRequest(event, frame, []);
    }
    this.#requestMap.set(event.request.request, upsertRequest);
    this.emit(NetworkManagerEvent.Request, upsertRequest);
  }
  #onResponseStarted(_event) {
  }
  #onResponseCompleted(event) {
    const request = this.#requestMap.get(event.request.request);
    if (!request) {
      return;
    }
    const response = new BidiHTTPResponse(request, event);
    request._response = response;
    if (event.navigation) {
      this.#navigationMap.set(event.navigation, response);
    }
    if (response.fromCache()) {
      this.emit(NetworkManagerEvent.RequestServedFromCache, request);
    }
    this.emit(NetworkManagerEvent.Response, response);
    this.emit(NetworkManagerEvent.RequestFinished, request);
  }
  #onFetchError(event) {
    const request = this.#requestMap.get(event.request.request);
    if (!request) {
      return;
    }
    request._failureText = event.errorText;
    this.emit(NetworkManagerEvent.RequestFailed, request);
    this.#requestMap.delete(event.request.request);
  }
  getNavigationResponse(navigationId) {
    if (!navigationId) {
      return null;
    }
    const response = this.#navigationMap.get(navigationId);
    return response ?? null;
  }
  inFlightRequestsCount() {
    let inFlightRequestCounter = 0;
    for (const request of this.#requestMap.values()) {
      if (!request.response() || request._failureText) {
        inFlightRequestCounter++;
      }
    }
    return inFlightRequestCounter;
  }
  clearMapAfterFrameDispose(frame) {
    for (const [id, request] of this.#requestMap.entries()) {
      if (request.frame() === frame) {
        this.#requestMap.delete(id);
      }
    }
    for (const [id, response] of this.#navigationMap.entries()) {
      if (response.frame() === frame) {
        this.#navigationMap.delete(id);
      }
    }
  }
  dispose() {
    this.removeAllListeners();
    this.#requestMap.clear();
    this.#navigationMap.clear();
    this.#subscriptions.dispose();
  }
}
/**
 * @license
 * Copyright 2022 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
var __addDisposableResource = function(env, value, async) {
  if (value !== null && value !== void 0) {
    if (typeof value !== "object" && typeof value !== "function") throw new TypeError("Object expected.");
    var dispose;
    if (async) {
      if (!Symbol.asyncDispose) throw new TypeError("Symbol.asyncDispose is not defined.");
      dispose = value[Symbol.asyncDispose];
    }
    if (dispose === void 0) {
      if (!Symbol.dispose) throw new TypeError("Symbol.dispose is not defined.");
      dispose = value[Symbol.dispose];
    }
    if (typeof dispose !== "function") throw new TypeError("Object not disposable.");
    env.stack.push({ value, dispose, async });
  } else if (async) {
    env.stack.push({ async: true });
  }
  return value;
};
var __disposeResources = /* @__PURE__ */ function(SuppressedError2) {
  return function(env) {
    function fail(e) {
      env.error = env.hasError ? new SuppressedError2(e, env.error, "An error was suppressed during disposal.") : e;
      env.hasError = true;
    }
    function next() {
      while (env.stack.length) {
        var rec = env.stack.pop();
        try {
          var result = rec.dispose && rec.dispose.call(rec.value);
          if (rec.async) return Promise.resolve(result).then(next, function(e) {
            fail(e);
            return next();
          });
        } catch (e) {
          fail(e);
        }
      }
      if (env.hasError) throw env.error;
    }
    return next();
  };
}(typeof SuppressedError === "function" ? SuppressedError : function(error, suppressed, message) {
  var e = new Error(message);
  return e.name = "SuppressedError", e.error = error, e.suppressed = suppressed, e;
});
class BidiPage extends Page {
  #accessibility;
  #connection;
  #frameTree = new FrameTree();
  #networkManager;
  #viewport = null;
  #closedDeferred = Deferred.create();
  #subscribedEvents = /* @__PURE__ */ new Map([
    ["log.entryAdded", this.#onLogEntryAdded.bind(this)],
    ["browsingContext.load", this.#onFrameLoaded.bind(this)],
    [
      "browsingContext.fragmentNavigated",
      this.#onFrameFragmentNavigated.bind(this)
    ],
    [
      "browsingContext.domContentLoaded",
      this.#onFrameDOMContentLoaded.bind(this)
    ],
    ["browsingContext.userPromptOpened", this.#onDialog.bind(this)]
  ]);
  #networkManagerEvents = [
    [
      NetworkManagerEvent.Request,
      (request) => {
        this.emit("request", request);
      }
    ],
    [
      NetworkManagerEvent.RequestServedFromCache,
      (request) => {
        this.emit("requestservedfromcache", request);
      }
    ],
    [
      NetworkManagerEvent.RequestFailed,
      (request) => {
        this.emit("requestfailed", request);
      }
    ],
    [
      NetworkManagerEvent.RequestFinished,
      (request) => {
        this.emit("requestfinished", request);
      }
    ],
    [
      NetworkManagerEvent.Response,
      (response) => {
        this.emit("response", response);
      }
    ]
  ];
  #browsingContextEvents = /* @__PURE__ */ new Map([
    [BrowsingContextEvent.Created, this.#onContextCreated.bind(this)],
    [BrowsingContextEvent.Destroyed, this.#onContextDestroyed.bind(this)]
  ]);
  #tracing;
  #coverage;
  #cdpEmulationManager;
  #emulationManager;
  #mouse;
  #touchscreen;
  #keyboard;
  #browsingContext;
  #browserContext;
  #target;
  _client() {
    return this.mainFrame().context().cdpSession;
  }
  constructor(browsingContext, browserContext, target) {
    super();
    this.#browsingContext = browsingContext;
    this.#browserContext = browserContext;
    this.#target = target;
    this.#connection = browsingContext.connection;
    for (const [event, subscriber] of this.#browsingContextEvents) {
      this.#browsingContext.on(event, subscriber);
    }
    this.#networkManager = new BidiNetworkManager(this.#connection, this);
    for (const [event, subscriber] of this.#subscribedEvents) {
      this.#connection.on(event, subscriber);
    }
    for (const [event, subscriber] of this.#networkManagerEvents) {
      this.#networkManager.on(event, subscriber);
    }
    const frame = new BidiFrame(this, this.#browsingContext, this._timeoutSettings, this.#browsingContext.parent);
    this.#frameTree.addFrame(frame);
    this.emit("frameattached", frame);
    this.#accessibility = new Accessibility(this.mainFrame().context().cdpSession);
    this.#tracing = new Tracing(this.mainFrame().context().cdpSession);
    this.#coverage = new Coverage(this.mainFrame().context().cdpSession);
    this.#cdpEmulationManager = new EmulationManager$1(this.mainFrame().context().cdpSession);
    this.#emulationManager = new EmulationManager(browsingContext);
    this.#mouse = new BidiMouse(this.mainFrame().context());
    this.#touchscreen = new BidiTouchscreen(this.mainFrame().context());
    this.#keyboard = new BidiKeyboard(this);
  }
  /**
   * @internal
   */
  get connection() {
    return this.#connection;
  }
  async setUserAgent(userAgent, userAgentMetadata) {
    await this._client().send("Network.setUserAgentOverride", {
      userAgent,
      userAgentMetadata
    });
  }
  async setBypassCSP(enabled) {
    await this._client().send("Page.setBypassCSP", { enabled });
  }
  async queryObjects(prototypeHandle) {
    assert(!prototypeHandle.disposed, "Prototype JSHandle is disposed!");
    assert(prototypeHandle.id, "Prototype JSHandle must not be referencing primitive value");
    const response = await this.mainFrame().client.send("Runtime.queryObjects", {
      prototypeObjectId: prototypeHandle.id
    });
    return createBidiHandle(this.mainFrame().mainRealm(), {
      type: "array",
      handle: response.objects.objectId
    });
  }
  _setBrowserContext(browserContext) {
    this.#browserContext = browserContext;
  }
  get accessibility() {
    return this.#accessibility;
  }
  get tracing() {
    return this.#tracing;
  }
  get coverage() {
    return this.#coverage;
  }
  get mouse() {
    return this.#mouse;
  }
  get touchscreen() {
    return this.#touchscreen;
  }
  get keyboard() {
    return this.#keyboard;
  }
  browser() {
    return this.browserContext().browser();
  }
  browserContext() {
    return this.#browserContext;
  }
  mainFrame() {
    const mainFrame = this.#frameTree.getMainFrame();
    assert(mainFrame, "Requesting main frame too early!");
    return mainFrame;
  }
  /**
   * @internal
   */
  async focusedFrame() {
    const env_1 = { stack: [], error: void 0, hasError: false };
    try {
      const frame = __addDisposableResource(env_1, await this.mainFrame().isolatedRealm().evaluateHandle(() => {
        let frame2;
        let win = window;
        while (win?.document.activeElement instanceof HTMLIFrameElement) {
          frame2 = win.document.activeElement;
          win = frame2.contentWindow;
        }
        return frame2;
      }), false);
      if (!(frame instanceof BidiElementHandle)) {
        return this.mainFrame();
      }
      return await frame.contentFrame();
    } catch (e_1) {
      env_1.error = e_1;
      env_1.hasError = true;
    } finally {
      __disposeResources(env_1);
    }
  }
  frames() {
    return Array.from(this.#frameTree.frames());
  }
  frame(frameId) {
    return this.#frameTree.getById(frameId ?? "") || null;
  }
  childFrames(frameId) {
    return this.#frameTree.childFrames(frameId);
  }
  #onFrameLoaded(info) {
    const frame = this.frame(info.context);
    if (frame && this.mainFrame() === frame) {
      this.emit("load", void 0);
    }
  }
  #onFrameFragmentNavigated(info) {
    const frame = this.frame(info.context);
    if (frame) {
      this.emit("framenavigated", frame);
    }
  }
  #onFrameDOMContentLoaded(info) {
    const frame = this.frame(info.context);
    if (frame) {
      frame._hasStartedLoading = true;
      if (this.mainFrame() === frame) {
        this.emit("domcontentloaded", void 0);
      }
      this.emit("framenavigated", frame);
    }
  }
  #onContextCreated(context) {
    if (!this.frame(context.id) && (this.frame(context.parent ?? "") || !this.#frameTree.getMainFrame())) {
      const frame = new BidiFrame(this, context, this._timeoutSettings, context.parent);
      this.#frameTree.addFrame(frame);
      if (frame !== this.mainFrame()) {
        this.emit("frameattached", frame);
      }
    }
  }
  #onContextDestroyed(context) {
    const frame = this.frame(context.id);
    if (frame) {
      if (frame === this.mainFrame()) {
        this.emit("close", void 0);
      }
      this.#removeFramesRecursively(frame);
    }
  }
  #removeFramesRecursively(frame) {
    for (const child of frame.childFrames()) {
      this.#removeFramesRecursively(child);
    }
    frame[disposeSymbol]();
    this.#networkManager.clearMapAfterFrameDispose(frame);
    this.#frameTree.removeFrame(frame);
    this.emit("framedetached", frame);
  }
  #onLogEntryAdded(event) {
    const frame = this.frame(event.source.context);
    if (!frame) {
      return;
    }
    if (isConsoleLogEntry(event)) {
      const args = event.args.map((arg) => {
        return createBidiHandle(frame.mainRealm(), arg);
      });
      const text = args.reduce((value, arg) => {
        const parsedValue = arg.isPrimitiveValue ? BidiDeserializer.deserialize(arg.remoteValue()) : arg.toString();
        return `${value} ${parsedValue}`;
      }, "").slice(1);
      this.emit("console", new ConsoleMessage(event.method, text, args, getStackTraceLocations(event.stackTrace)));
    } else if (isJavaScriptLogEntry(event)) {
      const error = new Error(event.text ?? "");
      const messageHeight = error.message.split("\n").length;
      const messageLines = error.stack.split("\n").splice(0, messageHeight);
      const stackLines = [];
      if (event.stackTrace) {
        for (const frame2 of event.stackTrace.callFrames) {
          stackLines.push(`    at ${frame2.functionName || "<anonymous>"} (${frame2.url}:${frame2.lineNumber + 1}:${frame2.columnNumber + 1})`);
          if (stackLines.length >= Error.stackTraceLimit) {
            break;
          }
        }
      }
      error.stack = [...messageLines, ...stackLines].join("\n");
      this.emit("pageerror", error);
    } else {
      debugError(`Unhandled LogEntry with type "${event.type}", text "${event.text}" and level "${event.level}"`);
    }
  }
  #onDialog(event) {
    const frame = this.frame(event.context);
    if (!frame) {
      return;
    }
    const type = validateDialogType(event.type);
    const dialog = new BidiDialog(frame.context(), type, event.message, event.defaultValue);
    this.emit("dialog", dialog);
  }
  getNavigationResponse(id) {
    return this.#networkManager.getNavigationResponse(id);
  }
  isClosed() {
    return this.#closedDeferred.finished();
  }
  async close(options) {
    if (this.#closedDeferred.finished()) {
      return;
    }
    this.#closedDeferred.reject(new TargetCloseError("Page closed!"));
    this.#networkManager.dispose();
    await this.#connection.send("browsingContext.close", {
      context: this.mainFrame()._id,
      promptUnload: options?.runBeforeUnload ?? false
    });
    this.emit("close", void 0);
    this.removeAllListeners();
  }
  async reload(options = {}) {
    const { waitUntil = "load", timeout: ms = this._timeoutSettings.navigationTimeout() } = options;
    const [readiness, networkIdle] = getBiDiReadinessState(waitUntil);
    const result$ = Ee(F(this.#connection.send("browsingContext.reload", {
      context: this.mainFrame()._id,
      wait: readiness
    })), ...networkIdle !== null ? [
      this.waitForNetworkIdle$({
        timeout: ms,
        concurrency: networkIdle === "networkidle2" ? 2 : 0,
        idleTime: NETWORK_IDLE_TIME
      })
    ] : []).pipe(k(([{ result: result2 }]) => {
      return result2;
    }), Fe(timeout(ms), F(this.#closedDeferred.valueOrThrow())), rewriteNavigationError(this.url(), ms));
    const result = await me(result$);
    return this.getNavigationResponse(result.navigation);
  }
  setDefaultNavigationTimeout(timeout2) {
    this._timeoutSettings.setDefaultNavigationTimeout(timeout2);
  }
  setDefaultTimeout(timeout2) {
    this._timeoutSettings.setDefaultTimeout(timeout2);
  }
  getDefaultTimeout() {
    return this._timeoutSettings.timeout();
  }
  isJavaScriptEnabled() {
    return this.#cdpEmulationManager.javascriptEnabled;
  }
  async setGeolocation(options) {
    return await this.#cdpEmulationManager.setGeolocation(options);
  }
  async setJavaScriptEnabled(enabled) {
    return await this.#cdpEmulationManager.setJavaScriptEnabled(enabled);
  }
  async emulateMediaType(type) {
    return await this.#cdpEmulationManager.emulateMediaType(type);
  }
  async emulateCPUThrottling(factor) {
    return await this.#cdpEmulationManager.emulateCPUThrottling(factor);
  }
  async emulateMediaFeatures(features) {
    return await this.#cdpEmulationManager.emulateMediaFeatures(features);
  }
  async emulateTimezone(timezoneId) {
    return await this.#cdpEmulationManager.emulateTimezone(timezoneId);
  }
  async emulateIdleState(overrides) {
    return await this.#cdpEmulationManager.emulateIdleState(overrides);
  }
  async emulateVisionDeficiency(type) {
    return await this.#cdpEmulationManager.emulateVisionDeficiency(type);
  }
  async setViewport(viewport) {
    if (!this.#browsingContext.supportsCdp()) {
      await this.#emulationManager.emulateViewport(viewport);
      this.#viewport = viewport;
      return;
    }
    const needsReload = await this.#cdpEmulationManager.emulateViewport(viewport);
    this.#viewport = viewport;
    if (needsReload) {
      await this.reload();
    }
  }
  viewport() {
    return this.#viewport;
  }
  async pdf(options = {}) {
    const { timeout: ms = this._timeoutSettings.timeout(), path = void 0 } = options;
    const { printBackground: background, margin, landscape, width, height, pageRanges: ranges, scale, preferCSSPageSize } = parsePDFOptions(options, "cm");
    const pageRanges = ranges ? ranges.split(", ") : [];
    const { result } = await me(F(this.#connection.send("browsingContext.print", {
      context: this.mainFrame()._id,
      background,
      margin,
      orientation: landscape ? "landscape" : "portrait",
      page: {
        width,
        height
      },
      pageRanges,
      scale,
      shrinkToFit: !preferCSSPageSize
    })).pipe(Fe(timeout(ms))));
    const buffer = Buffer.from(result.data, "base64");
    await this._maybeWriteBufferToFile(path, buffer);
    return buffer;
  }
  async createPDFStream(options) {
    const buffer = await this.pdf(options);
    try {
      const { Readable } = await import("stream");
      return Readable.from(buffer);
    } catch (error) {
      if (error instanceof TypeError) {
        throw new Error("Can only pass a file path in a Node-like environment.");
      }
      throw error;
    }
  }
  async _screenshot(options) {
    const { clip, type, captureBeyondViewport, quality } = options;
    if (options.omitBackground !== void 0 && options.omitBackground) {
      throw new UnsupportedOperation(`BiDi does not support 'omitBackground'.`);
    }
    if (options.optimizeForSpeed !== void 0 && options.optimizeForSpeed) {
      throw new UnsupportedOperation(`BiDi does not support 'optimizeForSpeed'.`);
    }
    if (options.fromSurface !== void 0 && !options.fromSurface) {
      throw new UnsupportedOperation(`BiDi does not support 'fromSurface'.`);
    }
    if (clip !== void 0 && clip.scale !== void 0 && clip.scale !== 1) {
      throw new UnsupportedOperation(`BiDi does not support 'scale' in 'clip'.`);
    }
    let box;
    if (clip) {
      if (captureBeyondViewport) {
        box = clip;
      } else {
        const [pageLeft, pageTop] = await this.evaluate(() => {
          if (!window.visualViewport) {
            throw new Error("window.visualViewport is not supported.");
          }
          return [
            window.visualViewport.pageLeft,
            window.visualViewport.pageTop
          ];
        });
        box = {
          ...clip,
          x: clip.x - pageLeft,
          y: clip.y - pageTop
        };
      }
    }
    const { result: { data } } = await this.#connection.send("browsingContext.captureScreenshot", {
      context: this.mainFrame()._id,
      origin: captureBeyondViewport ? "document" : "viewport",
      format: {
        type: `image/${type}`,
        ...quality !== void 0 ? { quality: quality / 100 } : {}
      },
      ...box ? { clip: { type: "box", ...box } } : {}
    });
    return data;
  }
  async createCDPSession() {
    const { sessionId } = await this.mainFrame().context().cdpSession.send("Target.attachToTarget", {
      targetId: this.mainFrame()._id,
      flatten: true
    });
    return new CdpSessionWrapper(this.mainFrame().context(), sessionId);
  }
  async bringToFront() {
    await this.#connection.send("browsingContext.activate", {
      context: this.mainFrame()._id
    });
  }
  async evaluateOnNewDocument(pageFunction, ...args) {
    const expression = evaluationExpression(pageFunction, ...args);
    const { result } = await this.#connection.send("script.addPreloadScript", {
      functionDeclaration: expression,
      contexts: [this.mainFrame()._id]
    });
    return { identifier: result.script };
  }
  async removeScriptToEvaluateOnNewDocument(id) {
    await this.#connection.send("script.removePreloadScript", {
      script: id
    });
  }
  async exposeFunction(name, pptrFunction) {
    return await this.mainFrame().exposeFunction(name, "default" in pptrFunction ? pptrFunction.default : pptrFunction);
  }
  isDragInterceptionEnabled() {
    return false;
  }
  async setCacheEnabled(enabled) {
    await this._client().send("Network.setCacheDisabled", {
      cacheDisabled: !enabled
    });
  }
  isServiceWorkerBypassed() {
    throw new UnsupportedOperation();
  }
  target() {
    return this.#target;
  }
  waitForFileChooser() {
    throw new UnsupportedOperation();
  }
  workers() {
    throw new UnsupportedOperation();
  }
  setRequestInterception() {
    throw new UnsupportedOperation();
  }
  setDragInterception() {
    throw new UnsupportedOperation();
  }
  setBypassServiceWorker() {
    throw new UnsupportedOperation();
  }
  setOfflineMode() {
    throw new UnsupportedOperation();
  }
  emulateNetworkConditions() {
    throw new UnsupportedOperation();
  }
  cookies() {
    throw new UnsupportedOperation();
  }
  setCookie() {
    throw new UnsupportedOperation();
  }
  deleteCookie() {
    throw new UnsupportedOperation();
  }
  removeExposedFunction() {
    throw new UnsupportedOperation();
  }
  authenticate() {
    throw new UnsupportedOperation();
  }
  setExtraHTTPHeaders() {
    throw new UnsupportedOperation();
  }
  metrics() {
    throw new UnsupportedOperation();
  }
  async goBack(options = {}) {
    return await this.#go(-1, options);
  }
  async goForward(options = {}) {
    return await this.#go(1, options);
  }
  async #go(delta, options) {
    try {
      const result = await Promise.all([
        this.waitForNavigation(options),
        this.#connection.send("browsingContext.traverseHistory", {
          delta,
          context: this.mainFrame()._id
        })
      ]);
      return result[0];
    } catch (err) {
      if (isErrorLike(err)) {
        if (err.message.includes("no such history entry")) {
          return null;
        }
      }
      throw err;
    }
  }
  waitForDevicePrompt() {
    throw new UnsupportedOperation();
  }
}
function isConsoleLogEntry(event) {
  return event.type === "console";
}
function isJavaScriptLogEntry(event) {
  return event.type === "javascript";
}
function getStackTraceLocations(stackTrace) {
  const stackTraceLocations = [];
  if (stackTrace) {
    for (const callFrame of stackTrace.callFrames) {
      stackTraceLocations.push({
        url: callFrame.url,
        lineNumber: callFrame.lineNumber,
        columnNumber: callFrame.columnNumber
      });
    }
  }
  return stackTraceLocations;
}
function evaluationExpression(fun, ...args) {
  return `() => {${evaluationString(fun, ...args)}}`;
}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
class BidiTarget extends Target {
  _browserContext;
  constructor(browserContext) {
    super();
    this._browserContext = browserContext;
  }
  _setBrowserContext(browserContext) {
    this._browserContext = browserContext;
  }
  asPage() {
    throw new UnsupportedOperation();
  }
  browser() {
    return this._browserContext.browser();
  }
  browserContext() {
    return this._browserContext;
  }
  opener() {
    throw new UnsupportedOperation();
  }
  createCDPSession() {
    throw new UnsupportedOperation();
  }
}
class BiDiBrowserTarget extends Target {
  #browser;
  constructor(browser) {
    super();
    this.#browser = browser;
  }
  url() {
    return "";
  }
  type() {
    return TargetType.BROWSER;
  }
  asPage() {
    throw new UnsupportedOperation();
  }
  browser() {
    return this.#browser;
  }
  browserContext() {
    return this.#browser.defaultBrowserContext();
  }
  opener() {
    throw new UnsupportedOperation();
  }
  createCDPSession() {
    throw new UnsupportedOperation();
  }
}
class BiDiBrowsingContextTarget extends BidiTarget {
  _browsingContext;
  constructor(browserContext, browsingContext) {
    super(browserContext);
    this._browsingContext = browsingContext;
  }
  url() {
    return this._browsingContext.url;
  }
  async createCDPSession() {
    const { sessionId } = await this._browsingContext.cdpSession.send("Target.attachToTarget", {
      targetId: this._browsingContext.id,
      flatten: true
    });
    return new CdpSessionWrapper(this._browsingContext, sessionId);
  }
  type() {
    return TargetType.PAGE;
  }
}
class BiDiPageTarget extends BiDiBrowsingContextTarget {
  #page;
  constructor(browserContext, browsingContext) {
    super(browserContext, browsingContext);
    this.#page = new BidiPage(browsingContext, browserContext, this);
  }
  async page() {
    return this.#page;
  }
  _setBrowserContext(browserContext) {
    super._setBrowserContext(browserContext);
    this.#page._setBrowserContext(browserContext);
  }
}
/**
 * @license
 * Copyright 2022 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
class BidiBrowser extends Browser$1 {
  protocol = "webDriverBiDi";
  // TODO: Update generator to include fully module
  static subscribeModules = [
    "browsingContext",
    "network",
    "log",
    "script"
  ];
  static subscribeCdpEvents = [
    // Coverage
    "cdp.Debugger.scriptParsed",
    "cdp.CSS.styleSheetAdded",
    "cdp.Runtime.executionContextsCleared",
    // Tracing
    "cdp.Tracing.tracingComplete",
    // TODO: subscribe to all CDP events in the future.
    "cdp.Network.requestWillBeSent",
    "cdp.Debugger.scriptParsed",
    "cdp.Page.screencastFrame"
  ];
  static async create(opts) {
    const session = await Session.from(opts.connection, {
      alwaysMatch: {
        acceptInsecureCerts: opts.ignoreHTTPSErrors,
        webSocketUrl: true
      }
    });
    await session.subscribe(session.capabilities.browserName.toLocaleLowerCase().includes("firefox") ? BidiBrowser.subscribeModules : [...BidiBrowser.subscribeModules, ...BidiBrowser.subscribeCdpEvents]);
    const browser = new BidiBrowser(session.browser, opts);
    browser.#initialize();
    await browser.#getTree();
    return browser;
  }
  #process;
  #closeCallback;
  #browserCore;
  #defaultViewport;
  #targets = /* @__PURE__ */ new Map();
  #browserContexts = /* @__PURE__ */ new WeakMap();
  #browserTarget;
  #connectionEventHandlers = /* @__PURE__ */ new Map([
    ["browsingContext.contextCreated", this.#onContextCreated.bind(this)],
    ["browsingContext.contextDestroyed", this.#onContextDestroyed.bind(this)],
    ["browsingContext.domContentLoaded", this.#onContextDomLoaded.bind(this)],
    ["browsingContext.fragmentNavigated", this.#onContextNavigation.bind(this)],
    ["browsingContext.navigationStarted", this.#onContextNavigation.bind(this)]
  ]);
  constructor(browserCore, opts) {
    super();
    this.#process = opts.process;
    this.#closeCallback = opts.closeCallback;
    this.#browserCore = browserCore;
    this.#defaultViewport = opts.defaultViewport;
    this.#browserTarget = new BiDiBrowserTarget(this);
    for (const context of this.#browserCore.userContexts) {
      this.#createBrowserContext(context);
    }
  }
  #initialize() {
    this.#browserCore.once("disconnected", () => {
      this.emit("disconnected", void 0);
    });
    this.#process?.once("close", () => {
      this.#browserCore.dispose("Browser process exited.", true);
      this.connection.dispose();
    });
    for (const [eventName, handler] of this.#connectionEventHandlers) {
      this.connection.on(eventName, handler);
    }
  }
  get #browserName() {
    return this.#browserCore.session.capabilities.browserName;
  }
  get #browserVersion() {
    return this.#browserCore.session.capabilities.browserVersion;
  }
  userAgent() {
    throw new UnsupportedOperation();
  }
  #createBrowserContext(userContext) {
    const browserContext = new BidiBrowserContext(this, userContext, {
      defaultViewport: this.#defaultViewport
    });
    this.#browserContexts.set(userContext, browserContext);
    return browserContext;
  }
  #onContextDomLoaded(event) {
    const target = this.#targets.get(event.context);
    if (target) {
      this.emit("targetchanged", target);
      target.browserContext().emit("targetchanged", target);
    }
  }
  #onContextNavigation(event) {
    const target = this.#targets.get(event.context);
    if (target) {
      this.emit("targetchanged", target);
      target.browserContext().emit("targetchanged", target);
    }
  }
  #onContextCreated(event) {
    const context = new BrowsingContext$1(this.connection, event, this.#browserName);
    this.connection.registerBrowsingContexts(context);
    const browserContext = event.userContext === "default" ? this.defaultBrowserContext() : this.browserContexts().find((browserContext2) => {
      return browserContext2.id === event.userContext;
    });
    if (!browserContext) {
      throw new Error("Missing browser contexts");
    }
    const target = !context.parent ? new BiDiPageTarget(browserContext, context) : new BiDiBrowsingContextTarget(browserContext, context);
    this.#targets.set(event.context, target);
    this.emit("targetcreated", target);
    target.browserContext().emit("targetcreated", target);
    if (context.parent) {
      const topLevel = this.connection.getTopLevelContext(context.parent);
      topLevel.emit(BrowsingContextEvent.Created, context);
    }
  }
  async #getTree() {
    const { result } = await this.connection.send("browsingContext.getTree", {});
    for (const context of result.contexts) {
      this.#onContextCreated(context);
    }
  }
  async #onContextDestroyed(event) {
    const context = this.connection.getBrowsingContext(event.context);
    const topLevelContext = this.connection.getTopLevelContext(event.context);
    topLevelContext.emit(BrowsingContextEvent.Destroyed, context);
    const target = this.#targets.get(event.context);
    const page = await target?.page();
    await page?.close().catch(debugError);
    this.#targets.delete(event.context);
    if (target) {
      this.emit("targetdestroyed", target);
      target.browserContext().emit("targetdestroyed", target);
    }
  }
  get connection() {
    return this.#browserCore.session.connection;
  }
  wsEndpoint() {
    return this.connection.url;
  }
  async close() {
    for (const [eventName, handler] of this.#connectionEventHandlers) {
      this.connection.off(eventName, handler);
    }
    if (this.connection.closed) {
      return;
    }
    try {
      await this.#browserCore.close();
      await this.#closeCallback?.call(null);
    } catch (error) {
      debugError(error);
    } finally {
      this.connection.dispose();
    }
  }
  get connected() {
    return !this.#browserCore.disposed;
  }
  process() {
    return this.#process ?? null;
  }
  async createIncognitoBrowserContext(_options) {
    const userContext = await this.#browserCore.createUserContext();
    return this.#createBrowserContext(userContext);
  }
  async version() {
    return `${this.#browserName}/${this.#browserVersion}`;
  }
  browserContexts() {
    return [...this.#browserCore.userContexts].map((context) => {
      return this.#browserContexts.get(context);
    });
  }
  defaultBrowserContext() {
    return this.#browserContexts.get(this.#browserCore.defaultUserContext);
  }
  newPage() {
    return this.defaultBrowserContext().newPage();
  }
  targets() {
    return [this.#browserTarget, ...Array.from(this.#targets.values())];
  }
  _getTargetById(id) {
    const target = this.#targets.get(id);
    if (!target) {
      throw new Error("Target not found");
    }
    return target;
  }
  target() {
    return this.#browserTarget;
  }
  async disconnect() {
    try {
      await this.#browserCore.session.end();
    } catch (error) {
      debugError(error);
    } finally {
      this.connection.dispose();
    }
  }
  get debugInfo() {
    return {
      pendingProtocolErrors: this.connection.getPendingProtocolErrors()
    };
  }
}
export {
  BiDiBrowserTarget,
  BiDiBrowsingContextTarget,
  BiDiPageTarget,
  BidiBrowser,
  BidiBrowserContext,
  BidiConnection,
  BidiElementHandle,
  BidiFrame,
  BidiHTTPRequest,
  BidiHTTPResponse,
  BidiJSHandle,
  BidiKeyboard,
  BidiMouse,
  BidiNetworkManager,
  BidiPage,
  BidiRealm,
  BidiTarget,
  BidiTouchscreen,
  BrowsingContext$1 as BrowsingContext,
  BrowsingContextEvent,
  CdpSessionWrapper,
  MAIN_SANDBOX,
  PUPPETEER_SANDBOX,
  Sandbox,
  cdpSessions,
  connectBidiOverCdp,
  createBidiHandle
};
//# sourceMappingURL=bidi-Ce_Zx9CG.js.map

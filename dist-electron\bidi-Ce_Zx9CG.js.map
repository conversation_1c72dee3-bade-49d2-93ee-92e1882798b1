{"version": 3, "file": "bidi-Ce_Zx9CG.js", "sources": ["../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/Deserializer.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/util.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/JSHandle.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/ElementHandle.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/Serializer.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/Realm.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/BrowsingContext.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/Connection.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/BidiOverCdp.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/core/Navigation.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/core/Realm.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/core/Request.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/core/UserPrompt.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/core/BrowsingContext.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/core/UserContext.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/BrowserContext.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/core/Browser.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/core/Session.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/Dialog.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/EmulationManager.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/ExposedFunction.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/lifecycle.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/Sandbox.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/Frame.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/Input.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/HTTPRequest.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/HTTPResponse.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/NetworkManager.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/Page.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/Target.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/Browser.js"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nimport { debugError } from '../common/util.js';\n/**\n * @internal\n */\nexport class BidiDeserializer {\n    static deserializeNumber(value) {\n        switch (value) {\n            case '-0':\n                return -0;\n            case 'NaN':\n                return NaN;\n            case 'Infinity':\n                return Infinity;\n            case '-Infinity':\n                return -Infinity;\n            default:\n                return value;\n        }\n    }\n    static deserializeLocalValue(result) {\n        switch (result.type) {\n            case 'array':\n                return result.value?.map(value => {\n                    return BidiDeserializer.deserializeLocalValue(value);\n                });\n            case 'set':\n                return result.value?.reduce((acc, value) => {\n                    return acc.add(BidiDeserializer.deserializeLocalValue(value));\n                }, new Set());\n            case 'object':\n                return result.value?.reduce((acc, tuple) => {\n                    const { key, value } = BidiDeserializer.deserializeTuple(tuple);\n                    acc[key] = value;\n                    return acc;\n                }, {});\n            case 'map':\n                return result.value?.reduce((acc, tuple) => {\n                    const { key, value } = BidiDeserializer.deserializeTuple(tuple);\n                    return acc.set(key, value);\n                }, new Map());\n            case 'promise':\n                return {};\n            case 'regexp':\n                return new RegExp(result.value.pattern, result.value.flags);\n            case 'date':\n                return new Date(result.value);\n            case 'undefined':\n                return undefined;\n            case 'null':\n                return null;\n            case 'number':\n                return BidiDeserializer.deserializeNumber(result.value);\n            case 'bigint':\n                return BigInt(result.value);\n            case 'boolean':\n                return Boolean(result.value);\n            case 'string':\n                return result.value;\n        }\n        debugError(`Deserialization of type ${result.type} not supported.`);\n        return undefined;\n    }\n    static deserializeTuple([serializedKey, serializedValue]) {\n        const key = typeof serializedKey === 'string'\n            ? serializedKey\n            : BidiDeserializer.deserializeLocalValue(serializedKey);\n        const value = BidiDeserializer.deserializeLocalValue(serializedValue);\n        return { key, value };\n    }\n    static deserialize(result) {\n        if (!result) {\n            debugError('Service did not produce a result.');\n            return undefined;\n        }\n        return BidiDeserializer.deserializeLocalValue(result);\n    }\n}\n//# sourceMappingURL=Deserializer.js.map", "/**\n * @license\n * Copyright 2023 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nimport { PuppeteerURL, debugError } from '../common/util.js';\nimport { BidiDeserializer } from './Deserializer.js';\n/**\n * @internal\n */\nexport async function releaseReference(client, remoteReference) {\n    if (!remoteReference.handle) {\n        return;\n    }\n    await client.connection\n        .send('script.disown', {\n        target: client.target,\n        handles: [remoteReference.handle],\n    })\n        .catch(error => {\n        // Exceptions might happen in case of a page been navigated or closed.\n        // Swallow these since they are harmless and we don't leak anything in this case.\n        debugError(error);\n    });\n}\n/**\n * @internal\n */\nexport function createEvaluationError(details) {\n    if (details.exception.type !== 'error') {\n        return BidiDeserializer.deserialize(details.exception);\n    }\n    const [name = '', ...parts] = details.text.split(': ');\n    const message = parts.join(': ');\n    const error = new Error(message);\n    error.name = name;\n    // The first line is this function which we ignore.\n    const stackLines = [];\n    if (details.stackTrace && stackLines.length < Error.stackTraceLimit) {\n        for (const frame of details.stackTrace.callFrames.reverse()) {\n            if (PuppeteerURL.isPuppeteerURL(frame.url) &&\n                frame.url !== PuppeteerURL.INTERNAL_URL) {\n                const url = PuppeteerURL.parse(frame.url);\n                stackLines.unshift(`    at ${frame.functionName || url.functionName} (${url.functionName} at ${url.siteString}, <anonymous>:${frame.lineNumber}:${frame.columnNumber})`);\n            }\n            else {\n                stackLines.push(`    at ${frame.functionName || '<anonymous>'} (${frame.url}:${frame.lineNumber}:${frame.columnNumber})`);\n            }\n            if (stackLines.length >= Error.stackTraceLimit) {\n                break;\n            }\n        }\n    }\n    error.stack = [details.text, ...stackLines].join('\\n');\n    return error;\n}\n//# sourceMappingURL=util.js.map", "/**\n * @license\n * Copyright 2023 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nimport { JSHandle } from '../api/JSHandle.js';\nimport { UnsupportedOperation } from '../common/Errors.js';\nimport { BidiDeserializer } from './Deserializer.js';\nimport { releaseReference } from './util.js';\n/**\n * @internal\n */\nexport class BidiJSHandle extends JSHandle {\n    #disposed = false;\n    #sandbox;\n    #remoteValue;\n    constructor(sandbox, remoteValue) {\n        super();\n        this.#sandbox = sandbox;\n        this.#remoteValue = remoteValue;\n    }\n    context() {\n        return this.realm.environment.context();\n    }\n    get realm() {\n        return this.#sandbox;\n    }\n    get disposed() {\n        return this.#disposed;\n    }\n    async jsonValue() {\n        return await this.evaluate(value => {\n            return value;\n        });\n    }\n    asElement() {\n        return null;\n    }\n    async dispose() {\n        if (this.#disposed) {\n            return;\n        }\n        this.#disposed = true;\n        if ('handle' in this.#remoteValue) {\n            await releaseReference(this.context(), this.#remoteValue);\n        }\n    }\n    get isPrimitiveValue() {\n        switch (this.#remoteValue.type) {\n            case 'string':\n            case 'number':\n            case 'bigint':\n            case 'boolean':\n            case 'undefined':\n            case 'null':\n                return true;\n            default:\n                return false;\n        }\n    }\n    toString() {\n        if (this.isPrimitiveValue) {\n            return 'JSHandle:' + BidiDeserializer.deserialize(this.#remoteValue);\n        }\n        return 'JSHandle@' + this.#remoteValue.type;\n    }\n    get id() {\n        return 'handle' in this.#remoteValue ? this.#remoteValue.handle : undefined;\n    }\n    remoteValue() {\n        return this.#remoteValue;\n    }\n    remoteObject() {\n        throw new UnsupportedOperation('Not available in WebDriver BiDi');\n    }\n}\n//# sourceMappingURL=JSHandle.js.map", "/**\n * @license\n * Copyright 2023 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nvar __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {\n    var useValue = arguments.length > 2;\n    for (var i = 0; i < initializers.length; i++) {\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n    }\n    return useValue ? value : void 0;\n};\nvar __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n    var _, done = false;\n    for (var i = decorators.length - 1; i >= 0; i--) {\n        var context = {};\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n        if (kind === \"accessor\") {\n            if (result === void 0) continue;\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n            if (_ = accept(result.get)) descriptor.get = _;\n            if (_ = accept(result.set)) descriptor.set = _;\n            if (_ = accept(result.init)) initializers.unshift(_);\n        }\n        else if (_ = accept(result)) {\n            if (kind === \"field\") initializers.unshift(_);\n            else descriptor[key] = _;\n        }\n    }\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\n    done = true;\n};\nvar __addDisposableResource = (this && this.__addDisposableResource) || function (env, value, async) {\n    if (value !== null && value !== void 0) {\n        if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n        var dispose;\n        if (async) {\n            if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n            dispose = value[Symbol.asyncDispose];\n        }\n        if (dispose === void 0) {\n            if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n            dispose = value[Symbol.dispose];\n        }\n        if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n        env.stack.push({ value: value, dispose: dispose, async: async });\n    }\n    else if (async) {\n        env.stack.push({ async: true });\n    }\n    return value;\n};\nvar __disposeResources = (this && this.__disposeResources) || (function (SuppressedError) {\n    return function (env) {\n        function fail(e) {\n            env.error = env.hasError ? new SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n            env.hasError = true;\n        }\n        function next() {\n            while (env.stack.length) {\n                var rec = env.stack.pop();\n                try {\n                    var result = rec.dispose && rec.dispose.call(rec.value);\n                    if (rec.async) return Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n                }\n                catch (e) {\n                    fail(e);\n                }\n            }\n            if (env.hasError) throw env.error;\n        }\n        return next();\n    };\n})(typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n    var e = new Error(message);\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n});\nimport { ElementHandle } from '../api/ElementHandle.js';\nimport { UnsupportedOperation } from '../common/Errors.js';\nimport { throwIfDisposed } from '../util/decorators.js';\nimport { BidiJSHandle } from './JSHandle.js';\n/**\n * @internal\n */\nlet BidiElementHandle = (() => {\n    var _a;\n    let _classSuper = ElementHandle;\n    let _instanceExtraInitializers = [];\n    let _autofill_decorators;\n    let _contentFrame_decorators;\n    return class BidiElementHandle extends _classSuper {\n        static {\n            const _metadata = typeof Symbol === \"function\" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;\n            _autofill_decorators = [throwIfDisposed()];\n            _contentFrame_decorators = [throwIfDisposed(), (_a = ElementHandle).bindIsolatedHandle.bind(_a)];\n            __esDecorate(this, null, _autofill_decorators, { kind: \"method\", name: \"autofill\", static: false, private: false, access: { has: obj => \"autofill\" in obj, get: obj => obj.autofill }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _contentFrame_decorators, { kind: \"method\", name: \"contentFrame\", static: false, private: false, access: { has: obj => \"contentFrame\" in obj, get: obj => obj.contentFrame }, metadata: _metadata }, null, _instanceExtraInitializers);\n            if (_metadata) Object.defineProperty(this, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });\n        }\n        constructor(sandbox, remoteValue) {\n            super(new BidiJSHandle(sandbox, remoteValue));\n            __runInitializers(this, _instanceExtraInitializers);\n        }\n        get realm() {\n            return this.handle.realm;\n        }\n        get frame() {\n            return this.realm.environment;\n        }\n        context() {\n            return this.handle.context();\n        }\n        get isPrimitiveValue() {\n            return this.handle.isPrimitiveValue;\n        }\n        remoteValue() {\n            return this.handle.remoteValue();\n        }\n        async autofill(data) {\n            const client = this.frame.client;\n            const nodeInfo = await client.send('DOM.describeNode', {\n                objectId: this.handle.id,\n            });\n            const fieldId = nodeInfo.node.backendNodeId;\n            const frameId = this.frame._id;\n            await client.send('Autofill.trigger', {\n                fieldId,\n                frameId,\n                card: data.creditCard,\n            });\n        }\n        async contentFrame() {\n            const env_1 = { stack: [], error: void 0, hasError: false };\n            try {\n                const handle = __addDisposableResource(env_1, (await this.evaluateHandle(element => {\n                    if (element instanceof HTMLIFrameElement) {\n                        return element.contentWindow;\n                    }\n                    return;\n                })), false);\n                const value = handle.remoteValue();\n                if (value.type === 'window') {\n                    return this.frame.page().frame(value.value.context);\n                }\n                return null;\n            }\n            catch (e_1) {\n                env_1.error = e_1;\n                env_1.hasError = true;\n            }\n            finally {\n                __disposeResources(env_1);\n            }\n        }\n        uploadFile() {\n            throw new UnsupportedOperation();\n        }\n    };\n})();\nexport { BidiElementHandle };\n//# sourceMappingURL=ElementHandle.js.map", "/**\n * @license\n * Copyright 2023 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nimport { LazyArg } from '../common/LazyArg.js';\nimport { isDate, isPlainObject, isRegExp } from '../common/util.js';\nimport { BidiElementHandle } from './ElementHandle.js';\nimport { BidiJSHandle } from './JSHandle.js';\n/**\n * @internal\n */\nclass UnserializableError extends Error {\n}\n/**\n * @internal\n */\nexport class BidiSerializer {\n    static serializeNumber(arg) {\n        let value;\n        if (Object.is(arg, -0)) {\n            value = '-0';\n        }\n        else if (Object.is(arg, Infinity)) {\n            value = 'Infinity';\n        }\n        else if (Object.is(arg, -Infinity)) {\n            value = '-Infinity';\n        }\n        else if (Object.is(arg, NaN)) {\n            value = 'NaN';\n        }\n        else {\n            value = arg;\n        }\n        return {\n            type: 'number',\n            value,\n        };\n    }\n    static serializeObject(arg) {\n        if (arg === null) {\n            return {\n                type: 'null',\n            };\n        }\n        else if (Array.isArray(arg)) {\n            const parsedArray = arg.map(subArg => {\n                return BidiSerializer.serializeRemoteValue(subArg);\n            });\n            return {\n                type: 'array',\n                value: parsedArray,\n            };\n        }\n        else if (isPlainObject(arg)) {\n            try {\n                JSON.stringify(arg);\n            }\n            catch (error) {\n                if (error instanceof TypeError &&\n                    error.message.startsWith('Converting circular structure to JSON')) {\n                    error.message += ' Recursive objects are not allowed.';\n                }\n                throw error;\n            }\n            const parsedObject = [];\n            for (const key in arg) {\n                parsedObject.push([\n                    BidiSerializer.serializeRemoteValue(key),\n                    BidiSerializer.serializeRemoteValue(arg[key]),\n                ]);\n            }\n            return {\n                type: 'object',\n                value: parsedObject,\n            };\n        }\n        else if (isRegExp(arg)) {\n            return {\n                type: 'regexp',\n                value: {\n                    pattern: arg.source,\n                    flags: arg.flags,\n                },\n            };\n        }\n        else if (isDate(arg)) {\n            return {\n                type: 'date',\n                value: arg.toISOString(),\n            };\n        }\n        throw new UnserializableError('Custom object sterilization not possible. Use plain objects instead.');\n    }\n    static serializeRemoteValue(arg) {\n        switch (typeof arg) {\n            case 'symbol':\n            case 'function':\n                throw new UnserializableError(`Unable to serializable ${typeof arg}`);\n            case 'object':\n                return BidiSerializer.serializeObject(arg);\n            case 'undefined':\n                return {\n                    type: 'undefined',\n                };\n            case 'number':\n                return BidiSerializer.serializeNumber(arg);\n            case 'bigint':\n                return {\n                    type: 'bigint',\n                    value: arg.toString(),\n                };\n            case 'string':\n                return {\n                    type: 'string',\n                    value: arg,\n                };\n            case 'boolean':\n                return {\n                    type: 'boolean',\n                    value: arg,\n                };\n        }\n    }\n    static async serialize(sandbox, arg) {\n        if (arg instanceof LazyArg) {\n            arg = await arg.get(sandbox.realm);\n        }\n        // eslint-disable-next-line rulesdir/use-using -- We want this to continue living.\n        const objectHandle = arg && (arg instanceof BidiJSHandle || arg instanceof BidiElementHandle)\n            ? arg\n            : null;\n        if (objectHandle) {\n            if (objectHandle.realm.environment.context() !==\n                sandbox.environment.context()) {\n                throw new Error('JSHandles can be evaluated only in the context they were created!');\n            }\n            if (objectHandle.disposed) {\n                throw new Error('JSHandle is disposed!');\n            }\n            return objectHandle.remoteValue();\n        }\n        return BidiSerializer.serializeRemoteValue(arg);\n    }\n}\n//# sourceMappingURL=Serializer.js.map", "import * as Bidi from 'chromium-bidi/lib/cjs/protocol/protocol.js';\nimport { EventEmitter } from '../common/EventEmitter.js';\nimport { scriptInjector } from '../common/ScriptInjector.js';\nimport { PuppeteerURL, SOURCE_URL_REGEX, getSourcePuppeteerURLIfAvailable, getSourceUrlComment, isString, } from '../common/util.js';\nimport { disposeSymbol } from '../util/disposable.js';\nimport { stringifyFunction } from '../util/Function.js';\nimport { BidiDeserializer } from './Deserializer.js';\nimport { BidiElementHandle } from './ElementHandle.js';\nimport { BidiJSHandle } from './JSHandle.js';\nimport { BidiSerializer } from './Serializer.js';\nimport { createEvaluationError } from './util.js';\n/**\n * @internal\n */\nexport class BidiRealm extends EventEmitter {\n    connection;\n    #id;\n    #sandbox;\n    constructor(connection) {\n        super();\n        this.connection = connection;\n    }\n    get target() {\n        return {\n            context: this.#sandbox.environment._id,\n            sandbox: this.#sandbox.name,\n        };\n    }\n    handleRealmDestroyed = async (params) => {\n        if (params.realm === this.#id) {\n            // Note: The Realm is destroyed, so in theory the handle should be as\n            // well.\n            this.internalPuppeteerUtil = undefined;\n            this.#sandbox.environment.clearDocumentHandle();\n        }\n    };\n    handleRealmCreated = (params) => {\n        if (params.type === 'window' &&\n            params.context === this.#sandbox.environment._id &&\n            params.sandbox === this.#sandbox.name) {\n            this.#id = params.realm;\n            void this.#sandbox.taskManager.rerunAll();\n        }\n    };\n    setSandbox(sandbox) {\n        this.#sandbox = sandbox;\n        this.connection.on(Bidi.ChromiumBidi.Script.EventNames.RealmCreated, this.handleRealmCreated);\n        this.connection.on(Bidi.ChromiumBidi.Script.EventNames.RealmDestroyed, this.handleRealmDestroyed);\n    }\n    internalPuppeteerUtil;\n    get puppeteerUtil() {\n        const promise = Promise.resolve();\n        scriptInjector.inject(script => {\n            if (this.internalPuppeteerUtil) {\n                void this.internalPuppeteerUtil.then(handle => {\n                    void handle.dispose();\n                });\n            }\n            this.internalPuppeteerUtil = promise.then(() => {\n                return this.evaluateHandle(script);\n            });\n        }, !this.internalPuppeteerUtil);\n        return this.internalPuppeteerUtil;\n    }\n    async evaluateHandle(pageFunction, ...args) {\n        return await this.#evaluate(false, pageFunction, ...args);\n    }\n    async evaluate(pageFunction, ...args) {\n        return await this.#evaluate(true, pageFunction, ...args);\n    }\n    async #evaluate(returnByValue, pageFunction, ...args) {\n        const sourceUrlComment = getSourceUrlComment(getSourcePuppeteerURLIfAvailable(pageFunction)?.toString() ??\n            PuppeteerURL.INTERNAL_URL);\n        const sandbox = this.#sandbox;\n        let responsePromise;\n        const resultOwnership = returnByValue\n            ? \"none\" /* Bidi.Script.ResultOwnership.None */\n            : \"root\" /* Bidi.Script.ResultOwnership.Root */;\n        const serializationOptions = returnByValue\n            ? {}\n            : {\n                maxObjectDepth: 0,\n                maxDomDepth: 0,\n            };\n        if (isString(pageFunction)) {\n            const expression = SOURCE_URL_REGEX.test(pageFunction)\n                ? pageFunction\n                : `${pageFunction}\\n${sourceUrlComment}\\n`;\n            responsePromise = this.connection.send('script.evaluate', {\n                expression,\n                target: this.target,\n                resultOwnership,\n                awaitPromise: true,\n                userActivation: true,\n                serializationOptions,\n            });\n        }\n        else {\n            let functionDeclaration = stringifyFunction(pageFunction);\n            functionDeclaration = SOURCE_URL_REGEX.test(functionDeclaration)\n                ? functionDeclaration\n                : `${functionDeclaration}\\n${sourceUrlComment}\\n`;\n            responsePromise = this.connection.send('script.callFunction', {\n                functionDeclaration,\n                arguments: args.length\n                    ? await Promise.all(args.map(arg => {\n                        return BidiSerializer.serialize(sandbox, arg);\n                    }))\n                    : [],\n                target: this.target,\n                resultOwnership,\n                awaitPromise: true,\n                userActivation: true,\n                serializationOptions,\n            });\n        }\n        const { result } = await responsePromise;\n        if ('type' in result && result.type === 'exception') {\n            throw createEvaluationError(result.exceptionDetails);\n        }\n        return returnByValue\n            ? BidiDeserializer.deserialize(result.result)\n            : createBidiHandle(sandbox, result.result);\n    }\n    [disposeSymbol]() {\n        this.connection.off(Bidi.ChromiumBidi.Script.EventNames.RealmCreated, this.handleRealmCreated);\n        this.connection.off(Bidi.ChromiumBidi.Script.EventNames.RealmDestroyed, this.handleRealmDestroyed);\n    }\n}\n/**\n * @internal\n */\nexport function createBidiHandle(sandbox, result) {\n    if (result.type === 'node' || result.type === 'window') {\n        return new BidiElementHandle(sandbox, result);\n    }\n    return new BidiJSHandle(sandbox, result);\n}\n//# sourceMappingURL=Realm.js.map", "import { CDPSession } from '../api/CDPSession.js';\nimport { TargetCloseError, UnsupportedOperation } from '../common/Errors.js';\nimport { debugError } from '../common/util.js';\nimport { Deferred } from '../util/Deferred.js';\nimport { BidiRealm } from './Realm.js';\n/**\n * @internal\n */\nexport const cdpSessions = new Map();\n/**\n * @internal\n */\nexport class CdpSessionWrapper extends CDPSession {\n    #context;\n    #sessionId = Deferred.create();\n    #detached = false;\n    constructor(context, sessionId) {\n        super();\n        this.#context = context;\n        if (!this.#context.supportsCdp()) {\n            return;\n        }\n        if (sessionId) {\n            this.#sessionId.resolve(sessionId);\n            cdpSessions.set(sessionId, this);\n        }\n        else {\n            context.connection\n                .send('cdp.getSession', {\n                context: context.id,\n            })\n                .then(session => {\n                this.#sessionId.resolve(session.result.session);\n                cdpSessions.set(session.result.session, this);\n            })\n                .catch(err => {\n                this.#sessionId.reject(err);\n            });\n        }\n    }\n    connection() {\n        return undefined;\n    }\n    async send(method, ...paramArgs) {\n        if (!this.#context.supportsCdp()) {\n            throw new UnsupportedOperation('CDP support is required for this feature. The current browser does not support CDP.');\n        }\n        if (this.#detached) {\n            throw new TargetCloseError(`Protocol error (${method}): Session closed. Most likely the page has been closed.`);\n        }\n        const session = await this.#sessionId.valueOrThrow();\n        const { result } = await this.#context.connection.send('cdp.sendCommand', {\n            method: method,\n            params: paramArgs[0],\n            session,\n        });\n        return result.result;\n    }\n    async detach() {\n        cdpSessions.delete(this.id());\n        if (!this.#detached && this.#context.supportsCdp()) {\n            await this.#context.cdpSession.send('Target.detachFromTarget', {\n                sessionId: this.id(),\n            });\n        }\n        this.#detached = true;\n    }\n    id() {\n        const val = this.#sessionId.value();\n        return val instanceof Error || val === undefined ? '' : val;\n    }\n}\n/**\n * Internal events that the BrowsingContext class emits.\n *\n * @internal\n */\n// eslint-disable-next-line @typescript-eslint/no-namespace\nexport var BrowsingContextEvent;\n(function (BrowsingContextEvent) {\n    /**\n     * Emitted on the top-level context, when a descendant context is created.\n     */\n    BrowsingContextEvent.Created = Symbol('BrowsingContext.created');\n    /**\n     * Emitted on the top-level context, when a descendant context or the\n     * top-level context itself is destroyed.\n     */\n    BrowsingContextEvent.Destroyed = Symbol('BrowsingContext.destroyed');\n})(BrowsingContextEvent || (BrowsingContextEvent = {}));\n/**\n * @internal\n */\nexport class BrowsingContext extends BidiRealm {\n    #id;\n    #url;\n    #cdpSession;\n    #parent;\n    #browserName = '';\n    constructor(connection, info, browserName) {\n        super(connection);\n        this.#id = info.context;\n        this.#url = info.url;\n        this.#parent = info.parent;\n        this.#browserName = browserName;\n        this.#cdpSession = new CdpSessionWrapper(this, undefined);\n        this.on('browsingContext.domContentLoaded', this.#updateUrl.bind(this));\n        this.on('browsingContext.fragmentNavigated', this.#updateUrl.bind(this));\n        this.on('browsingContext.load', this.#updateUrl.bind(this));\n    }\n    supportsCdp() {\n        return !this.#browserName.toLowerCase().includes('firefox');\n    }\n    #updateUrl(info) {\n        this.#url = info.url;\n    }\n    createRealmForSandbox() {\n        return new BidiRealm(this.connection);\n    }\n    get url() {\n        return this.#url;\n    }\n    get id() {\n        return this.#id;\n    }\n    get parent() {\n        return this.#parent;\n    }\n    get cdpSession() {\n        return this.#cdpSession;\n    }\n    async sendCdpCommand(method, ...paramArgs) {\n        return await this.#cdpSession.send(method, ...paramArgs);\n    }\n    dispose() {\n        this.removeAllListeners();\n        this.connection.unregisterBrowsingContexts(this.#id);\n        void this.#cdpSession.detach().catch(debugError);\n    }\n}\n//# sourceMappingURL=BrowsingContext.js.map", "/**\n * @license\n * Copyright 2017 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nimport { CallbackRegistry } from '../common/CallbackRegistry.js';\nimport { debug } from '../common/Debug.js';\nimport { EventEmitter } from '../common/EventEmitter.js';\nimport { debugError } from '../common/util.js';\nimport { assert } from '../util/assert.js';\nimport { cdpSessions } from './BrowsingContext.js';\nconst debugProtocolSend = debug('puppeteer:webDriverBiDi:SEND ►');\nconst debugProtocolReceive = debug('puppeteer:webDriverBiDi:RECV ◀');\n/**\n * @internal\n */\nexport class BidiConnection extends EventEmitter {\n    #url;\n    #transport;\n    #delay;\n    #timeout = 0;\n    #closed = false;\n    #callbacks = new CallbackRegistry();\n    #browsingContexts = new Map();\n    #emitters = [];\n    constructor(url, transport, delay = 0, timeout) {\n        super();\n        this.#url = url;\n        this.#delay = delay;\n        this.#timeout = timeout ?? 180000;\n        this.#transport = transport;\n        this.#transport.onmessage = this.onMessage.bind(this);\n        this.#transport.onclose = this.unbind.bind(this);\n    }\n    get closed() {\n        return this.#closed;\n    }\n    get url() {\n        return this.#url;\n    }\n    pipeTo(emitter) {\n        this.#emitters.push(emitter);\n    }\n    emit(type, event) {\n        for (const emitter of this.#emitters) {\n            emitter.emit(type, event);\n        }\n        return super.emit(type, event);\n    }\n    send(method, params) {\n        assert(!this.#closed, 'Protocol error: Connection closed.');\n        return this.#callbacks.create(method, this.#timeout, id => {\n            const stringifiedMessage = JSON.stringify({\n                id,\n                method,\n                params,\n            });\n            debugProtocolSend(stringifiedMessage);\n            this.#transport.send(stringifiedMessage);\n        });\n    }\n    /**\n     * @internal\n     */\n    async onMessage(message) {\n        if (this.#delay) {\n            await new Promise(f => {\n                return setTimeout(f, this.#delay);\n            });\n        }\n        debugProtocolReceive(message);\n        const object = JSON.parse(message);\n        if ('type' in object) {\n            switch (object.type) {\n                case 'success':\n                    this.#callbacks.resolve(object.id, object);\n                    return;\n                case 'error':\n                    if (object.id === null) {\n                        break;\n                    }\n                    this.#callbacks.reject(object.id, createProtocolError(object), object.message);\n                    return;\n                case 'event':\n                    if (isCdpEvent(object)) {\n                        cdpSessions\n                            .get(object.params.session)\n                            ?.emit(object.params.event, object.params.params);\n                        return;\n                    }\n                    this.#maybeEmitOnContext(object);\n                    // SAFETY: We know the method and parameter still match here.\n                    this.emit(object.method, object.params);\n                    return;\n            }\n        }\n        // Even if the response in not in BiDi protocol format but `id` is provided, reject\n        // the callback. This can happen if the endpoint supports CDP instead of BiDi.\n        if ('id' in object) {\n            this.#callbacks.reject(object.id, `Protocol Error. Message is not in BiDi protocol format: '${message}'`, object.message);\n        }\n        debugError(object);\n    }\n    #maybeEmitOnContext(event) {\n        let context;\n        // Context specific events\n        if ('context' in event.params && event.params.context !== null) {\n            context = this.#browsingContexts.get(event.params.context);\n            // `log.entryAdded` specific context\n        }\n        else if ('source' in event.params &&\n            event.params.source.context !== undefined) {\n            context = this.#browsingContexts.get(event.params.source.context);\n        }\n        context?.emit(event.method, event.params);\n    }\n    registerBrowsingContexts(context) {\n        this.#browsingContexts.set(context.id, context);\n    }\n    getBrowsingContext(contextId) {\n        const currentContext = this.#browsingContexts.get(contextId);\n        if (!currentContext) {\n            throw new Error(`BrowsingContext ${contextId} does not exist.`);\n        }\n        return currentContext;\n    }\n    getTopLevelContext(contextId) {\n        let currentContext = this.#browsingContexts.get(contextId);\n        if (!currentContext) {\n            throw new Error(`BrowsingContext ${contextId} does not exist.`);\n        }\n        while (currentContext.parent) {\n            contextId = currentContext.parent;\n            currentContext = this.#browsingContexts.get(contextId);\n            if (!currentContext) {\n                throw new Error(`BrowsingContext ${contextId} does not exist.`);\n            }\n        }\n        return currentContext;\n    }\n    unregisterBrowsingContexts(id) {\n        this.#browsingContexts.delete(id);\n    }\n    /**\n     * Unbinds the connection, but keeps the transport open. Useful when the transport will\n     * be reused by other connection e.g. with different protocol.\n     * @internal\n     */\n    unbind() {\n        if (this.#closed) {\n            return;\n        }\n        this.#closed = true;\n        // Both may still be invoked and produce errors\n        this.#transport.onmessage = () => { };\n        this.#transport.onclose = () => { };\n        this.#browsingContexts.clear();\n        this.#callbacks.clear();\n    }\n    /**\n     * Unbinds the connection and closes the transport.\n     */\n    dispose() {\n        this.unbind();\n        this.#transport.close();\n    }\n    getPendingProtocolErrors() {\n        return this.#callbacks.getPendingProtocolErrors();\n    }\n}\n/**\n * @internal\n */\nfunction createProtocolError(object) {\n    let message = `${object.error} ${object.message}`;\n    if (object.stacktrace) {\n        message += ` ${object.stacktrace}`;\n    }\n    return message;\n}\nfunction isCdpEvent(event) {\n    return event.method.startsWith('cdp.');\n}\n//# sourceMappingURL=Connection.js.map", "/**\n * @license\n * Copyright 2023 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nimport * as BidiMapper from 'chromium-bidi/lib/cjs/bidiMapper/BidiMapper.js';\nimport { debug } from '../common/Debug.js';\nimport { TargetCloseError } from '../common/Errors.js';\nimport { BidiConnection } from './Connection.js';\nconst bidiServerLogger = (prefix, ...args) => {\n    debug(`bidi:${prefix}`)(args);\n};\n/**\n * @internal\n */\nexport async function connectBidiOverCdp(cdp, \n// TODO: replace with `BidiMapper.MapperOptions`, once it's exported in\n//  https://github.com/puppeteer/puppeteer/pull/11415.\noptions) {\n    const transportBiDi = new NoOpTransport();\n    const cdpConnectionAdapter = new CdpConnectionAdapter(cdp);\n    const pptrTransport = {\n        send(message) {\n            // Forwards a BiDi command sent by <PERSON><PERSON><PERSON><PERSON> to the input of the BidiServer.\n            transportBiDi.emitMessage(JSON.parse(message));\n        },\n        close() {\n            bidiServer.close();\n            cdpConnectionAdapter.close();\n            cdp.dispose();\n        },\n        onmessage(_message) {\n            // The method is overridden by the Connection.\n        },\n    };\n    transportBiDi.on('bidiResponse', (message) => {\n        // Forwards a BiDi event sent by BidiServer to Puppeteer.\n        pptrTransport.onmessage(JSON.stringify(message));\n    });\n    const pptrBiDiConnection = new BidiConnection(cdp.url(), pptrTransport);\n    const bidiServer = await BidiMapper.BidiServer.createAndStart(transportBiDi, cdpConnectionAdapter, \n    // TODO: most likely need a little bit of refactoring\n    cdpConnectionAdapter.browserClient(), '', options, undefined, bidiServerLogger);\n    return pptrBiDiConnection;\n}\n/**\n * Manages CDPSessions for BidiServer.\n * @internal\n */\nclass CdpConnectionAdapter {\n    #cdp;\n    #adapters = new Map();\n    #browserCdpConnection;\n    constructor(cdp) {\n        this.#cdp = cdp;\n        this.#browserCdpConnection = new CDPClientAdapter(cdp);\n    }\n    browserClient() {\n        return this.#browserCdpConnection;\n    }\n    getCdpClient(id) {\n        const session = this.#cdp.session(id);\n        if (!session) {\n            throw new Error(`Unknown CDP session with id ${id}`);\n        }\n        if (!this.#adapters.has(session)) {\n            const adapter = new CDPClientAdapter(session, id, this.#browserCdpConnection);\n            this.#adapters.set(session, adapter);\n            return adapter;\n        }\n        return this.#adapters.get(session);\n    }\n    close() {\n        this.#browserCdpConnection.close();\n        for (const adapter of this.#adapters.values()) {\n            adapter.close();\n        }\n    }\n}\n/**\n * Wrapper on top of CDPSession/CDPConnection to satisfy CDP interface that\n * BidiServer needs.\n *\n * @internal\n */\nclass CDPClientAdapter extends BidiMapper.EventEmitter {\n    #closed = false;\n    #client;\n    sessionId = undefined;\n    #browserClient;\n    constructor(client, sessionId, browserClient) {\n        super();\n        this.#client = client;\n        this.sessionId = sessionId;\n        this.#browserClient = browserClient;\n        this.#client.on('*', this.#forwardMessage);\n    }\n    browserClient() {\n        return this.#browserClient;\n    }\n    #forwardMessage = (method, event) => {\n        this.emit(method, event);\n    };\n    async sendCommand(method, ...params) {\n        if (this.#closed) {\n            return;\n        }\n        try {\n            return await this.#client.send(method, ...params);\n        }\n        catch (err) {\n            if (this.#closed) {\n                return;\n            }\n            throw err;\n        }\n    }\n    close() {\n        this.#client.off('*', this.#forwardMessage);\n        this.#closed = true;\n    }\n    isCloseError(error) {\n        return error instanceof TargetCloseError;\n    }\n}\n/**\n * This transport is given to the BiDi server instance and allows Puppeteer\n * to send and receive commands to the BiDiServer.\n * @internal\n */\nclass NoOpTransport extends BidiMapper.EventEmitter {\n    #onMessage = async (_m) => {\n        return;\n    };\n    emitMessage(message) {\n        void this.#onMessage(message);\n    }\n    setOnMessage(onMessage) {\n        this.#onMessage = onMessage;\n    }\n    async sendMessage(message) {\n        this.emit('bidiResponse', message);\n    }\n    close() {\n        this.#onMessage = async (_m) => {\n            return;\n        };\n    }\n}\n//# sourceMappingURL=BidiOverCdp.js.map", "/**\n * @license\n * Copyright 2024 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nvar __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {\n    var useValue = arguments.length > 2;\n    for (var i = 0; i < initializers.length; i++) {\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n    }\n    return useValue ? value : void 0;\n};\nvar __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n    var _, done = false;\n    for (var i = decorators.length - 1; i >= 0; i--) {\n        var context = {};\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n        if (kind === \"accessor\") {\n            if (result === void 0) continue;\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n            if (_ = accept(result.get)) descriptor.get = _;\n            if (_ = accept(result.set)) descriptor.set = _;\n            if (_ = accept(result.init)) initializers.unshift(_);\n        }\n        else if (_ = accept(result)) {\n            if (kind === \"field\") initializers.unshift(_);\n            else descriptor[key] = _;\n        }\n    }\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\n    done = true;\n};\nimport { EventEmitter } from '../../common/EventEmitter.js';\nimport { inertIfDisposed } from '../../util/decorators.js';\nimport { Deferred } from '../../util/Deferred.js';\nimport { DisposableStack, disposeSymbol } from '../../util/disposable.js';\n/**\n * @internal\n */\nlet Navigation = (() => {\n    let _classSuper = EventEmitter;\n    let _instanceExtraInitializers = [];\n    let _dispose_decorators;\n    return class Navigation extends _classSuper {\n        static {\n            const _metadata = typeof Symbol === \"function\" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;\n            __esDecorate(this, null, _dispose_decorators, { kind: \"method\", name: \"dispose\", static: false, private: false, access: { has: obj => \"dispose\" in obj, get: obj => obj.dispose }, metadata: _metadata }, null, _instanceExtraInitializers);\n            if (_metadata) Object.defineProperty(this, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });\n        }\n        static from(context) {\n            const navigation = new Navigation(context);\n            navigation.#initialize();\n            return navigation;\n        }\n        // keep-sorted start\n        #request = (__runInitializers(this, _instanceExtraInitializers), void 0);\n        #browsingContext;\n        #disposables = new DisposableStack();\n        #id = new Deferred();\n        // keep-sorted end\n        constructor(context) {\n            super();\n            // keep-sorted start\n            this.#browsingContext = context;\n            // keep-sorted end\n        }\n        #initialize() {\n            const browsingContextEmitter = this.#disposables.use(new EventEmitter(this.#browsingContext));\n            browsingContextEmitter.once('closed', () => {\n                this.emit('failed', {\n                    url: this.#browsingContext.url,\n                    timestamp: new Date(),\n                });\n                this.dispose();\n            });\n            this.#browsingContext.on('request', ({ request }) => {\n                if (request.navigation === this.#id.value()) {\n                    this.#request = request;\n                    this.emit('request', request);\n                }\n            });\n            const sessionEmitter = this.#disposables.use(new EventEmitter(this.#session));\n            // To get the navigation ID if any.\n            for (const eventName of [\n                'browsingContext.domContentLoaded',\n                'browsingContext.load',\n            ]) {\n                sessionEmitter.on(eventName, info => {\n                    if (info.context !== this.#browsingContext.id) {\n                        return;\n                    }\n                    if (!info.navigation) {\n                        return;\n                    }\n                    if (!this.#id.resolved()) {\n                        this.#id.resolve(info.navigation);\n                    }\n                });\n            }\n            for (const [eventName, event] of [\n                ['browsingContext.fragmentNavigated', 'fragment'],\n                ['browsingContext.navigationFailed', 'failed'],\n                ['browsingContext.navigationAborted', 'aborted'],\n            ]) {\n                sessionEmitter.on(eventName, info => {\n                    if (info.context !== this.#browsingContext.id) {\n                        return;\n                    }\n                    if (!info.navigation) {\n                        return;\n                    }\n                    if (!this.#id.resolved()) {\n                        this.#id.resolve(info.navigation);\n                    }\n                    if (this.#id.value() !== info.navigation) {\n                        return;\n                    }\n                    this.emit(event, {\n                        url: info.url,\n                        timestamp: new Date(info.timestamp),\n                    });\n                    this.dispose();\n                });\n            }\n        }\n        // keep-sorted start block=yes\n        get #session() {\n            return this.#browsingContext.userContext.browser.session;\n        }\n        get disposed() {\n            return this.#disposables.disposed;\n        }\n        get request() {\n            return this.#request;\n        }\n        // keep-sorted end\n        dispose() {\n            this[disposeSymbol]();\n        }\n        [(_dispose_decorators = [inertIfDisposed], disposeSymbol)]() {\n            this.#disposables.dispose();\n            super[disposeSymbol]();\n        }\n    };\n})();\nexport { Navigation };\n//# sourceMappingURL=Navigation.js.map", "/**\n * @license\n * Copyright 2024 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nvar __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {\n    var useValue = arguments.length > 2;\n    for (var i = 0; i < initializers.length; i++) {\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n    }\n    return useValue ? value : void 0;\n};\nvar __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n    var _, done = false;\n    for (var i = decorators.length - 1; i >= 0; i--) {\n        var context = {};\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n        if (kind === \"accessor\") {\n            if (result === void 0) continue;\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n            if (_ = accept(result.get)) descriptor.get = _;\n            if (_ = accept(result.set)) descriptor.set = _;\n            if (_ = accept(result.init)) initializers.unshift(_);\n        }\n        else if (_ = accept(result)) {\n            if (kind === \"field\") initializers.unshift(_);\n            else descriptor[key] = _;\n        }\n    }\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\n    done = true;\n};\nimport { EventEmitter } from '../../common/EventEmitter.js';\nimport { inertIfDisposed, throwIfDisposed } from '../../util/decorators.js';\nimport { DisposableStack, disposeSymbol } from '../../util/disposable.js';\n/**\n * @internal\n */\nlet Realm = (() => {\n    let _classSuper = EventEmitter;\n    let _instanceExtraInitializers = [];\n    let _dispose_decorators;\n    let _disown_decorators;\n    let _callFunction_decorators;\n    let _evaluate_decorators;\n    return class Realm extends _classSuper {\n        static {\n            const _metadata = typeof Symbol === \"function\" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;\n            __esDecorate(this, null, _dispose_decorators, { kind: \"method\", name: \"dispose\", static: false, private: false, access: { has: obj => \"dispose\" in obj, get: obj => obj.dispose }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _disown_decorators, { kind: \"method\", name: \"disown\", static: false, private: false, access: { has: obj => \"disown\" in obj, get: obj => obj.disown }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _callFunction_decorators, { kind: \"method\", name: \"callFunction\", static: false, private: false, access: { has: obj => \"callFunction\" in obj, get: obj => obj.callFunction }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _evaluate_decorators, { kind: \"method\", name: \"evaluate\", static: false, private: false, access: { has: obj => \"evaluate\" in obj, get: obj => obj.evaluate }, metadata: _metadata }, null, _instanceExtraInitializers);\n            if (_metadata) Object.defineProperty(this, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });\n        }\n        // keep-sorted start\n        #reason = (__runInitializers(this, _instanceExtraInitializers), void 0);\n        disposables = new DisposableStack();\n        id;\n        origin;\n        // keep-sorted end\n        constructor(id, origin) {\n            super();\n            // keep-sorted start\n            this.id = id;\n            this.origin = origin;\n            // keep-sorted end\n        }\n        initialize() {\n            const sessionEmitter = this.disposables.use(new EventEmitter(this.session));\n            sessionEmitter.on('script.realmDestroyed', info => {\n                if (info.realm !== this.id) {\n                    return;\n                }\n                this.dispose('Realm already destroyed.');\n            });\n        }\n        // keep-sorted start block=yes\n        get disposed() {\n            return this.#reason !== undefined;\n        }\n        get target() {\n            return { realm: this.id };\n        }\n        // keep-sorted end\n        dispose(reason) {\n            this.#reason = reason;\n            this[disposeSymbol]();\n        }\n        async disown(handles) {\n            await this.session.send('script.disown', {\n                target: this.target,\n                handles,\n            });\n        }\n        async callFunction(functionDeclaration, awaitPromise, options = {}) {\n            const { result } = await this.session.send('script.callFunction', {\n                functionDeclaration,\n                awaitPromise,\n                target: this.target,\n                ...options,\n            });\n            return result;\n        }\n        async evaluate(expression, awaitPromise, options = {}) {\n            const { result } = await this.session.send('script.evaluate', {\n                expression,\n                awaitPromise,\n                target: this.target,\n                ...options,\n            });\n            return result;\n        }\n        [(_dispose_decorators = [inertIfDisposed], _disown_decorators = [throwIfDisposed(realm => {\n                // SAFETY: Disposal implies this exists.\n                return realm.#reason;\n            })], _callFunction_decorators = [throwIfDisposed(realm => {\n                // SAFETY: Disposal implies this exists.\n                return realm.#reason;\n            })], _evaluate_decorators = [throwIfDisposed(realm => {\n                // SAFETY: Disposal implies this exists.\n                return realm.#reason;\n            })], disposeSymbol)]() {\n            this.#reason ??=\n                'Realm already destroyed, probably because all associated browsing contexts closed.';\n            this.emit('destroyed', { reason: this.#reason });\n            this.disposables.dispose();\n            super[disposeSymbol]();\n        }\n    };\n})();\nexport { Realm };\n/**\n * @internal\n */\nexport class WindowRealm extends Realm {\n    static from(context, sandbox) {\n        const realm = new WindowRealm(context, sandbox);\n        realm.initialize();\n        return realm;\n    }\n    // keep-sorted start\n    browsingContext;\n    sandbox;\n    // keep-sorted end\n    #workers = {\n        dedicated: new Map(),\n        shared: new Map(),\n    };\n    constructor(context, sandbox) {\n        super('', '');\n        // keep-sorted start\n        this.browsingContext = context;\n        this.sandbox = sandbox;\n        // keep-sorted end\n    }\n    initialize() {\n        super.initialize();\n        const sessionEmitter = this.disposables.use(new EventEmitter(this.session));\n        sessionEmitter.on('script.realmCreated', info => {\n            if (info.type !== 'window') {\n                return;\n            }\n            this.id = info.realm;\n            this.origin = info.origin;\n        });\n        sessionEmitter.on('script.realmCreated', info => {\n            if (info.type !== 'dedicated-worker') {\n                return;\n            }\n            if (!info.owners.includes(this.id)) {\n                return;\n            }\n            const realm = DedicatedWorkerRealm.from(this, info.realm, info.origin);\n            this.#workers.dedicated.set(realm.id, realm);\n            const realmEmitter = this.disposables.use(new EventEmitter(realm));\n            realmEmitter.once('destroyed', () => {\n                realmEmitter.removeAllListeners();\n                this.#workers.dedicated.delete(realm.id);\n            });\n            this.emit('worker', realm);\n        });\n        this.browsingContext.userContext.browser.on('sharedworker', ({ realm }) => {\n            if (!realm.owners.has(this)) {\n                return;\n            }\n            this.#workers.shared.set(realm.id, realm);\n            const realmEmitter = this.disposables.use(new EventEmitter(realm));\n            realmEmitter.once('destroyed', () => {\n                realmEmitter.removeAllListeners();\n                this.#workers.shared.delete(realm.id);\n            });\n            this.emit('sharedworker', realm);\n        });\n    }\n    get session() {\n        return this.browsingContext.userContext.browser.session;\n    }\n    get target() {\n        return { context: this.browsingContext.id, sandbox: this.sandbox };\n    }\n}\n/**\n * @internal\n */\nexport class DedicatedWorkerRealm extends Realm {\n    static from(owner, id, origin) {\n        const realm = new DedicatedWorkerRealm(owner, id, origin);\n        realm.initialize();\n        return realm;\n    }\n    // keep-sorted start\n    #workers = new Map();\n    owners;\n    // keep-sorted end\n    constructor(owner, id, origin) {\n        super(id, origin);\n        this.owners = new Set([owner]);\n    }\n    initialize() {\n        super.initialize();\n        const sessionEmitter = this.disposables.use(new EventEmitter(this.session));\n        sessionEmitter.on('script.realmCreated', info => {\n            if (info.type !== 'dedicated-worker') {\n                return;\n            }\n            if (!info.owners.includes(this.id)) {\n                return;\n            }\n            const realm = DedicatedWorkerRealm.from(this, info.realm, info.origin);\n            this.#workers.set(realm.id, realm);\n            const realmEmitter = this.disposables.use(new EventEmitter(realm));\n            realmEmitter.once('destroyed', () => {\n                this.#workers.delete(realm.id);\n            });\n            this.emit('worker', realm);\n        });\n    }\n    get session() {\n        // SAFETY: At least one owner will exist.\n        return this.owners.values().next().value.session;\n    }\n}\n/**\n * @internal\n */\nexport class SharedWorkerRealm extends Realm {\n    static from(owners, id, origin) {\n        const realm = new SharedWorkerRealm(owners, id, origin);\n        realm.initialize();\n        return realm;\n    }\n    // keep-sorted start\n    #workers = new Map();\n    owners;\n    // keep-sorted end\n    constructor(owners, id, origin) {\n        super(id, origin);\n        this.owners = new Set(owners);\n    }\n    initialize() {\n        super.initialize();\n        const sessionEmitter = this.disposables.use(new EventEmitter(this.session));\n        sessionEmitter.on('script.realmCreated', info => {\n            if (info.type !== 'dedicated-worker') {\n                return;\n            }\n            if (!info.owners.includes(this.id)) {\n                return;\n            }\n            const realm = DedicatedWorkerRealm.from(this, info.realm, info.origin);\n            this.#workers.set(realm.id, realm);\n            const realmEmitter = this.disposables.use(new EventEmitter(realm));\n            realmEmitter.once('destroyed', () => {\n                this.#workers.delete(realm.id);\n            });\n            this.emit('worker', realm);\n        });\n    }\n    get session() {\n        // SAFETY: At least one owner will exist.\n        return this.owners.values().next().value.session;\n    }\n}\n//# sourceMappingURL=Realm.js.map", "/**\n * @license\n * Copyright 2024 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nvar __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {\n    var useValue = arguments.length > 2;\n    for (var i = 0; i < initializers.length; i++) {\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n    }\n    return useValue ? value : void 0;\n};\nvar __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n    var _, done = false;\n    for (var i = decorators.length - 1; i >= 0; i--) {\n        var context = {};\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n        if (kind === \"accessor\") {\n            if (result === void 0) continue;\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n            if (_ = accept(result.get)) descriptor.get = _;\n            if (_ = accept(result.set)) descriptor.set = _;\n            if (_ = accept(result.init)) initializers.unshift(_);\n        }\n        else if (_ = accept(result)) {\n            if (kind === \"field\") initializers.unshift(_);\n            else descriptor[key] = _;\n        }\n    }\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\n    done = true;\n};\nimport { EventEmitter } from '../../common/EventEmitter.js';\nimport { inertIfDisposed } from '../../util/decorators.js';\nimport { DisposableStack, disposeSymbol } from '../../util/disposable.js';\n/**\n * @internal\n */\nlet Request = (() => {\n    var _a;\n    let _classSuper = EventEmitter;\n    let _instanceExtraInitializers = [];\n    let _dispose_decorators;\n    return class Request extends _classSuper {\n        static {\n            const _metadata = typeof Symbol === \"function\" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;\n            __esDecorate(this, null, _dispose_decorators, { kind: \"method\", name: \"dispose\", static: false, private: false, access: { has: obj => \"dispose\" in obj, get: obj => obj.dispose }, metadata: _metadata }, null, _instanceExtraInitializers);\n            if (_metadata) Object.defineProperty(this, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });\n        }\n        static from(browsingContext, event) {\n            const request = new Request(browsingContext, event);\n            request.#initialize();\n            return request;\n        }\n        // keep-sorted start\n        #error = (__runInitializers(this, _instanceExtraInitializers), void 0);\n        #redirect;\n        #response;\n        #browsingContext;\n        #disposables = new DisposableStack();\n        #event;\n        // keep-sorted end\n        constructor(browsingContext, event) {\n            super();\n            // keep-sorted start\n            this.#browsingContext = browsingContext;\n            this.#event = event;\n            // keep-sorted end\n        }\n        #initialize() {\n            const browsingContextEmitter = this.#disposables.use(new EventEmitter(this.#browsingContext));\n            browsingContextEmitter.once('closed', ({ reason }) => {\n                this.#error = reason;\n                this.emit('error', this.#error);\n                this.dispose();\n            });\n            const sessionEmitter = this.#disposables.use(new EventEmitter(this.#session));\n            sessionEmitter.on('network.beforeRequestSent', event => {\n                if (event.context !== this.#browsingContext.id) {\n                    return;\n                }\n                if (event.request.request !== this.id) {\n                    return;\n                }\n                this.#redirect = Request.from(this.#browsingContext, event);\n                this.emit('redirect', this.#redirect);\n                this.dispose();\n            });\n            sessionEmitter.on('network.fetchError', event => {\n                if (event.context !== this.#browsingContext.id) {\n                    return;\n                }\n                if (event.request.request !== this.id) {\n                    return;\n                }\n                this.#error = event.errorText;\n                this.emit('error', this.#error);\n                this.dispose();\n            });\n            sessionEmitter.on('network.responseCompleted', event => {\n                if (event.context !== this.#browsingContext.id) {\n                    return;\n                }\n                if (event.request.request !== this.id) {\n                    return;\n                }\n                this.#response = event.response;\n                this.emit('success', this.#response);\n                this.dispose();\n            });\n        }\n        // keep-sorted start block=yes\n        get #session() {\n            return this.#browsingContext.userContext.browser.session;\n        }\n        get disposed() {\n            return this.#disposables.disposed;\n        }\n        get error() {\n            return this.#error;\n        }\n        get headers() {\n            return this.#event.request.headers;\n        }\n        get id() {\n            return this.#event.request.request;\n        }\n        get initiator() {\n            return this.#event.initiator;\n        }\n        get method() {\n            return this.#event.request.method;\n        }\n        get navigation() {\n            return this.#event.navigation ?? undefined;\n        }\n        get redirect() {\n            return this.redirect;\n        }\n        get response() {\n            return this.#response;\n        }\n        get url() {\n            return this.#event.request.url;\n        }\n        // keep-sorted end\n        dispose() {\n            this[disposeSymbol]();\n        }\n        [(_dispose_decorators = [inertIfDisposed], disposeSymbol)]() {\n            this.#disposables.dispose();\n            super[disposeSymbol]();\n        }\n    };\n})();\nexport { Request };\n//# sourceMappingURL=Request.js.map", "/**\n * @license\n * Copyright 2024 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nvar __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {\n    var useValue = arguments.length > 2;\n    for (var i = 0; i < initializers.length; i++) {\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n    }\n    return useValue ? value : void 0;\n};\nvar __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n    var _, done = false;\n    for (var i = decorators.length - 1; i >= 0; i--) {\n        var context = {};\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n        if (kind === \"accessor\") {\n            if (result === void 0) continue;\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n            if (_ = accept(result.get)) descriptor.get = _;\n            if (_ = accept(result.set)) descriptor.set = _;\n            if (_ = accept(result.init)) initializers.unshift(_);\n        }\n        else if (_ = accept(result)) {\n            if (kind === \"field\") initializers.unshift(_);\n            else descriptor[key] = _;\n        }\n    }\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\n    done = true;\n};\nimport { EventEmitter } from '../../common/EventEmitter.js';\nimport { inertIfDisposed, throwIfDisposed } from '../../util/decorators.js';\nimport { DisposableStack, disposeSymbol } from '../../util/disposable.js';\n/**\n * @internal\n */\nlet UserPrompt = (() => {\n    let _classSuper = EventEmitter;\n    let _instanceExtraInitializers = [];\n    let _dispose_decorators;\n    let _handle_decorators;\n    return class UserPrompt extends _classSuper {\n        static {\n            const _metadata = typeof Symbol === \"function\" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;\n            __esDecorate(this, null, _dispose_decorators, { kind: \"method\", name: \"dispose\", static: false, private: false, access: { has: obj => \"dispose\" in obj, get: obj => obj.dispose }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _handle_decorators, { kind: \"method\", name: \"handle\", static: false, private: false, access: { has: obj => \"handle\" in obj, get: obj => obj.handle }, metadata: _metadata }, null, _instanceExtraInitializers);\n            if (_metadata) Object.defineProperty(this, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });\n        }\n        static from(browsingContext, info) {\n            const userPrompt = new UserPrompt(browsingContext, info);\n            userPrompt.#initialize();\n            return userPrompt;\n        }\n        // keep-sorted start\n        #reason = (__runInitializers(this, _instanceExtraInitializers), void 0);\n        #result;\n        #disposables = new DisposableStack();\n        browsingContext;\n        info;\n        // keep-sorted end\n        constructor(context, info) {\n            super();\n            // keep-sorted start\n            this.browsingContext = context;\n            this.info = info;\n            // keep-sorted end\n        }\n        #initialize() {\n            const browserContextEmitter = this.#disposables.use(new EventEmitter(this.browsingContext));\n            browserContextEmitter.once('closed', ({ reason }) => {\n                this.dispose(`User prompt already closed: ${reason}`);\n            });\n            const sessionEmitter = this.#disposables.use(new EventEmitter(this.#session));\n            sessionEmitter.on('browsingContext.userPromptClosed', parameters => {\n                if (parameters.context !== this.browsingContext.id) {\n                    return;\n                }\n                this.#result = parameters;\n                this.emit('handled', parameters);\n                this.dispose('User prompt already handled.');\n            });\n        }\n        // keep-sorted start block=yes\n        get #session() {\n            return this.browsingContext.userContext.browser.session;\n        }\n        get closed() {\n            return this.#reason !== undefined;\n        }\n        get disposed() {\n            return this.closed;\n        }\n        get handled() {\n            return this.#result !== undefined;\n        }\n        get result() {\n            return this.#result;\n        }\n        // keep-sorted end\n        dispose(reason) {\n            this.#reason = reason;\n            this[disposeSymbol]();\n        }\n        async handle(options = {}) {\n            await this.#session.send('browsingContext.handleUserPrompt', {\n                ...options,\n                context: this.info.context,\n            });\n            // SAFETY: `handled` is triggered before the above promise resolved.\n            return this.#result;\n        }\n        [(_dispose_decorators = [inertIfDisposed], _handle_decorators = [throwIfDisposed(prompt => {\n                // SAFETY: Disposal implies this exists.\n                return prompt.#reason;\n            })], disposeSymbol)]() {\n            this.#reason ??=\n                'User prompt already closed, probably because the associated browsing context was destroyed.';\n            this.emit('closed', { reason: this.#reason });\n            this.#disposables.dispose();\n            super[disposeSymbol]();\n        }\n    };\n})();\nexport { UserPrompt };\n//# sourceMappingURL=UserPrompt.js.map", "/**\n * @license\n * Copyright 2024 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nvar __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {\n    var useValue = arguments.length > 2;\n    for (var i = 0; i < initializers.length; i++) {\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n    }\n    return useValue ? value : void 0;\n};\nvar __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n    var _, done = false;\n    for (var i = decorators.length - 1; i >= 0; i--) {\n        var context = {};\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n        if (kind === \"accessor\") {\n            if (result === void 0) continue;\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n            if (_ = accept(result.get)) descriptor.get = _;\n            if (_ = accept(result.set)) descriptor.set = _;\n            if (_ = accept(result.init)) initializers.unshift(_);\n        }\n        else if (_ = accept(result)) {\n            if (kind === \"field\") initializers.unshift(_);\n            else descriptor[key] = _;\n        }\n    }\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\n    done = true;\n};\nimport { EventEmitter } from '../../common/EventEmitter.js';\nimport { inertIfDisposed, throwIfDisposed } from '../../util/decorators.js';\nimport { DisposableStack, disposeSymbol } from '../../util/disposable.js';\nimport { Navigation } from './Navigation.js';\nimport { WindowRealm } from './Realm.js';\nimport { Request } from './Request.js';\nimport { UserPrompt } from './UserPrompt.js';\n/**\n * @internal\n */\nlet BrowsingContext = (() => {\n    var _a;\n    let _classSuper = EventEmitter;\n    let _instanceExtraInitializers = [];\n    let _dispose_decorators;\n    let _activate_decorators;\n    let _captureScreenshot_decorators;\n    let _close_decorators;\n    let _traverseHistory_decorators;\n    let _navigate_decorators;\n    let _reload_decorators;\n    let _print_decorators;\n    let _handleUserPrompt_decorators;\n    let _setViewport_decorators;\n    let _performActions_decorators;\n    let _releaseActions_decorators;\n    let _createWindowRealm_decorators;\n    let _addPreloadScript_decorators;\n    let _removePreloadScript_decorators;\n    return class BrowsingContext extends _classSuper {\n        static {\n            const _metadata = typeof Symbol === \"function\" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;\n            __esDecorate(this, null, _dispose_decorators, { kind: \"method\", name: \"dispose\", static: false, private: false, access: { has: obj => \"dispose\" in obj, get: obj => obj.dispose }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _activate_decorators, { kind: \"method\", name: \"activate\", static: false, private: false, access: { has: obj => \"activate\" in obj, get: obj => obj.activate }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _captureScreenshot_decorators, { kind: \"method\", name: \"captureScreenshot\", static: false, private: false, access: { has: obj => \"captureScreenshot\" in obj, get: obj => obj.captureScreenshot }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _close_decorators, { kind: \"method\", name: \"close\", static: false, private: false, access: { has: obj => \"close\" in obj, get: obj => obj.close }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _traverseHistory_decorators, { kind: \"method\", name: \"traverseHistory\", static: false, private: false, access: { has: obj => \"traverseHistory\" in obj, get: obj => obj.traverseHistory }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _navigate_decorators, { kind: \"method\", name: \"navigate\", static: false, private: false, access: { has: obj => \"navigate\" in obj, get: obj => obj.navigate }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _reload_decorators, { kind: \"method\", name: \"reload\", static: false, private: false, access: { has: obj => \"reload\" in obj, get: obj => obj.reload }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _print_decorators, { kind: \"method\", name: \"print\", static: false, private: false, access: { has: obj => \"print\" in obj, get: obj => obj.print }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _handleUserPrompt_decorators, { kind: \"method\", name: \"handleUserPrompt\", static: false, private: false, access: { has: obj => \"handleUserPrompt\" in obj, get: obj => obj.handleUserPrompt }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _setViewport_decorators, { kind: \"method\", name: \"setViewport\", static: false, private: false, access: { has: obj => \"setViewport\" in obj, get: obj => obj.setViewport }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _performActions_decorators, { kind: \"method\", name: \"performActions\", static: false, private: false, access: { has: obj => \"performActions\" in obj, get: obj => obj.performActions }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _releaseActions_decorators, { kind: \"method\", name: \"releaseActions\", static: false, private: false, access: { has: obj => \"releaseActions\" in obj, get: obj => obj.releaseActions }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _createWindowRealm_decorators, { kind: \"method\", name: \"createWindowRealm\", static: false, private: false, access: { has: obj => \"createWindowRealm\" in obj, get: obj => obj.createWindowRealm }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _addPreloadScript_decorators, { kind: \"method\", name: \"addPreloadScript\", static: false, private: false, access: { has: obj => \"addPreloadScript\" in obj, get: obj => obj.addPreloadScript }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _removePreloadScript_decorators, { kind: \"method\", name: \"removePreloadScript\", static: false, private: false, access: { has: obj => \"removePreloadScript\" in obj, get: obj => obj.removePreloadScript }, metadata: _metadata }, null, _instanceExtraInitializers);\n            if (_metadata) Object.defineProperty(this, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });\n        }\n        static from(userContext, parent, id, url) {\n            const browsingContext = new BrowsingContext(userContext, parent, id, url);\n            browsingContext.#initialize();\n            return browsingContext;\n        }\n        // keep-sorted start\n        #navigation = (__runInitializers(this, _instanceExtraInitializers), void 0);\n        #reason;\n        #url;\n        #children = new Map();\n        #disposables = new DisposableStack();\n        #realms = new Map();\n        #requests = new Map();\n        defaultRealm;\n        id;\n        parent;\n        userContext;\n        // keep-sorted end\n        constructor(context, parent, id, url) {\n            super();\n            // keep-sorted start\n            this.#url = url;\n            this.id = id;\n            this.parent = parent;\n            this.userContext = context;\n            // keep-sorted end\n            this.defaultRealm = WindowRealm.from(this);\n        }\n        #initialize() {\n            const userContextEmitter = this.#disposables.use(new EventEmitter(this.userContext));\n            userContextEmitter.once('closed', ({ reason }) => {\n                this.dispose(`Browsing context already closed: ${reason}`);\n            });\n            const sessionEmitter = this.#disposables.use(new EventEmitter(this.#session));\n            sessionEmitter.on('browsingContext.contextCreated', info => {\n                if (info.parent !== this.id) {\n                    return;\n                }\n                const browsingContext = BrowsingContext.from(this.userContext, this, info.context, info.url);\n                this.#children.set(info.context, browsingContext);\n                const browsingContextEmitter = this.#disposables.use(new EventEmitter(browsingContext));\n                browsingContextEmitter.once('closed', () => {\n                    browsingContextEmitter.removeAllListeners();\n                    this.#children.delete(browsingContext.id);\n                });\n                this.emit('browsingcontext', { browsingContext });\n            });\n            sessionEmitter.on('browsingContext.contextDestroyed', info => {\n                if (info.context !== this.id) {\n                    return;\n                }\n                this.dispose('Browsing context already closed.');\n            });\n            sessionEmitter.on('browsingContext.domContentLoaded', info => {\n                if (info.context !== this.id) {\n                    return;\n                }\n                this.#url = info.url;\n                this.emit('DOMContentLoaded', undefined);\n            });\n            sessionEmitter.on('browsingContext.load', info => {\n                if (info.context !== this.id) {\n                    return;\n                }\n                this.#url = info.url;\n                this.emit('load', undefined);\n            });\n            sessionEmitter.on('browsingContext.navigationStarted', info => {\n                if (info.context !== this.id) {\n                    return;\n                }\n                this.#url = info.url;\n                this.#requests.clear();\n                // Note the navigation ID is null for this event.\n                this.#navigation = Navigation.from(this);\n                const navigationEmitter = this.#disposables.use(new EventEmitter(this.#navigation));\n                for (const eventName of ['fragment', 'failed', 'aborted']) {\n                    navigationEmitter.once(eventName, ({ url }) => {\n                        navigationEmitter[disposeSymbol]();\n                        this.#url = url;\n                    });\n                }\n                this.emit('navigation', { navigation: this.#navigation });\n            });\n            sessionEmitter.on('network.beforeRequestSent', event => {\n                if (event.context !== this.id) {\n                    return;\n                }\n                if (this.#requests.has(event.request.request)) {\n                    return;\n                }\n                const request = Request.from(this, event);\n                this.#requests.set(request.id, request);\n                this.emit('request', { request });\n            });\n            sessionEmitter.on('log.entryAdded', entry => {\n                if (entry.source.context !== this.id) {\n                    return;\n                }\n                this.emit('log', { entry });\n            });\n            sessionEmitter.on('browsingContext.userPromptOpened', info => {\n                if (info.context !== this.id) {\n                    return;\n                }\n                const userPrompt = UserPrompt.from(this, info);\n                this.emit('userprompt', { userPrompt });\n            });\n        }\n        // keep-sorted start block=yes\n        get #session() {\n            return this.userContext.browser.session;\n        }\n        get children() {\n            return this.#children.values();\n        }\n        get closed() {\n            return this.#reason !== undefined;\n        }\n        get disposed() {\n            return this.closed;\n        }\n        get realms() {\n            return this.#realms.values();\n        }\n        get top() {\n            let context = this;\n            for (let { parent } = context; parent; { parent } = context) {\n                context = parent;\n            }\n            return context;\n        }\n        get url() {\n            return this.#url;\n        }\n        // keep-sorted end\n        dispose(reason) {\n            this.#reason = reason;\n            this[disposeSymbol]();\n        }\n        async activate() {\n            await this.#session.send('browsingContext.activate', {\n                context: this.id,\n            });\n        }\n        async captureScreenshot(options = {}) {\n            const { result: { data }, } = await this.#session.send('browsingContext.captureScreenshot', {\n                context: this.id,\n                ...options,\n            });\n            return data;\n        }\n        async close(promptUnload) {\n            await Promise.all([...this.#children.values()].map(async (child) => {\n                await child.close(promptUnload);\n            }));\n            await this.#session.send('browsingContext.close', {\n                context: this.id,\n                promptUnload,\n            });\n        }\n        async traverseHistory(delta) {\n            await this.#session.send('browsingContext.traverseHistory', {\n                context: this.id,\n                delta,\n            });\n        }\n        async navigate(url, wait) {\n            await this.#session.send('browsingContext.navigate', {\n                context: this.id,\n                url,\n                wait,\n            });\n            return await new Promise(resolve => {\n                this.once('navigation', ({ navigation }) => {\n                    resolve(navigation);\n                });\n            });\n        }\n        async reload(options = {}) {\n            await this.#session.send('browsingContext.reload', {\n                context: this.id,\n                ...options,\n            });\n            return await new Promise(resolve => {\n                this.once('navigation', ({ navigation }) => {\n                    resolve(navigation);\n                });\n            });\n        }\n        async print(options = {}) {\n            const { result: { data }, } = await this.#session.send('browsingContext.print', {\n                context: this.id,\n                ...options,\n            });\n            return data;\n        }\n        async handleUserPrompt(options = {}) {\n            await this.#session.send('browsingContext.handleUserPrompt', {\n                context: this.id,\n                ...options,\n            });\n        }\n        async setViewport(options = {}) {\n            await this.#session.send('browsingContext.setViewport', {\n                context: this.id,\n                ...options,\n            });\n        }\n        async performActions(actions) {\n            await this.#session.send('input.performActions', {\n                context: this.id,\n                actions,\n            });\n        }\n        async releaseActions() {\n            await this.#session.send('input.releaseActions', {\n                context: this.id,\n            });\n        }\n        createWindowRealm(sandbox) {\n            return WindowRealm.from(this, sandbox);\n        }\n        async addPreloadScript(functionDeclaration, options = {}) {\n            return await this.userContext.browser.addPreloadScript(functionDeclaration, {\n                ...options,\n                contexts: [this, ...(options.contexts ?? [])],\n            });\n        }\n        async removePreloadScript(script) {\n            await this.userContext.browser.removePreloadScript(script);\n        }\n        [(_dispose_decorators = [inertIfDisposed], _activate_decorators = [throwIfDisposed(context => {\n                // SAFETY: Disposal implies this exists.\n                return context.#reason;\n            })], _captureScreenshot_decorators = [throwIfDisposed(context => {\n                // SAFETY: Disposal implies this exists.\n                return context.#reason;\n            })], _close_decorators = [throwIfDisposed(context => {\n                // SAFETY: Disposal implies this exists.\n                return context.#reason;\n            })], _traverseHistory_decorators = [throwIfDisposed(context => {\n                // SAFETY: Disposal implies this exists.\n                return context.#reason;\n            })], _navigate_decorators = [throwIfDisposed(context => {\n                // SAFETY: Disposal implies this exists.\n                return context.#reason;\n            })], _reload_decorators = [throwIfDisposed(context => {\n                // SAFETY: Disposal implies this exists.\n                return context.#reason;\n            })], _print_decorators = [throwIfDisposed(context => {\n                // SAFETY: Disposal implies this exists.\n                return context.#reason;\n            })], _handleUserPrompt_decorators = [throwIfDisposed(context => {\n                // SAFETY: Disposal implies this exists.\n                return context.#reason;\n            })], _setViewport_decorators = [throwIfDisposed(context => {\n                // SAFETY: Disposal implies this exists.\n                return context.#reason;\n            })], _performActions_decorators = [throwIfDisposed(context => {\n                // SAFETY: Disposal implies this exists.\n                return context.#reason;\n            })], _releaseActions_decorators = [throwIfDisposed(context => {\n                // SAFETY: Disposal implies this exists.\n                return context.#reason;\n            })], _createWindowRealm_decorators = [throwIfDisposed(context => {\n                // SAFETY: Disposal implies this exists.\n                return context.#reason;\n            })], _addPreloadScript_decorators = [throwIfDisposed(context => {\n                // SAFETY: Disposal implies this exists.\n                return context.#reason;\n            })], _removePreloadScript_decorators = [throwIfDisposed(context => {\n                // SAFETY: Disposal implies this exists.\n                return context.#reason;\n            })], disposeSymbol)]() {\n            this.#reason ??=\n                'Browsing context already closed, probably because the user context closed.';\n            this.emit('closed', { reason: this.#reason });\n            this.#disposables.dispose();\n            super[disposeSymbol]();\n        }\n    };\n})();\nexport { BrowsingContext };\n//# sourceMappingURL=BrowsingContext.js.map", "/**\n * @license\n * Copyright 2024 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nvar __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {\n    var useValue = arguments.length > 2;\n    for (var i = 0; i < initializers.length; i++) {\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n    }\n    return useValue ? value : void 0;\n};\nvar __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n    var _, done = false;\n    for (var i = decorators.length - 1; i >= 0; i--) {\n        var context = {};\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n        if (kind === \"accessor\") {\n            if (result === void 0) continue;\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n            if (_ = accept(result.get)) descriptor.get = _;\n            if (_ = accept(result.set)) descriptor.set = _;\n            if (_ = accept(result.init)) initializers.unshift(_);\n        }\n        else if (_ = accept(result)) {\n            if (kind === \"field\") initializers.unshift(_);\n            else descriptor[key] = _;\n        }\n    }\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\n    done = true;\n};\nimport { EventEmitter } from '../../common/EventEmitter.js';\nimport { assert } from '../../util/assert.js';\nimport { inertIfDisposed, throwIfDisposed } from '../../util/decorators.js';\nimport { DisposableStack, disposeSymbol } from '../../util/disposable.js';\nimport { BrowsingContext } from './BrowsingContext.js';\n/**\n * @internal\n */\nlet UserContext = (() => {\n    let _classSuper = EventEmitter;\n    let _instanceExtraInitializers = [];\n    let _dispose_decorators;\n    let _createBrowsingContext_decorators;\n    let _remove_decorators;\n    return class UserContext extends _classSuper {\n        static {\n            const _metadata = typeof Symbol === \"function\" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;\n            __esDecorate(this, null, _dispose_decorators, { kind: \"method\", name: \"dispose\", static: false, private: false, access: { has: obj => \"dispose\" in obj, get: obj => obj.dispose }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _createBrowsingContext_decorators, { kind: \"method\", name: \"createBrowsingContext\", static: false, private: false, access: { has: obj => \"createBrowsingContext\" in obj, get: obj => obj.createBrowsingContext }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _remove_decorators, { kind: \"method\", name: \"remove\", static: false, private: false, access: { has: obj => \"remove\" in obj, get: obj => obj.remove }, metadata: _metadata }, null, _instanceExtraInitializers);\n            if (_metadata) Object.defineProperty(this, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });\n        }\n        static DEFAULT = 'default';\n        static create(browser, id) {\n            const context = new UserContext(browser, id);\n            context.#initialize();\n            return context;\n        }\n        // keep-sorted start\n        #reason = (__runInitializers(this, _instanceExtraInitializers), void 0);\n        // Note these are only top-level contexts.\n        #browsingContexts = new Map();\n        #disposables = new DisposableStack();\n        #id;\n        browser;\n        // keep-sorted end\n        constructor(browser, id) {\n            super();\n            // keep-sorted start\n            this.#id = id;\n            this.browser = browser;\n            // keep-sorted end\n        }\n        #initialize() {\n            const browserEmitter = this.#disposables.use(new EventEmitter(this.browser));\n            browserEmitter.once('closed', ({ reason }) => {\n                this.dispose(`User context already closed: ${reason}`);\n            });\n            const sessionEmitter = this.#disposables.use(new EventEmitter(this.#session));\n            sessionEmitter.on('browsingContext.contextCreated', info => {\n                if (info.parent) {\n                    return;\n                }\n                if (info.userContext !== this.#id) {\n                    return;\n                }\n                const browsingContext = BrowsingContext.from(this, undefined, info.context, info.url);\n                this.#browsingContexts.set(browsingContext.id, browsingContext);\n                const browsingContextEmitter = this.#disposables.use(new EventEmitter(browsingContext));\n                browsingContextEmitter.on('closed', () => {\n                    browsingContextEmitter.removeAllListeners();\n                    this.#browsingContexts.delete(browsingContext.id);\n                });\n                this.emit('browsingcontext', { browsingContext });\n            });\n        }\n        // keep-sorted start block=yes\n        get #session() {\n            return this.browser.session;\n        }\n        get browsingContexts() {\n            return this.#browsingContexts.values();\n        }\n        get closed() {\n            return this.#reason !== undefined;\n        }\n        get disposed() {\n            return this.closed;\n        }\n        get id() {\n            return this.#id;\n        }\n        // keep-sorted end\n        dispose(reason) {\n            this.#reason = reason;\n            this[disposeSymbol]();\n        }\n        async createBrowsingContext(type, options = {}) {\n            const { result: { context: contextId }, } = await this.#session.send('browsingContext.create', {\n                type,\n                ...options,\n                referenceContext: options.referenceContext?.id,\n                userContext: this.#id,\n            });\n            const browsingContext = this.#browsingContexts.get(contextId);\n            assert(browsingContext, 'The WebDriver BiDi implementation is failing to create a browsing context correctly.');\n            // We use an array to avoid the promise from being awaited.\n            return browsingContext;\n        }\n        async remove() {\n            try {\n                await this.#session.send('browser.removeUserContext', {\n                    userContext: this.#id,\n                });\n            }\n            finally {\n                this.dispose('User context already closed.');\n            }\n        }\n        [(_dispose_decorators = [inertIfDisposed], _createBrowsingContext_decorators = [throwIfDisposed(context => {\n                // SAFETY: Disposal implies this exists.\n                return context.#reason;\n            })], _remove_decorators = [throwIfDisposed(context => {\n                // SAFETY: Disposal implies this exists.\n                return context.#reason;\n            })], disposeSymbol)]() {\n            this.#reason ??=\n                'User context already closed, probably because the browser disconnected/closed.';\n            this.emit('closed', { reason: this.#reason });\n            this.#disposables.dispose();\n            super[disposeSymbol]();\n        }\n    };\n})();\nexport { UserContext };\n//# sourceMappingURL=UserContext.js.map", "/**\n * @license\n * Copyright 2022 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nimport { BrowserContext } from '../api/BrowserContext.js';\nimport { UnsupportedOperation } from '../common/Errors.js';\nimport { debugError } from '../common/util.js';\nimport { UserContext } from './core/UserContext.js';\n/**\n * @internal\n */\nexport class BidiBrowserContext extends BrowserContext {\n    #browser;\n    #connection;\n    #defaultViewport;\n    #userContext;\n    constructor(browser, userContext, options) {\n        super();\n        this.#browser = browser;\n        this.#userContext = userContext;\n        this.#connection = this.#browser.connection;\n        this.#defaultViewport = options.defaultViewport;\n    }\n    targets() {\n        return this.#browser.targets().filter(target => {\n            return target.browserContext() === this;\n        });\n    }\n    waitForTarget(predicate, options = {}) {\n        return this.#browser.waitForTarget(target => {\n            return target.browserContext() === this && predicate(target);\n        }, options);\n    }\n    get connection() {\n        return this.#connection;\n    }\n    async newPage() {\n        const { result } = await this.#connection.send('browsingContext.create', {\n            type: \"tab\" /* Bidi.BrowsingContext.CreateType.Tab */,\n            userContext: this.#userContext.id,\n        });\n        const target = this.#browser._getTargetById(result.context);\n        // TODO: once BiDi has some concept matching BrowserContext, the newly\n        // created contexts should get automatically assigned to the right\n        // BrowserContext. For now, we assume that only explicitly created pages go\n        // to the current BrowserContext. Otherwise, the contexts get assigned to\n        // the default BrowserContext by the Browser.\n        target._setBrowserContext(this);\n        const page = await target.page();\n        if (!page) {\n            throw new Error('Page is not found');\n        }\n        if (this.#defaultViewport) {\n            try {\n                await page.setViewport(this.#defaultViewport);\n            }\n            catch {\n                // No support for setViewport in Firefox.\n            }\n        }\n        return page;\n    }\n    async close() {\n        if (!this.isIncognito()) {\n            throw new Error('Default context cannot be closed!');\n        }\n        try {\n            await this.#userContext.remove();\n        }\n        catch (error) {\n            debugError(error);\n        }\n    }\n    browser() {\n        return this.#browser;\n    }\n    async pages() {\n        const results = await Promise.all([...this.targets()].map(t => {\n            return t.page();\n        }));\n        return results.filter((p) => {\n            return p !== null;\n        });\n    }\n    isIncognito() {\n        return this.#userContext.id !== UserContext.DEFAULT;\n    }\n    overridePermissions() {\n        throw new UnsupportedOperation();\n    }\n    clearPermissionOverrides() {\n        throw new UnsupportedOperation();\n    }\n    get id() {\n        if (this.#userContext.id === 'default') {\n            return undefined;\n        }\n        return this.#userContext.id;\n    }\n}\n//# sourceMappingURL=BrowserContext.js.map", "/**\n * @license\n * Copyright 2024 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nvar __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {\n    var useValue = arguments.length > 2;\n    for (var i = 0; i < initializers.length; i++) {\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n    }\n    return useValue ? value : void 0;\n};\nvar __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n    var _, done = false;\n    for (var i = decorators.length - 1; i >= 0; i--) {\n        var context = {};\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n        if (kind === \"accessor\") {\n            if (result === void 0) continue;\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n            if (_ = accept(result.get)) descriptor.get = _;\n            if (_ = accept(result.set)) descriptor.set = _;\n            if (_ = accept(result.init)) initializers.unshift(_);\n        }\n        else if (_ = accept(result)) {\n            if (kind === \"field\") initializers.unshift(_);\n            else descriptor[key] = _;\n        }\n    }\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\n    done = true;\n};\nvar __addDisposableResource = (this && this.__addDisposableResource) || function (env, value, async) {\n    if (value !== null && value !== void 0) {\n        if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n        var dispose;\n        if (async) {\n            if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n            dispose = value[Symbol.asyncDispose];\n        }\n        if (dispose === void 0) {\n            if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n            dispose = value[Symbol.dispose];\n        }\n        if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n        env.stack.push({ value: value, dispose: dispose, async: async });\n    }\n    else if (async) {\n        env.stack.push({ async: true });\n    }\n    return value;\n};\nvar __disposeResources = (this && this.__disposeResources) || (function (SuppressedError) {\n    return function (env) {\n        function fail(e) {\n            env.error = env.hasError ? new SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n            env.hasError = true;\n        }\n        function next() {\n            while (env.stack.length) {\n                var rec = env.stack.pop();\n                try {\n                    var result = rec.dispose && rec.dispose.call(rec.value);\n                    if (rec.async) return Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n                }\n                catch (e) {\n                    fail(e);\n                }\n            }\n            if (env.hasError) throw env.error;\n        }\n        return next();\n    };\n})(typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n    var e = new Error(message);\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n});\nimport { EventEmitter } from '../../common/EventEmitter.js';\nimport { inertIfDisposed, throwIfDisposed } from '../../util/decorators.js';\nimport { DisposableStack, disposeSymbol } from '../../util/disposable.js';\nimport { UserContext } from './UserContext.js';\n/**\n * @internal\n */\nlet Browser = (() => {\n    let _classSuper = EventEmitter;\n    let _instanceExtraInitializers = [];\n    let _dispose_decorators;\n    let _close_decorators;\n    let _addPreloadScript_decorators;\n    let _removePreloadScript_decorators;\n    let _createUserContext_decorators;\n    return class Browser extends _classSuper {\n        static {\n            const _metadata = typeof Symbol === \"function\" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;\n            __esDecorate(this, null, _dispose_decorators, { kind: \"method\", name: \"dispose\", static: false, private: false, access: { has: obj => \"dispose\" in obj, get: obj => obj.dispose }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _close_decorators, { kind: \"method\", name: \"close\", static: false, private: false, access: { has: obj => \"close\" in obj, get: obj => obj.close }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _addPreloadScript_decorators, { kind: \"method\", name: \"addPreloadScript\", static: false, private: false, access: { has: obj => \"addPreloadScript\" in obj, get: obj => obj.addPreloadScript }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _removePreloadScript_decorators, { kind: \"method\", name: \"removePreloadScript\", static: false, private: false, access: { has: obj => \"removePreloadScript\" in obj, get: obj => obj.removePreloadScript }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _createUserContext_decorators, { kind: \"method\", name: \"createUserContext\", static: false, private: false, access: { has: obj => \"createUserContext\" in obj, get: obj => obj.createUserContext }, metadata: _metadata }, null, _instanceExtraInitializers);\n            if (_metadata) Object.defineProperty(this, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });\n        }\n        static async from(session) {\n            const browser = new Browser(session);\n            await browser.#initialize();\n            return browser;\n        }\n        // keep-sorted start\n        #closed = (__runInitializers(this, _instanceExtraInitializers), false);\n        #reason;\n        #disposables = new DisposableStack();\n        #userContexts = new Map();\n        session;\n        // keep-sorted end\n        constructor(session) {\n            super();\n            // keep-sorted start\n            this.session = session;\n            // keep-sorted end\n            this.#userContexts.set(UserContext.DEFAULT, UserContext.create(this, UserContext.DEFAULT));\n        }\n        async #initialize() {\n            const sessionEmitter = this.#disposables.use(new EventEmitter(this.session));\n            sessionEmitter.once('ended', ({ reason }) => {\n                this.dispose(reason);\n            });\n            sessionEmitter.on('script.realmCreated', info => {\n                if (info.type === 'shared-worker') {\n                    // TODO: Create a SharedWorkerRealm.\n                }\n            });\n            await this.#syncUserContexts();\n            await this.#syncBrowsingContexts();\n        }\n        async #syncUserContexts() {\n            const { result: { userContexts }, } = await this.session.send('browser.getUserContexts', {});\n            for (const context of userContexts) {\n                if (context.userContext === UserContext.DEFAULT) {\n                    continue;\n                }\n                this.#userContexts.set(context.userContext, UserContext.create(this, context.userContext));\n            }\n        }\n        async #syncBrowsingContexts() {\n            // In case contexts are created or destroyed during `getTree`, we use this\n            // set to detect them.\n            const contextIds = new Set();\n            let contexts;\n            {\n                const env_1 = { stack: [], error: void 0, hasError: false };\n                try {\n                    const sessionEmitter = __addDisposableResource(env_1, new EventEmitter(this.session), false);\n                    sessionEmitter.on('browsingContext.contextCreated', info => {\n                        contextIds.add(info.context);\n                    });\n                    sessionEmitter.on('browsingContext.contextDestroyed', info => {\n                        contextIds.delete(info.context);\n                    });\n                    const { result } = await this.session.send('browsingContext.getTree', {});\n                    contexts = result.contexts;\n                }\n                catch (e_1) {\n                    env_1.error = e_1;\n                    env_1.hasError = true;\n                }\n                finally {\n                    __disposeResources(env_1);\n                }\n            }\n            // Simulating events so contexts are created naturally.\n            for (const info of contexts) {\n                if (contextIds.has(info.context)) {\n                    this.session.emit('browsingContext.contextCreated', info);\n                }\n                if (info.children) {\n                    contexts.push(...info.children);\n                }\n            }\n        }\n        // keep-sorted start block=yes\n        get closed() {\n            return this.#closed;\n        }\n        get defaultUserContext() {\n            // SAFETY: A UserContext is always created for the default context.\n            return this.#userContexts.get(UserContext.DEFAULT);\n        }\n        get disconnected() {\n            return this.#reason !== undefined;\n        }\n        get disposed() {\n            return this.disconnected;\n        }\n        get userContexts() {\n            return this.#userContexts.values();\n        }\n        // keep-sorted end\n        dispose(reason, closed = false) {\n            this.#closed = closed;\n            this.#reason = reason;\n            this[disposeSymbol]();\n        }\n        async close() {\n            try {\n                await this.session.send('browser.close', {});\n            }\n            finally {\n                this.dispose('Browser already closed.', true);\n            }\n        }\n        async addPreloadScript(functionDeclaration, options = {}) {\n            const { result: { script }, } = await this.session.send('script.addPreloadScript', {\n                functionDeclaration,\n                ...options,\n                contexts: options.contexts?.map(context => {\n                    return context.id;\n                }),\n            });\n            return script;\n        }\n        async removePreloadScript(script) {\n            await this.session.send('script.removePreloadScript', {\n                script,\n            });\n        }\n        async createUserContext() {\n            const { result: { userContext: context }, } = await this.session.send('browser.createUserContext', {});\n            const userContext = UserContext.create(this, context);\n            this.#userContexts.set(userContext.id, userContext);\n            const userContextEmitter = this.#disposables.use(new EventEmitter(userContext));\n            userContextEmitter.once('closed', () => {\n                userContextEmitter.removeAllListeners();\n                this.#userContexts.delete(context);\n            });\n            return userContext;\n        }\n        [(_dispose_decorators = [inertIfDisposed], _close_decorators = [throwIfDisposed(browser => {\n                // SAFETY: By definition of `disposed`, `#reason` is defined.\n                return browser.#reason;\n            })], _addPreloadScript_decorators = [throwIfDisposed(browser => {\n                // SAFETY: By definition of `disposed`, `#reason` is defined.\n                return browser.#reason;\n            })], _removePreloadScript_decorators = [throwIfDisposed(browser => {\n                // SAFETY: By definition of `disposed`, `#reason` is defined.\n                return browser.#reason;\n            })], _createUserContext_decorators = [throwIfDisposed(browser => {\n                // SAFETY: By definition of `disposed`, `#reason` is defined.\n                return browser.#reason;\n            })], disposeSymbol)]() {\n            this.#reason ??=\n                'Browser was disconnected, probably because the session ended.';\n            if (this.closed) {\n                this.emit('closed', { reason: this.#reason });\n            }\n            this.emit('disconnected', { reason: this.#reason });\n            this.#disposables.dispose();\n            super[disposeSymbol]();\n        }\n    };\n})();\nexport { Browser };\n//# sourceMappingURL=Browser.js.map", "/**\n * @license\n * Copyright 2024 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nvar __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {\n    var useValue = arguments.length > 2;\n    for (var i = 0; i < initializers.length; i++) {\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n    }\n    return useValue ? value : void 0;\n};\nvar __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n    var _, done = false;\n    for (var i = decorators.length - 1; i >= 0; i--) {\n        var context = {};\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n        if (kind === \"accessor\") {\n            if (result === void 0) continue;\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n            if (_ = accept(result.get)) descriptor.get = _;\n            if (_ = accept(result.set)) descriptor.set = _;\n            if (_ = accept(result.init)) initializers.unshift(_);\n        }\n        else if (_ = accept(result)) {\n            if (kind === \"field\") initializers.unshift(_);\n            else descriptor[key] = _;\n        }\n    }\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\n    done = true;\n};\nimport { EventEmitter } from '../../common/EventEmitter.js';\nimport { debugError } from '../../common/util.js';\nimport { inertIfDisposed, throwIfDisposed } from '../../util/decorators.js';\nimport { DisposableStack, disposeSymbol } from '../../util/disposable.js';\nimport { Browser } from './Browser.js';\n// TODO: Once Chrome supports session.status properly, uncomment this block.\n// const MAX_RETRIES = 5;\n/**\n * @internal\n */\nlet Session = (() => {\n    let _classSuper = EventEmitter;\n    let _instanceExtraInitializers = [];\n    let _dispose_decorators;\n    let _send_decorators;\n    let _subscribe_decorators;\n    let _end_decorators;\n    return class Session extends _classSuper {\n        static {\n            const _metadata = typeof Symbol === \"function\" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;\n            __esDecorate(this, null, _dispose_decorators, { kind: \"method\", name: \"dispose\", static: false, private: false, access: { has: obj => \"dispose\" in obj, get: obj => obj.dispose }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _send_decorators, { kind: \"method\", name: \"send\", static: false, private: false, access: { has: obj => \"send\" in obj, get: obj => obj.send }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _subscribe_decorators, { kind: \"method\", name: \"subscribe\", static: false, private: false, access: { has: obj => \"subscribe\" in obj, get: obj => obj.subscribe }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _end_decorators, { kind: \"method\", name: \"end\", static: false, private: false, access: { has: obj => \"end\" in obj, get: obj => obj.end }, metadata: _metadata }, null, _instanceExtraInitializers);\n            if (_metadata) Object.defineProperty(this, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });\n        }\n        static async from(connection, capabilities) {\n            // Wait until the session is ready.\n            //\n            // TODO: Once Chrome supports session.status properly, uncomment this block\n            // and remove `getBiDiConnection` in BrowserConnector.\n            // let status = {message: '', ready: false};\n            // for (let i = 0; i < MAX_RETRIES; ++i) {\n            //   status = (await connection.send('session.status', {})).result;\n            //   if (status.ready) {\n            //     break;\n            //   }\n            //   // Backoff a little bit each time.\n            //   await new Promise(resolve => {\n            //     return setTimeout(resolve, (1 << i) * 100);\n            //   });\n            // }\n            // if (!status.ready) {\n            //   throw new Error(status.message);\n            // }\n            let result;\n            try {\n                result = (await connection.send('session.new', {\n                    capabilities,\n                })).result;\n            }\n            catch (err) {\n                // Chrome does not support session.new.\n                debugError(err);\n                result = {\n                    sessionId: '',\n                    capabilities: {\n                        acceptInsecureCerts: false,\n                        browserName: '',\n                        browserVersion: '',\n                        platformName: '',\n                        setWindowRect: false,\n                        webSocketUrl: '',\n                    },\n                };\n            }\n            const session = new Session(connection, result);\n            await session.#initialize();\n            return session;\n        }\n        // keep-sorted start\n        #reason = (__runInitializers(this, _instanceExtraInitializers), void 0);\n        #disposables = new DisposableStack();\n        #info;\n        browser;\n        connection;\n        // keep-sorted end\n        constructor(connection, info) {\n            super();\n            // keep-sorted start\n            this.#info = info;\n            this.connection = connection;\n            // keep-sorted end\n        }\n        async #initialize() {\n            this.connection.pipeTo(this);\n            // SAFETY: We use `any` to allow assignment of the readonly property.\n            this.browser = await Browser.from(this);\n            const browserEmitter = this.#disposables.use(this.browser);\n            browserEmitter.once('closed', ({ reason }) => {\n                this.dispose(reason);\n            });\n        }\n        // keep-sorted start block=yes\n        get capabilities() {\n            return this.#info.capabilities;\n        }\n        get disposed() {\n            return this.ended;\n        }\n        get ended() {\n            return this.#reason !== undefined;\n        }\n        get id() {\n            return this.#info.sessionId;\n        }\n        // keep-sorted end\n        dispose(reason) {\n            this.#reason = reason;\n            this[disposeSymbol]();\n        }\n        pipeTo(emitter) {\n            this.connection.pipeTo(emitter);\n        }\n        /**\n         * Currently, there is a 1:1 relationship between the session and the\n         * session. In the future, we might support multiple sessions and in that\n         * case we always needs to make sure that the session for the right session\n         * object is used, so we implement this method here, although it's not defined\n         * in the spec.\n         */\n        async send(method, params) {\n            return await this.connection.send(method, params);\n        }\n        async subscribe(events) {\n            await this.send('session.subscribe', {\n                events,\n            });\n        }\n        async end() {\n            try {\n                await this.send('session.end', {});\n            }\n            finally {\n                this.dispose(`Session already ended.`);\n            }\n        }\n        [(_dispose_decorators = [inertIfDisposed], _send_decorators = [throwIfDisposed(session => {\n                // SAFETY: By definition of `disposed`, `#reason` is defined.\n                return session.#reason;\n            })], _subscribe_decorators = [throwIfDisposed(session => {\n                // SAFETY: By definition of `disposed`, `#reason` is defined.\n                return session.#reason;\n            })], _end_decorators = [throwIfDisposed(session => {\n                // SAFETY: By definition of `disposed`, `#reason` is defined.\n                return session.#reason;\n            })], disposeSymbol)]() {\n            this.#reason ??=\n                'Session already destroyed, probably because the connection broke.';\n            this.emit('ended', { reason: this.#reason });\n            this.#disposables.dispose();\n            super[disposeSymbol]();\n        }\n    };\n})();\nexport { Session };\n//# sourceMappingURL=Session.js.map", "/**\n * @license\n * Copyright 2017 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nimport { Dialog } from '../api/Dialog.js';\n/**\n * @internal\n */\nexport class BidiDialog extends Dialog {\n    #context;\n    /**\n     * @internal\n     */\n    constructor(context, type, message, defaultValue) {\n        super(type, message, defaultValue);\n        this.#context = context;\n    }\n    /**\n     * @internal\n     */\n    async handle(options) {\n        await this.#context.connection.send('browsingContext.handleUserPrompt', {\n            context: this.#context.id,\n            accept: options.accept,\n            userText: options.text,\n        });\n    }\n}\n//# sourceMappingURL=Dialog.js.map", "/**\n * @internal\n */\nexport class EmulationManager {\n    #browsingContext;\n    constructor(browsingContext) {\n        this.#browsingContext = browsingContext;\n    }\n    async emulateViewport(viewport) {\n        await this.#browsingContext.connection.send('browsingContext.setViewport', {\n            context: this.#browsingContext.id,\n            viewport: viewport.width && viewport.height\n                ? {\n                    width: viewport.width,\n                    height: viewport.height,\n                }\n                : null,\n            devicePixelRatio: viewport.deviceScaleFactor\n                ? viewport.deviceScaleFactor\n                : null,\n        });\n    }\n}\n//# sourceMappingURL=EmulationManager.js.map", "/**\n * @license\n * Copyright 2023 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nimport * as Bidi from 'chromium-bidi/lib/cjs/protocol/protocol.js';\nimport { debugError } from '../common/util.js';\nimport { assert } from '../util/assert.js';\nimport { Deferred } from '../util/Deferred.js';\nimport { interpolateFunction, stringifyFunction } from '../util/Function.js';\nimport { BidiDeserializer } from './Deserializer.js';\nimport { BidiSerializer } from './Serializer.js';\n/**\n * @internal\n */\nexport class ExposeableFunction {\n    #frame;\n    name;\n    #apply;\n    #channels;\n    #callerInfos = new Map();\n    #preloadScriptId;\n    constructor(frame, name, apply) {\n        this.#frame = frame;\n        this.name = name;\n        this.#apply = apply;\n        this.#channels = {\n            args: `__puppeteer__${this.#frame._id}_page_exposeFunction_${this.name}_args`,\n            resolve: `__puppeteer__${this.#frame._id}_page_exposeFunction_${this.name}_resolve`,\n            reject: `__puppeteer__${this.#frame._id}_page_exposeFunction_${this.name}_reject`,\n        };\n    }\n    async expose() {\n        const connection = this.#connection;\n        const channelArguments = this.#channelArguments;\n        // TODO(jrandolf): Implement cleanup with removePreloadScript.\n        connection.on(Bidi.ChromiumBidi.Script.EventNames.Message, this.#handleArgumentsMessage);\n        connection.on(Bidi.ChromiumBidi.Script.EventNames.Message, this.#handleResolveMessage);\n        connection.on(Bidi.ChromiumBidi.Script.EventNames.Message, this.#handleRejectMessage);\n        const functionDeclaration = stringifyFunction(interpolateFunction((sendArgs, sendResolve, sendReject) => {\n            let id = 0;\n            Object.assign(globalThis, {\n                [PLACEHOLDER('name')]: function (...args) {\n                    return new Promise((resolve, reject) => {\n                        sendArgs([id, args]);\n                        sendResolve([id, resolve]);\n                        sendReject([id, reject]);\n                        ++id;\n                    });\n                },\n            });\n        }, { name: JSON.stringify(this.name) }));\n        const { result } = await connection.send('script.addPreloadScript', {\n            functionDeclaration,\n            arguments: channelArguments,\n            contexts: [this.#frame.page().mainFrame()._id],\n        });\n        this.#preloadScriptId = result.script;\n        await Promise.all(this.#frame\n            .page()\n            .frames()\n            .map(async (frame) => {\n            return await connection.send('script.callFunction', {\n                functionDeclaration,\n                arguments: channelArguments,\n                awaitPromise: false,\n                target: frame.mainRealm().realm.target,\n            });\n        }));\n    }\n    #handleArgumentsMessage = async (params) => {\n        if (params.channel !== this.#channels.args) {\n            return;\n        }\n        const connection = this.#connection;\n        const { callbacks, remoteValue } = this.#getCallbacksAndRemoteValue(params);\n        const args = remoteValue.value?.[1];\n        assert(args);\n        try {\n            const result = await this.#apply(...BidiDeserializer.deserialize(args));\n            await connection.send('script.callFunction', {\n                functionDeclaration: stringifyFunction(([_, resolve], result) => {\n                    resolve(result);\n                }),\n                arguments: [\n                    (await callbacks.resolve.valueOrThrow()),\n                    BidiSerializer.serializeRemoteValue(result),\n                ],\n                awaitPromise: false,\n                target: {\n                    realm: params.source.realm,\n                },\n            });\n        }\n        catch (error) {\n            try {\n                if (error instanceof Error) {\n                    await connection.send('script.callFunction', {\n                        functionDeclaration: stringifyFunction(([_, reject], name, message, stack) => {\n                            const error = new Error(message);\n                            error.name = name;\n                            if (stack) {\n                                error.stack = stack;\n                            }\n                            reject(error);\n                        }),\n                        arguments: [\n                            (await callbacks.reject.valueOrThrow()),\n                            BidiSerializer.serializeRemoteValue(error.name),\n                            BidiSerializer.serializeRemoteValue(error.message),\n                            BidiSerializer.serializeRemoteValue(error.stack),\n                        ],\n                        awaitPromise: false,\n                        target: {\n                            realm: params.source.realm,\n                        },\n                    });\n                }\n                else {\n                    await connection.send('script.callFunction', {\n                        functionDeclaration: stringifyFunction(([_, reject], error) => {\n                            reject(error);\n                        }),\n                        arguments: [\n                            (await callbacks.reject.valueOrThrow()),\n                            BidiSerializer.serializeRemoteValue(error),\n                        ],\n                        awaitPromise: false,\n                        target: {\n                            realm: params.source.realm,\n                        },\n                    });\n                }\n            }\n            catch (error) {\n                debugError(error);\n            }\n        }\n    };\n    get #connection() {\n        return this.#frame.context().connection;\n    }\n    get #channelArguments() {\n        return [\n            {\n                type: 'channel',\n                value: {\n                    channel: this.#channels.args,\n                    ownership: \"root\" /* Bidi.Script.ResultOwnership.Root */,\n                },\n            },\n            {\n                type: 'channel',\n                value: {\n                    channel: this.#channels.resolve,\n                    ownership: \"root\" /* Bidi.Script.ResultOwnership.Root */,\n                },\n            },\n            {\n                type: 'channel',\n                value: {\n                    channel: this.#channels.reject,\n                    ownership: \"root\" /* Bidi.Script.ResultOwnership.Root */,\n                },\n            },\n        ];\n    }\n    #handleResolveMessage = (params) => {\n        if (params.channel !== this.#channels.resolve) {\n            return;\n        }\n        const { callbacks, remoteValue } = this.#getCallbacksAndRemoteValue(params);\n        callbacks.resolve.resolve(remoteValue);\n    };\n    #handleRejectMessage = (params) => {\n        if (params.channel !== this.#channels.reject) {\n            return;\n        }\n        const { callbacks, remoteValue } = this.#getCallbacksAndRemoteValue(params);\n        callbacks.reject.resolve(remoteValue);\n    };\n    #getCallbacksAndRemoteValue(params) {\n        const { data, source } = params;\n        assert(data.type === 'array');\n        assert(data.value);\n        const callerIdRemote = data.value[0];\n        assert(callerIdRemote);\n        assert(callerIdRemote.type === 'number');\n        assert(typeof callerIdRemote.value === 'number');\n        let bindingMap = this.#callerInfos.get(source.realm);\n        if (!bindingMap) {\n            bindingMap = new Map();\n            this.#callerInfos.set(source.realm, bindingMap);\n        }\n        const callerId = callerIdRemote.value;\n        let callbacks = bindingMap.get(callerId);\n        if (!callbacks) {\n            callbacks = {\n                resolve: new Deferred(),\n                reject: new Deferred(),\n            };\n            bindingMap.set(callerId, callbacks);\n        }\n        return { callbacks, remoteValue: data };\n    }\n    [Symbol.dispose]() {\n        void this[Symbol.asyncDispose]().catch(debugError);\n    }\n    async [Symbol.asyncDispose]() {\n        if (this.#preloadScriptId) {\n            await this.#connection.send('script.removePreloadScript', {\n                script: this.#preloadScriptId,\n            });\n        }\n    }\n}\n//# sourceMappingURL=ExposedFunction.js.map", "import { catchError } from '../../third_party/rxjs/rxjs.js';\nimport { ProtocolError, TimeoutError } from '../common/Errors.js';\n/**\n * @internal\n */\nexport function getBiDiLifeCycles(event) {\n    if (Array.isArray(event)) {\n        const pageLifeCycle = event.some(lifeCycle => {\n            return lifeCycle !== 'domcontentloaded';\n        })\n            ? 'load'\n            : 'domcontentloaded';\n        const networkLifeCycle = event.reduce((acc, lifeCycle) => {\n            if (lifeCycle === 'networkidle0') {\n                return lifeCycle;\n            }\n            else if (acc !== 'networkidle0' && lifeCycle === 'networkidle2') {\n                return lifeCycle;\n            }\n            return acc;\n        }, null);\n        return [pageLifeCycle, networkLifeCycle];\n    }\n    if (event === 'networkidle0' || event === 'networkidle2') {\n        return ['load', event];\n    }\n    return [event, null];\n}\n/**\n * @internal\n */\nexport const lifeCycleToReadinessState = new Map([\n    ['load', \"complete\" /* Bidi.BrowsingContext.ReadinessState.Complete */],\n    ['domcontentloaded', \"interactive\" /* Bidi.BrowsingContext.ReadinessState.Interactive */],\n]);\nexport function getBiDiReadinessState(event) {\n    const lifeCycles = getBiDiLifeCycles(event);\n    const readiness = lifeCycleToReadinessState.get(lifeCycles[0]);\n    return [readiness, lifeCycles[1]];\n}\n/**\n * @internal\n */\nexport const lifeCycleToSubscribedEvent = new Map([\n    ['load', 'browsingContext.load'],\n    ['domcontentloaded', 'browsingContext.domContentLoaded'],\n]);\n/**\n * @internal\n */\nexport function getBiDiLifecycleEvent(event) {\n    const lifeCycles = getBiDiLifeCycles(event);\n    const bidiEvent = lifeCycleToSubscribedEvent.get(lifeCycles[0]);\n    return [bidiEvent, lifeCycles[1]];\n}\n/**\n * @internal\n */\nexport function rewriteNavigationError(message, ms) {\n    return catchError(error => {\n        if (error instanceof ProtocolError) {\n            error.message += ` at ${message}`;\n        }\n        else if (error instanceof TimeoutError) {\n            error.message = `Navigation timeout of ${ms} ms exceeded`;\n        }\n        throw error;\n    });\n}\n//# sourceMappingURL=lifecycle.js.map", "/**\n * @license\n * Copyright 2023 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nimport { Realm } from '../api/Realm.js';\nimport { withSourcePuppeteerURLIfNone } from '../common/util.js';\nimport { BidiElementHandle } from './ElementHandle.js';\n/**\n * A unique key for {@link SandboxChart} to denote the default world.\n * Realms are automatically created in the default sandbox.\n *\n * @internal\n */\nexport const MAIN_SANDBOX = Symbol('mainSandbox');\n/**\n * A unique key for {@link SandboxChart} to denote the puppeteer sandbox.\n * This world contains all puppeteer-internal bindings/code.\n *\n * @internal\n */\nexport const PUPPETEER_SANDBOX = Symbol('puppeteerSandbox');\n/**\n * @internal\n */\nexport class Sandbox extends Realm {\n    name;\n    realm;\n    #frame;\n    constructor(name, frame, \n    // TODO: We should split the Realm and BrowsingContext\n    realm, timeoutSettings) {\n        super(timeoutSettings);\n        this.name = name;\n        this.realm = realm;\n        this.#frame = frame;\n        this.realm.setSandbox(this);\n    }\n    get environment() {\n        return this.#frame;\n    }\n    async evaluateHandle(pageFunction, ...args) {\n        pageFunction = withSourcePuppeteerURLIfNone(this.evaluateHandle.name, pageFunction);\n        return await this.realm.evaluateHandle(pageFunction, ...args);\n    }\n    async evaluate(pageFunction, ...args) {\n        pageFunction = withSourcePuppeteerURLIfNone(this.evaluate.name, pageFunction);\n        return await this.realm.evaluate(pageFunction, ...args);\n    }\n    async adoptHandle(handle) {\n        return (await this.evaluateHandle(node => {\n            return node;\n        }, handle));\n    }\n    async transferHandle(handle) {\n        if (handle.realm === this) {\n            return handle;\n        }\n        const transferredHandle = await this.evaluateHandle(node => {\n            return node;\n        }, handle);\n        await handle.dispose();\n        return transferredHandle;\n    }\n    async adoptBackendNode(backendNodeId) {\n        const { object } = await this.environment.client.send('DOM.resolveNode', {\n            backendNodeId: backendNodeId,\n        });\n        return new BidiElementHandle(this, {\n            handle: object.objectId,\n            type: 'node',\n        });\n    }\n}\n//# sourceMappingURL=Sandbox.js.map", "/**\n * @license\n * Copyright 2023 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nvar __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {\n    var useValue = arguments.length > 2;\n    for (var i = 0; i < initializers.length; i++) {\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n    }\n    return useValue ? value : void 0;\n};\nvar __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n    var _, done = false;\n    for (var i = decorators.length - 1; i >= 0; i--) {\n        var context = {};\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n        if (kind === \"accessor\") {\n            if (result === void 0) continue;\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n            if (_ = accept(result.get)) descriptor.get = _;\n            if (_ = accept(result.set)) descriptor.set = _;\n            if (_ = accept(result.init)) initializers.unshift(_);\n        }\n        else if (_ = accept(result)) {\n            if (kind === \"field\") initializers.unshift(_);\n            else descriptor[key] = _;\n        }\n    }\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\n    done = true;\n};\nimport * as Bidi from 'chromium-bidi/lib/cjs/protocol/protocol.js';\nimport { first, firstValueFrom, forkJoin, from, map, merge, raceWith, zip, } from '../../third_party/rxjs/rxjs.js';\nimport { Frame, throwIfDetached, } from '../api/Frame.js';\nimport { UnsupportedOperation } from '../common/Errors.js';\nimport { fromEmitterEvent, NETWORK_IDLE_TIME, timeout, UTILITY_WORLD_NAME, } from '../common/util.js';\nimport { Deferred } from '../util/Deferred.js';\nimport { disposeSymbol } from '../util/disposable.js';\nimport { ExposeableFunction } from './ExposedFunction.js';\nimport { getBiDiLifecycleEvent, getBiDiReadinessState, rewriteNavigationError, } from './lifecycle.js';\nimport { MAIN_SANDBOX, PUPPETEER_SANDBOX, Sandbox, } from './Sandbox.js';\n/**\n * Puppeteer's Frame class could be viewed as a BiDi BrowsingContext implementation\n * @internal\n */\nlet BidiFrame = (() => {\n    let _classSuper = Frame;\n    let _instanceExtraInitializers = [];\n    let _goto_decorators;\n    let _setContent_decorators;\n    let _waitForNavigation_decorators;\n    return class BidiFrame extends _classSuper {\n        static {\n            const _metadata = typeof Symbol === \"function\" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;\n            __esDecorate(this, null, _goto_decorators, { kind: \"method\", name: \"goto\", static: false, private: false, access: { has: obj => \"goto\" in obj, get: obj => obj.goto }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _setContent_decorators, { kind: \"method\", name: \"setContent\", static: false, private: false, access: { has: obj => \"setContent\" in obj, get: obj => obj.setContent }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _waitForNavigation_decorators, { kind: \"method\", name: \"waitForNavigation\", static: false, private: false, access: { has: obj => \"waitForNavigation\" in obj, get: obj => obj.waitForNavigation }, metadata: _metadata }, null, _instanceExtraInitializers);\n            if (_metadata) Object.defineProperty(this, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });\n        }\n        #page = (__runInitializers(this, _instanceExtraInitializers), void 0);\n        #context;\n        #timeoutSettings;\n        #abortDeferred = Deferred.create();\n        #disposed = false;\n        sandboxes;\n        _id;\n        constructor(page, context, timeoutSettings, parentId) {\n            super();\n            this.#page = page;\n            this.#context = context;\n            this.#timeoutSettings = timeoutSettings;\n            this._id = this.#context.id;\n            this._parentId = parentId ?? undefined;\n            this.sandboxes = {\n                [MAIN_SANDBOX]: new Sandbox(undefined, this, context, timeoutSettings),\n                [PUPPETEER_SANDBOX]: new Sandbox(UTILITY_WORLD_NAME, this, context.createRealmForSandbox(), timeoutSettings),\n            };\n        }\n        get client() {\n            return this.context().cdpSession;\n        }\n        mainRealm() {\n            return this.sandboxes[MAIN_SANDBOX];\n        }\n        isolatedRealm() {\n            return this.sandboxes[PUPPETEER_SANDBOX];\n        }\n        page() {\n            return this.#page;\n        }\n        isOOPFrame() {\n            throw new UnsupportedOperation();\n        }\n        url() {\n            return this.#context.url;\n        }\n        parentFrame() {\n            return this.#page.frame(this._parentId ?? '');\n        }\n        childFrames() {\n            return this.#page.childFrames(this.#context.id);\n        }\n        async goto(url, options = {}) {\n            const { waitUntil = 'load', timeout: ms = this.#timeoutSettings.navigationTimeout(), } = options;\n            const [readiness, networkIdle] = getBiDiReadinessState(waitUntil);\n            const result$ = zip(from(this.#context.connection.send('browsingContext.navigate', {\n                context: this.#context.id,\n                url,\n                wait: readiness,\n            })), ...(networkIdle !== null\n                ? [\n                    this.#page.waitForNetworkIdle$({\n                        timeout: ms,\n                        concurrency: networkIdle === 'networkidle2' ? 2 : 0,\n                        idleTime: NETWORK_IDLE_TIME,\n                    }),\n                ]\n                : [])).pipe(map(([{ result }]) => {\n                return result;\n            }), raceWith(timeout(ms), from(this.#abortDeferred.valueOrThrow())), rewriteNavigationError(url, ms));\n            const result = await firstValueFrom(result$);\n            return this.#page.getNavigationResponse(result.navigation);\n        }\n        async setContent(html, options = {}) {\n            const { waitUntil = 'load', timeout: ms = this.#timeoutSettings.navigationTimeout(), } = options;\n            const [waitEvent, networkIdle] = getBiDiLifecycleEvent(waitUntil);\n            const result$ = zip(forkJoin([\n                fromEmitterEvent(this.#context, waitEvent).pipe(first()),\n                from(this.setFrameContent(html)),\n            ]).pipe(map(() => {\n                return null;\n            })), ...(networkIdle !== null\n                ? [\n                    this.#page.waitForNetworkIdle$({\n                        timeout: ms,\n                        concurrency: networkIdle === 'networkidle2' ? 2 : 0,\n                        idleTime: NETWORK_IDLE_TIME,\n                    }),\n                ]\n                : [])).pipe(raceWith(timeout(ms), from(this.#abortDeferred.valueOrThrow())), rewriteNavigationError('setContent', ms));\n            await firstValueFrom(result$);\n        }\n        context() {\n            return this.#context;\n        }\n        async waitForNavigation(options = {}) {\n            const { waitUntil = 'load', timeout: ms = this.#timeoutSettings.navigationTimeout(), } = options;\n            const [waitUntilEvent, networkIdle] = getBiDiLifecycleEvent(waitUntil);\n            const navigation$ = merge(forkJoin([\n                fromEmitterEvent(this.#context, Bidi.ChromiumBidi.BrowsingContext.EventNames.NavigationStarted).pipe(first()),\n                fromEmitterEvent(this.#context, waitUntilEvent).pipe(first()),\n            ]), fromEmitterEvent(this.#context, Bidi.ChromiumBidi.BrowsingContext.EventNames.FragmentNavigated)).pipe(map(result => {\n                if (Array.isArray(result)) {\n                    return { result: result[1] };\n                }\n                return { result };\n            }));\n            const result$ = zip(navigation$, ...(networkIdle !== null\n                ? [\n                    this.#page.waitForNetworkIdle$({\n                        timeout: ms,\n                        concurrency: networkIdle === 'networkidle2' ? 2 : 0,\n                        idleTime: NETWORK_IDLE_TIME,\n                    }),\n                ]\n                : [])).pipe(map(([{ result }]) => {\n                return result;\n            }), raceWith(timeout(ms), from(this.#abortDeferred.valueOrThrow())));\n            const result = await firstValueFrom(result$);\n            return this.#page.getNavigationResponse(result.navigation);\n        }\n        waitForDevicePrompt() {\n            throw new UnsupportedOperation();\n        }\n        get detached() {\n            return this.#disposed;\n        }\n        [(_goto_decorators = [throwIfDetached], _setContent_decorators = [throwIfDetached], _waitForNavigation_decorators = [throwIfDetached], disposeSymbol)]() {\n            if (this.#disposed) {\n                return;\n            }\n            this.#disposed = true;\n            this.#abortDeferred.reject(new Error('Frame detached'));\n            this.#context.dispose();\n            this.sandboxes[MAIN_SANDBOX][disposeSymbol]();\n            this.sandboxes[PUPPETEER_SANDBOX][disposeSymbol]();\n        }\n        #exposedFunctions = new Map();\n        async exposeFunction(name, apply) {\n            if (this.#exposedFunctions.has(name)) {\n                throw new Error(`Failed to add page binding with name ${name}: globalThis['${name}'] already exists!`);\n            }\n            const exposeable = new ExposeableFunction(this, name, apply);\n            this.#exposedFunctions.set(name, exposeable);\n            try {\n                await exposeable.expose();\n            }\n            catch (error) {\n                this.#exposedFunctions.delete(name);\n                throw error;\n            }\n        }\n        waitForSelector(selector, options) {\n            if (selector.startsWith('aria')) {\n                throw new UnsupportedOperation('ARIA selector is not supported for BiDi!');\n            }\n            return super.waitForSelector(selector, options);\n        }\n    };\n})();\nexport { BidiFrame };\n//# sourceMappingURL=Frame.js.map", "/**\n * @license\n * Copyright 2017 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nimport { <PERSON><PERSON>, Mouse, MouseButton, Touchscreen, } from '../api/Input.js';\nimport { UnsupportedOperation } from '../common/Errors.js';\nvar SourceActionsType;\n(function (SourceActionsType) {\n    SourceActionsType[\"None\"] = \"none\";\n    SourceActionsType[\"Key\"] = \"key\";\n    SourceActionsType[\"Pointer\"] = \"pointer\";\n    SourceActionsType[\"Wheel\"] = \"wheel\";\n})(SourceActionsType || (SourceActionsType = {}));\nvar ActionType;\n(function (ActionType) {\n    ActionType[\"Pause\"] = \"pause\";\n    ActionType[\"KeyDown\"] = \"keyDown\";\n    ActionType[\"KeyUp\"] = \"keyUp\";\n    ActionType[\"PointerUp\"] = \"pointerUp\";\n    ActionType[\"PointerDown\"] = \"pointerDown\";\n    ActionType[\"PointerMove\"] = \"pointerMove\";\n    ActionType[\"Scroll\"] = \"scroll\";\n})(ActionType || (ActionType = {}));\nconst getBidiKeyValue = (key) => {\n    switch (key) {\n        case '\\r':\n        case '\\n':\n            key = 'Enter';\n            break;\n    }\n    // Measures the number of code points rather than UTF-16 code units.\n    if ([...key].length === 1) {\n        return key;\n    }\n    switch (key) {\n        case 'Cancel':\n            return '\\uE001';\n        case 'Help':\n            return '\\uE002';\n        case 'Backspace':\n            return '\\uE003';\n        case 'Tab':\n            return '\\uE004';\n        case 'Clear':\n            return '\\uE005';\n        case 'Enter':\n            return '\\uE007';\n        case 'Shift':\n        case 'ShiftLeft':\n            return '\\uE008';\n        case 'Control':\n        case 'ControlLeft':\n            return '\\uE009';\n        case 'Alt':\n        case 'AltLeft':\n            return '\\uE00A';\n        case 'Pause':\n            return '\\uE00B';\n        case 'Escape':\n            return '\\uE00C';\n        case 'PageUp':\n            return '\\uE00E';\n        case 'PageDown':\n            return '\\uE00F';\n        case 'End':\n            return '\\uE010';\n        case 'Home':\n            return '\\uE011';\n        case 'ArrowLeft':\n            return '\\uE012';\n        case 'ArrowUp':\n            return '\\uE013';\n        case 'ArrowRight':\n            return '\\uE014';\n        case 'ArrowDown':\n            return '\\uE015';\n        case 'Insert':\n            return '\\uE016';\n        case 'Delete':\n            return '\\uE017';\n        case 'NumpadEqual':\n            return '\\uE019';\n        case 'Numpad0':\n            return '\\uE01A';\n        case 'Numpad1':\n            return '\\uE01B';\n        case 'Numpad2':\n            return '\\uE01C';\n        case 'Numpad3':\n            return '\\uE01D';\n        case 'Numpad4':\n            return '\\uE01E';\n        case 'Numpad5':\n            return '\\uE01F';\n        case 'Numpad6':\n            return '\\uE020';\n        case 'Numpad7':\n            return '\\uE021';\n        case 'Numpad8':\n            return '\\uE022';\n        case 'Numpad9':\n            return '\\uE023';\n        case 'NumpadMultiply':\n            return '\\uE024';\n        case 'NumpadAdd':\n            return '\\uE025';\n        case 'NumpadSubtract':\n            return '\\uE027';\n        case 'NumpadDecimal':\n            return '\\uE028';\n        case 'NumpadDivide':\n            return '\\uE029';\n        case 'F1':\n            return '\\uE031';\n        case 'F2':\n            return '\\uE032';\n        case 'F3':\n            return '\\uE033';\n        case 'F4':\n            return '\\uE034';\n        case 'F5':\n            return '\\uE035';\n        case 'F6':\n            return '\\uE036';\n        case 'F7':\n            return '\\uE037';\n        case 'F8':\n            return '\\uE038';\n        case 'F9':\n            return '\\uE039';\n        case 'F10':\n            return '\\uE03A';\n        case 'F11':\n            return '\\uE03B';\n        case 'F12':\n            return '\\uE03C';\n        case 'Meta':\n        case 'MetaLeft':\n            return '\\uE03D';\n        case 'ShiftRight':\n            return '\\uE050';\n        case 'ControlRight':\n            return '\\uE051';\n        case 'AltRight':\n            return '\\uE052';\n        case 'MetaRight':\n            return '\\uE053';\n        case 'Digit0':\n            return '0';\n        case 'Digit1':\n            return '1';\n        case 'Digit2':\n            return '2';\n        case 'Digit3':\n            return '3';\n        case 'Digit4':\n            return '4';\n        case 'Digit5':\n            return '5';\n        case 'Digit6':\n            return '6';\n        case 'Digit7':\n            return '7';\n        case 'Digit8':\n            return '8';\n        case 'Digit9':\n            return '9';\n        case 'KeyA':\n            return 'a';\n        case 'KeyB':\n            return 'b';\n        case 'KeyC':\n            return 'c';\n        case 'KeyD':\n            return 'd';\n        case 'KeyE':\n            return 'e';\n        case 'KeyF':\n            return 'f';\n        case 'KeyG':\n            return 'g';\n        case 'KeyH':\n            return 'h';\n        case 'KeyI':\n            return 'i';\n        case 'KeyJ':\n            return 'j';\n        case 'KeyK':\n            return 'k';\n        case 'KeyL':\n            return 'l';\n        case 'KeyM':\n            return 'm';\n        case 'KeyN':\n            return 'n';\n        case 'KeyO':\n            return 'o';\n        case 'KeyP':\n            return 'p';\n        case 'KeyQ':\n            return 'q';\n        case 'KeyR':\n            return 'r';\n        case 'KeyS':\n            return 's';\n        case 'KeyT':\n            return 't';\n        case 'KeyU':\n            return 'u';\n        case 'KeyV':\n            return 'v';\n        case 'KeyW':\n            return 'w';\n        case 'KeyX':\n            return 'x';\n        case 'KeyY':\n            return 'y';\n        case 'KeyZ':\n            return 'z';\n        case 'Semicolon':\n            return ';';\n        case 'Equal':\n            return '=';\n        case 'Comma':\n            return ',';\n        case 'Minus':\n            return '-';\n        case 'Period':\n            return '.';\n        case 'Slash':\n            return '/';\n        case 'Backquote':\n            return '`';\n        case 'BracketLeft':\n            return '[';\n        case 'Backslash':\n            return '\\\\';\n        case 'BracketRight':\n            return ']';\n        case 'Quote':\n            return '\"';\n        default:\n            throw new Error(`Unknown key: \"${key}\"`);\n    }\n};\n/**\n * @internal\n */\nexport class BidiKeyboard extends Keyboard {\n    #page;\n    constructor(page) {\n        super();\n        this.#page = page;\n    }\n    async down(key, _options) {\n        await this.#page.connection.send('input.performActions', {\n            context: this.#page.mainFrame()._id,\n            actions: [\n                {\n                    type: SourceActionsType.Key,\n                    id: \"__puppeteer_keyboard\" /* InputId.Keyboard */,\n                    actions: [\n                        {\n                            type: ActionType.KeyDown,\n                            value: getBidiKeyValue(key),\n                        },\n                    ],\n                },\n            ],\n        });\n    }\n    async up(key) {\n        await this.#page.connection.send('input.performActions', {\n            context: this.#page.mainFrame()._id,\n            actions: [\n                {\n                    type: SourceActionsType.Key,\n                    id: \"__puppeteer_keyboard\" /* InputId.Keyboard */,\n                    actions: [\n                        {\n                            type: ActionType.KeyUp,\n                            value: getBidiKeyValue(key),\n                        },\n                    ],\n                },\n            ],\n        });\n    }\n    async press(key, options = {}) {\n        const { delay = 0 } = options;\n        const actions = [\n            {\n                type: ActionType.KeyDown,\n                value: getBidiKeyValue(key),\n            },\n        ];\n        if (delay > 0) {\n            actions.push({\n                type: ActionType.Pause,\n                duration: delay,\n            });\n        }\n        actions.push({\n            type: ActionType.KeyUp,\n            value: getBidiKeyValue(key),\n        });\n        await this.#page.connection.send('input.performActions', {\n            context: this.#page.mainFrame()._id,\n            actions: [\n                {\n                    type: SourceActionsType.Key,\n                    id: \"__puppeteer_keyboard\" /* InputId.Keyboard */,\n                    actions,\n                },\n            ],\n        });\n    }\n    async type(text, options = {}) {\n        const { delay = 0 } = options;\n        // This spread separates the characters into code points rather than UTF-16\n        // code units.\n        const values = [...text].map(getBidiKeyValue);\n        const actions = [];\n        if (delay <= 0) {\n            for (const value of values) {\n                actions.push({\n                    type: ActionType.KeyDown,\n                    value,\n                }, {\n                    type: ActionType.KeyUp,\n                    value,\n                });\n            }\n        }\n        else {\n            for (const value of values) {\n                actions.push({\n                    type: ActionType.KeyDown,\n                    value,\n                }, {\n                    type: ActionType.Pause,\n                    duration: delay,\n                }, {\n                    type: ActionType.KeyUp,\n                    value,\n                });\n            }\n        }\n        await this.#page.connection.send('input.performActions', {\n            context: this.#page.mainFrame()._id,\n            actions: [\n                {\n                    type: SourceActionsType.Key,\n                    id: \"__puppeteer_keyboard\" /* InputId.Keyboard */,\n                    actions,\n                },\n            ],\n        });\n    }\n    async sendCharacter(char) {\n        // Measures the number of code points rather than UTF-16 code units.\n        if ([...char].length > 1) {\n            throw new Error('Cannot send more than 1 character.');\n        }\n        const frame = await this.#page.focusedFrame();\n        await frame.isolatedRealm().evaluate(async (char) => {\n            document.execCommand('insertText', false, char);\n        }, char);\n    }\n}\nconst getBidiButton = (button) => {\n    switch (button) {\n        case MouseButton.Left:\n            return 0;\n        case MouseButton.Middle:\n            return 1;\n        case MouseButton.Right:\n            return 2;\n        case MouseButton.Back:\n            return 3;\n        case MouseButton.Forward:\n            return 4;\n    }\n};\n/**\n * @internal\n */\nexport class BidiMouse extends Mouse {\n    #context;\n    #lastMovePoint = { x: 0, y: 0 };\n    constructor(context) {\n        super();\n        this.#context = context;\n    }\n    async reset() {\n        this.#lastMovePoint = { x: 0, y: 0 };\n        await this.#context.connection.send('input.releaseActions', {\n            context: this.#context.id,\n        });\n    }\n    async move(x, y, options = {}) {\n        const from = this.#lastMovePoint;\n        const to = {\n            x: Math.round(x),\n            y: Math.round(y),\n        };\n        const actions = [];\n        const steps = options.steps ?? 0;\n        for (let i = 0; i < steps; ++i) {\n            actions.push({\n                type: ActionType.PointerMove,\n                x: from.x + (to.x - from.x) * (i / steps),\n                y: from.y + (to.y - from.y) * (i / steps),\n                origin: options.origin,\n            });\n        }\n        actions.push({\n            type: ActionType.PointerMove,\n            ...to,\n            origin: options.origin,\n        });\n        // https://w3c.github.io/webdriver-bidi/#command-input-performActions:~:text=input.PointerMoveAction%20%3D%20%7B%0A%20%20type%3A%20%22pointerMove%22%2C%0A%20%20x%3A%20js%2Dint%2C\n        this.#lastMovePoint = to;\n        await this.#context.connection.send('input.performActions', {\n            context: this.#context.id,\n            actions: [\n                {\n                    type: SourceActionsType.Pointer,\n                    id: \"__puppeteer_mouse\" /* InputId.Mouse */,\n                    actions,\n                },\n            ],\n        });\n    }\n    async down(options = {}) {\n        await this.#context.connection.send('input.performActions', {\n            context: this.#context.id,\n            actions: [\n                {\n                    type: SourceActionsType.Pointer,\n                    id: \"__puppeteer_mouse\" /* InputId.Mouse */,\n                    actions: [\n                        {\n                            type: ActionType.PointerDown,\n                            button: getBidiButton(options.button ?? MouseButton.Left),\n                        },\n                    ],\n                },\n            ],\n        });\n    }\n    async up(options = {}) {\n        await this.#context.connection.send('input.performActions', {\n            context: this.#context.id,\n            actions: [\n                {\n                    type: SourceActionsType.Pointer,\n                    id: \"__puppeteer_mouse\" /* InputId.Mouse */,\n                    actions: [\n                        {\n                            type: ActionType.PointerUp,\n                            button: getBidiButton(options.button ?? MouseButton.Left),\n                        },\n                    ],\n                },\n            ],\n        });\n    }\n    async click(x, y, options = {}) {\n        const actions = [\n            {\n                type: ActionType.PointerMove,\n                x: Math.round(x),\n                y: Math.round(y),\n                origin: options.origin,\n            },\n        ];\n        const pointerDownAction = {\n            type: ActionType.PointerDown,\n            button: getBidiButton(options.button ?? MouseButton.Left),\n        };\n        const pointerUpAction = {\n            type: ActionType.PointerUp,\n            button: pointerDownAction.button,\n        };\n        for (let i = 1; i < (options.count ?? 1); ++i) {\n            actions.push(pointerDownAction, pointerUpAction);\n        }\n        actions.push(pointerDownAction);\n        if (options.delay) {\n            actions.push({\n                type: ActionType.Pause,\n                duration: options.delay,\n            });\n        }\n        actions.push(pointerUpAction);\n        await this.#context.connection.send('input.performActions', {\n            context: this.#context.id,\n            actions: [\n                {\n                    type: SourceActionsType.Pointer,\n                    id: \"__puppeteer_mouse\" /* InputId.Mouse */,\n                    actions,\n                },\n            ],\n        });\n    }\n    async wheel(options = {}) {\n        await this.#context.connection.send('input.performActions', {\n            context: this.#context.id,\n            actions: [\n                {\n                    type: SourceActionsType.Wheel,\n                    id: \"__puppeteer_wheel\" /* InputId.Wheel */,\n                    actions: [\n                        {\n                            type: ActionType.Scroll,\n                            ...(this.#lastMovePoint ?? {\n                                x: 0,\n                                y: 0,\n                            }),\n                            deltaX: options.deltaX ?? 0,\n                            deltaY: options.deltaY ?? 0,\n                        },\n                    ],\n                },\n            ],\n        });\n    }\n    drag() {\n        throw new UnsupportedOperation();\n    }\n    dragOver() {\n        throw new UnsupportedOperation();\n    }\n    dragEnter() {\n        throw new UnsupportedOperation();\n    }\n    drop() {\n        throw new UnsupportedOperation();\n    }\n    dragAndDrop() {\n        throw new UnsupportedOperation();\n    }\n}\n/**\n * @internal\n */\nexport class BidiTouchscreen extends Touchscreen {\n    #context;\n    constructor(context) {\n        super();\n        this.#context = context;\n    }\n    async touchStart(x, y, options = {}) {\n        await this.#context.connection.send('input.performActions', {\n            context: this.#context.id,\n            actions: [\n                {\n                    type: SourceActionsType.Pointer,\n                    id: \"__puppeteer_finger\" /* InputId.Finger */,\n                    parameters: {\n                        pointerType: \"touch\" /* Bidi.Input.PointerType.Touch */,\n                    },\n                    actions: [\n                        {\n                            type: ActionType.PointerMove,\n                            x: Math.round(x),\n                            y: Math.round(y),\n                            origin: options.origin,\n                        },\n                        {\n                            type: ActionType.PointerDown,\n                            button: 0,\n                        },\n                    ],\n                },\n            ],\n        });\n    }\n    async touchMove(x, y, options = {}) {\n        await this.#context.connection.send('input.performActions', {\n            context: this.#context.id,\n            actions: [\n                {\n                    type: SourceActionsType.Pointer,\n                    id: \"__puppeteer_finger\" /* InputId.Finger */,\n                    parameters: {\n                        pointerType: \"touch\" /* Bidi.Input.PointerType.Touch */,\n                    },\n                    actions: [\n                        {\n                            type: ActionType.PointerMove,\n                            x: Math.round(x),\n                            y: Math.round(y),\n                            origin: options.origin,\n                        },\n                    ],\n                },\n            ],\n        });\n    }\n    async touchEnd() {\n        await this.#context.connection.send('input.performActions', {\n            context: this.#context.id,\n            actions: [\n                {\n                    type: SourceActionsType.Pointer,\n                    id: \"__puppeteer_finger\" /* InputId.Finger */,\n                    parameters: {\n                        pointerType: \"touch\" /* Bidi.Input.PointerType.Touch */,\n                    },\n                    actions: [\n                        {\n                            type: ActionType.PointerUp,\n                            button: 0,\n                        },\n                    ],\n                },\n            ],\n        });\n    }\n}\n//# sourceMappingURL=Input.js.map", "import { HTTPRequest } from '../api/HTTPRequest.js';\nimport { UnsupportedOperation } from '../common/Errors.js';\n/**\n * @internal\n */\nexport class BidiHTTPRequest extends HTTPRequest {\n    _response = null;\n    _redirectChain;\n    _navigationId;\n    #url;\n    #resourceType;\n    #method;\n    #postData;\n    #headers = {};\n    #initiator;\n    #frame;\n    constructor(event, frame, redirectChain = []) {\n        super();\n        this.#url = event.request.url;\n        this.#resourceType = event.initiator.type.toLowerCase();\n        this.#method = event.request.method;\n        this.#postData = undefined;\n        this.#initiator = event.initiator;\n        this.#frame = frame;\n        this._requestId = event.request.request;\n        this._redirectChain = redirectChain;\n        this._navigationId = event.navigation;\n        for (const header of event.request.headers) {\n            // TODO: How to handle Binary Headers\n            // https://w3c.github.io/webdriver-bidi/#type-network-Header\n            if (header.value.type === 'string') {\n                this.#headers[header.name.toLowerCase()] = header.value.value;\n            }\n        }\n    }\n    get client() {\n        throw new UnsupportedOperation();\n    }\n    url() {\n        return this.#url;\n    }\n    resourceType() {\n        return this.#resourceType;\n    }\n    method() {\n        return this.#method;\n    }\n    postData() {\n        return this.#postData;\n    }\n    hasPostData() {\n        return this.#postData !== undefined;\n    }\n    async fetchPostData() {\n        return this.#postData;\n    }\n    headers() {\n        return this.#headers;\n    }\n    response() {\n        return this._response;\n    }\n    isNavigationRequest() {\n        return Boolean(this._navigationId);\n    }\n    initiator() {\n        return this.#initiator;\n    }\n    redirectChain() {\n        return this._redirectChain.slice();\n    }\n    enqueueInterceptAction(pendingHandler) {\n        // Execute the handler when interception is not supported\n        void pendingHandler();\n    }\n    frame() {\n        return this.#frame;\n    }\n    continueRequestOverrides() {\n        throw new UnsupportedOperation();\n    }\n    continue(_overrides = {}) {\n        throw new UnsupportedOperation();\n    }\n    responseForRequest() {\n        throw new UnsupportedOperation();\n    }\n    abortErrorReason() {\n        throw new UnsupportedOperation();\n    }\n    interceptResolutionState() {\n        throw new UnsupportedOperation();\n    }\n    isInterceptResolutionHandled() {\n        throw new UnsupportedOperation();\n    }\n    finalizeInterceptions() {\n        throw new UnsupportedOperation();\n    }\n    abort() {\n        throw new UnsupportedOperation();\n    }\n    respond(_response, _priority) {\n        throw new UnsupportedOperation();\n    }\n    failure() {\n        throw new UnsupportedOperation();\n    }\n}\n//# sourceMappingURL=HTTPRequest.js.map", "import { HTTPResponse as HTTPResponse, } from '../api/HTTPResponse.js';\nimport { UnsupportedOperation } from '../common/Errors.js';\n/**\n * @internal\n */\nexport class BidiHTTPResponse extends HTTPResponse {\n    #request;\n    #remoteAddress;\n    #status;\n    #statusText;\n    #url;\n    #fromCache;\n    #headers = {};\n    #timings;\n    constructor(request, { response }) {\n        super();\n        this.#request = request;\n        this.#remoteAddress = {\n            ip: '',\n            port: -1,\n        };\n        this.#url = response.url;\n        this.#fromCache = response.fromCache;\n        this.#status = response.status;\n        this.#statusText = response.statusText;\n        // TODO: File and issue with BiDi spec\n        this.#timings = null;\n        // TODO: Removed once the Firefox implementation is compliant with https://w3c.github.io/webdriver-bidi/#get-the-response-data.\n        for (const header of response.headers || []) {\n            // TODO: How to handle Binary Headers\n            // https://w3c.github.io/webdriver-bidi/#type-network-Header\n            if (header.value.type === 'string') {\n                this.#headers[header.name.toLowerCase()] = header.value.value;\n            }\n        }\n    }\n    remoteAddress() {\n        return this.#remoteAddress;\n    }\n    url() {\n        return this.#url;\n    }\n    status() {\n        return this.#status;\n    }\n    statusText() {\n        return this.#statusText;\n    }\n    headers() {\n        return this.#headers;\n    }\n    request() {\n        return this.#request;\n    }\n    fromCache() {\n        return this.#fromCache;\n    }\n    timing() {\n        return this.#timings;\n    }\n    frame() {\n        return this.#request.frame();\n    }\n    fromServiceWorker() {\n        return false;\n    }\n    securityDetails() {\n        throw new UnsupportedOperation();\n    }\n    buffer() {\n        throw new UnsupportedOperation();\n    }\n}\n//# sourceMappingURL=HTTPResponse.js.map", "/**\n * @license\n * Copyright 2023 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nimport { EventEmitter, EventSubscription } from '../common/EventEmitter.js';\nimport { NetworkManagerEvent, } from '../common/NetworkManagerEvents.js';\nimport { DisposableStack } from '../util/disposable.js';\nimport { BidiHTTPRequest } from './HTTPRequest.js';\nimport { BidiHTTPResponse } from './HTTPResponse.js';\n/**\n * @internal\n */\nexport class BidiNetworkManager extends EventEmitter {\n    #connection;\n    #page;\n    #subscriptions = new DisposableStack();\n    #requestMap = new Map();\n    #navigationMap = new Map();\n    constructor(connection, page) {\n        super();\n        this.#connection = connection;\n        this.#page = page;\n        // TODO: Subscribe to the Frame individually\n        this.#subscriptions.use(new EventSubscription(this.#connection, 'network.beforeRequestSent', this.#onBeforeRequestSent.bind(this)));\n        this.#subscriptions.use(new EventSubscription(this.#connection, 'network.responseStarted', this.#onResponseStarted.bind(this)));\n        this.#subscriptions.use(new EventSubscription(this.#connection, 'network.responseCompleted', this.#onResponseCompleted.bind(this)));\n        this.#subscriptions.use(new EventSubscription(this.#connection, 'network.fetchError', this.#onFetchError.bind(this)));\n    }\n    #onBeforeRequestSent(event) {\n        const frame = this.#page.frame(event.context ?? '');\n        if (!frame) {\n            return;\n        }\n        const request = this.#requestMap.get(event.request.request);\n        let upsertRequest;\n        if (request) {\n            request._redirectChain.push(request);\n            upsertRequest = new BidiHTTPRequest(event, frame, request._redirectChain);\n        }\n        else {\n            upsertRequest = new BidiHTTPRequest(event, frame, []);\n        }\n        this.#requestMap.set(event.request.request, upsertRequest);\n        this.emit(NetworkManagerEvent.Request, upsertRequest);\n    }\n    #onResponseStarted(_event) { }\n    #onResponseCompleted(event) {\n        const request = this.#requestMap.get(event.request.request);\n        if (!request) {\n            return;\n        }\n        const response = new BidiHTTPResponse(request, event);\n        request._response = response;\n        if (event.navigation) {\n            this.#navigationMap.set(event.navigation, response);\n        }\n        if (response.fromCache()) {\n            this.emit(NetworkManagerEvent.RequestServedFromCache, request);\n        }\n        this.emit(NetworkManagerEvent.Response, response);\n        this.emit(NetworkManagerEvent.RequestFinished, request);\n    }\n    #onFetchError(event) {\n        const request = this.#requestMap.get(event.request.request);\n        if (!request) {\n            return;\n        }\n        request._failureText = event.errorText;\n        this.emit(NetworkManagerEvent.RequestFailed, request);\n        this.#requestMap.delete(event.request.request);\n    }\n    getNavigationResponse(navigationId) {\n        if (!navigationId) {\n            return null;\n        }\n        const response = this.#navigationMap.get(navigationId);\n        return response ?? null;\n    }\n    inFlightRequestsCount() {\n        let inFlightRequestCounter = 0;\n        for (const request of this.#requestMap.values()) {\n            if (!request.response() || request._failureText) {\n                inFlightRequestCounter++;\n            }\n        }\n        return inFlightRequestCounter;\n    }\n    clearMapAfterFrameDispose(frame) {\n        for (const [id, request] of this.#requestMap.entries()) {\n            if (request.frame() === frame) {\n                this.#requestMap.delete(id);\n            }\n        }\n        for (const [id, response] of this.#navigationMap.entries()) {\n            if (response.frame() === frame) {\n                this.#navigationMap.delete(id);\n            }\n        }\n    }\n    dispose() {\n        this.removeAllListeners();\n        this.#requestMap.clear();\n        this.#navigationMap.clear();\n        this.#subscriptions.dispose();\n    }\n}\n//# sourceMappingURL=NetworkManager.js.map", "/**\n * @license\n * Copyright 2022 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nvar __addDisposableResource = (this && this.__addDisposableResource) || function (env, value, async) {\n    if (value !== null && value !== void 0) {\n        if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n        var dispose;\n        if (async) {\n            if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n            dispose = value[Symbol.asyncDispose];\n        }\n        if (dispose === void 0) {\n            if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n            dispose = value[Symbol.dispose];\n        }\n        if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n        env.stack.push({ value: value, dispose: dispose, async: async });\n    }\n    else if (async) {\n        env.stack.push({ async: true });\n    }\n    return value;\n};\nvar __disposeResources = (this && this.__disposeResources) || (function (SuppressedError) {\n    return function (env) {\n        function fail(e) {\n            env.error = env.hasError ? new SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n            env.hasError = true;\n        }\n        function next() {\n            while (env.stack.length) {\n                var rec = env.stack.pop();\n                try {\n                    var result = rec.dispose && rec.dispose.call(rec.value);\n                    if (rec.async) return Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n                }\n                catch (e) {\n                    fail(e);\n                }\n            }\n            if (env.hasError) throw env.error;\n        }\n        return next();\n    };\n})(typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n    var e = new Error(message);\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n});\nimport { firstValueFrom, from, map, raceWith, zip, } from '../../third_party/rxjs/rxjs.js';\nimport { Page, } from '../api/Page.js';\nimport { Accessibility } from '../cdp/Accessibility.js';\nimport { Coverage } from '../cdp/Coverage.js';\nimport { EmulationManager as CdpEmulationManager } from '../cdp/EmulationManager.js';\nimport { FrameTree } from '../cdp/FrameTree.js';\nimport { Tracing } from '../cdp/Tracing.js';\nimport { ConsoleMessage, } from '../common/ConsoleMessage.js';\nimport { TargetCloseError, UnsupportedOperation } from '../common/Errors.js';\nimport { NetworkManagerEvent } from '../common/NetworkManagerEvents.js';\nimport { debugError, evaluationString, NETWORK_IDLE_TIME, parsePDFOptions, timeout, validateDialogType, } from '../common/util.js';\nimport { assert } from '../util/assert.js';\nimport { Deferred } from '../util/Deferred.js';\nimport { disposeSymbol } from '../util/disposable.js';\nimport { isErrorLike } from '../util/ErrorLike.js';\nimport { BrowsingContextEvent, CdpSessionWrapper, } from './BrowsingContext.js';\nimport { BidiDeserializer } from './Deserializer.js';\nimport { BidiDialog } from './Dialog.js';\nimport { BidiElementHandle } from './ElementHandle.js';\nimport { EmulationManager } from './EmulationManager.js';\nimport { BidiFrame } from './Frame.js';\nimport { BidiKeyboard, BidiMouse, BidiTouchscreen } from './Input.js';\nimport { getBiDiReadinessState, rewriteNavigationError } from './lifecycle.js';\nimport { BidiNetworkManager } from './NetworkManager.js';\nimport { createBidiHandle } from './Realm.js';\n/**\n * @internal\n */\nexport class BidiPage extends Page {\n    #accessibility;\n    #connection;\n    #frameTree = new FrameTree();\n    #networkManager;\n    #viewport = null;\n    #closedDeferred = Deferred.create();\n    #subscribedEvents = new Map([\n        ['log.entryAdded', this.#onLogEntryAdded.bind(this)],\n        ['browsingContext.load', this.#onFrameLoaded.bind(this)],\n        [\n            'browsingContext.fragmentNavigated',\n            this.#onFrameFragmentNavigated.bind(this),\n        ],\n        [\n            'browsingContext.domContentLoaded',\n            this.#onFrameDOMContentLoaded.bind(this),\n        ],\n        ['browsingContext.userPromptOpened', this.#onDialog.bind(this)],\n    ]);\n    #networkManagerEvents = [\n        [\n            NetworkManagerEvent.Request,\n            (request) => {\n                this.emit(\"request\" /* PageEvent.Request */, request);\n            },\n        ],\n        [\n            NetworkManagerEvent.RequestServedFromCache,\n            (request) => {\n                this.emit(\"requestservedfromcache\" /* PageEvent.RequestServedFromCache */, request);\n            },\n        ],\n        [\n            NetworkManagerEvent.RequestFailed,\n            (request) => {\n                this.emit(\"requestfailed\" /* PageEvent.RequestFailed */, request);\n            },\n        ],\n        [\n            NetworkManagerEvent.RequestFinished,\n            (request) => {\n                this.emit(\"requestfinished\" /* PageEvent.RequestFinished */, request);\n            },\n        ],\n        [\n            NetworkManagerEvent.Response,\n            (response) => {\n                this.emit(\"response\" /* PageEvent.Response */, response);\n            },\n        ],\n    ];\n    #browsingContextEvents = new Map([\n        [BrowsingContextEvent.Created, this.#onContextCreated.bind(this)],\n        [BrowsingContextEvent.Destroyed, this.#onContextDestroyed.bind(this)],\n    ]);\n    #tracing;\n    #coverage;\n    #cdpEmulationManager;\n    #emulationManager;\n    #mouse;\n    #touchscreen;\n    #keyboard;\n    #browsingContext;\n    #browserContext;\n    #target;\n    _client() {\n        return this.mainFrame().context().cdpSession;\n    }\n    constructor(browsingContext, browserContext, target) {\n        super();\n        this.#browsingContext = browsingContext;\n        this.#browserContext = browserContext;\n        this.#target = target;\n        this.#connection = browsingContext.connection;\n        for (const [event, subscriber] of this.#browsingContextEvents) {\n            this.#browsingContext.on(event, subscriber);\n        }\n        this.#networkManager = new BidiNetworkManager(this.#connection, this);\n        for (const [event, subscriber] of this.#subscribedEvents) {\n            this.#connection.on(event, subscriber);\n        }\n        for (const [event, subscriber] of this.#networkManagerEvents) {\n            // TODO: remove any\n            this.#networkManager.on(event, subscriber);\n        }\n        const frame = new BidiFrame(this, this.#browsingContext, this._timeoutSettings, this.#browsingContext.parent);\n        this.#frameTree.addFrame(frame);\n        this.emit(\"frameattached\" /* PageEvent.FrameAttached */, frame);\n        // TODO: https://github.com/w3c/webdriver-bidi/issues/443\n        this.#accessibility = new Accessibility(this.mainFrame().context().cdpSession);\n        this.#tracing = new Tracing(this.mainFrame().context().cdpSession);\n        this.#coverage = new Coverage(this.mainFrame().context().cdpSession);\n        this.#cdpEmulationManager = new CdpEmulationManager(this.mainFrame().context().cdpSession);\n        this.#emulationManager = new EmulationManager(browsingContext);\n        this.#mouse = new BidiMouse(this.mainFrame().context());\n        this.#touchscreen = new BidiTouchscreen(this.mainFrame().context());\n        this.#keyboard = new BidiKeyboard(this);\n    }\n    /**\n     * @internal\n     */\n    get connection() {\n        return this.#connection;\n    }\n    async setUserAgent(userAgent, userAgentMetadata) {\n        // TODO: handle CDP-specific cases such as mprach.\n        await this._client().send('Network.setUserAgentOverride', {\n            userAgent: userAgent,\n            userAgentMetadata: userAgentMetadata,\n        });\n    }\n    async setBypassCSP(enabled) {\n        // TODO: handle CDP-specific cases such as mprach.\n        await this._client().send('Page.setBypassCSP', { enabled });\n    }\n    async queryObjects(prototypeHandle) {\n        assert(!prototypeHandle.disposed, 'Prototype JSHandle is disposed!');\n        assert(prototypeHandle.id, 'Prototype JSHandle must not be referencing primitive value');\n        const response = await this.mainFrame().client.send('Runtime.queryObjects', {\n            prototypeObjectId: prototypeHandle.id,\n        });\n        return createBidiHandle(this.mainFrame().mainRealm(), {\n            type: 'array',\n            handle: response.objects.objectId,\n        });\n    }\n    _setBrowserContext(browserContext) {\n        this.#browserContext = browserContext;\n    }\n    get accessibility() {\n        return this.#accessibility;\n    }\n    get tracing() {\n        return this.#tracing;\n    }\n    get coverage() {\n        return this.#coverage;\n    }\n    get mouse() {\n        return this.#mouse;\n    }\n    get touchscreen() {\n        return this.#touchscreen;\n    }\n    get keyboard() {\n        return this.#keyboard;\n    }\n    browser() {\n        return this.browserContext().browser();\n    }\n    browserContext() {\n        return this.#browserContext;\n    }\n    mainFrame() {\n        const mainFrame = this.#frameTree.getMainFrame();\n        assert(mainFrame, 'Requesting main frame too early!');\n        return mainFrame;\n    }\n    /**\n     * @internal\n     */\n    async focusedFrame() {\n        const env_1 = { stack: [], error: void 0, hasError: false };\n        try {\n            const frame = __addDisposableResource(env_1, await this.mainFrame()\n                .isolatedRealm()\n                .evaluateHandle(() => {\n                let frame;\n                let win = window;\n                while (win?.document.activeElement instanceof HTMLIFrameElement) {\n                    frame = win.document.activeElement;\n                    win = frame.contentWindow;\n                }\n                return frame;\n            }), false);\n            if (!(frame instanceof BidiElementHandle)) {\n                return this.mainFrame();\n            }\n            return await frame.contentFrame();\n        }\n        catch (e_1) {\n            env_1.error = e_1;\n            env_1.hasError = true;\n        }\n        finally {\n            __disposeResources(env_1);\n        }\n    }\n    frames() {\n        return Array.from(this.#frameTree.frames());\n    }\n    frame(frameId) {\n        return this.#frameTree.getById(frameId ?? '') || null;\n    }\n    childFrames(frameId) {\n        return this.#frameTree.childFrames(frameId);\n    }\n    #onFrameLoaded(info) {\n        const frame = this.frame(info.context);\n        if (frame && this.mainFrame() === frame) {\n            this.emit(\"load\" /* PageEvent.Load */, undefined);\n        }\n    }\n    #onFrameFragmentNavigated(info) {\n        const frame = this.frame(info.context);\n        if (frame) {\n            this.emit(\"framenavigated\" /* PageEvent.FrameNavigated */, frame);\n        }\n    }\n    #onFrameDOMContentLoaded(info) {\n        const frame = this.frame(info.context);\n        if (frame) {\n            frame._hasStartedLoading = true;\n            if (this.mainFrame() === frame) {\n                this.emit(\"domcontentloaded\" /* PageEvent.DOMContentLoaded */, undefined);\n            }\n            this.emit(\"framenavigated\" /* PageEvent.FrameNavigated */, frame);\n        }\n    }\n    #onContextCreated(context) {\n        if (!this.frame(context.id) &&\n            (this.frame(context.parent ?? '') || !this.#frameTree.getMainFrame())) {\n            const frame = new BidiFrame(this, context, this._timeoutSettings, context.parent);\n            this.#frameTree.addFrame(frame);\n            if (frame !== this.mainFrame()) {\n                this.emit(\"frameattached\" /* PageEvent.FrameAttached */, frame);\n            }\n        }\n    }\n    #onContextDestroyed(context) {\n        const frame = this.frame(context.id);\n        if (frame) {\n            if (frame === this.mainFrame()) {\n                this.emit(\"close\" /* PageEvent.Close */, undefined);\n            }\n            this.#removeFramesRecursively(frame);\n        }\n    }\n    #removeFramesRecursively(frame) {\n        for (const child of frame.childFrames()) {\n            this.#removeFramesRecursively(child);\n        }\n        frame[disposeSymbol]();\n        this.#networkManager.clearMapAfterFrameDispose(frame);\n        this.#frameTree.removeFrame(frame);\n        this.emit(\"framedetached\" /* PageEvent.FrameDetached */, frame);\n    }\n    #onLogEntryAdded(event) {\n        const frame = this.frame(event.source.context);\n        if (!frame) {\n            return;\n        }\n        if (isConsoleLogEntry(event)) {\n            const args = event.args.map(arg => {\n                return createBidiHandle(frame.mainRealm(), arg);\n            });\n            const text = args\n                .reduce((value, arg) => {\n                const parsedValue = arg.isPrimitiveValue\n                    ? BidiDeserializer.deserialize(arg.remoteValue())\n                    : arg.toString();\n                return `${value} ${parsedValue}`;\n            }, '')\n                .slice(1);\n            this.emit(\"console\" /* PageEvent.Console */, new ConsoleMessage(event.method, text, args, getStackTraceLocations(event.stackTrace)));\n        }\n        else if (isJavaScriptLogEntry(event)) {\n            const error = new Error(event.text ?? '');\n            const messageHeight = error.message.split('\\n').length;\n            const messageLines = error.stack.split('\\n').splice(0, messageHeight);\n            const stackLines = [];\n            if (event.stackTrace) {\n                for (const frame of event.stackTrace.callFrames) {\n                    // Note we need to add `1` because the values are 0-indexed.\n                    stackLines.push(`    at ${frame.functionName || '<anonymous>'} (${frame.url}:${frame.lineNumber + 1}:${frame.columnNumber + 1})`);\n                    if (stackLines.length >= Error.stackTraceLimit) {\n                        break;\n                    }\n                }\n            }\n            error.stack = [...messageLines, ...stackLines].join('\\n');\n            this.emit(\"pageerror\" /* PageEvent.PageError */, error);\n        }\n        else {\n            debugError(`Unhandled LogEntry with type \"${event.type}\", text \"${event.text}\" and level \"${event.level}\"`);\n        }\n    }\n    #onDialog(event) {\n        const frame = this.frame(event.context);\n        if (!frame) {\n            return;\n        }\n        const type = validateDialogType(event.type);\n        const dialog = new BidiDialog(frame.context(), type, event.message, event.defaultValue);\n        this.emit(\"dialog\" /* PageEvent.Dialog */, dialog);\n    }\n    getNavigationResponse(id) {\n        return this.#networkManager.getNavigationResponse(id);\n    }\n    isClosed() {\n        return this.#closedDeferred.finished();\n    }\n    async close(options) {\n        if (this.#closedDeferred.finished()) {\n            return;\n        }\n        this.#closedDeferred.reject(new TargetCloseError('Page closed!'));\n        this.#networkManager.dispose();\n        await this.#connection.send('browsingContext.close', {\n            context: this.mainFrame()._id,\n            promptUnload: options?.runBeforeUnload ?? false,\n        });\n        this.emit(\"close\" /* PageEvent.Close */, undefined);\n        this.removeAllListeners();\n    }\n    async reload(options = {}) {\n        const { waitUntil = 'load', timeout: ms = this._timeoutSettings.navigationTimeout(), } = options;\n        const [readiness, networkIdle] = getBiDiReadinessState(waitUntil);\n        const result$ = zip(from(this.#connection.send('browsingContext.reload', {\n            context: this.mainFrame()._id,\n            wait: readiness,\n        })), ...(networkIdle !== null\n            ? [\n                this.waitForNetworkIdle$({\n                    timeout: ms,\n                    concurrency: networkIdle === 'networkidle2' ? 2 : 0,\n                    idleTime: NETWORK_IDLE_TIME,\n                }),\n            ]\n            : [])).pipe(map(([{ result }]) => {\n            return result;\n        }), raceWith(timeout(ms), from(this.#closedDeferred.valueOrThrow())), rewriteNavigationError(this.url(), ms));\n        const result = await firstValueFrom(result$);\n        return this.getNavigationResponse(result.navigation);\n    }\n    setDefaultNavigationTimeout(timeout) {\n        this._timeoutSettings.setDefaultNavigationTimeout(timeout);\n    }\n    setDefaultTimeout(timeout) {\n        this._timeoutSettings.setDefaultTimeout(timeout);\n    }\n    getDefaultTimeout() {\n        return this._timeoutSettings.timeout();\n    }\n    isJavaScriptEnabled() {\n        return this.#cdpEmulationManager.javascriptEnabled;\n    }\n    async setGeolocation(options) {\n        return await this.#cdpEmulationManager.setGeolocation(options);\n    }\n    async setJavaScriptEnabled(enabled) {\n        return await this.#cdpEmulationManager.setJavaScriptEnabled(enabled);\n    }\n    async emulateMediaType(type) {\n        return await this.#cdpEmulationManager.emulateMediaType(type);\n    }\n    async emulateCPUThrottling(factor) {\n        return await this.#cdpEmulationManager.emulateCPUThrottling(factor);\n    }\n    async emulateMediaFeatures(features) {\n        return await this.#cdpEmulationManager.emulateMediaFeatures(features);\n    }\n    async emulateTimezone(timezoneId) {\n        return await this.#cdpEmulationManager.emulateTimezone(timezoneId);\n    }\n    async emulateIdleState(overrides) {\n        return await this.#cdpEmulationManager.emulateIdleState(overrides);\n    }\n    async emulateVisionDeficiency(type) {\n        return await this.#cdpEmulationManager.emulateVisionDeficiency(type);\n    }\n    async setViewport(viewport) {\n        if (!this.#browsingContext.supportsCdp()) {\n            await this.#emulationManager.emulateViewport(viewport);\n            this.#viewport = viewport;\n            return;\n        }\n        const needsReload = await this.#cdpEmulationManager.emulateViewport(viewport);\n        this.#viewport = viewport;\n        if (needsReload) {\n            await this.reload();\n        }\n    }\n    viewport() {\n        return this.#viewport;\n    }\n    async pdf(options = {}) {\n        const { timeout: ms = this._timeoutSettings.timeout(), path = undefined } = options;\n        const { printBackground: background, margin, landscape, width, height, pageRanges: ranges, scale, preferCSSPageSize, } = parsePDFOptions(options, 'cm');\n        const pageRanges = ranges ? ranges.split(', ') : [];\n        const { result } = await firstValueFrom(from(this.#connection.send('browsingContext.print', {\n            context: this.mainFrame()._id,\n            background,\n            margin,\n            orientation: landscape ? 'landscape' : 'portrait',\n            page: {\n                width,\n                height,\n            },\n            pageRanges,\n            scale,\n            shrinkToFit: !preferCSSPageSize,\n        })).pipe(raceWith(timeout(ms))));\n        const buffer = Buffer.from(result.data, 'base64');\n        await this._maybeWriteBufferToFile(path, buffer);\n        return buffer;\n    }\n    async createPDFStream(options) {\n        const buffer = await this.pdf(options);\n        try {\n            const { Readable } = await import('stream');\n            return Readable.from(buffer);\n        }\n        catch (error) {\n            if (error instanceof TypeError) {\n                throw new Error('Can only pass a file path in a Node-like environment.');\n            }\n            throw error;\n        }\n    }\n    async _screenshot(options) {\n        const { clip, type, captureBeyondViewport, quality } = options;\n        if (options.omitBackground !== undefined && options.omitBackground) {\n            throw new UnsupportedOperation(`BiDi does not support 'omitBackground'.`);\n        }\n        if (options.optimizeForSpeed !== undefined && options.optimizeForSpeed) {\n            throw new UnsupportedOperation(`BiDi does not support 'optimizeForSpeed'.`);\n        }\n        if (options.fromSurface !== undefined && !options.fromSurface) {\n            throw new UnsupportedOperation(`BiDi does not support 'fromSurface'.`);\n        }\n        if (clip !== undefined && clip.scale !== undefined && clip.scale !== 1) {\n            throw new UnsupportedOperation(`BiDi does not support 'scale' in 'clip'.`);\n        }\n        let box;\n        if (clip) {\n            if (captureBeyondViewport) {\n                box = clip;\n            }\n            else {\n                // The clip is always with respect to the document coordinates, so we\n                // need to convert this to viewport coordinates when we aren't capturing\n                // beyond the viewport.\n                const [pageLeft, pageTop] = await this.evaluate(() => {\n                    if (!window.visualViewport) {\n                        throw new Error('window.visualViewport is not supported.');\n                    }\n                    return [\n                        window.visualViewport.pageLeft,\n                        window.visualViewport.pageTop,\n                    ];\n                });\n                box = {\n                    ...clip,\n                    x: clip.x - pageLeft,\n                    y: clip.y - pageTop,\n                };\n            }\n        }\n        const { result: { data }, } = await this.#connection.send('browsingContext.captureScreenshot', {\n            context: this.mainFrame()._id,\n            origin: captureBeyondViewport ? 'document' : 'viewport',\n            format: {\n                type: `image/${type}`,\n                ...(quality !== undefined ? { quality: quality / 100 } : {}),\n            },\n            ...(box ? { clip: { type: 'box', ...box } } : {}),\n        });\n        return data;\n    }\n    async createCDPSession() {\n        const { sessionId } = await this.mainFrame()\n            .context()\n            .cdpSession.send('Target.attachToTarget', {\n            targetId: this.mainFrame()._id,\n            flatten: true,\n        });\n        return new CdpSessionWrapper(this.mainFrame().context(), sessionId);\n    }\n    async bringToFront() {\n        await this.#connection.send('browsingContext.activate', {\n            context: this.mainFrame()._id,\n        });\n    }\n    async evaluateOnNewDocument(pageFunction, ...args) {\n        const expression = evaluationExpression(pageFunction, ...args);\n        const { result } = await this.#connection.send('script.addPreloadScript', {\n            functionDeclaration: expression,\n            contexts: [this.mainFrame()._id],\n        });\n        return { identifier: result.script };\n    }\n    async removeScriptToEvaluateOnNewDocument(id) {\n        await this.#connection.send('script.removePreloadScript', {\n            script: id,\n        });\n    }\n    async exposeFunction(name, pptrFunction) {\n        return await this.mainFrame().exposeFunction(name, 'default' in pptrFunction ? pptrFunction.default : pptrFunction);\n    }\n    isDragInterceptionEnabled() {\n        return false;\n    }\n    async setCacheEnabled(enabled) {\n        // TODO: handle CDP-specific cases such as mprach.\n        await this._client().send('Network.setCacheDisabled', {\n            cacheDisabled: !enabled,\n        });\n    }\n    isServiceWorkerBypassed() {\n        throw new UnsupportedOperation();\n    }\n    target() {\n        return this.#target;\n    }\n    waitForFileChooser() {\n        throw new UnsupportedOperation();\n    }\n    workers() {\n        throw new UnsupportedOperation();\n    }\n    setRequestInterception() {\n        throw new UnsupportedOperation();\n    }\n    setDragInterception() {\n        throw new UnsupportedOperation();\n    }\n    setBypassServiceWorker() {\n        throw new UnsupportedOperation();\n    }\n    setOfflineMode() {\n        throw new UnsupportedOperation();\n    }\n    emulateNetworkConditions() {\n        throw new UnsupportedOperation();\n    }\n    cookies() {\n        throw new UnsupportedOperation();\n    }\n    setCookie() {\n        throw new UnsupportedOperation();\n    }\n    deleteCookie() {\n        throw new UnsupportedOperation();\n    }\n    removeExposedFunction() {\n        // TODO: Quick win?\n        throw new UnsupportedOperation();\n    }\n    authenticate() {\n        throw new UnsupportedOperation();\n    }\n    setExtraHTTPHeaders() {\n        throw new UnsupportedOperation();\n    }\n    metrics() {\n        throw new UnsupportedOperation();\n    }\n    async goBack(options = {}) {\n        return await this.#go(-1, options);\n    }\n    async goForward(options = {}) {\n        return await this.#go(+1, options);\n    }\n    async #go(delta, options) {\n        try {\n            const result = await Promise.all([\n                this.waitForNavigation(options),\n                this.#connection.send('browsingContext.traverseHistory', {\n                    delta,\n                    context: this.mainFrame()._id,\n                }),\n            ]);\n            return result[0];\n        }\n        catch (err) {\n            // TODO: waitForNavigation should be cancelled if an error happens.\n            if (isErrorLike(err)) {\n                if (err.message.includes('no such history entry')) {\n                    return null;\n                }\n            }\n            throw err;\n        }\n    }\n    waitForDevicePrompt() {\n        throw new UnsupportedOperation();\n    }\n}\nfunction isConsoleLogEntry(event) {\n    return event.type === 'console';\n}\nfunction isJavaScriptLogEntry(event) {\n    return event.type === 'javascript';\n}\nfunction getStackTraceLocations(stackTrace) {\n    const stackTraceLocations = [];\n    if (stackTrace) {\n        for (const callFrame of stackTrace.callFrames) {\n            stackTraceLocations.push({\n                url: callFrame.url,\n                lineNumber: callFrame.lineNumber,\n                columnNumber: callFrame.columnNumber,\n            });\n        }\n    }\n    return stackTraceLocations;\n}\nfunction evaluationExpression(fun, ...args) {\n    return `() => {${evaluationString(fun, ...args)}}`;\n}\n//# sourceMappingURL=Page.js.map", "/**\n * @license\n * Copyright 2023 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nimport { Target, TargetType } from '../api/Target.js';\nimport { UnsupportedOperation } from '../common/Errors.js';\nimport { CdpSessionWrapper } from './BrowsingContext.js';\nimport { BidiPage } from './Page.js';\n/**\n * @internal\n */\nexport class BidiTarget extends Target {\n    _browserContext;\n    constructor(browserContext) {\n        super();\n        this._browserContext = browserContext;\n    }\n    _setBrowserContext(browserContext) {\n        this._browserContext = browserContext;\n    }\n    asPage() {\n        throw new UnsupportedOperation();\n    }\n    browser() {\n        return this._browserContext.browser();\n    }\n    browserContext() {\n        return this._browserContext;\n    }\n    opener() {\n        throw new UnsupportedOperation();\n    }\n    createCDPSession() {\n        throw new UnsupportedOperation();\n    }\n}\n/**\n * @internal\n */\nexport class BiDi<PERSON>rowserTarget extends Target {\n    #browser;\n    constructor(browser) {\n        super();\n        this.#browser = browser;\n    }\n    url() {\n        return '';\n    }\n    type() {\n        return TargetType.BROWSER;\n    }\n    asPage() {\n        throw new UnsupportedOperation();\n    }\n    browser() {\n        return this.#browser;\n    }\n    browserContext() {\n        return this.#browser.defaultBrowserContext();\n    }\n    opener() {\n        throw new UnsupportedOperation();\n    }\n    createCDPSession() {\n        throw new UnsupportedOperation();\n    }\n}\n/**\n * @internal\n */\nexport class BiDiBrowsingContextTarget extends BidiTarget {\n    _browsingContext;\n    constructor(browserContext, browsingContext) {\n        super(browserContext);\n        this._browsingContext = browsingContext;\n    }\n    url() {\n        return this._browsingContext.url;\n    }\n    async createCDPSession() {\n        const { sessionId } = await this._browsingContext.cdpSession.send('Target.attachToTarget', {\n            targetId: this._browsingContext.id,\n            flatten: true,\n        });\n        return new CdpSessionWrapper(this._browsingContext, sessionId);\n    }\n    type() {\n        return TargetType.PAGE;\n    }\n}\n/**\n * @internal\n */\nexport class BiDiPageTarget extends BiDiBrowsingContextTarget {\n    #page;\n    constructor(browserContext, browsingContext) {\n        super(browserContext, browsingContext);\n        this.#page = new BidiPage(browsingContext, browserContext, this);\n    }\n    async page() {\n        return this.#page;\n    }\n    _setBrowserContext(browserContext) {\n        super._setBrowserContext(browserContext);\n        this.#page._setBrowserContext(browserContext);\n    }\n}\n//# sourceMappingURL=Target.js.map", "/**\n * @license\n * Copyright 2022 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nimport { Browser, } from '../api/Browser.js';\nimport { UnsupportedOperation } from '../common/Errors.js';\nimport { debugError } from '../common/util.js';\nimport { BidiBrowserContext } from './BrowserContext.js';\nimport { BrowsingContext, BrowsingContextEvent } from './BrowsingContext.js';\nimport { Session } from './core/Session.js';\nimport { BiDiBrowserTarget, BiDiBrowsingContextTarget, BiDiPageTarget, } from './Target.js';\n/**\n * @internal\n */\nexport class BidiBrowser extends Browser {\n    protocol = 'webDriverBiDi';\n    // TODO: Update generator to include fully module\n    static subscribeModules = [\n        'browsingContext',\n        'network',\n        'log',\n        'script',\n    ];\n    static subscribeCdpEvents = [\n        // Coverage\n        'cdp.Debugger.scriptParsed',\n        'cdp.CSS.styleSheetAdded',\n        'cdp.Runtime.executionContextsCleared',\n        // Tracing\n        'cdp.Tracing.tracingComplete',\n        // TODO: subscribe to all CDP events in the future.\n        'cdp.Network.requestWillBeSent',\n        'cdp.Debugger.scriptParsed',\n        'cdp.Page.screencastFrame',\n    ];\n    static async create(opts) {\n        const session = await Session.from(opts.connection, {\n            alwaysMatch: {\n                acceptInsecureCerts: opts.ignoreHTTPSErrors,\n                webSocketUrl: true,\n            },\n        });\n        await session.subscribe(session.capabilities.browserName.toLocaleLowerCase().includes('firefox')\n            ? BidiBrowser.subscribeModules\n            : [...BidiBrowser.subscribeModules, ...BidiBrowser.subscribeCdpEvents]);\n        const browser = new BidiBrowser(session.browser, opts);\n        browser.#initialize();\n        await browser.#getTree();\n        return browser;\n    }\n    #process;\n    #closeCallback;\n    #browserCore;\n    #defaultViewport;\n    #targets = new Map();\n    #browserContexts = new WeakMap();\n    #browserTarget;\n    #connectionEventHandlers = new Map([\n        ['browsingContext.contextCreated', this.#onContextCreated.bind(this)],\n        ['browsingContext.contextDestroyed', this.#onContextDestroyed.bind(this)],\n        ['browsingContext.domContentLoaded', this.#onContextDomLoaded.bind(this)],\n        ['browsingContext.fragmentNavigated', this.#onContextNavigation.bind(this)],\n        ['browsingContext.navigationStarted', this.#onContextNavigation.bind(this)],\n    ]);\n    constructor(browserCore, opts) {\n        super();\n        this.#process = opts.process;\n        this.#closeCallback = opts.closeCallback;\n        this.#browserCore = browserCore;\n        this.#defaultViewport = opts.defaultViewport;\n        this.#browserTarget = new BiDiBrowserTarget(this);\n        for (const context of this.#browserCore.userContexts) {\n            this.#createBrowserContext(context);\n        }\n    }\n    #initialize() {\n        this.#browserCore.once('disconnected', () => {\n            this.emit(\"disconnected\" /* BrowserEvent.Disconnected */, undefined);\n        });\n        this.#process?.once('close', () => {\n            this.#browserCore.dispose('Browser process exited.', true);\n            this.connection.dispose();\n        });\n        for (const [eventName, handler] of this.#connectionEventHandlers) {\n            this.connection.on(eventName, handler);\n        }\n    }\n    get #browserName() {\n        return this.#browserCore.session.capabilities.browserName;\n    }\n    get #browserVersion() {\n        return this.#browserCore.session.capabilities.browserVersion;\n    }\n    userAgent() {\n        throw new UnsupportedOperation();\n    }\n    #createBrowserContext(userContext) {\n        const browserContext = new BidiBrowserContext(this, userContext, {\n            defaultViewport: this.#defaultViewport,\n        });\n        this.#browserContexts.set(userContext, browserContext);\n        return browserContext;\n    }\n    #onContextDomLoaded(event) {\n        const target = this.#targets.get(event.context);\n        if (target) {\n            this.emit(\"targetchanged\" /* BrowserEvent.TargetChanged */, target);\n            target.browserContext().emit(\"targetchanged\" /* BrowserContextEvent.TargetChanged */, target);\n        }\n    }\n    #onContextNavigation(event) {\n        const target = this.#targets.get(event.context);\n        if (target) {\n            this.emit(\"targetchanged\" /* BrowserEvent.TargetChanged */, target);\n            target.browserContext().emit(\"targetchanged\" /* BrowserContextEvent.TargetChanged */, target);\n        }\n    }\n    #onContextCreated(event) {\n        const context = new BrowsingContext(this.connection, event, this.#browserName);\n        this.connection.registerBrowsingContexts(context);\n        const browserContext = event.userContext === 'default'\n            ? this.defaultBrowserContext()\n            : this.browserContexts().find(browserContext => {\n                return browserContext.id === event.userContext;\n            });\n        if (!browserContext) {\n            throw new Error('Missing browser contexts');\n        }\n        const target = !context.parent\n            ? new BiDiPageTarget(browserContext, context)\n            : new BiDiBrowsingContextTarget(browserContext, context);\n        this.#targets.set(event.context, target);\n        this.emit(\"targetcreated\" /* BrowserEvent.TargetCreated */, target);\n        target.browserContext().emit(\"targetcreated\" /* BrowserContextEvent.TargetCreated */, target);\n        if (context.parent) {\n            const topLevel = this.connection.getTopLevelContext(context.parent);\n            topLevel.emit(BrowsingContextEvent.Created, context);\n        }\n    }\n    async #getTree() {\n        const { result } = await this.connection.send('browsingContext.getTree', {});\n        for (const context of result.contexts) {\n            this.#onContextCreated(context);\n        }\n    }\n    async #onContextDestroyed(event) {\n        const context = this.connection.getBrowsingContext(event.context);\n        const topLevelContext = this.connection.getTopLevelContext(event.context);\n        topLevelContext.emit(BrowsingContextEvent.Destroyed, context);\n        const target = this.#targets.get(event.context);\n        const page = await target?.page();\n        await page?.close().catch(debugError);\n        this.#targets.delete(event.context);\n        if (target) {\n            this.emit(\"targetdestroyed\" /* BrowserEvent.TargetDestroyed */, target);\n            target.browserContext().emit(\"targetdestroyed\" /* BrowserContextEvent.TargetDestroyed */, target);\n        }\n    }\n    get connection() {\n        // SAFETY: We only have one implementation.\n        return this.#browserCore.session.connection;\n    }\n    wsEndpoint() {\n        return this.connection.url;\n    }\n    async close() {\n        for (const [eventName, handler] of this.#connectionEventHandlers) {\n            this.connection.off(eventName, handler);\n        }\n        if (this.connection.closed) {\n            return;\n        }\n        try {\n            await this.#browserCore.close();\n            await this.#closeCallback?.call(null);\n        }\n        catch (error) {\n            // Fail silently.\n            debugError(error);\n        }\n        finally {\n            this.connection.dispose();\n        }\n    }\n    get connected() {\n        return !this.#browserCore.disposed;\n    }\n    process() {\n        return this.#process ?? null;\n    }\n    async createIncognitoBrowserContext(_options) {\n        const userContext = await this.#browserCore.createUserContext();\n        return this.#createBrowserContext(userContext);\n    }\n    async version() {\n        return `${this.#browserName}/${this.#browserVersion}`;\n    }\n    browserContexts() {\n        return [...this.#browserCore.userContexts].map(context => {\n            return this.#browserContexts.get(context);\n        });\n    }\n    defaultBrowserContext() {\n        return this.#browserContexts.get(this.#browserCore.defaultUserContext);\n    }\n    newPage() {\n        return this.defaultBrowserContext().newPage();\n    }\n    targets() {\n        return [this.#browserTarget, ...Array.from(this.#targets.values())];\n    }\n    _getTargetById(id) {\n        const target = this.#targets.get(id);\n        if (!target) {\n            throw new Error('Target not found');\n        }\n        return target;\n    }\n    target() {\n        return this.#browserTarget;\n    }\n    async disconnect() {\n        try {\n            await this.#browserCore.session.end();\n        }\n        catch (error) {\n            // Fail silently.\n            debugError(error);\n        }\n        finally {\n            this.connection.dispose();\n        }\n    }\n    get debugInfo() {\n        return {\n            pendingProtocolErrors: this.connection.getPendingProtocolErrors(),\n        };\n    }\n}\n//# sourceMappingURL=Browser.js.map"], "names": ["__runInitializers", "__esDecorate", "__addDisposableResource", "__disposeResources", "SuppressedError", "Bidi.ChromiumBidi", "BrowsingContextEvent", "timeout", "BidiMapper.BidiServer", "BidiMapper.EventEmitter", "Navigation", "Request", "UserPrompt", "BrowsingContext", "UserContext", "Browser", "Session", "result", "error", "catchError", "Realm", "zip", "from", "map", "raceWith", "firstValueFrom", "fork<PERSON><PERSON>n", "first", "merge", "SourceActionsType", "ActionType", "char", "CdpEmulationManager", "frame", "browserContext"], "mappings": ";AAAA;AAAA;AAAA;AAAA;AAAA;AASO,MAAM,iBAAiB;AAAA,EAC1B,OAAO,kBAAkB,OAAO;AAC5B,YAAQ,OAAK;AAAA,MACT,KAAK;AACD,eAAO;AAAA,MACX,KAAK;AACD,eAAO;AAAA,MACX,KAAK;AACD,eAAO;AAAA,MACX,KAAK;AACD,eAAO;AAAA,MACX;AACI,eAAO;AAAA,IACvB;AAAA,EACA;AAAA,EACI,OAAO,sBAAsB,QAAQ;AACjC,YAAQ,OAAO,MAAI;AAAA,MACf,KAAK;AACD,eAAO,OAAO,OAAO,IAAI,WAAS;AAC9B,iBAAO,iBAAiB,sBAAsB,KAAK;AAAA,QACvE,CAAiB;AAAA,MACL,KAAK;AACD,eAAO,OAAO,OAAO,OAAO,CAAC,KAAK,UAAU;AACxC,iBAAO,IAAI,IAAI,iBAAiB,sBAAsB,KAAK,CAAC;AAAA,QAChF,GAAmB,oBAAI,IAAG,CAAE;AAAA,MAChB,KAAK;AACD,eAAO,OAAO,OAAO,OAAO,CAAC,KAAK,UAAU;AACxC,gBAAM,EAAE,KAAK,MAAK,IAAK,iBAAiB,iBAAiB,KAAK;AAC9D,cAAI,GAAG,IAAI;AACX,iBAAO;AAAA,QACV,GAAE,EAAE;AAAA,MACT,KAAK;AACD,eAAO,OAAO,OAAO,OAAO,CAAC,KAAK,UAAU;AACxC,gBAAM,EAAE,KAAK,MAAK,IAAK,iBAAiB,iBAAiB,KAAK;AAC9D,iBAAO,IAAI,IAAI,KAAK,KAAK;AAAA,QAC7C,GAAmB,oBAAI,IAAG,CAAE;AAAA,MAChB,KAAK;AACD,eAAO,CAAE;AAAA,MACb,KAAK;AACD,eAAO,IAAI,OAAO,OAAO,MAAM,SAAS,OAAO,MAAM,KAAK;AAAA,MAC9D,KAAK;AACD,eAAO,IAAI,KAAK,OAAO,KAAK;AAAA,MAChC,KAAK;AACD,eAAO;AAAA,MACX,KAAK;AACD,eAAO;AAAA,MACX,KAAK;AACD,eAAO,iBAAiB,kBAAkB,OAAO,KAAK;AAAA,MAC1D,KAAK;AACD,eAAO,OAAO,OAAO,KAAK;AAAA,MAC9B,KAAK;AACD,eAAO,QAAQ,OAAO,KAAK;AAAA,MAC/B,KAAK;AACD,eAAO,OAAO;AAAA,IAC9B;AACQ,eAAW,2BAA2B,OAAO,IAAI,iBAAiB;AAClE,WAAO;AAAA,EACf;AAAA,EACI,OAAO,iBAAiB,CAAC,eAAe,eAAe,GAAG;AACtD,UAAM,MAAM,OAAO,kBAAkB,WAC/B,gBACA,iBAAiB,sBAAsB,aAAa;AAC1D,UAAM,QAAQ,iBAAiB,sBAAsB,eAAe;AACpE,WAAO,EAAE,KAAK,MAAO;AAAA,EAC7B;AAAA,EACI,OAAO,YAAY,QAAQ;AACvB,QAAI,CAAC,QAAQ;AACT,iBAAW,mCAAmC;AAC9C,aAAO;AAAA,IACnB;AACQ,WAAO,iBAAiB,sBAAsB,MAAM;AAAA,EAC5D;AACA;ACjFA;AAAA;AAAA;AAAA;AAAA;AAUO,eAAe,iBAAiB,QAAQ,iBAAiB;AAC5D,MAAI,CAAC,gBAAgB,QAAQ;AACzB;AAAA,EACR;AACI,QAAM,OAAO,WACR,KAAK,iBAAiB;AAAA,IACvB,QAAQ,OAAO;AAAA,IACf,SAAS,CAAC,gBAAgB,MAAM;AAAA,EACnC,CAAA,EACI,MAAM,WAAS;AAGhB,eAAW,KAAK;AAAA,EACxB,CAAK;AACL;AAIO,SAAS,sBAAsB,SAAS;AAC3C,MAAI,QAAQ,UAAU,SAAS,SAAS;AACpC,WAAO,iBAAiB,YAAY,QAAQ,SAAS;AAAA,EAC7D;AACI,QAAM,CAAC,OAAO,IAAI,GAAG,KAAK,IAAI,QAAQ,KAAK,MAAM,IAAI;AACrD,QAAM,UAAU,MAAM,KAAK,IAAI;AAC/B,QAAM,QAAQ,IAAI,MAAM,OAAO;AAC/B,QAAM,OAAO;AAEb,QAAM,aAAa,CAAE;AACrB,MAAI,QAAQ,cAAc,WAAW,SAAS,MAAM,iBAAiB;AACjE,eAAW,SAAS,QAAQ,WAAW,WAAW,QAAO,GAAI;AACzD,UAAI,aAAa,eAAe,MAAM,GAAG,KACrC,MAAM,QAAQ,aAAa,cAAc;AACzC,cAAM,MAAM,aAAa,MAAM,MAAM,GAAG;AACxC,mBAAW,QAAQ,UAAU,MAAM,gBAAgB,IAAI,YAAY,KAAK,IAAI,YAAY,OAAO,IAAI,UAAU,iBAAiB,MAAM,UAAU,IAAI,MAAM,YAAY,GAAG;AAAA,MACvL,OACiB;AACD,mBAAW,KAAK,UAAU,MAAM,gBAAgB,aAAa,KAAK,MAAM,GAAG,IAAI,MAAM,UAAU,IAAI,MAAM,YAAY,GAAG;AAAA,MACxI;AACY,UAAI,WAAW,UAAU,MAAM,iBAAiB;AAC5C;AAAA,MAChB;AAAA,IACA;AAAA,EACA;AACI,QAAM,QAAQ,CAAC,QAAQ,MAAM,GAAG,UAAU,EAAE,KAAK,IAAI;AACrD,SAAO;AACX;ACvDA;AAAA;AAAA;AAAA;AAAA;AAYO,MAAM,qBAAqB,SAAS;AAAA,EACvC,YAAY;AAAA,EACZ;AAAA,EACA;AAAA,EACA,YAAY,SAAS,aAAa;AAC9B,UAAO;AACP,SAAK,WAAW;AAChB,SAAK,eAAe;AAAA,EAC5B;AAAA,EACI,UAAU;AACN,WAAO,KAAK,MAAM,YAAY,QAAS;AAAA,EAC/C;AAAA,EACI,IAAI,QAAQ;AACR,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,IAAI,WAAW;AACX,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,MAAM,YAAY;AACd,WAAO,MAAM,KAAK,SAAS,WAAS;AAChC,aAAO;AAAA,IACnB,CAAS;AAAA,EACT;AAAA,EACI,YAAY;AACR,WAAO;AAAA,EACf;AAAA,EACI,MAAM,UAAU;AACZ,QAAI,KAAK,WAAW;AAChB;AAAA,IACZ;AACQ,SAAK,YAAY;AACjB,QAAI,YAAY,KAAK,cAAc;AAC/B,YAAM,iBAAiB,KAAK,QAAO,GAAI,KAAK,YAAY;AAAA,IACpE;AAAA,EACA;AAAA,EACI,IAAI,mBAAmB;AACnB,YAAQ,KAAK,aAAa,MAAI;AAAA,MAC1B,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,eAAO;AAAA,MACX;AACI,eAAO;AAAA,IACvB;AAAA,EACA;AAAA,EACI,WAAW;AACP,QAAI,KAAK,kBAAkB;AACvB,aAAO,cAAc,iBAAiB,YAAY,KAAK,YAAY;AAAA,IAC/E;AACQ,WAAO,cAAc,KAAK,aAAa;AAAA,EAC/C;AAAA,EACI,IAAI,KAAK;AACL,WAAO,YAAY,KAAK,eAAe,KAAK,aAAa,SAAS;AAAA,EAC1E;AAAA,EACI,cAAc;AACV,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,eAAe;AACX,UAAM,IAAI,qBAAqB,iCAAiC;AAAA,EACxE;AACA;AC3EA;AAAA;AAAA;AAAA;AAAA;AAKA,IAAIA,sBAAwD,SAAU,SAAS,cAAc,OAAO;AAChG,MAAI,WAAW,UAAU,SAAS;AAClC,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC1C,YAAQ,WAAW,aAAa,CAAC,EAAE,KAAK,SAAS,KAAK,IAAI,aAAa,CAAC,EAAE,KAAK,OAAO;AAAA,EAC9F;AACI,SAAO,WAAW,QAAQ;AAC9B;AACA,IAAIC,iBAA8C,SAAU,MAAM,cAAc,YAAY,WAAW,cAAc,mBAAmB;AACpI,WAAS,OAAO,GAAG;AAAE,QAAI,MAAM,UAAU,OAAO,MAAM,WAAY,OAAM,IAAI,UAAU,mBAAmB;AAAG,WAAO;AAAA,EAAE;AACrH,MAAI,OAAO,UAAU,MAAM,MAAM,SAAS,WAAW,QAAQ,SAAS,WAAW,QAAQ;AACzF,MAAI,SAAS,CAAC,gBAAgB,OAAO,UAAU,QAAQ,IAAI,OAAO,KAAK,YAAY;AACnF,MAAI,aAAa,iBAAiB,SAAS,OAAO,yBAAyB,QAAQ,UAAU,IAAI,IAAI;AACrG,MAAI,GAAG,OAAO;AACd,WAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,QAAI,UAAU,CAAE;AAChB,aAAS,KAAK,UAAW,SAAQ,CAAC,IAAI,MAAM,WAAW,CAAA,IAAK,UAAU,CAAC;AACvE,aAAS,KAAK,UAAU,OAAQ,SAAQ,OAAO,CAAC,IAAI,UAAU,OAAO,CAAC;AACtE,YAAQ,iBAAiB,SAAU,GAAG;AAAE,UAAI,KAAM,OAAM,IAAI,UAAU,wDAAwD;AAAG,wBAAkB,KAAK,OAAO,KAAK,IAAI,CAAC;AAAA,IAAI;AAC7K,QAAI,UAAS,GAAI,WAAW,CAAC,GAAG,SAAS,aAAa,EAAE,KAAK,WAAW,KAAK,KAAK,WAAW,IAAK,IAAG,WAAW,GAAG,GAAG,OAAO;AAC7H,QAAI,SAAS,YAAY;AACrB,UAAI,WAAW,OAAQ;AACvB,UAAI,WAAW,QAAQ,OAAO,WAAW,SAAU,OAAM,IAAI,UAAU,iBAAiB;AACxF,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,IAAI,EAAG,cAAa,QAAQ,CAAC;AAAA,IAC/D,WACiB,IAAI,OAAO,MAAM,GAAG;AACzB,UAAI,SAAS,QAAS,cAAa,QAAQ,CAAC;AAAA,UACvC,YAAW,GAAG,IAAI;AAAA,IACnC;AAAA,EACA;AACI,MAAI,OAAQ,QAAO,eAAe,QAAQ,UAAU,MAAM,UAAU;AACpE,SAAO;AACX;AACA,IAAIC,4BAAoE,SAAU,KAAK,OAAO,OAAO;AACjG,MAAI,UAAU,QAAQ,UAAU,QAAQ;AACpC,QAAI,OAAO,UAAU,YAAY,OAAO,UAAU,WAAY,OAAM,IAAI,UAAU,kBAAkB;AACpG,QAAI;AACJ,QAAI,OAAO;AACP,UAAI,CAAC,OAAO,aAAc,OAAM,IAAI,UAAU,qCAAqC;AACnF,gBAAU,MAAM,OAAO,YAAY;AAAA,IAC/C;AACQ,QAAI,YAAY,QAAQ;AACpB,UAAI,CAAC,OAAO,QAAS,OAAM,IAAI,UAAU,gCAAgC;AACzE,gBAAU,MAAM,OAAO,OAAO;AAAA,IAC1C;AACQ,QAAI,OAAO,YAAY,WAAY,OAAM,IAAI,UAAU,wBAAwB;AAC/E,QAAI,MAAM,KAAK,EAAE,OAAc,SAAkB,OAAc;AAAA,EACvE,WACa,OAAO;AACZ,QAAI,MAAM,KAAK,EAAE,OAAO,KAAI,CAAE;AAAA,EACtC;AACI,SAAO;AACX;AACA,IAAIC,uBAA2D,yBAAUC,kBAAiB;AACtF,SAAO,SAAU,KAAK;AAClB,aAAS,KAAK,GAAG;AACb,UAAI,QAAQ,IAAI,WAAW,IAAIA,iBAAgB,GAAG,IAAI,OAAO,0CAA0C,IAAI;AAC3G,UAAI,WAAW;AAAA,IAC3B;AACQ,aAAS,OAAO;AACZ,aAAO,IAAI,MAAM,QAAQ;AACrB,YAAI,MAAM,IAAI,MAAM,IAAK;AACzB,YAAI;AACA,cAAI,SAAS,IAAI,WAAW,IAAI,QAAQ,KAAK,IAAI,KAAK;AACtD,cAAI,IAAI,MAAO,QAAO,QAAQ,QAAQ,MAAM,EAAE,KAAK,MAAM,SAAS,GAAG;AAAE,iBAAK,CAAC;AAAG,mBAAO,KAAM;AAAA,WAAG;AAAA,QACpH,SACuB,GAAG;AACN,eAAK,CAAC;AAAA,QAC1B;AAAA,MACA;AACY,UAAI,IAAI,SAAU,OAAM,IAAI;AAAA,IACxC;AACQ,WAAO,KAAM;AAAA,EAChB;AACL,EAAG,OAAO,oBAAoB,aAAa,kBAAkB,SAAU,OAAO,YAAY,SAAS;AAC/F,MAAI,IAAI,IAAI,MAAM,OAAO;AACzB,SAAO,EAAE,OAAO,mBAAmB,EAAE,QAAQ,OAAO,EAAE,aAAa,YAAY;AACnF,CAAC;AAQE,IAAC,qBAAqB,MAAM;AAC3B,MAAI;AACJ,MAAI,cAAc;AAClB,MAAI,6BAA6B,CAAE;AACnC,MAAI;AACJ,MAAI;AACJ,SAAO,MAAM,0BAA0B,YAAY;AAAA,IAC/C,OAAA;AACI,YAAM,YAAY,OAAO,WAAW,cAAc,OAAO,WAAW,OAAO,OAAO,YAAY,OAAO,QAAQ,KAAK,IAAI,IAAI;AAC1H,6BAAuB,CAAC,iBAAiB;AACzC,iCAA2B,CAAC,gBAAe,IAAK,KAAK,eAAe,mBAAmB,KAAK,EAAE,CAAC;AAC/FH,qBAAa,MAAM,MAAM,sBAAsB,EAAE,MAAM,UAAU,MAAM,YAAY,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,cAAc,KAAK,KAAK,SAAO,IAAI,SAAU,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC9OA,qBAAa,MAAM,MAAM,0BAA0B,EAAE,MAAM,UAAU,MAAM,gBAAgB,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,kBAAkB,KAAK,KAAK,SAAO,IAAI,aAAc,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC9P,UAAI,UAAW,QAAO,eAAe,MAAM,OAAO,UAAU,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,OAAO,WAAW;AAAA,IAClJ;AAAA,IACQ,YAAY,SAAS,aAAa;AAC9B,YAAM,IAAI,aAAa,SAAS,WAAW,CAAC;AAC5CD,0BAAkB,MAAM,0BAA0B;AAAA,IAC9D;AAAA,IACQ,IAAI,QAAQ;AACR,aAAO,KAAK,OAAO;AAAA,IAC/B;AAAA,IACQ,IAAI,QAAQ;AACR,aAAO,KAAK,MAAM;AAAA,IAC9B;AAAA,IACQ,UAAU;AACN,aAAO,KAAK,OAAO,QAAS;AAAA,IACxC;AAAA,IACQ,IAAI,mBAAmB;AACnB,aAAO,KAAK,OAAO;AAAA,IAC/B;AAAA,IACQ,cAAc;AACV,aAAO,KAAK,OAAO,YAAa;AAAA,IAC5C;AAAA,IACQ,MAAM,SAAS,MAAM;AACjB,YAAM,SAAS,KAAK,MAAM;AAC1B,YAAM,WAAW,MAAM,OAAO,KAAK,oBAAoB;AAAA,QACnD,UAAU,KAAK,OAAO;AAAA,MACtC,CAAa;AACD,YAAM,UAAU,SAAS,KAAK;AAC9B,YAAM,UAAU,KAAK,MAAM;AAC3B,YAAM,OAAO,KAAK,oBAAoB;AAAA,QAClC;AAAA,QACA;AAAA,QACA,MAAM,KAAK;AAAA,MAC3B,CAAa;AAAA,IACb;AAAA,IACQ,MAAM,eAAe;AACjB,YAAM,QAAQ,EAAE,OAAO,CAAA,GAAI,OAAO,QAAQ,UAAU,MAAO;AAC3D,UAAI;AACA,cAAM,SAASE,0BAAwB,OAAQ,MAAM,KAAK,eAAe,aAAW;AAChF,cAAI,mBAAmB,mBAAmB;AACtC,mBAAO,QAAQ;AAAA,UACvC;AACoB;AAAA,QACH,CAAA,GAAI,KAAK;AACV,cAAM,QAAQ,OAAO,YAAa;AAClC,YAAI,MAAM,SAAS,UAAU;AACzB,iBAAO,KAAK,MAAM,KAAI,EAAG,MAAM,MAAM,MAAM,OAAO;AAAA,QACtE;AACgB,eAAO;AAAA,MACvB,SACmB,KAAK;AACR,cAAM,QAAQ;AACd,cAAM,WAAW;AAAA,MACjC,UACoB;AACJC,6BAAmB,KAAK;AAAA,MACxC;AAAA,IACA;AAAA,IACQ,aAAa;AACT,YAAM,IAAI,qBAAsB;AAAA,IAC5C;AAAA,EACK;AACL,GAAC;ACrKD;AAAA;AAAA;AAAA;AAAA;AAYA,MAAM,4BAA4B,MAAM;AACxC;AAIO,MAAM,eAAe;AAAA,EACxB,OAAO,gBAAgB,KAAK;AACxB,QAAI;AACJ,QAAI,OAAO,GAAG,KAAK,EAAE,GAAG;AACpB,cAAQ;AAAA,IACpB,WACiB,OAAO,GAAG,KAAK,QAAQ,GAAG;AAC/B,cAAQ;AAAA,IACpB,WACiB,OAAO,GAAG,KAAK,SAAS,GAAG;AAChC,cAAQ;AAAA,IACpB,WACiB,OAAO,GAAG,KAAK,GAAG,GAAG;AAC1B,cAAQ;AAAA,IACpB,OACa;AACD,cAAQ;AAAA,IACpB;AACQ,WAAO;AAAA,MACH,MAAM;AAAA,MACN;AAAA,IACH;AAAA,EACT;AAAA,EACI,OAAO,gBAAgB,KAAK;AACxB,QAAI,QAAQ,MAAM;AACd,aAAO;AAAA,QACH,MAAM;AAAA,MACT;AAAA,IACb,WACiB,MAAM,QAAQ,GAAG,GAAG;AACzB,YAAM,cAAc,IAAI,IAAI,YAAU;AAClC,eAAO,eAAe,qBAAqB,MAAM;AAAA,MACjE,CAAa;AACD,aAAO;AAAA,QACH,MAAM;AAAA,QACN,OAAO;AAAA,MACV;AAAA,IACb,WACiB,cAAc,GAAG,GAAG;AACzB,UAAI;AACA,aAAK,UAAU,GAAG;AAAA,MAClC,SACmB,OAAO;AACV,YAAI,iBAAiB,aACjB,MAAM,QAAQ,WAAW,uCAAuC,GAAG;AACnE,gBAAM,WAAW;AAAA,QACrC;AACgB,cAAM;AAAA,MACtB;AACY,YAAM,eAAe,CAAE;AACvB,iBAAW,OAAO,KAAK;AACnB,qBAAa,KAAK;AAAA,UACd,eAAe,qBAAqB,GAAG;AAAA,UACvC,eAAe,qBAAqB,IAAI,GAAG,CAAC;AAAA,QAChE,CAAiB;AAAA,MACjB;AACY,aAAO;AAAA,QACH,MAAM;AAAA,QACN,OAAO;AAAA,MACV;AAAA,IACb,WACiB,SAAS,GAAG,GAAG;AACpB,aAAO;AAAA,QACH,MAAM;AAAA,QACN,OAAO;AAAA,UACH,SAAS,IAAI;AAAA,UACb,OAAO,IAAI;AAAA,QACd;AAAA,MACJ;AAAA,IACb,WACiB,OAAO,GAAG,GAAG;AAClB,aAAO;AAAA,QACH,MAAM;AAAA,QACN,OAAO,IAAI,YAAa;AAAA,MAC3B;AAAA,IACb;AACQ,UAAM,IAAI,oBAAoB,sEAAsE;AAAA,EAC5G;AAAA,EACI,OAAO,qBAAqB,KAAK;AAC7B,YAAQ,OAAO,KAAG;AAAA,MACd,KAAK;AAAA,MACL,KAAK;AACD,cAAM,IAAI,oBAAoB,0BAA0B,OAAO,GAAG,EAAE;AAAA,MACxE,KAAK;AACD,eAAO,eAAe,gBAAgB,GAAG;AAAA,MAC7C,KAAK;AACD,eAAO;AAAA,UACH,MAAM;AAAA,QACT;AAAA,MACL,KAAK;AACD,eAAO,eAAe,gBAAgB,GAAG;AAAA,MAC7C,KAAK;AACD,eAAO;AAAA,UACH,MAAM;AAAA,UACN,OAAO,IAAI,SAAU;AAAA,QACxB;AAAA,MACL,KAAK;AACD,eAAO;AAAA,UACH,MAAM;AAAA,UACN,OAAO;AAAA,QACV;AAAA,MACL,KAAK;AACD,eAAO;AAAA,UACH,MAAM;AAAA,UACN,OAAO;AAAA,QACV;AAAA,IACjB;AAAA,EACA;AAAA,EACI,aAAa,UAAU,SAAS,KAAK;AACjC,QAAI,eAAe,SAAS;AACxB,YAAM,MAAM,IAAI,IAAI,QAAQ,KAAK;AAAA,IAC7C;AAEQ,UAAM,eAAe,QAAQ,eAAe,gBAAgB,eAAe,qBACrE,MACA;AACN,QAAI,cAAc;AACd,UAAI,aAAa,MAAM,YAAY,QAAS,MACxC,QAAQ,YAAY,WAAW;AAC/B,cAAM,IAAI,MAAM,mEAAmE;AAAA,MACnG;AACY,UAAI,aAAa,UAAU;AACvB,cAAM,IAAI,MAAM,uBAAuB;AAAA,MACvD;AACY,aAAO,aAAa,YAAa;AAAA,IAC7C;AACQ,WAAO,eAAe,qBAAqB,GAAG;AAAA,EACtD;AACA;ACnIO,MAAM,kBAAkB,aAAa;AAAA,EACxC;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,YAAY;AACpB,UAAO;AACP,SAAK,aAAa;AAAA,EAC1B;AAAA,EACI,IAAI,SAAS;AACT,WAAO;AAAA,MACH,SAAS,KAAK,SAAS,YAAY;AAAA,MACnC,SAAS,KAAK,SAAS;AAAA,IAC1B;AAAA,EACT;AAAA,EACI,uBAAuB,OAAO,WAAW;AACrC,QAAI,OAAO,UAAU,KAAK,KAAK;AAG3B,WAAK,wBAAwB;AAC7B,WAAK,SAAS,YAAY,oBAAqB;AAAA,IAC3D;AAAA,EACK;AAAA,EACD,qBAAqB,CAAC,WAAW;AAC7B,QAAI,OAAO,SAAS,YAChB,OAAO,YAAY,KAAK,SAAS,YAAY,OAC7C,OAAO,YAAY,KAAK,SAAS,MAAM;AACvC,WAAK,MAAM,OAAO;AAClB,WAAK,KAAK,SAAS,YAAY,SAAU;AAAA,IACrD;AAAA,EACK;AAAA,EACD,WAAW,SAAS;AAChB,SAAK,WAAW;AAChB,SAAK,WAAW,GAAGE,SAAiB,aAAC,OAAO,WAAW,cAAc,KAAK,kBAAkB;AAC5F,SAAK,WAAW,GAAGA,SAAiB,aAAC,OAAO,WAAW,gBAAgB,KAAK,oBAAoB;AAAA,EACxG;AAAA,EACI;AAAA,EACA,IAAI,gBAAgB;AAChB,UAAM,UAAU,QAAQ,QAAS;AACjC,mBAAe,OAAO,YAAU;AAC5B,UAAI,KAAK,uBAAuB;AAC5B,aAAK,KAAK,sBAAsB,KAAK,YAAU;AAC3C,eAAK,OAAO,QAAS;AAAA,QACzC,CAAiB;AAAA,MACjB;AACY,WAAK,wBAAwB,QAAQ,KAAK,MAAM;AAC5C,eAAO,KAAK,eAAe,MAAM;AAAA,MACjD,CAAa;AAAA,IACb,GAAW,CAAC,KAAK,qBAAqB;AAC9B,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,MAAM,eAAe,iBAAiB,MAAM;AACxC,WAAO,MAAM,KAAK,UAAU,OAAO,cAAc,GAAG,IAAI;AAAA,EAChE;AAAA,EACI,MAAM,SAAS,iBAAiB,MAAM;AAClC,WAAO,MAAM,KAAK,UAAU,MAAM,cAAc,GAAG,IAAI;AAAA,EAC/D;AAAA,EACI,MAAM,UAAU,eAAe,iBAAiB,MAAM;AAClD,UAAM,mBAAmB,oBAAoB,iCAAiC,YAAY,GAAG,SAAU,KACnG,aAAa,YAAY;AAC7B,UAAM,UAAU,KAAK;AACrB,QAAI;AACJ,UAAM,kBAAkB,gBAClB,SACA;AACN,UAAM,uBAAuB,gBACvB,CAAA,IACA;AAAA,MACE,gBAAgB;AAAA,MAChB,aAAa;AAAA,IAChB;AACL,QAAI,SAAS,YAAY,GAAG;AACxB,YAAM,aAAa,iBAAiB,KAAK,YAAY,IAC/C,eACA,GAAG,YAAY;AAAA,EAAK,gBAAgB;AAAA;AAC1C,wBAAkB,KAAK,WAAW,KAAK,mBAAmB;AAAA,QACtD;AAAA,QACA,QAAQ,KAAK;AAAA,QACb;AAAA,QACA,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB;AAAA,MAChB,CAAa;AAAA,IACb,OACa;AACD,UAAI,sBAAsB,kBAAkB,YAAY;AACxD,4BAAsB,iBAAiB,KAAK,mBAAmB,IACzD,sBACA,GAAG,mBAAmB;AAAA,EAAK,gBAAgB;AAAA;AACjD,wBAAkB,KAAK,WAAW,KAAK,uBAAuB;AAAA,QAC1D;AAAA,QACA,WAAW,KAAK,SACV,MAAM,QAAQ,IAAI,KAAK,IAAI,SAAO;AAChC,iBAAO,eAAe,UAAU,SAAS,GAAG;AAAA,QACpE,CAAqB,CAAC,IACA,CAAE;AAAA,QACR,QAAQ,KAAK;AAAA,QACb;AAAA,QACA,cAAc;AAAA,QACd,gBAAgB;AAAA,QAChB;AAAA,MAChB,CAAa;AAAA,IACb;AACQ,UAAM,EAAE,OAAQ,IAAG,MAAM;AACzB,QAAI,UAAU,UAAU,OAAO,SAAS,aAAa;AACjD,YAAM,sBAAsB,OAAO,gBAAgB;AAAA,IAC/D;AACQ,WAAO,gBACD,iBAAiB,YAAY,OAAO,MAAM,IAC1C,iBAAiB,SAAS,OAAO,MAAM;AAAA,EACrD;AAAA,EACI,CAAC,aAAa,IAAI;AACd,SAAK,WAAW,IAAIA,SAAiB,aAAC,OAAO,WAAW,cAAc,KAAK,kBAAkB;AAC7F,SAAK,WAAW,IAAIA,SAAiB,aAAC,OAAO,WAAW,gBAAgB,KAAK,oBAAoB;AAAA,EACzG;AACA;AAIO,SAAS,iBAAiB,SAAS,QAAQ;AAC9C,MAAI,OAAO,SAAS,UAAU,OAAO,SAAS,UAAU;AACpD,WAAO,IAAI,kBAAkB,SAAS,MAAM;AAAA,EACpD;AACI,SAAO,IAAI,aAAa,SAAS,MAAM;AAC3C;ACjIY,MAAC,cAAc,oBAAI,IAAG;AAI3B,MAAM,0BAA0B,WAAW;AAAA,EAC9C;AAAA,EACA,aAAa,SAAS,OAAQ;AAAA,EAC9B,YAAY;AAAA,EACZ,YAAY,SAAS,WAAW;AAC5B,UAAO;AACP,SAAK,WAAW;AAChB,QAAI,CAAC,KAAK,SAAS,eAAe;AAC9B;AAAA,IACZ;AACQ,QAAI,WAAW;AACX,WAAK,WAAW,QAAQ,SAAS;AACjC,kBAAY,IAAI,WAAW,IAAI;AAAA,IAC3C,OACa;AACD,cAAQ,WACH,KAAK,kBAAkB;AAAA,QACxB,SAAS,QAAQ;AAAA,MACpB,CAAA,EACI,KAAK,aAAW;AACjB,aAAK,WAAW,QAAQ,QAAQ,OAAO,OAAO;AAC9C,oBAAY,IAAI,QAAQ,OAAO,SAAS,IAAI;AAAA,MAC/C,CAAA,EACI,MAAM,SAAO;AACd,aAAK,WAAW,OAAO,GAAG;AAAA,MAC1C,CAAa;AAAA,IACb;AAAA,EACA;AAAA,EACI,aAAa;AACT,WAAO;AAAA,EACf;AAAA,EACI,MAAM,KAAK,WAAW,WAAW;AAC7B,QAAI,CAAC,KAAK,SAAS,eAAe;AAC9B,YAAM,IAAI,qBAAqB,qFAAqF;AAAA,IAChI;AACQ,QAAI,KAAK,WAAW;AAChB,YAAM,IAAI,iBAAiB,mBAAmB,MAAM,0DAA0D;AAAA,IAC1H;AACQ,UAAM,UAAU,MAAM,KAAK,WAAW,aAAc;AACpD,UAAM,EAAE,OAAM,IAAK,MAAM,KAAK,SAAS,WAAW,KAAK,mBAAmB;AAAA,MACtE;AAAA,MACA,QAAQ,UAAU,CAAC;AAAA,MACnB;AAAA,IACZ,CAAS;AACD,WAAO,OAAO;AAAA,EACtB;AAAA,EACI,MAAM,SAAS;AACX,gBAAY,OAAO,KAAK,IAAI;AAC5B,QAAI,CAAC,KAAK,aAAa,KAAK,SAAS,YAAW,GAAI;AAChD,YAAM,KAAK,SAAS,WAAW,KAAK,2BAA2B;AAAA,QAC3D,WAAW,KAAK,GAAI;AAAA,MACpC,CAAa;AAAA,IACb;AACQ,SAAK,YAAY;AAAA,EACzB;AAAA,EACI,KAAK;AACD,UAAM,MAAM,KAAK,WAAW,MAAO;AACnC,WAAO,eAAe,SAAS,QAAQ,SAAY,KAAK;AAAA,EAChE;AACA;AAOU,IAAC;AAAA,CACV,SAAUC,uBAAsB;AAI7B,EAAAA,sBAAqB,UAAU,OAAO,yBAAyB;AAK/D,EAAAA,sBAAqB,YAAY,OAAO,2BAA2B;AACvE,GAAG,yBAAyB,uBAAuB,CAAA,EAAG;wBAI/C,MAAM,wBAAwB,UAAU;AAAA,EAC3C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,eAAe;AAAA,EACf,YAAY,YAAY,MAAM,aAAa;AACvC,UAAM,UAAU;AAChB,SAAK,MAAM,KAAK;AAChB,SAAK,OAAO,KAAK;AACjB,SAAK,UAAU,KAAK;AACpB,SAAK,eAAe;AACpB,SAAK,cAAc,IAAI,kBAAkB,MAAM,MAAS;AACxD,SAAK,GAAG,oCAAoC,KAAK,WAAW,KAAK,IAAI,CAAC;AACtE,SAAK,GAAG,qCAAqC,KAAK,WAAW,KAAK,IAAI,CAAC;AACvE,SAAK,GAAG,wBAAwB,KAAK,WAAW,KAAK,IAAI,CAAC;AAAA,EAClE;AAAA,EACI,cAAc;AACV,WAAO,CAAC,KAAK,aAAa,YAAW,EAAG,SAAS,SAAS;AAAA,EAClE;AAAA,EACI,WAAW,MAAM;AACb,SAAK,OAAO,KAAK;AAAA,EACzB;AAAA,EACI,wBAAwB;AACpB,WAAO,IAAI,UAAU,KAAK,UAAU;AAAA,EAC5C;AAAA,EACI,IAAI,MAAM;AACN,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,IAAI,KAAK;AACL,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,IAAI,SAAS;AACT,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,IAAI,aAAa;AACb,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,MAAM,eAAe,WAAW,WAAW;AACvC,WAAO,MAAM,KAAK,YAAY,KAAK,QAAQ,GAAG,SAAS;AAAA,EAC/D;AAAA,EACI,UAAU;AACN,SAAK,mBAAoB;AACzB,SAAK,WAAW,2BAA2B,KAAK,GAAG;AACnD,SAAK,KAAK,YAAY,OAAM,EAAG,MAAM,UAAU;AAAA,EACvD;AACA;AC3IA;AAAA;AAAA;AAAA;AAAA;AAWA,MAAM,oBAAoB,MAAM,gCAAgC;AAChE,MAAM,uBAAuB,MAAM,gCAAgC;AAI5D,MAAM,uBAAuB,aAAa;AAAA,EAC7C;AAAA,EACA;AAAA,EACA;AAAA,EACA,WAAW;AAAA,EACX,UAAU;AAAA,EACV,aAAa,IAAI,iBAAkB;AAAA,EACnC,oBAAoB,oBAAI,IAAK;AAAA,EAC7B,YAAY,CAAE;AAAA,EACd,YAAY,KAAK,WAAW,QAAQ,GAAGC,UAAS;AAC5C,UAAO;AACP,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,WAAWA,YAAW;AAC3B,SAAK,aAAa;AAClB,SAAK,WAAW,YAAY,KAAK,UAAU,KAAK,IAAI;AACpD,SAAK,WAAW,UAAU,KAAK,OAAO,KAAK,IAAI;AAAA,EACvD;AAAA,EACI,IAAI,SAAS;AACT,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,IAAI,MAAM;AACN,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,OAAO,SAAS;AACZ,SAAK,UAAU,KAAK,OAAO;AAAA,EACnC;AAAA,EACI,KAAK,MAAM,OAAO;AACd,eAAW,WAAW,KAAK,WAAW;AAClC,cAAQ,KAAK,MAAM,KAAK;AAAA,IACpC;AACQ,WAAO,MAAM,KAAK,MAAM,KAAK;AAAA,EACrC;AAAA,EACI,KAAK,QAAQ,QAAQ;AACjB,WAAO,CAAC,KAAK,SAAS,oCAAoC;AAC1D,WAAO,KAAK,WAAW,OAAO,QAAQ,KAAK,UAAU,QAAM;AACvD,YAAM,qBAAqB,KAAK,UAAU;AAAA,QACtC;AAAA,QACA;AAAA,QACA;AAAA,MAChB,CAAa;AACD,wBAAkB,kBAAkB;AACpC,WAAK,WAAW,KAAK,kBAAkB;AAAA,IACnD,CAAS;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAII,MAAM,UAAU,SAAS;AACrB,QAAI,KAAK,QAAQ;AACb,YAAM,IAAI,QAAQ,OAAK;AACnB,eAAO,WAAW,GAAG,KAAK,MAAM;AAAA,MAChD,CAAa;AAAA,IACb;AACQ,yBAAqB,OAAO;AAC5B,UAAM,SAAS,KAAK,MAAM,OAAO;AACjC,QAAI,UAAU,QAAQ;AAClB,cAAQ,OAAO,MAAI;AAAA,QACf,KAAK;AACD,eAAK,WAAW,QAAQ,OAAO,IAAI,MAAM;AACzC;AAAA,QACJ,KAAK;AACD,cAAI,OAAO,OAAO,MAAM;AACpB;AAAA,UACxB;AACoB,eAAK,WAAW,OAAO,OAAO,IAAI,oBAAoB,MAAM,GAAG,OAAO,OAAO;AAC7E;AAAA,QACJ,KAAK;AACD,cAAI,WAAW,MAAM,GAAG;AACpB,wBACK,IAAI,OAAO,OAAO,OAAO,GACxB,KAAK,OAAO,OAAO,OAAO,OAAO,OAAO,MAAM;AACpD;AAAA,UACxB;AACoB,eAAK,oBAAoB,MAAM;AAE/B,eAAK,KAAK,OAAO,QAAQ,OAAO,MAAM;AACtC;AAAA,MACpB;AAAA,IACA;AAGQ,QAAI,QAAQ,QAAQ;AAChB,WAAK,WAAW,OAAO,OAAO,IAAI,4DAA4D,OAAO,KAAK,OAAO,OAAO;AAAA,IACpI;AACQ,eAAW,MAAM;AAAA,EACzB;AAAA,EACI,oBAAoB,OAAO;AACvB,QAAI;AAEJ,QAAI,aAAa,MAAM,UAAU,MAAM,OAAO,YAAY,MAAM;AAC5D,gBAAU,KAAK,kBAAkB,IAAI,MAAM,OAAO,OAAO;AAAA,IAErE,WACiB,YAAY,MAAM,UACvB,MAAM,OAAO,OAAO,YAAY,QAAW;AAC3C,gBAAU,KAAK,kBAAkB,IAAI,MAAM,OAAO,OAAO,OAAO;AAAA,IAC5E;AACQ,aAAS,KAAK,MAAM,QAAQ,MAAM,MAAM;AAAA,EAChD;AAAA,EACI,yBAAyB,SAAS;AAC9B,SAAK,kBAAkB,IAAI,QAAQ,IAAI,OAAO;AAAA,EACtD;AAAA,EACI,mBAAmB,WAAW;AAC1B,UAAM,iBAAiB,KAAK,kBAAkB,IAAI,SAAS;AAC3D,QAAI,CAAC,gBAAgB;AACjB,YAAM,IAAI,MAAM,mBAAmB,SAAS,kBAAkB;AAAA,IAC1E;AACQ,WAAO;AAAA,EACf;AAAA,EACI,mBAAmB,WAAW;AAC1B,QAAI,iBAAiB,KAAK,kBAAkB,IAAI,SAAS;AACzD,QAAI,CAAC,gBAAgB;AACjB,YAAM,IAAI,MAAM,mBAAmB,SAAS,kBAAkB;AAAA,IAC1E;AACQ,WAAO,eAAe,QAAQ;AAC1B,kBAAY,eAAe;AAC3B,uBAAiB,KAAK,kBAAkB,IAAI,SAAS;AACrD,UAAI,CAAC,gBAAgB;AACjB,cAAM,IAAI,MAAM,mBAAmB,SAAS,kBAAkB;AAAA,MAC9E;AAAA,IACA;AACQ,WAAO;AAAA,EACf;AAAA,EACI,2BAA2B,IAAI;AAC3B,SAAK,kBAAkB,OAAO,EAAE;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMI,SAAS;AACL,QAAI,KAAK,SAAS;AACd;AAAA,IACZ;AACQ,SAAK,UAAU;AAEf,SAAK,WAAW,YAAY,MAAM;AAAA,IAAG;AACrC,SAAK,WAAW,UAAU,MAAM;AAAA,IAAG;AACnC,SAAK,kBAAkB,MAAO;AAC9B,SAAK,WAAW,MAAO;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAII,UAAU;AACN,SAAK,OAAQ;AACb,SAAK,WAAW,MAAO;AAAA,EAC/B;AAAA,EACI,2BAA2B;AACvB,WAAO,KAAK,WAAW,yBAA0B;AAAA,EACzD;AACA;AAIA,SAAS,oBAAoB,QAAQ;AACjC,MAAI,UAAU,GAAG,OAAO,KAAK,IAAI,OAAO,OAAO;AAC/C,MAAI,OAAO,YAAY;AACnB,eAAW,IAAI,OAAO,UAAU;AAAA,EACxC;AACI,SAAO;AACX;AACA,SAAS,WAAW,OAAO;AACvB,SAAO,MAAM,OAAO,WAAW,MAAM;AACzC;ACtLA;AAAA;AAAA;AAAA;AAAA;AASA,MAAM,mBAAmB,CAAC,WAAW,SAAS;AAC1C,QAAM,QAAQ,MAAM,EAAE,EAAE,IAAI;AAChC;AAIO,eAAe,mBAAmB,KAGzC,SAAS;AACL,QAAM,gBAAgB,IAAI,cAAe;AACzC,QAAM,uBAAuB,IAAI,qBAAqB,GAAG;AACzD,QAAM,gBAAgB;AAAA,IAClB,KAAK,SAAS;AAEV,oBAAc,YAAY,KAAK,MAAM,OAAO,CAAC;AAAA,IAChD;AAAA,IACD,QAAQ;AACJ,iBAAW,MAAO;AAClB,2BAAqB,MAAO;AAC5B,UAAI,QAAS;AAAA,IAChB;AAAA,IACD,UAAU,UAAU;AAAA,IAEnB;AAAA,EACJ;AACD,gBAAc,GAAG,gBAAgB,CAAC,YAAY;AAE1C,kBAAc,UAAU,KAAK,UAAU,OAAO,CAAC;AAAA,EACvD,CAAK;AACD,QAAM,qBAAqB,IAAI,eAAe,IAAI,IAAG,GAAI,aAAa;AACtE,QAAM,aAAa,MAAMC,WAAAA,WAAsB;AAAA,IAAe;AAAA,IAAe;AAAA;AAAA,IAE7E,qBAAqB,cAAa;AAAA,IAAI;AAAA,IAAI;AAAA,IAAS;AAAA,IAAW;AAAA,EAAgB;AAC9E,SAAO;AACX;AAKA,MAAM,qBAAqB;AAAA,EACvB;AAAA,EACA,YAAY,oBAAI,IAAK;AAAA,EACrB;AAAA,EACA,YAAY,KAAK;AACb,SAAK,OAAO;AACZ,SAAK,wBAAwB,IAAI,iBAAiB,GAAG;AAAA,EAC7D;AAAA,EACI,gBAAgB;AACZ,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,aAAa,IAAI;AACb,UAAM,UAAU,KAAK,KAAK,QAAQ,EAAE;AACpC,QAAI,CAAC,SAAS;AACV,YAAM,IAAI,MAAM,+BAA+B,EAAE,EAAE;AAAA,IAC/D;AACQ,QAAI,CAAC,KAAK,UAAU,IAAI,OAAO,GAAG;AAC9B,YAAM,UAAU,IAAI,iBAAiB,SAAS,IAAI,KAAK,qBAAqB;AAC5E,WAAK,UAAU,IAAI,SAAS,OAAO;AACnC,aAAO;AAAA,IACnB;AACQ,WAAO,KAAK,UAAU,IAAI,OAAO;AAAA,EACzC;AAAA,EACI,QAAQ;AACJ,SAAK,sBAAsB,MAAO;AAClC,eAAW,WAAW,KAAK,UAAU,OAAM,GAAI;AAC3C,cAAQ,MAAO;AAAA,IAC3B;AAAA,EACA;AACA;AAOA,MAAM,yBAAyBC,WAAAA,aAAwB;AAAA,EACnD,UAAU;AAAA,EACV;AAAA,EACA,YAAY;AAAA,EACZ;AAAA,EACA,YAAY,QAAQ,WAAW,eAAe;AAC1C,UAAO;AACP,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,iBAAiB;AACtB,SAAK,QAAQ,GAAG,KAAK,KAAK,eAAe;AAAA,EACjD;AAAA,EACI,gBAAgB;AACZ,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,kBAAkB,CAAC,QAAQ,UAAU;AACjC,SAAK,KAAK,QAAQ,KAAK;AAAA,EAC1B;AAAA,EACD,MAAM,YAAY,WAAW,QAAQ;AACjC,QAAI,KAAK,SAAS;AACd;AAAA,IACZ;AACQ,QAAI;AACA,aAAO,MAAM,KAAK,QAAQ,KAAK,QAAQ,GAAG,MAAM;AAAA,IAC5D,SACe,KAAK;AACR,UAAI,KAAK,SAAS;AACd;AAAA,MAChB;AACY,YAAM;AAAA,IAClB;AAAA,EACA;AAAA,EACI,QAAQ;AACJ,SAAK,QAAQ,IAAI,KAAK,KAAK,eAAe;AAC1C,SAAK,UAAU;AAAA,EACvB;AAAA,EACI,aAAa,OAAO;AAChB,WAAO,iBAAiB;AAAA,EAChC;AACA;AAMA,MAAM,sBAAsBA,WAAAA,aAAwB;AAAA,EAChD,aAAa,OAAO,OAAO;AACvB;AAAA,EACH;AAAA,EACD,YAAY,SAAS;AACjB,SAAK,KAAK,WAAW,OAAO;AAAA,EACpC;AAAA,EACI,aAAa,WAAW;AACpB,SAAK,aAAa;AAAA,EAC1B;AAAA,EACI,MAAM,YAAY,SAAS;AACvB,SAAK,KAAK,gBAAgB,OAAO;AAAA,EACzC;AAAA,EACI,QAAQ;AACJ,SAAK,aAAa,OAAO,OAAO;AAC5B;AAAA,IACH;AAAA,EACT;AACA;ACpJA;AAAA;AAAA;AAAA;AAAA;AAKA,IAAIT,sBAAwD,SAAU,SAAS,cAAc,OAAO;AAChG,MAAI,WAAW,UAAU,SAAS;AAClC,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC1C,YAAQ,WAAW,aAAa,CAAC,EAAE,KAAK,SAAS,KAAK,IAAI,aAAa,CAAC,EAAE,KAAK,OAAO;AAAA,EAC9F;AACI,SAAO,WAAW,QAAQ;AAC9B;AACA,IAAIC,iBAA8C,SAAU,MAAM,cAAc,YAAY,WAAW,cAAc,mBAAmB;AACpI,WAAS,OAAO,GAAG;AAAE,QAAI,MAAM,UAAU,OAAO,MAAM,WAAY,OAAM,IAAI,UAAU,mBAAmB;AAAG,WAAO;AAAA,EAAE;AACrH,MAAI,OAAO,UAAU,MAAM,MAAM,SAAS,WAAW,QAAQ,SAAS,WAAW,QAAQ;AACzF,MAAI,SAAS,CAAC,gBAAgB,OAAO,UAAU,QAAQ,IAAI,OAAO,KAAK,YAAY;AACnF,MAAI,aAAa,iBAAiB,SAAS,OAAO,yBAAyB,QAAQ,UAAU,IAAI,IAAI;AACrG,MAAI,GAAG,OAAO;AACd,WAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,QAAI,UAAU,CAAE;AAChB,aAAS,KAAK,UAAW,SAAQ,CAAC,IAAI,MAAM,WAAW,CAAA,IAAK,UAAU,CAAC;AACvE,aAAS,KAAK,UAAU,OAAQ,SAAQ,OAAO,CAAC,IAAI,UAAU,OAAO,CAAC;AACtE,YAAQ,iBAAiB,SAAU,GAAG;AAAE,UAAI,KAAM,OAAM,IAAI,UAAU,wDAAwD;AAAG,wBAAkB,KAAK,OAAO,KAAK,IAAI,CAAC;AAAA,IAAI;AAC7K,QAAI,UAAS,GAAI,WAAW,CAAC,GAAG,SAAS,aAAa,EAAE,KAAK,WAAW,KAAK,KAAK,WAAW,IAAK,IAAG,WAAW,GAAG,GAAG,OAAO;AAC7H,QAAI,SAAS,YAAY;AACrB,UAAI,WAAW,OAAQ;AACvB,UAAI,WAAW,QAAQ,OAAO,WAAW,SAAU,OAAM,IAAI,UAAU,iBAAiB;AACxF,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,IAAI,EAAG,cAAa,QAAQ,CAAC;AAAA,IAC/D,WACiB,IAAI,OAAO,MAAM,GAAG;AACzB,UAAI,SAAS,QAAS,cAAa,QAAQ,CAAC;AAAA,UACvC,YAAW,GAAG,IAAI;AAAA,IACnC;AAAA,EACA;AACI,MAAI,OAAQ,QAAO,eAAe,QAAQ,UAAU,MAAM,UAAU;AACpE,SAAO;AACX;AAQA,IAAI,cAAc,MAAM;AACpB,MAAI,cAAc;AAClB,MAAI,6BAA6B,CAAE;AACnC,MAAI;AACJ,SAAO,MAAMS,oBAAmB,YAAY;AAAA,IACxC,OAAA;AACI,YAAM,YAAY,OAAO,WAAW,cAAc,OAAO,WAAW,OAAO,OAAO,YAAY,OAAO,QAAQ,KAAK,IAAI,IAAI;AAC1HT,qBAAa,MAAM,MAAM,qBAAqB,EAAE,MAAM,UAAU,MAAM,WAAW,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,aAAa,KAAK,KAAK,SAAO,IAAI,QAAS,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC1O,UAAI,UAAW,QAAO,eAAe,MAAM,OAAO,UAAU,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,OAAO,WAAW;AAAA,IAClJ;AAAA,IACQ,OAAO,KAAK,SAAS;AACjB,YAAM,aAAa,IAAIS,YAAW,OAAO;AACzC,iBAAW,YAAa;AACxB,aAAO;AAAA,IACnB;AAAA;AAAA,IAEQ,YAAYV,oBAAkB,MAAM,0BAA0B,GAAG;AAAA,IACjE;AAAA,IACA,eAAe,IAAI,gBAAiB;AAAA,IACpC,MAAM,IAAI,SAAU;AAAA;AAAA,IAEpB,YAAY,SAAS;AACjB,YAAO;AAEP,WAAK,mBAAmB;AAAA,IAEpC;AAAA,IACQ,cAAc;AACV,YAAM,yBAAyB,KAAK,aAAa,IAAI,IAAI,aAAa,KAAK,gBAAgB,CAAC;AAC5F,6BAAuB,KAAK,UAAU,MAAM;AACxC,aAAK,KAAK,UAAU;AAAA,UAChB,KAAK,KAAK,iBAAiB;AAAA,UAC3B,WAAW,oBAAI,KAAM;AAAA,QACzC,CAAiB;AACD,aAAK,QAAS;AAAA,MAC9B,CAAa;AACD,WAAK,iBAAiB,GAAG,WAAW,CAAC,EAAE,QAAO,MAAO;AACjD,YAAI,QAAQ,eAAe,KAAK,IAAI,MAAK,GAAI;AACzC,eAAK,WAAW;AAChB,eAAK,KAAK,WAAW,OAAO;AAAA,QAChD;AAAA,MACA,CAAa;AACD,YAAM,iBAAiB,KAAK,aAAa,IAAI,IAAI,aAAa,KAAK,QAAQ,CAAC;AAE5E,iBAAW,aAAa;AAAA,QACpB;AAAA,QACA;AAAA,MAChB,GAAe;AACC,uBAAe,GAAG,WAAW,UAAQ;AACjC,cAAI,KAAK,YAAY,KAAK,iBAAiB,IAAI;AAC3C;AAAA,UACxB;AACoB,cAAI,CAAC,KAAK,YAAY;AAClB;AAAA,UACxB;AACoB,cAAI,CAAC,KAAK,IAAI,YAAY;AACtB,iBAAK,IAAI,QAAQ,KAAK,UAAU;AAAA,UACxD;AAAA,QACA,CAAiB;AAAA,MACjB;AACY,iBAAW,CAAC,WAAW,KAAK,KAAK;AAAA,QAC7B,CAAC,qCAAqC,UAAU;AAAA,QAChD,CAAC,oCAAoC,QAAQ;AAAA,QAC7C,CAAC,qCAAqC,SAAS;AAAA,MAC/D,GAAe;AACC,uBAAe,GAAG,WAAW,UAAQ;AACjC,cAAI,KAAK,YAAY,KAAK,iBAAiB,IAAI;AAC3C;AAAA,UACxB;AACoB,cAAI,CAAC,KAAK,YAAY;AAClB;AAAA,UACxB;AACoB,cAAI,CAAC,KAAK,IAAI,YAAY;AACtB,iBAAK,IAAI,QAAQ,KAAK,UAAU;AAAA,UACxD;AACoB,cAAI,KAAK,IAAI,MAAK,MAAO,KAAK,YAAY;AACtC;AAAA,UACxB;AACoB,eAAK,KAAK,OAAO;AAAA,YACb,KAAK,KAAK;AAAA,YACV,WAAW,IAAI,KAAK,KAAK,SAAS;AAAA,UAC1D,CAAqB;AACD,eAAK,QAAS;AAAA,QAClC,CAAiB;AAAA,MACjB;AAAA,IACA;AAAA;AAAA,IAEQ,IAAI,WAAW;AACX,aAAO,KAAK,iBAAiB,YAAY,QAAQ;AAAA,IAC7D;AAAA,IACQ,IAAI,WAAW;AACX,aAAO,KAAK,aAAa;AAAA,IACrC;AAAA,IACQ,IAAI,UAAU;AACV,aAAO,KAAK;AAAA,IACxB;AAAA;AAAA,IAEQ,UAAU;AACN,WAAK,aAAa,EAAG;AAAA,IACjC;AAAA,IACQ,EAAE,sBAAsB,CAAC,eAAe,GAAG,cAAa,IAAK;AACzD,WAAK,aAAa,QAAS;AAC3B,YAAM,aAAa,EAAG;AAAA,IAClC;AAAA,EACK;AACL,GAAI;ACvJJ;AAAA;AAAA;AAAA;AAAA;AAKA,IAAIA,sBAAwD,SAAU,SAAS,cAAc,OAAO;AAChG,MAAI,WAAW,UAAU,SAAS;AAClC,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC1C,YAAQ,WAAW,aAAa,CAAC,EAAE,KAAK,SAAS,KAAK,IAAI,aAAa,CAAC,EAAE,KAAK,OAAO;AAAA,EAC9F;AACI,SAAO,WAAW,QAAQ;AAC9B;AACA,IAAIC,iBAA8C,SAAU,MAAM,cAAc,YAAY,WAAW,cAAc,mBAAmB;AACpI,WAAS,OAAO,GAAG;AAAE,QAAI,MAAM,UAAU,OAAO,MAAM,WAAY,OAAM,IAAI,UAAU,mBAAmB;AAAG,WAAO;AAAA,EAAE;AACrH,MAAI,OAAO,UAAU,MAAM,MAAM,SAAS,WAAW,QAAQ,SAAS,WAAW,QAAQ;AACzF,MAAI,SAAS,CAAC,gBAAgB,OAAO,UAAU,QAAQ,IAAI,OAAO,KAAK,YAAY;AACnF,MAAI,aAAa,iBAAiB,SAAS,OAAO,yBAAyB,QAAQ,UAAU,IAAI,IAAI;AACrG,MAAI,GAAG,OAAO;AACd,WAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,QAAI,UAAU,CAAE;AAChB,aAAS,KAAK,UAAW,SAAQ,CAAC,IAAI,MAAM,WAAW,CAAA,IAAK,UAAU,CAAC;AACvE,aAAS,KAAK,UAAU,OAAQ,SAAQ,OAAO,CAAC,IAAI,UAAU,OAAO,CAAC;AACtE,YAAQ,iBAAiB,SAAU,GAAG;AAAE,UAAI,KAAM,OAAM,IAAI,UAAU,wDAAwD;AAAG,wBAAkB,KAAK,OAAO,KAAK,IAAI,CAAC;AAAA,IAAI;AAC7K,QAAI,UAAS,GAAI,WAAW,CAAC,GAAG,SAAS,aAAa,EAAE,KAAK,WAAW,KAAK,KAAK,WAAW,IAAK,IAAG,WAAW,GAAG,GAAG,OAAO;AAC7H,QAAI,SAAS,YAAY;AACrB,UAAI,WAAW,OAAQ;AACvB,UAAI,WAAW,QAAQ,OAAO,WAAW,SAAU,OAAM,IAAI,UAAU,iBAAiB;AACxF,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,IAAI,EAAG,cAAa,QAAQ,CAAC;AAAA,IAC/D,WACiB,IAAI,OAAO,MAAM,GAAG;AACzB,UAAI,SAAS,QAAS,cAAa,QAAQ,CAAC;AAAA,UACvC,YAAW,GAAG,IAAI;AAAA,IACnC;AAAA,EACA;AACI,MAAI,OAAQ,QAAO,eAAe,QAAQ,UAAU,MAAM,UAAU;AACpE,SAAO;AACX;AAOA,IAAI,SAAS,MAAM;AACf,MAAI,cAAc;AAClB,MAAI,6BAA6B,CAAE;AACnC,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,SAAO,MAAM,cAAc,YAAY;AAAA,IACnC,OAAA;AACI,YAAM,YAAY,OAAO,WAAW,cAAc,OAAO,WAAW,OAAO,OAAO,YAAY,OAAO,QAAQ,KAAK,IAAI,IAAI;AAC1HA,qBAAa,MAAM,MAAM,qBAAqB,EAAE,MAAM,UAAU,MAAM,WAAW,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,aAAa,KAAK,KAAK,SAAO,IAAI,QAAS,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC1OA,qBAAa,MAAM,MAAM,oBAAoB,EAAE,MAAM,UAAU,MAAM,UAAU,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,YAAY,KAAK,KAAK,SAAO,IAAI,OAAQ,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AACtOA,qBAAa,MAAM,MAAM,0BAA0B,EAAE,MAAM,UAAU,MAAM,gBAAgB,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,kBAAkB,KAAK,KAAK,SAAO,IAAI,aAAc,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC9PA,qBAAa,MAAM,MAAM,sBAAsB,EAAE,MAAM,UAAU,MAAM,YAAY,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,cAAc,KAAK,KAAK,SAAO,IAAI,SAAU,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC9O,UAAI,UAAW,QAAO,eAAe,MAAM,OAAO,UAAU,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,OAAO,WAAW;AAAA,IAClJ;AAAA;AAAA,IAEQ,WAAWD,oBAAkB,MAAM,0BAA0B,GAAG;AAAA,IAChE,cAAc,IAAI,gBAAiB;AAAA,IACnC;AAAA,IACA;AAAA;AAAA,IAEA,YAAY,IAAI,QAAQ;AACpB,YAAO;AAEP,WAAK,KAAK;AACV,WAAK,SAAS;AAAA,IAE1B;AAAA,IACQ,aAAa;AACT,YAAM,iBAAiB,KAAK,YAAY,IAAI,IAAI,aAAa,KAAK,OAAO,CAAC;AAC1E,qBAAe,GAAG,yBAAyB,UAAQ;AAC/C,YAAI,KAAK,UAAU,KAAK,IAAI;AACxB;AAAA,QACpB;AACgB,aAAK,QAAQ,0BAA0B;AAAA,MACvD,CAAa;AAAA,IACb;AAAA;AAAA,IAEQ,IAAI,WAAW;AACX,aAAO,KAAK,YAAY;AAAA,IACpC;AAAA,IACQ,IAAI,SAAS;AACT,aAAO,EAAE,OAAO,KAAK,GAAI;AAAA,IACrC;AAAA;AAAA,IAEQ,QAAQ,QAAQ;AACZ,WAAK,UAAU;AACf,WAAK,aAAa,EAAG;AAAA,IACjC;AAAA,IACQ,MAAM,OAAO,SAAS;AAClB,YAAM,KAAK,QAAQ,KAAK,iBAAiB;AAAA,QACrC,QAAQ,KAAK;AAAA,QACb;AAAA,MAChB,CAAa;AAAA,IACb;AAAA,IACQ,MAAM,aAAa,qBAAqB,cAAc,UAAU,CAAA,GAAI;AAChE,YAAM,EAAE,OAAM,IAAK,MAAM,KAAK,QAAQ,KAAK,uBAAuB;AAAA,QAC9D;AAAA,QACA;AAAA,QACA,QAAQ,KAAK;AAAA,QACb,GAAG;AAAA,MACnB,CAAa;AACD,aAAO;AAAA,IACnB;AAAA,IACQ,MAAM,SAAS,YAAY,cAAc,UAAU,CAAA,GAAI;AACnD,YAAM,EAAE,OAAM,IAAK,MAAM,KAAK,QAAQ,KAAK,mBAAmB;AAAA,QAC1D;AAAA,QACA;AAAA,QACA,QAAQ,KAAK;AAAA,QACb,GAAG;AAAA,MACnB,CAAa;AACD,aAAO;AAAA,IACnB;AAAA,IACQ,EAAE,sBAAsB,CAAC,eAAe,GAAG,qBAAqB,CAAC,gBAAgB,WAAS;AAElF,aAAO,MAAM;AAAA,IAChB,CAAA,CAAC,GAAG,2BAA2B,CAAC,gBAAgB,WAAS;AAEtD,aAAO,MAAM;AAAA,IAChB,CAAA,CAAC,GAAG,uBAAuB,CAAC,gBAAgB,WAAS;AAElD,aAAO,MAAM;AAAA,IAC7B,CAAa,CAAC,GAAG,kBAAkB;AACvB,WAAK,YACD;AACJ,WAAK,KAAK,aAAa,EAAE,QAAQ,KAAK,SAAS;AAC/C,WAAK,YAAY,QAAS;AAC1B,YAAM,aAAa,EAAG;AAAA,IAClC;AAAA,EACK;AACL,GAAI;AAKG,MAAM,oBAAoB,MAAM;AAAA,EACnC,OAAO,KAAK,SAAS,SAAS;AAC1B,UAAM,QAAQ,IAAI,YAAY,SAAS,OAAO;AAC9C,UAAM,WAAY;AAClB,WAAO;AAAA,EACf;AAAA;AAAA,EAEI;AAAA,EACA;AAAA;AAAA,EAEA,WAAW;AAAA,IACP,WAAW,oBAAI,IAAK;AAAA,IACpB,QAAQ,oBAAI,IAAK;AAAA,EACpB;AAAA,EACD,YAAY,SAAS,SAAS;AAC1B,UAAM,IAAI,EAAE;AAEZ,SAAK,kBAAkB;AACvB,SAAK,UAAU;AAAA,EAEvB;AAAA,EACI,aAAa;AACT,UAAM,WAAY;AAClB,UAAM,iBAAiB,KAAK,YAAY,IAAI,IAAI,aAAa,KAAK,OAAO,CAAC;AAC1E,mBAAe,GAAG,uBAAuB,UAAQ;AAC7C,UAAI,KAAK,SAAS,UAAU;AACxB;AAAA,MAChB;AACY,WAAK,KAAK,KAAK;AACf,WAAK,SAAS,KAAK;AAAA,IAC/B,CAAS;AACD,mBAAe,GAAG,uBAAuB,UAAQ;AAC7C,UAAI,KAAK,SAAS,oBAAoB;AAClC;AAAA,MAChB;AACY,UAAI,CAAC,KAAK,OAAO,SAAS,KAAK,EAAE,GAAG;AAChC;AAAA,MAChB;AACY,YAAM,QAAQ,qBAAqB,KAAK,MAAM,KAAK,OAAO,KAAK,MAAM;AACrE,WAAK,SAAS,UAAU,IAAI,MAAM,IAAI,KAAK;AAC3C,YAAM,eAAe,KAAK,YAAY,IAAI,IAAI,aAAa,KAAK,CAAC;AACjE,mBAAa,KAAK,aAAa,MAAM;AACjC,qBAAa,mBAAoB;AACjC,aAAK,SAAS,UAAU,OAAO,MAAM,EAAE;AAAA,MACvD,CAAa;AACD,WAAK,KAAK,UAAU,KAAK;AAAA,IACrC,CAAS;AACD,SAAK,gBAAgB,YAAY,QAAQ,GAAG,gBAAgB,CAAC,EAAE,YAAY;AACvE,UAAI,CAAC,MAAM,OAAO,IAAI,IAAI,GAAG;AACzB;AAAA,MAChB;AACY,WAAK,SAAS,OAAO,IAAI,MAAM,IAAI,KAAK;AACxC,YAAM,eAAe,KAAK,YAAY,IAAI,IAAI,aAAa,KAAK,CAAC;AACjE,mBAAa,KAAK,aAAa,MAAM;AACjC,qBAAa,mBAAoB;AACjC,aAAK,SAAS,OAAO,OAAO,MAAM,EAAE;AAAA,MACpD,CAAa;AACD,WAAK,KAAK,gBAAgB,KAAK;AAAA,IAC3C,CAAS;AAAA,EACT;AAAA,EACI,IAAI,UAAU;AACV,WAAO,KAAK,gBAAgB,YAAY,QAAQ;AAAA,EACxD;AAAA,EACI,IAAI,SAAS;AACT,WAAO,EAAE,SAAS,KAAK,gBAAgB,IAAI,SAAS,KAAK,QAAS;AAAA,EAC1E;AACA;AAIO,MAAM,6BAA6B,MAAM;AAAA,EAC5C,OAAO,KAAK,OAAO,IAAI,QAAQ;AAC3B,UAAM,QAAQ,IAAI,qBAAqB,OAAO,IAAI,MAAM;AACxD,UAAM,WAAY;AAClB,WAAO;AAAA,EACf;AAAA;AAAA,EAEI,WAAW,oBAAI,IAAK;AAAA,EACpB;AAAA;AAAA,EAEA,YAAY,OAAO,IAAI,QAAQ;AAC3B,UAAM,IAAI,MAAM;AAChB,SAAK,SAAS,oBAAI,IAAI,CAAC,KAAK,CAAC;AAAA,EACrC;AAAA,EACI,aAAa;AACT,UAAM,WAAY;AAClB,UAAM,iBAAiB,KAAK,YAAY,IAAI,IAAI,aAAa,KAAK,OAAO,CAAC;AAC1E,mBAAe,GAAG,uBAAuB,UAAQ;AAC7C,UAAI,KAAK,SAAS,oBAAoB;AAClC;AAAA,MAChB;AACY,UAAI,CAAC,KAAK,OAAO,SAAS,KAAK,EAAE,GAAG;AAChC;AAAA,MAChB;AACY,YAAM,QAAQ,qBAAqB,KAAK,MAAM,KAAK,OAAO,KAAK,MAAM;AACrE,WAAK,SAAS,IAAI,MAAM,IAAI,KAAK;AACjC,YAAM,eAAe,KAAK,YAAY,IAAI,IAAI,aAAa,KAAK,CAAC;AACjE,mBAAa,KAAK,aAAa,MAAM;AACjC,aAAK,SAAS,OAAO,MAAM,EAAE;AAAA,MAC7C,CAAa;AACD,WAAK,KAAK,UAAU,KAAK;AAAA,IACrC,CAAS;AAAA,EACT;AAAA,EACI,IAAI,UAAU;AAEV,WAAO,KAAK,OAAO,OAAQ,EAAC,KAAI,EAAG,MAAM;AAAA,EACjD;AACA;ACxPA;AAAA;AAAA;AAAA;AAAA;AAKA,IAAIA,sBAAwD,SAAU,SAAS,cAAc,OAAO;AAChG,MAAI,WAAW,UAAU,SAAS;AAClC,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC1C,YAAQ,WAAW,aAAa,CAAC,EAAE,KAAK,SAAS,KAAK,IAAI,aAAa,CAAC,EAAE,KAAK,OAAO;AAAA,EAC9F;AACI,SAAO,WAAW,QAAQ;AAC9B;AACA,IAAIC,iBAA8C,SAAU,MAAM,cAAc,YAAY,WAAW,cAAc,mBAAmB;AACpI,WAAS,OAAO,GAAG;AAAE,QAAI,MAAM,UAAU,OAAO,MAAM,WAAY,OAAM,IAAI,UAAU,mBAAmB;AAAG,WAAO;AAAA,EAAE;AACrH,MAAI,OAAO,UAAU,MAAM,MAAM,SAAS,WAAW,QAAQ,SAAS,WAAW,QAAQ;AACzF,MAAI,SAAS,CAAC,gBAAgB,OAAO,UAAU,QAAQ,IAAI,OAAO,KAAK,YAAY;AACnF,MAAI,aAAa,iBAAiB,SAAS,OAAO,yBAAyB,QAAQ,UAAU,IAAI,IAAI;AACrG,MAAI,GAAG,OAAO;AACd,WAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,QAAI,UAAU,CAAE;AAChB,aAAS,KAAK,UAAW,SAAQ,CAAC,IAAI,MAAM,WAAW,CAAA,IAAK,UAAU,CAAC;AACvE,aAAS,KAAK,UAAU,OAAQ,SAAQ,OAAO,CAAC,IAAI,UAAU,OAAO,CAAC;AACtE,YAAQ,iBAAiB,SAAU,GAAG;AAAE,UAAI,KAAM,OAAM,IAAI,UAAU,wDAAwD;AAAG,wBAAkB,KAAK,OAAO,KAAK,IAAI,CAAC;AAAA,IAAI;AAC7K,QAAI,UAAS,GAAI,WAAW,CAAC,GAAG,SAAS,aAAa,EAAE,KAAK,WAAW,KAAK,KAAK,WAAW,IAAK,IAAG,WAAW,GAAG,GAAG,OAAO;AAC7H,QAAI,SAAS,YAAY;AACrB,UAAI,WAAW,OAAQ;AACvB,UAAI,WAAW,QAAQ,OAAO,WAAW,SAAU,OAAM,IAAI,UAAU,iBAAiB;AACxF,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,IAAI,EAAG,cAAa,QAAQ,CAAC;AAAA,IAC/D,WACiB,IAAI,OAAO,MAAM,GAAG;AACzB,UAAI,SAAS,QAAS,cAAa,QAAQ,CAAC;AAAA,UACvC,YAAW,GAAG,IAAI;AAAA,IACnC;AAAA,EACA;AACI,MAAI,OAAQ,QAAO,eAAe,QAAQ,UAAU,MAAM,UAAU;AACpE,SAAO;AACX;AAOA,IAAI,WAAW,MAAM;AAEjB,MAAI,cAAc;AAClB,MAAI,6BAA6B,CAAE;AACnC,MAAI;AACJ,SAAO,MAAMU,iBAAgB,YAAY;AAAA,IACrC,OAAA;AACI,YAAM,YAAY,OAAO,WAAW,cAAc,OAAO,WAAW,OAAO,OAAO,YAAY,OAAO,QAAQ,KAAK,IAAI,IAAI;AAC1HV,qBAAa,MAAM,MAAM,qBAAqB,EAAE,MAAM,UAAU,MAAM,WAAW,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,aAAa,KAAK,KAAK,SAAO,IAAI,QAAS,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC1O,UAAI,UAAW,QAAO,eAAe,MAAM,OAAO,UAAU,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,OAAO,WAAW;AAAA,IAClJ;AAAA,IACQ,OAAO,KAAK,iBAAiB,OAAO;AAChC,YAAM,UAAU,IAAIU,SAAQ,iBAAiB,KAAK;AAClD,cAAQ,YAAa;AACrB,aAAO;AAAA,IACnB;AAAA;AAAA,IAEQ,UAAUX,oBAAkB,MAAM,0BAA0B,GAAG;AAAA,IAC/D;AAAA,IACA;AAAA,IACA;AAAA,IACA,eAAe,IAAI,gBAAiB;AAAA,IACpC;AAAA;AAAA,IAEA,YAAY,iBAAiB,OAAO;AAChC,YAAO;AAEP,WAAK,mBAAmB;AACxB,WAAK,SAAS;AAAA,IAE1B;AAAA,IACQ,cAAc;AACV,YAAM,yBAAyB,KAAK,aAAa,IAAI,IAAI,aAAa,KAAK,gBAAgB,CAAC;AAC5F,6BAAuB,KAAK,UAAU,CAAC,EAAE,OAAM,MAAO;AAClD,aAAK,SAAS;AACd,aAAK,KAAK,SAAS,KAAK,MAAM;AAC9B,aAAK,QAAS;AAAA,MAC9B,CAAa;AACD,YAAM,iBAAiB,KAAK,aAAa,IAAI,IAAI,aAAa,KAAK,QAAQ,CAAC;AAC5E,qBAAe,GAAG,6BAA6B,WAAS;AACpD,YAAI,MAAM,YAAY,KAAK,iBAAiB,IAAI;AAC5C;AAAA,QACpB;AACgB,YAAI,MAAM,QAAQ,YAAY,KAAK,IAAI;AACnC;AAAA,QACpB;AACgB,aAAK,YAAYW,SAAQ,KAAK,KAAK,kBAAkB,KAAK;AAC1D,aAAK,KAAK,YAAY,KAAK,SAAS;AACpC,aAAK,QAAS;AAAA,MAC9B,CAAa;AACD,qBAAe,GAAG,sBAAsB,WAAS;AAC7C,YAAI,MAAM,YAAY,KAAK,iBAAiB,IAAI;AAC5C;AAAA,QACpB;AACgB,YAAI,MAAM,QAAQ,YAAY,KAAK,IAAI;AACnC;AAAA,QACpB;AACgB,aAAK,SAAS,MAAM;AACpB,aAAK,KAAK,SAAS,KAAK,MAAM;AAC9B,aAAK,QAAS;AAAA,MAC9B,CAAa;AACD,qBAAe,GAAG,6BAA6B,WAAS;AACpD,YAAI,MAAM,YAAY,KAAK,iBAAiB,IAAI;AAC5C;AAAA,QACpB;AACgB,YAAI,MAAM,QAAQ,YAAY,KAAK,IAAI;AACnC;AAAA,QACpB;AACgB,aAAK,YAAY,MAAM;AACvB,aAAK,KAAK,WAAW,KAAK,SAAS;AACnC,aAAK,QAAS;AAAA,MAC9B,CAAa;AAAA,IACb;AAAA;AAAA,IAEQ,IAAI,WAAW;AACX,aAAO,KAAK,iBAAiB,YAAY,QAAQ;AAAA,IAC7D;AAAA,IACQ,IAAI,WAAW;AACX,aAAO,KAAK,aAAa;AAAA,IACrC;AAAA,IACQ,IAAI,QAAQ;AACR,aAAO,KAAK;AAAA,IACxB;AAAA,IACQ,IAAI,UAAU;AACV,aAAO,KAAK,OAAO,QAAQ;AAAA,IACvC;AAAA,IACQ,IAAI,KAAK;AACL,aAAO,KAAK,OAAO,QAAQ;AAAA,IACvC;AAAA,IACQ,IAAI,YAAY;AACZ,aAAO,KAAK,OAAO;AAAA,IAC/B;AAAA,IACQ,IAAI,SAAS;AACT,aAAO,KAAK,OAAO,QAAQ;AAAA,IACvC;AAAA,IACQ,IAAI,aAAa;AACb,aAAO,KAAK,OAAO,cAAc;AAAA,IAC7C;AAAA,IACQ,IAAI,WAAW;AACX,aAAO,KAAK;AAAA,IACxB;AAAA,IACQ,IAAI,WAAW;AACX,aAAO,KAAK;AAAA,IACxB;AAAA,IACQ,IAAI,MAAM;AACN,aAAO,KAAK,OAAO,QAAQ;AAAA,IACvC;AAAA;AAAA,IAEQ,UAAU;AACN,WAAK,aAAa,EAAG;AAAA,IACjC;AAAA,IACQ,EAAE,sBAAsB,CAAC,eAAe,GAAG,cAAa,IAAK;AACzD,WAAK,aAAa,QAAS;AAC3B,YAAM,aAAa,EAAG;AAAA,IAClC;AAAA,EACK;AACL,GAAI;ACjKJ;AAAA;AAAA;AAAA;AAAA;AAKA,IAAIX,sBAAwD,SAAU,SAAS,cAAc,OAAO;AAChG,MAAI,WAAW,UAAU,SAAS;AAClC,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC1C,YAAQ,WAAW,aAAa,CAAC,EAAE,KAAK,SAAS,KAAK,IAAI,aAAa,CAAC,EAAE,KAAK,OAAO;AAAA,EAC9F;AACI,SAAO,WAAW,QAAQ;AAC9B;AACA,IAAIC,iBAA8C,SAAU,MAAM,cAAc,YAAY,WAAW,cAAc,mBAAmB;AACpI,WAAS,OAAO,GAAG;AAAE,QAAI,MAAM,UAAU,OAAO,MAAM,WAAY,OAAM,IAAI,UAAU,mBAAmB;AAAG,WAAO;AAAA,EAAE;AACrH,MAAI,OAAO,UAAU,MAAM,MAAM,SAAS,WAAW,QAAQ,SAAS,WAAW,QAAQ;AACzF,MAAI,SAAS,CAAC,gBAAgB,OAAO,UAAU,QAAQ,IAAI,OAAO,KAAK,YAAY;AACnF,MAAI,aAAa,iBAAiB,SAAS,OAAO,yBAAyB,QAAQ,UAAU,IAAI,IAAI;AACrG,MAAI,GAAG,OAAO;AACd,WAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,QAAI,UAAU,CAAE;AAChB,aAAS,KAAK,UAAW,SAAQ,CAAC,IAAI,MAAM,WAAW,CAAA,IAAK,UAAU,CAAC;AACvE,aAAS,KAAK,UAAU,OAAQ,SAAQ,OAAO,CAAC,IAAI,UAAU,OAAO,CAAC;AACtE,YAAQ,iBAAiB,SAAU,GAAG;AAAE,UAAI,KAAM,OAAM,IAAI,UAAU,wDAAwD;AAAG,wBAAkB,KAAK,OAAO,KAAK,IAAI,CAAC;AAAA,IAAI;AAC7K,QAAI,UAAS,GAAI,WAAW,CAAC,GAAG,SAAS,aAAa,EAAE,KAAK,WAAW,KAAK,KAAK,WAAW,IAAK,IAAG,WAAW,GAAG,GAAG,OAAO;AAC7H,QAAI,SAAS,YAAY;AACrB,UAAI,WAAW,OAAQ;AACvB,UAAI,WAAW,QAAQ,OAAO,WAAW,SAAU,OAAM,IAAI,UAAU,iBAAiB;AACxF,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,IAAI,EAAG,cAAa,QAAQ,CAAC;AAAA,IAC/D,WACiB,IAAI,OAAO,MAAM,GAAG;AACzB,UAAI,SAAS,QAAS,cAAa,QAAQ,CAAC;AAAA,UACvC,YAAW,GAAG,IAAI;AAAA,IACnC;AAAA,EACA;AACI,MAAI,OAAQ,QAAO,eAAe,QAAQ,UAAU,MAAM,UAAU;AACpE,SAAO;AACX;AAOA,IAAI,cAAc,MAAM;AACpB,MAAI,cAAc;AAClB,MAAI,6BAA6B,CAAE;AACnC,MAAI;AACJ,MAAI;AACJ,SAAO,MAAMW,oBAAmB,YAAY;AAAA,IACxC,OAAA;AACI,YAAM,YAAY,OAAO,WAAW,cAAc,OAAO,WAAW,OAAO,OAAO,YAAY,OAAO,QAAQ,KAAK,IAAI,IAAI;AAC1HX,qBAAa,MAAM,MAAM,qBAAqB,EAAE,MAAM,UAAU,MAAM,WAAW,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,aAAa,KAAK,KAAK,SAAO,IAAI,QAAS,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC1OA,qBAAa,MAAM,MAAM,oBAAoB,EAAE,MAAM,UAAU,MAAM,UAAU,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,YAAY,KAAK,KAAK,SAAO,IAAI,OAAQ,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AACtO,UAAI,UAAW,QAAO,eAAe,MAAM,OAAO,UAAU,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,OAAO,WAAW;AAAA,IAClJ;AAAA,IACQ,OAAO,KAAK,iBAAiB,MAAM;AAC/B,YAAM,aAAa,IAAIW,YAAW,iBAAiB,IAAI;AACvD,iBAAW,YAAa;AACxB,aAAO;AAAA,IACnB;AAAA;AAAA,IAEQ,WAAWZ,oBAAkB,MAAM,0BAA0B,GAAG;AAAA,IAChE;AAAA,IACA,eAAe,IAAI,gBAAiB;AAAA,IACpC;AAAA,IACA;AAAA;AAAA,IAEA,YAAY,SAAS,MAAM;AACvB,YAAO;AAEP,WAAK,kBAAkB;AACvB,WAAK,OAAO;AAAA,IAExB;AAAA,IACQ,cAAc;AACV,YAAM,wBAAwB,KAAK,aAAa,IAAI,IAAI,aAAa,KAAK,eAAe,CAAC;AAC1F,4BAAsB,KAAK,UAAU,CAAC,EAAE,OAAM,MAAO;AACjD,aAAK,QAAQ,+BAA+B,MAAM,EAAE;AAAA,MACpE,CAAa;AACD,YAAM,iBAAiB,KAAK,aAAa,IAAI,IAAI,aAAa,KAAK,QAAQ,CAAC;AAC5E,qBAAe,GAAG,oCAAoC,gBAAc;AAChE,YAAI,WAAW,YAAY,KAAK,gBAAgB,IAAI;AAChD;AAAA,QACpB;AACgB,aAAK,UAAU;AACf,aAAK,KAAK,WAAW,UAAU;AAC/B,aAAK,QAAQ,8BAA8B;AAAA,MAC3D,CAAa;AAAA,IACb;AAAA;AAAA,IAEQ,IAAI,WAAW;AACX,aAAO,KAAK,gBAAgB,YAAY,QAAQ;AAAA,IAC5D;AAAA,IACQ,IAAI,SAAS;AACT,aAAO,KAAK,YAAY;AAAA,IACpC;AAAA,IACQ,IAAI,WAAW;AACX,aAAO,KAAK;AAAA,IACxB;AAAA,IACQ,IAAI,UAAU;AACV,aAAO,KAAK,YAAY;AAAA,IACpC;AAAA,IACQ,IAAI,SAAS;AACT,aAAO,KAAK;AAAA,IACxB;AAAA;AAAA,IAEQ,QAAQ,QAAQ;AACZ,WAAK,UAAU;AACf,WAAK,aAAa,EAAG;AAAA,IACjC;AAAA,IACQ,MAAM,OAAO,UAAU,IAAI;AACvB,YAAM,KAAK,SAAS,KAAK,oCAAoC;AAAA,QACzD,GAAG;AAAA,QACH,SAAS,KAAK,KAAK;AAAA,MACnC,CAAa;AAED,aAAO,KAAK;AAAA,IACxB;AAAA,IACQ,EAAE,sBAAsB,CAAC,eAAe,GAAG,qBAAqB,CAAC,gBAAgB,YAAU;AAEnF,aAAO,OAAO;AAAA,IAC9B,CAAa,CAAC,GAAG,kBAAkB;AACvB,WAAK,YACD;AACJ,WAAK,KAAK,UAAU,EAAE,QAAQ,KAAK,SAAS;AAC5C,WAAK,aAAa,QAAS;AAC3B,YAAM,aAAa,EAAG;AAAA,IAClC;AAAA,EACK;AACL,GAAI;ACnIJ;AAAA;AAAA;AAAA;AAAA;AAKA,IAAIA,sBAAwD,SAAU,SAAS,cAAc,OAAO;AAChG,MAAI,WAAW,UAAU,SAAS;AAClC,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC1C,YAAQ,WAAW,aAAa,CAAC,EAAE,KAAK,SAAS,KAAK,IAAI,aAAa,CAAC,EAAE,KAAK,OAAO;AAAA,EAC9F;AACI,SAAO,WAAW,QAAQ;AAC9B;AACA,IAAIC,iBAA8C,SAAU,MAAM,cAAc,YAAY,WAAW,cAAc,mBAAmB;AACpI,WAAS,OAAO,GAAG;AAAE,QAAI,MAAM,UAAU,OAAO,MAAM,WAAY,OAAM,IAAI,UAAU,mBAAmB;AAAG,WAAO;AAAA,EAAE;AACrH,MAAI,OAAO,UAAU,MAAM,MAAM,SAAS,WAAW,QAAQ,SAAS,WAAW,QAAQ;AACzF,MAAI,SAAS,CAAC,gBAAgB,OAAO,UAAU,QAAQ,IAAI,OAAO,KAAK,YAAY;AACnF,MAAI,aAAa,iBAAiB,SAAS,OAAO,yBAAyB,QAAQ,UAAU,IAAI,IAAI;AACrG,MAAI,GAAG,OAAO;AACd,WAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,QAAI,UAAU,CAAE;AAChB,aAAS,KAAK,UAAW,SAAQ,CAAC,IAAI,MAAM,WAAW,CAAA,IAAK,UAAU,CAAC;AACvE,aAAS,KAAK,UAAU,OAAQ,SAAQ,OAAO,CAAC,IAAI,UAAU,OAAO,CAAC;AACtE,YAAQ,iBAAiB,SAAU,GAAG;AAAE,UAAI,KAAM,OAAM,IAAI,UAAU,wDAAwD;AAAG,wBAAkB,KAAK,OAAO,KAAK,IAAI,CAAC;AAAA,IAAI;AAC7K,QAAI,UAAS,GAAI,WAAW,CAAC,GAAG,SAAS,aAAa,EAAE,KAAK,WAAW,KAAK,KAAK,WAAW,IAAK,IAAG,WAAW,GAAG,GAAG,OAAO;AAC7H,QAAI,SAAS,YAAY;AACrB,UAAI,WAAW,OAAQ;AACvB,UAAI,WAAW,QAAQ,OAAO,WAAW,SAAU,OAAM,IAAI,UAAU,iBAAiB;AACxF,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,IAAI,EAAG,cAAa,QAAQ,CAAC;AAAA,IAC/D,WACiB,IAAI,OAAO,MAAM,GAAG;AACzB,UAAI,SAAS,QAAS,cAAa,QAAQ,CAAC;AAAA,UACvC,YAAW,GAAG,IAAI;AAAA,IACnC;AAAA,EACA;AACI,MAAI,OAAQ,QAAO,eAAe,QAAQ,UAAU,MAAM,UAAU;AACpE,SAAO;AACX;AAWA,IAAIY,oBAAmB,MAAM;AAEzB,MAAI,cAAc;AAClB,MAAI,6BAA6B,CAAE;AACnC,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,SAAO,MAAMA,yBAAwB,YAAY;AAAA,IAC7C,OAAA;AACI,YAAM,YAAY,OAAO,WAAW,cAAc,OAAO,WAAW,OAAO,OAAO,YAAY,OAAO,QAAQ,KAAK,IAAI,IAAI;AAC1HZ,qBAAa,MAAM,MAAM,qBAAqB,EAAE,MAAM,UAAU,MAAM,WAAW,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,aAAa,KAAK,KAAK,SAAO,IAAI,QAAS,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC1OA,qBAAa,MAAM,MAAM,sBAAsB,EAAE,MAAM,UAAU,MAAM,YAAY,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,cAAc,KAAK,KAAK,SAAO,IAAI,SAAU,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC9OA,qBAAa,MAAM,MAAM,+BAA+B,EAAE,MAAM,UAAU,MAAM,qBAAqB,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,uBAAuB,KAAK,KAAK,SAAO,IAAI,kBAAmB,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAClRA,qBAAa,MAAM,MAAM,mBAAmB,EAAE,MAAM,UAAU,MAAM,SAAS,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,WAAW,KAAK,KAAK,SAAO,IAAI,MAAO,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAClOA,qBAAa,MAAM,MAAM,6BAA6B,EAAE,MAAM,UAAU,MAAM,mBAAmB,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,qBAAqB,KAAK,KAAK,SAAO,IAAI,gBAAiB,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC1QA,qBAAa,MAAM,MAAM,sBAAsB,EAAE,MAAM,UAAU,MAAM,YAAY,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,cAAc,KAAK,KAAK,SAAO,IAAI,SAAU,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC9OA,qBAAa,MAAM,MAAM,oBAAoB,EAAE,MAAM,UAAU,MAAM,UAAU,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,YAAY,KAAK,KAAK,SAAO,IAAI,OAAQ,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AACtOA,qBAAa,MAAM,MAAM,mBAAmB,EAAE,MAAM,UAAU,MAAM,SAAS,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,WAAW,KAAK,KAAK,SAAO,IAAI,MAAO,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAClOA,qBAAa,MAAM,MAAM,8BAA8B,EAAE,MAAM,UAAU,MAAM,oBAAoB,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,sBAAsB,KAAK,KAAK,SAAO,IAAI,iBAAkB,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC9QA,qBAAa,MAAM,MAAM,yBAAyB,EAAE,MAAM,UAAU,MAAM,eAAe,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,iBAAiB,KAAK,KAAK,SAAO,IAAI,YAAa,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC1PA,qBAAa,MAAM,MAAM,4BAA4B,EAAE,MAAM,UAAU,MAAM,kBAAkB,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,oBAAoB,KAAK,KAAK,SAAO,IAAI,eAAgB,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AACtQA,qBAAa,MAAM,MAAM,4BAA4B,EAAE,MAAM,UAAU,MAAM,kBAAkB,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,oBAAoB,KAAK,KAAK,SAAO,IAAI,eAAgB,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AACtQA,qBAAa,MAAM,MAAM,+BAA+B,EAAE,MAAM,UAAU,MAAM,qBAAqB,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,uBAAuB,KAAK,KAAK,SAAO,IAAI,kBAAmB,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAClRA,qBAAa,MAAM,MAAM,8BAA8B,EAAE,MAAM,UAAU,MAAM,oBAAoB,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,sBAAsB,KAAK,KAAK,SAAO,IAAI,iBAAkB,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC9QA,qBAAa,MAAM,MAAM,iCAAiC,EAAE,MAAM,UAAU,MAAM,uBAAuB,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,yBAAyB,KAAK,KAAK,SAAO,IAAI,oBAAqB,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC1R,UAAI,UAAW,QAAO,eAAe,MAAM,OAAO,UAAU,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,OAAO,WAAW;AAAA,IAClJ;AAAA,IACQ,OAAO,KAAK,aAAa,QAAQ,IAAI,KAAK;AACtC,YAAM,kBAAkB,IAAIY,iBAAgB,aAAa,QAAQ,IAAI,GAAG;AACxE,sBAAgB,YAAa;AAC7B,aAAO;AAAA,IACnB;AAAA;AAAA,IAEQ,eAAeb,oBAAkB,MAAM,0BAA0B,GAAG;AAAA,IACpE;AAAA,IACA;AAAA,IACA,YAAY,oBAAI,IAAK;AAAA,IACrB,eAAe,IAAI,gBAAiB;AAAA,IACpC,UAAU,oBAAI,IAAK;AAAA,IACnB,YAAY,oBAAI,IAAK;AAAA,IACrB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA,YAAY,SAAS,QAAQ,IAAI,KAAK;AAClC,YAAO;AAEP,WAAK,OAAO;AACZ,WAAK,KAAK;AACV,WAAK,SAAS;AACd,WAAK,cAAc;AAEnB,WAAK,eAAe,YAAY,KAAK,IAAI;AAAA,IACrD;AAAA,IACQ,cAAc;AACV,YAAM,qBAAqB,KAAK,aAAa,IAAI,IAAI,aAAa,KAAK,WAAW,CAAC;AACnF,yBAAmB,KAAK,UAAU,CAAC,EAAE,OAAM,MAAO;AAC9C,aAAK,QAAQ,oCAAoC,MAAM,EAAE;AAAA,MACzE,CAAa;AACD,YAAM,iBAAiB,KAAK,aAAa,IAAI,IAAI,aAAa,KAAK,QAAQ,CAAC;AAC5E,qBAAe,GAAG,kCAAkC,UAAQ;AACxD,YAAI,KAAK,WAAW,KAAK,IAAI;AACzB;AAAA,QACpB;AACgB,cAAM,kBAAkBa,iBAAgB,KAAK,KAAK,aAAa,MAAM,KAAK,SAAS,KAAK,GAAG;AAC3F,aAAK,UAAU,IAAI,KAAK,SAAS,eAAe;AAChD,cAAM,yBAAyB,KAAK,aAAa,IAAI,IAAI,aAAa,eAAe,CAAC;AACtF,+BAAuB,KAAK,UAAU,MAAM;AACxC,iCAAuB,mBAAoB;AAC3C,eAAK,UAAU,OAAO,gBAAgB,EAAE;AAAA,QAC5D,CAAiB;AACD,aAAK,KAAK,mBAAmB,EAAE,gBAAe,CAAE;AAAA,MAChE,CAAa;AACD,qBAAe,GAAG,oCAAoC,UAAQ;AAC1D,YAAI,KAAK,YAAY,KAAK,IAAI;AAC1B;AAAA,QACpB;AACgB,aAAK,QAAQ,kCAAkC;AAAA,MAC/D,CAAa;AACD,qBAAe,GAAG,oCAAoC,UAAQ;AAC1D,YAAI,KAAK,YAAY,KAAK,IAAI;AAC1B;AAAA,QACpB;AACgB,aAAK,OAAO,KAAK;AACjB,aAAK,KAAK,oBAAoB,MAAS;AAAA,MACvD,CAAa;AACD,qBAAe,GAAG,wBAAwB,UAAQ;AAC9C,YAAI,KAAK,YAAY,KAAK,IAAI;AAC1B;AAAA,QACpB;AACgB,aAAK,OAAO,KAAK;AACjB,aAAK,KAAK,QAAQ,MAAS;AAAA,MAC3C,CAAa;AACD,qBAAe,GAAG,qCAAqC,UAAQ;AAC3D,YAAI,KAAK,YAAY,KAAK,IAAI;AAC1B;AAAA,QACpB;AACgB,aAAK,OAAO,KAAK;AACjB,aAAK,UAAU,MAAO;AAEtB,aAAK,cAAc,WAAW,KAAK,IAAI;AACvC,cAAM,oBAAoB,KAAK,aAAa,IAAI,IAAI,aAAa,KAAK,WAAW,CAAC;AAClF,mBAAW,aAAa,CAAC,YAAY,UAAU,SAAS,GAAG;AACvD,4BAAkB,KAAK,WAAW,CAAC,EAAE,IAAG,MAAO;AAC3C,8BAAkB,aAAa,EAAG;AAClC,iBAAK,OAAO;AAAA,UACpC,CAAqB;AAAA,QACrB;AACgB,aAAK,KAAK,cAAc,EAAE,YAAY,KAAK,aAAa;AAAA,MACxE,CAAa;AACD,qBAAe,GAAG,6BAA6B,WAAS;AACpD,YAAI,MAAM,YAAY,KAAK,IAAI;AAC3B;AAAA,QACpB;AACgB,YAAI,KAAK,UAAU,IAAI,MAAM,QAAQ,OAAO,GAAG;AAC3C;AAAA,QACpB;AACgB,cAAM,UAAU,QAAQ,KAAK,MAAM,KAAK;AACxC,aAAK,UAAU,IAAI,QAAQ,IAAI,OAAO;AACtC,aAAK,KAAK,WAAW,EAAE,QAAO,CAAE;AAAA,MAChD,CAAa;AACD,qBAAe,GAAG,kBAAkB,WAAS;AACzC,YAAI,MAAM,OAAO,YAAY,KAAK,IAAI;AAClC;AAAA,QACpB;AACgB,aAAK,KAAK,OAAO,EAAE,MAAK,CAAE;AAAA,MAC1C,CAAa;AACD,qBAAe,GAAG,oCAAoC,UAAQ;AAC1D,YAAI,KAAK,YAAY,KAAK,IAAI;AAC1B;AAAA,QACpB;AACgB,cAAM,aAAa,WAAW,KAAK,MAAM,IAAI;AAC7C,aAAK,KAAK,cAAc,EAAE,WAAU,CAAE;AAAA,MACtD,CAAa;AAAA,IACb;AAAA;AAAA,IAEQ,IAAI,WAAW;AACX,aAAO,KAAK,YAAY,QAAQ;AAAA,IAC5C;AAAA,IACQ,IAAI,WAAW;AACX,aAAO,KAAK,UAAU,OAAQ;AAAA,IAC1C;AAAA,IACQ,IAAI,SAAS;AACT,aAAO,KAAK,YAAY;AAAA,IACpC;AAAA,IACQ,IAAI,WAAW;AACX,aAAO,KAAK;AAAA,IACxB;AAAA,IACQ,IAAI,SAAS;AACT,aAAO,KAAK,QAAQ,OAAQ;AAAA,IACxC;AAAA,IACQ,IAAI,MAAM;AACN,UAAI,UAAU;AACd,eAAS,EAAE,WAAW,SAAS,QAAQ,EAAE,OAAQ,IAAG,SAAS;AACzD,kBAAU;AAAA,MAC1B;AACY,aAAO;AAAA,IACnB;AAAA,IACQ,IAAI,MAAM;AACN,aAAO,KAAK;AAAA,IACxB;AAAA;AAAA,IAEQ,QAAQ,QAAQ;AACZ,WAAK,UAAU;AACf,WAAK,aAAa,EAAG;AAAA,IACjC;AAAA,IACQ,MAAM,WAAW;AACb,YAAM,KAAK,SAAS,KAAK,4BAA4B;AAAA,QACjD,SAAS,KAAK;AAAA,MAC9B,CAAa;AAAA,IACb;AAAA,IACQ,MAAM,kBAAkB,UAAU,IAAI;AAClC,YAAM,EAAE,QAAQ,EAAE,KAAM,EAAA,IAAM,MAAM,KAAK,SAAS,KAAK,qCAAqC;AAAA,QACxF,SAAS,KAAK;AAAA,QACd,GAAG;AAAA,MACnB,CAAa;AACD,aAAO;AAAA,IACnB;AAAA,IACQ,MAAM,MAAM,cAAc;AACtB,YAAM,QAAQ,IAAI,CAAC,GAAG,KAAK,UAAU,QAAQ,EAAE,IAAI,OAAO,UAAU;AAChE,cAAM,MAAM,MAAM,YAAY;AAAA,MAC9C,CAAa,CAAC;AACF,YAAM,KAAK,SAAS,KAAK,yBAAyB;AAAA,QAC9C,SAAS,KAAK;AAAA,QACd;AAAA,MAChB,CAAa;AAAA,IACb;AAAA,IACQ,MAAM,gBAAgB,OAAO;AACzB,YAAM,KAAK,SAAS,KAAK,mCAAmC;AAAA,QACxD,SAAS,KAAK;AAAA,QACd;AAAA,MAChB,CAAa;AAAA,IACb;AAAA,IACQ,MAAM,SAAS,KAAK,MAAM;AACtB,YAAM,KAAK,SAAS,KAAK,4BAA4B;AAAA,QACjD,SAAS,KAAK;AAAA,QACd;AAAA,QACA;AAAA,MAChB,CAAa;AACD,aAAO,MAAM,IAAI,QAAQ,aAAW;AAChC,aAAK,KAAK,cAAc,CAAC,EAAE,WAAU,MAAO;AACxC,kBAAQ,UAAU;AAAA,QACtC,CAAiB;AAAA,MACjB,CAAa;AAAA,IACb;AAAA,IACQ,MAAM,OAAO,UAAU,IAAI;AACvB,YAAM,KAAK,SAAS,KAAK,0BAA0B;AAAA,QAC/C,SAAS,KAAK;AAAA,QACd,GAAG;AAAA,MACnB,CAAa;AACD,aAAO,MAAM,IAAI,QAAQ,aAAW;AAChC,aAAK,KAAK,cAAc,CAAC,EAAE,WAAU,MAAO;AACxC,kBAAQ,UAAU;AAAA,QACtC,CAAiB;AAAA,MACjB,CAAa;AAAA,IACb;AAAA,IACQ,MAAM,MAAM,UAAU,IAAI;AACtB,YAAM,EAAE,QAAQ,EAAE,KAAM,EAAA,IAAM,MAAM,KAAK,SAAS,KAAK,yBAAyB;AAAA,QAC5E,SAAS,KAAK;AAAA,QACd,GAAG;AAAA,MACnB,CAAa;AACD,aAAO;AAAA,IACnB;AAAA,IACQ,MAAM,iBAAiB,UAAU,IAAI;AACjC,YAAM,KAAK,SAAS,KAAK,oCAAoC;AAAA,QACzD,SAAS,KAAK;AAAA,QACd,GAAG;AAAA,MACnB,CAAa;AAAA,IACb;AAAA,IACQ,MAAM,YAAY,UAAU,IAAI;AAC5B,YAAM,KAAK,SAAS,KAAK,+BAA+B;AAAA,QACpD,SAAS,KAAK;AAAA,QACd,GAAG;AAAA,MACnB,CAAa;AAAA,IACb;AAAA,IACQ,MAAM,eAAe,SAAS;AAC1B,YAAM,KAAK,SAAS,KAAK,wBAAwB;AAAA,QAC7C,SAAS,KAAK;AAAA,QACd;AAAA,MAChB,CAAa;AAAA,IACb;AAAA,IACQ,MAAM,iBAAiB;AACnB,YAAM,KAAK,SAAS,KAAK,wBAAwB;AAAA,QAC7C,SAAS,KAAK;AAAA,MAC9B,CAAa;AAAA,IACb;AAAA,IACQ,kBAAkB,SAAS;AACvB,aAAO,YAAY,KAAK,MAAM,OAAO;AAAA,IACjD;AAAA,IACQ,MAAM,iBAAiB,qBAAqB,UAAU,IAAI;AACtD,aAAO,MAAM,KAAK,YAAY,QAAQ,iBAAiB,qBAAqB;AAAA,QACxE,GAAG;AAAA,QACH,UAAU,CAAC,MAAM,GAAI,QAAQ,YAAY,CAAE,CAAC;AAAA,MAC5D,CAAa;AAAA,IACb;AAAA,IACQ,MAAM,oBAAoB,QAAQ;AAC9B,YAAM,KAAK,YAAY,QAAQ,oBAAoB,MAAM;AAAA,IACrE;AAAA,IACQ,EAAE,sBAAsB,CAAC,eAAe,GAAG,uBAAuB,CAAC,gBAAgB,aAAW;AAEtF,aAAO,QAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,gCAAgC,CAAC,gBAAgB,aAAW;AAE7D,aAAO,QAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,oBAAoB,CAAC,gBAAgB,aAAW;AAEjD,aAAO,QAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,8BAA8B,CAAC,gBAAgB,aAAW;AAE3D,aAAO,QAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,uBAAuB,CAAC,gBAAgB,aAAW;AAEpD,aAAO,QAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,qBAAqB,CAAC,gBAAgB,aAAW;AAElD,aAAO,QAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,oBAAoB,CAAC,gBAAgB,aAAW;AAEjD,aAAO,QAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,+BAA+B,CAAC,gBAAgB,aAAW;AAE5D,aAAO,QAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,0BAA0B,CAAC,gBAAgB,aAAW;AAEvD,aAAO,QAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,6BAA6B,CAAC,gBAAgB,aAAW;AAE1D,aAAO,QAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,6BAA6B,CAAC,gBAAgB,aAAW;AAE1D,aAAO,QAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,gCAAgC,CAAC,gBAAgB,aAAW;AAE7D,aAAO,QAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,+BAA+B,CAAC,gBAAgB,aAAW;AAE5D,aAAO,QAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,kCAAkC,CAAC,gBAAgB,aAAW;AAE/D,aAAO,QAAQ;AAAA,IAC/B,CAAa,CAAC,GAAG,kBAAkB;AACvB,WAAK,YACD;AACJ,WAAK,KAAK,UAAU,EAAE,QAAQ,KAAK,SAAS;AAC5C,WAAK,aAAa,QAAS;AAC3B,YAAM,aAAa,EAAG;AAAA,IAClC;AAAA,EACK;AACL,GAAI;AClXJ;AAAA;AAAA;AAAA;AAAA;AAKA,IAAIb,sBAAwD,SAAU,SAAS,cAAc,OAAO;AAChG,MAAI,WAAW,UAAU,SAAS;AAClC,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC1C,YAAQ,WAAW,aAAa,CAAC,EAAE,KAAK,SAAS,KAAK,IAAI,aAAa,CAAC,EAAE,KAAK,OAAO;AAAA,EAC9F;AACI,SAAO,WAAW,QAAQ;AAC9B;AACA,IAAIC,iBAA8C,SAAU,MAAM,cAAc,YAAY,WAAW,cAAc,mBAAmB;AACpI,WAAS,OAAO,GAAG;AAAE,QAAI,MAAM,UAAU,OAAO,MAAM,WAAY,OAAM,IAAI,UAAU,mBAAmB;AAAG,WAAO;AAAA,EAAE;AACrH,MAAI,OAAO,UAAU,MAAM,MAAM,SAAS,WAAW,QAAQ,SAAS,WAAW,QAAQ;AACzF,MAAI,SAAS,CAAC,gBAAgB,OAAO,UAAU,QAAQ,IAAI,OAAO,KAAK,YAAY;AACnF,MAAI,aAAa,iBAAiB,SAAS,OAAO,yBAAyB,QAAQ,UAAU,IAAI,IAAI;AACrG,MAAI,GAAG,OAAO;AACd,WAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,QAAI,UAAU,CAAE;AAChB,aAAS,KAAK,UAAW,SAAQ,CAAC,IAAI,MAAM,WAAW,CAAA,IAAK,UAAU,CAAC;AACvE,aAAS,KAAK,UAAU,OAAQ,SAAQ,OAAO,CAAC,IAAI,UAAU,OAAO,CAAC;AACtE,YAAQ,iBAAiB,SAAU,GAAG;AAAE,UAAI,KAAM,OAAM,IAAI,UAAU,wDAAwD;AAAG,wBAAkB,KAAK,OAAO,KAAK,IAAI,CAAC;AAAA,IAAI;AAC7K,QAAI,UAAS,GAAI,WAAW,CAAC,GAAG,SAAS,aAAa,EAAE,KAAK,WAAW,KAAK,KAAK,WAAW,IAAK,IAAG,WAAW,GAAG,GAAG,OAAO;AAC7H,QAAI,SAAS,YAAY;AACrB,UAAI,WAAW,OAAQ;AACvB,UAAI,WAAW,QAAQ,OAAO,WAAW,SAAU,OAAM,IAAI,UAAU,iBAAiB;AACxF,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,IAAI,EAAG,cAAa,QAAQ,CAAC;AAAA,IAC/D,WACiB,IAAI,OAAO,MAAM,GAAG;AACzB,UAAI,SAAS,QAAS,cAAa,QAAQ,CAAC;AAAA,UACvC,YAAW,GAAG,IAAI;AAAA,IACnC;AAAA,EACA;AACI,MAAI,OAAQ,QAAO,eAAe,QAAQ,UAAU,MAAM,UAAU;AACpE,SAAO;AACX;AASA,IAAI,eAAe,MAAM;AACrB,MAAI,cAAc;AAClB,MAAI,6BAA6B,CAAE;AACnC,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,SAAO,MAAMa,qBAAoB,YAAY;AAAA,IACzC,OAAA;AACI,YAAM,YAAY,OAAO,WAAW,cAAc,OAAO,WAAW,OAAO,OAAO,YAAY,OAAO,QAAQ,KAAK,IAAI,IAAI;AAC1Hb,qBAAa,MAAM,MAAM,qBAAqB,EAAE,MAAM,UAAU,MAAM,WAAW,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,aAAa,KAAK,KAAK,SAAO,IAAI,QAAS,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC1OA,qBAAa,MAAM,MAAM,mCAAmC,EAAE,MAAM,UAAU,MAAM,yBAAyB,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,2BAA2B,KAAK,KAAK,SAAO,IAAI,sBAAuB,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAClSA,qBAAa,MAAM,MAAM,oBAAoB,EAAE,MAAM,UAAU,MAAM,UAAU,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,YAAY,KAAK,KAAK,SAAO,IAAI,OAAQ,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AACtO,UAAI,UAAW,QAAO,eAAe,MAAM,OAAO,UAAU,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,OAAO,WAAW;AAAA,IAClJ;AAAA,IACQ,OAAO,UAAU;AAAA,IACjB,OAAO,OAAO,SAAS,IAAI;AACvB,YAAM,UAAU,IAAIa,aAAY,SAAS,EAAE;AAC3C,cAAQ,YAAa;AACrB,aAAO;AAAA,IACnB;AAAA;AAAA,IAEQ,WAAWd,oBAAkB,MAAM,0BAA0B,GAAG;AAAA;AAAA,IAEhE,oBAAoB,oBAAI,IAAK;AAAA,IAC7B,eAAe,IAAI,gBAAiB;AAAA,IACpC;AAAA,IACA;AAAA;AAAA,IAEA,YAAY,SAAS,IAAI;AACrB,YAAO;AAEP,WAAK,MAAM;AACX,WAAK,UAAU;AAAA,IAE3B;AAAA,IACQ,cAAc;AACV,YAAM,iBAAiB,KAAK,aAAa,IAAI,IAAI,aAAa,KAAK,OAAO,CAAC;AAC3E,qBAAe,KAAK,UAAU,CAAC,EAAE,OAAM,MAAO;AAC1C,aAAK,QAAQ,gCAAgC,MAAM,EAAE;AAAA,MACrE,CAAa;AACD,YAAM,iBAAiB,KAAK,aAAa,IAAI,IAAI,aAAa,KAAK,QAAQ,CAAC;AAC5E,qBAAe,GAAG,kCAAkC,UAAQ;AACxD,YAAI,KAAK,QAAQ;AACb;AAAA,QACpB;AACgB,YAAI,KAAK,gBAAgB,KAAK,KAAK;AAC/B;AAAA,QACpB;AACgB,cAAM,kBAAkBa,iBAAgB,KAAK,MAAM,QAAW,KAAK,SAAS,KAAK,GAAG;AACpF,aAAK,kBAAkB,IAAI,gBAAgB,IAAI,eAAe;AAC9D,cAAM,yBAAyB,KAAK,aAAa,IAAI,IAAI,aAAa,eAAe,CAAC;AACtF,+BAAuB,GAAG,UAAU,MAAM;AACtC,iCAAuB,mBAAoB;AAC3C,eAAK,kBAAkB,OAAO,gBAAgB,EAAE;AAAA,QACpE,CAAiB;AACD,aAAK,KAAK,mBAAmB,EAAE,gBAAe,CAAE;AAAA,MAChE,CAAa;AAAA,IACb;AAAA;AAAA,IAEQ,IAAI,WAAW;AACX,aAAO,KAAK,QAAQ;AAAA,IAChC;AAAA,IACQ,IAAI,mBAAmB;AACnB,aAAO,KAAK,kBAAkB,OAAQ;AAAA,IAClD;AAAA,IACQ,IAAI,SAAS;AACT,aAAO,KAAK,YAAY;AAAA,IACpC;AAAA,IACQ,IAAI,WAAW;AACX,aAAO,KAAK;AAAA,IACxB;AAAA,IACQ,IAAI,KAAK;AACL,aAAO,KAAK;AAAA,IACxB;AAAA;AAAA,IAEQ,QAAQ,QAAQ;AACZ,WAAK,UAAU;AACf,WAAK,aAAa,EAAG;AAAA,IACjC;AAAA,IACQ,MAAM,sBAAsB,MAAM,UAAU,IAAI;AAC5C,YAAM,EAAE,QAAQ,EAAE,SAAS,UAAS,MAAQ,MAAM,KAAK,SAAS,KAAK,0BAA0B;AAAA,QAC3F;AAAA,QACA,GAAG;AAAA,QACH,kBAAkB,QAAQ,kBAAkB;AAAA,QAC5C,aAAa,KAAK;AAAA,MAClC,CAAa;AACD,YAAM,kBAAkB,KAAK,kBAAkB,IAAI,SAAS;AAC5D,aAAO,iBAAiB,sFAAsF;AAE9G,aAAO;AAAA,IACnB;AAAA,IACQ,MAAM,SAAS;AACX,UAAI;AACA,cAAM,KAAK,SAAS,KAAK,6BAA6B;AAAA,UAClD,aAAa,KAAK;AAAA,QACtC,CAAiB;AAAA,MACjB,UACoB;AACJ,aAAK,QAAQ,8BAA8B;AAAA,MAC3D;AAAA,IACA;AAAA,IACQ,EAAE,sBAAsB,CAAC,eAAe,GAAG,oCAAoC,CAAC,gBAAgB,aAAW;AAEnG,aAAO,QAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,qBAAqB,CAAC,gBAAgB,aAAW;AAElD,aAAO,QAAQ;AAAA,IAC/B,CAAa,CAAC,GAAG,kBAAkB;AACvB,WAAK,YACD;AACJ,WAAK,KAAK,UAAU,EAAE,QAAQ,KAAK,SAAS;AAC5C,WAAK,aAAa,QAAS;AAC3B,YAAM,aAAa,EAAG;AAAA,IAClC;AAAA,EACK;AACL,GAAI;AClKJ;AAAA;AAAA;AAAA;AAAA;AAYO,MAAM,2BAA2B,eAAe;AAAA,EACnD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,SAAS,aAAa,SAAS;AACvC,UAAO;AACP,SAAK,WAAW;AAChB,SAAK,eAAe;AACpB,SAAK,cAAc,KAAK,SAAS;AACjC,SAAK,mBAAmB,QAAQ;AAAA,EACxC;AAAA,EACI,UAAU;AACN,WAAO,KAAK,SAAS,QAAO,EAAG,OAAO,YAAU;AAC5C,aAAO,OAAO,eAAc,MAAO;AAAA,IAC/C,CAAS;AAAA,EACT;AAAA,EACI,cAAc,WAAW,UAAU,IAAI;AACnC,WAAO,KAAK,SAAS,cAAc,YAAU;AACzC,aAAO,OAAO,eAAc,MAAO,QAAQ,UAAU,MAAM;AAAA,IAC9D,GAAE,OAAO;AAAA,EAClB;AAAA,EACI,IAAI,aAAa;AACb,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,MAAM,UAAU;AACZ,UAAM,EAAE,OAAM,IAAK,MAAM,KAAK,YAAY,KAAK,0BAA0B;AAAA,MACrE,MAAM;AAAA,MACN,aAAa,KAAK,aAAa;AAAA,IAC3C,CAAS;AACD,UAAM,SAAS,KAAK,SAAS,eAAe,OAAO,OAAO;AAM1D,WAAO,mBAAmB,IAAI;AAC9B,UAAM,OAAO,MAAM,OAAO,KAAM;AAChC,QAAI,CAAC,MAAM;AACP,YAAM,IAAI,MAAM,mBAAmB;AAAA,IAC/C;AACQ,QAAI,KAAK,kBAAkB;AACvB,UAAI;AACA,cAAM,KAAK,YAAY,KAAK,gBAAgB;AAAA,MAC5D,QACkB;AAAA,MAElB;AAAA,IACA;AACQ,WAAO;AAAA,EACf;AAAA,EACI,MAAM,QAAQ;AACV,QAAI,CAAC,KAAK,eAAe;AACrB,YAAM,IAAI,MAAM,mCAAmC;AAAA,IAC/D;AACQ,QAAI;AACA,YAAM,KAAK,aAAa,OAAQ;AAAA,IAC5C,SACe,OAAO;AACV,iBAAW,KAAK;AAAA,IAC5B;AAAA,EACA;AAAA,EACI,UAAU;AACN,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,MAAM,QAAQ;AACV,UAAM,UAAU,MAAM,QAAQ,IAAI,CAAC,GAAG,KAAK,QAAS,CAAA,EAAE,IAAI,OAAK;AAC3D,aAAO,EAAE,KAAM;AAAA,IAC3B,CAAS,CAAC;AACF,WAAO,QAAQ,OAAO,CAAC,MAAM;AACzB,aAAO,MAAM;AAAA,IACzB,CAAS;AAAA,EACT;AAAA,EACI,cAAc;AACV,WAAO,KAAK,aAAa,OAAO,YAAY;AAAA,EACpD;AAAA,EACI,sBAAsB;AAClB,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,2BAA2B;AACvB,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,IAAI,KAAK;AACL,QAAI,KAAK,aAAa,OAAO,WAAW;AACpC,aAAO;AAAA,IACnB;AACQ,WAAO,KAAK,aAAa;AAAA,EACjC;AACA;ACpGA;AAAA;AAAA;AAAA;AAAA;AAKA,IAAIb,sBAAwD,SAAU,SAAS,cAAc,OAAO;AAChG,MAAI,WAAW,UAAU,SAAS;AAClC,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC1C,YAAQ,WAAW,aAAa,CAAC,EAAE,KAAK,SAAS,KAAK,IAAI,aAAa,CAAC,EAAE,KAAK,OAAO;AAAA,EAC9F;AACI,SAAO,WAAW,QAAQ;AAC9B;AACA,IAAIC,iBAA8C,SAAU,MAAM,cAAc,YAAY,WAAW,cAAc,mBAAmB;AACpI,WAAS,OAAO,GAAG;AAAE,QAAI,MAAM,UAAU,OAAO,MAAM,WAAY,OAAM,IAAI,UAAU,mBAAmB;AAAG,WAAO;AAAA,EAAE;AACrH,MAAI,OAAO,UAAU,MAAM,MAAM,SAAS,WAAW,QAAQ,SAAS,WAAW,QAAQ;AACzF,MAAI,SAAS,CAAC,gBAAgB,OAAO,UAAU,QAAQ,IAAI,OAAO,KAAK,YAAY;AACnF,MAAI,aAAa,iBAAiB,SAAS,OAAO,yBAAyB,QAAQ,UAAU,IAAI,IAAI;AACrG,MAAI,GAAG,OAAO;AACd,WAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,QAAI,UAAU,CAAE;AAChB,aAAS,KAAK,UAAW,SAAQ,CAAC,IAAI,MAAM,WAAW,CAAA,IAAK,UAAU,CAAC;AACvE,aAAS,KAAK,UAAU,OAAQ,SAAQ,OAAO,CAAC,IAAI,UAAU,OAAO,CAAC;AACtE,YAAQ,iBAAiB,SAAU,GAAG;AAAE,UAAI,KAAM,OAAM,IAAI,UAAU,wDAAwD;AAAG,wBAAkB,KAAK,OAAO,KAAK,IAAI,CAAC;AAAA,IAAI;AAC7K,QAAI,UAAS,GAAI,WAAW,CAAC,GAAG,SAAS,aAAa,EAAE,KAAK,WAAW,KAAK,KAAK,WAAW,IAAK,IAAG,WAAW,GAAG,GAAG,OAAO;AAC7H,QAAI,SAAS,YAAY;AACrB,UAAI,WAAW,OAAQ;AACvB,UAAI,WAAW,QAAQ,OAAO,WAAW,SAAU,OAAM,IAAI,UAAU,iBAAiB;AACxF,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,IAAI,EAAG,cAAa,QAAQ,CAAC;AAAA,IAC/D,WACiB,IAAI,OAAO,MAAM,GAAG;AACzB,UAAI,SAAS,QAAS,cAAa,QAAQ,CAAC;AAAA,UACvC,YAAW,GAAG,IAAI;AAAA,IACnC;AAAA,EACA;AACI,MAAI,OAAQ,QAAO,eAAe,QAAQ,UAAU,MAAM,UAAU;AACpE,SAAO;AACX;AACA,IAAIC,4BAAoE,SAAU,KAAK,OAAO,OAAO;AACjG,MAAI,UAAU,QAAQ,UAAU,QAAQ;AACpC,QAAI,OAAO,UAAU,YAAY,OAAO,UAAU,WAAY,OAAM,IAAI,UAAU,kBAAkB;AACpG,QAAI;AACJ,QAAI,OAAO;AACP,UAAI,CAAC,OAAO,aAAc,OAAM,IAAI,UAAU,qCAAqC;AACnF,gBAAU,MAAM,OAAO,YAAY;AAAA,IAC/C;AACQ,QAAI,YAAY,QAAQ;AACpB,UAAI,CAAC,OAAO,QAAS,OAAM,IAAI,UAAU,gCAAgC;AACzE,gBAAU,MAAM,OAAO,OAAO;AAAA,IAC1C;AACQ,QAAI,OAAO,YAAY,WAAY,OAAM,IAAI,UAAU,wBAAwB;AAC/E,QAAI,MAAM,KAAK,EAAE,OAAc,SAAkB,OAAc;AAAA,EACvE,WACa,OAAO;AACZ,QAAI,MAAM,KAAK,EAAE,OAAO,KAAI,CAAE;AAAA,EACtC;AACI,SAAO;AACX;AACA,IAAIC,uBAA2D,yBAAUC,kBAAiB;AACtF,SAAO,SAAU,KAAK;AAClB,aAAS,KAAK,GAAG;AACb,UAAI,QAAQ,IAAI,WAAW,IAAIA,iBAAgB,GAAG,IAAI,OAAO,0CAA0C,IAAI;AAC3G,UAAI,WAAW;AAAA,IAC3B;AACQ,aAAS,OAAO;AACZ,aAAO,IAAI,MAAM,QAAQ;AACrB,YAAI,MAAM,IAAI,MAAM,IAAK;AACzB,YAAI;AACA,cAAI,SAAS,IAAI,WAAW,IAAI,QAAQ,KAAK,IAAI,KAAK;AACtD,cAAI,IAAI,MAAO,QAAO,QAAQ,QAAQ,MAAM,EAAE,KAAK,MAAM,SAAS,GAAG;AAAE,iBAAK,CAAC;AAAG,mBAAO,KAAM;AAAA,WAAG;AAAA,QACpH,SACuB,GAAG;AACN,eAAK,CAAC;AAAA,QAC1B;AAAA,MACA;AACY,UAAI,IAAI,SAAU,OAAM,IAAI;AAAA,IACxC;AACQ,WAAO,KAAM;AAAA,EAChB;AACL,EAAG,OAAO,oBAAoB,aAAa,kBAAkB,SAAU,OAAO,YAAY,SAAS;AAC/F,MAAI,IAAI,IAAI,MAAM,OAAO;AACzB,SAAO,EAAE,OAAO,mBAAmB,EAAE,QAAQ,OAAO,EAAE,aAAa,YAAY;AACnF,CAAC;AAQD,IAAI,WAAW,MAAM;AACjB,MAAI,cAAc;AAClB,MAAI,6BAA6B,CAAE;AACnC,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,SAAO,MAAMW,iBAAgB,YAAY;AAAA,IACrC,OAAA;AACI,YAAM,YAAY,OAAO,WAAW,cAAc,OAAO,WAAW,OAAO,OAAO,YAAY,OAAO,QAAQ,KAAK,IAAI,IAAI;AAC1Hd,qBAAa,MAAM,MAAM,qBAAqB,EAAE,MAAM,UAAU,MAAM,WAAW,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,aAAa,KAAK,KAAK,SAAO,IAAI,QAAS,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC1OA,qBAAa,MAAM,MAAM,mBAAmB,EAAE,MAAM,UAAU,MAAM,SAAS,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,WAAW,KAAK,KAAK,SAAO,IAAI,MAAO,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAClOA,qBAAa,MAAM,MAAM,8BAA8B,EAAE,MAAM,UAAU,MAAM,oBAAoB,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,sBAAsB,KAAK,KAAK,SAAO,IAAI,iBAAkB,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC9QA,qBAAa,MAAM,MAAM,iCAAiC,EAAE,MAAM,UAAU,MAAM,uBAAuB,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,yBAAyB,KAAK,KAAK,SAAO,IAAI,oBAAqB,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC1RA,qBAAa,MAAM,MAAM,+BAA+B,EAAE,MAAM,UAAU,MAAM,qBAAqB,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,uBAAuB,KAAK,KAAK,SAAO,IAAI,kBAAmB,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAClR,UAAI,UAAW,QAAO,eAAe,MAAM,OAAO,UAAU,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,OAAO,WAAW;AAAA,IAClJ;AAAA,IACQ,aAAa,KAAK,SAAS;AACvB,YAAM,UAAU,IAAIc,SAAQ,OAAO;AACnC,YAAM,QAAQ,YAAa;AAC3B,aAAO;AAAA,IACnB;AAAA;AAAA,IAEQ,WAAWf,oBAAkB,MAAM,0BAA0B,GAAG;AAAA,IAChE;AAAA,IACA,eAAe,IAAI,gBAAiB;AAAA,IACpC,gBAAgB,oBAAI,IAAK;AAAA,IACzB;AAAA;AAAA,IAEA,YAAY,SAAS;AACjB,YAAO;AAEP,WAAK,UAAU;AAEf,WAAK,cAAc,IAAI,YAAY,SAAS,YAAY,OAAO,MAAM,YAAY,OAAO,CAAC;AAAA,IACrG;AAAA,IACQ,MAAM,cAAc;AAChB,YAAM,iBAAiB,KAAK,aAAa,IAAI,IAAI,aAAa,KAAK,OAAO,CAAC;AAC3E,qBAAe,KAAK,SAAS,CAAC,EAAE,OAAM,MAAO;AACzC,aAAK,QAAQ,MAAM;AAAA,MACnC,CAAa;AACD,qBAAe,GAAG,uBAAuB,UAAQ;AAC7C,YAAI,KAAK,SAAS,gBAAiB;AAAA,MAGnD,CAAa;AACD,YAAM,KAAK,kBAAmB;AAC9B,YAAM,KAAK,sBAAuB;AAAA,IAC9C;AAAA,IACQ,MAAM,oBAAoB;AACtB,YAAM,EAAE,QAAQ,EAAE,eAAiB,IAAG,MAAM,KAAK,QAAQ,KAAK,2BAA2B,EAAE;AAC3F,iBAAW,WAAW,cAAc;AAChC,YAAI,QAAQ,gBAAgB,YAAY,SAAS;AAC7C;AAAA,QACpB;AACgB,aAAK,cAAc,IAAI,QAAQ,aAAa,YAAY,OAAO,MAAM,QAAQ,WAAW,CAAC;AAAA,MACzG;AAAA,IACA;AAAA,IACQ,MAAM,wBAAwB;AAG1B,YAAM,aAAa,oBAAI,IAAK;AAC5B,UAAI;AACJ;AACI,cAAM,QAAQ,EAAE,OAAO,CAAA,GAAI,OAAO,QAAQ,UAAU,MAAO;AAC3D,YAAI;AACA,gBAAM,iBAAiBE,0BAAwB,OAAO,IAAI,aAAa,KAAK,OAAO,GAAG,KAAK;AAC3F,yBAAe,GAAG,kCAAkC,UAAQ;AACxD,uBAAW,IAAI,KAAK,OAAO;AAAA,UACnD,CAAqB;AACD,yBAAe,GAAG,oCAAoC,UAAQ;AAC1D,uBAAW,OAAO,KAAK,OAAO;AAAA,UACtD,CAAqB;AACD,gBAAM,EAAE,OAAM,IAAK,MAAM,KAAK,QAAQ,KAAK,2BAA2B,EAAE;AACxE,qBAAW,OAAO;AAAA,QACtC,SACuB,KAAK;AACR,gBAAM,QAAQ;AACd,gBAAM,WAAW;AAAA,QACrC,UACwB;AACJC,+BAAmB,KAAK;AAAA,QAC5C;AAAA,MACA;AAEY,iBAAW,QAAQ,UAAU;AACzB,YAAI,WAAW,IAAI,KAAK,OAAO,GAAG;AAC9B,eAAK,QAAQ,KAAK,kCAAkC,IAAI;AAAA,QAC5E;AACgB,YAAI,KAAK,UAAU;AACf,mBAAS,KAAK,GAAG,KAAK,QAAQ;AAAA,QAClD;AAAA,MACA;AAAA,IACA;AAAA;AAAA,IAEQ,IAAI,SAAS;AACT,aAAO,KAAK;AAAA,IACxB;AAAA,IACQ,IAAI,qBAAqB;AAErB,aAAO,KAAK,cAAc,IAAI,YAAY,OAAO;AAAA,IAC7D;AAAA,IACQ,IAAI,eAAe;AACf,aAAO,KAAK,YAAY;AAAA,IACpC;AAAA,IACQ,IAAI,WAAW;AACX,aAAO,KAAK;AAAA,IACxB;AAAA,IACQ,IAAI,eAAe;AACf,aAAO,KAAK,cAAc,OAAQ;AAAA,IAC9C;AAAA;AAAA,IAEQ,QAAQ,QAAQ,SAAS,OAAO;AAC5B,WAAK,UAAU;AACf,WAAK,UAAU;AACf,WAAK,aAAa,EAAG;AAAA,IACjC;AAAA,IACQ,MAAM,QAAQ;AACV,UAAI;AACA,cAAM,KAAK,QAAQ,KAAK,iBAAiB,CAAA,CAAE;AAAA,MAC3D,UACoB;AACJ,aAAK,QAAQ,2BAA2B,IAAI;AAAA,MAC5D;AAAA,IACA;AAAA,IACQ,MAAM,iBAAiB,qBAAqB,UAAU,IAAI;AACtD,YAAM,EAAE,QAAQ,EAAE,OAAQ,EAAA,IAAM,MAAM,KAAK,QAAQ,KAAK,2BAA2B;AAAA,QAC/E;AAAA,QACA,GAAG;AAAA,QACH,UAAU,QAAQ,UAAU,IAAI,aAAW;AACvC,iBAAO,QAAQ;AAAA,QACnC,CAAiB;AAAA,MACjB,CAAa;AACD,aAAO;AAAA,IACnB;AAAA,IACQ,MAAM,oBAAoB,QAAQ;AAC9B,YAAM,KAAK,QAAQ,KAAK,8BAA8B;AAAA,QAClD;AAAA,MAChB,CAAa;AAAA,IACb;AAAA,IACQ,MAAM,oBAAoB;AACtB,YAAM,EAAE,QAAQ,EAAE,aAAa,QAAS,EAAA,IAAM,MAAM,KAAK,QAAQ,KAAK,6BAA6B,CAAA,CAAE;AACrG,YAAM,cAAc,YAAY,OAAO,MAAM,OAAO;AACpD,WAAK,cAAc,IAAI,YAAY,IAAI,WAAW;AAClD,YAAM,qBAAqB,KAAK,aAAa,IAAI,IAAI,aAAa,WAAW,CAAC;AAC9E,yBAAmB,KAAK,UAAU,MAAM;AACpC,2BAAmB,mBAAoB;AACvC,aAAK,cAAc,OAAO,OAAO;AAAA,MACjD,CAAa;AACD,aAAO;AAAA,IACnB;AAAA,IACQ,EAAE,sBAAsB,CAAC,eAAe,GAAG,oBAAoB,CAAC,gBAAgB,aAAW;AAEnF,aAAO,QAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,+BAA+B,CAAC,gBAAgB,aAAW;AAE5D,aAAO,QAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,kCAAkC,CAAC,gBAAgB,aAAW;AAE/D,aAAO,QAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,gCAAgC,CAAC,gBAAgB,aAAW;AAE7D,aAAO,QAAQ;AAAA,IAC/B,CAAa,CAAC,GAAG,kBAAkB;AACvB,WAAK,YACD;AACJ,UAAI,KAAK,QAAQ;AACb,aAAK,KAAK,UAAU,EAAE,QAAQ,KAAK,SAAS;AAAA,MAC5D;AACY,WAAK,KAAK,gBAAgB,EAAE,QAAQ,KAAK,SAAS;AAClD,WAAK,aAAa,QAAS;AAC3B,YAAM,aAAa,EAAG;AAAA,IAClC;AAAA,EACK;AACL,GAAI;AC1QJ;AAAA;AAAA;AAAA;AAAA;AAKA,IAAIH,sBAAwD,SAAU,SAAS,cAAc,OAAO;AAChG,MAAI,WAAW,UAAU,SAAS;AAClC,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC1C,YAAQ,WAAW,aAAa,CAAC,EAAE,KAAK,SAAS,KAAK,IAAI,aAAa,CAAC,EAAE,KAAK,OAAO;AAAA,EAC9F;AACI,SAAO,WAAW,QAAQ;AAC9B;AACA,IAAIC,iBAA8C,SAAU,MAAM,cAAc,YAAY,WAAW,cAAc,mBAAmB;AACpI,WAAS,OAAO,GAAG;AAAE,QAAI,MAAM,UAAU,OAAO,MAAM,WAAY,OAAM,IAAI,UAAU,mBAAmB;AAAG,WAAO;AAAA,EAAE;AACrH,MAAI,OAAO,UAAU,MAAM,MAAM,SAAS,WAAW,QAAQ,SAAS,WAAW,QAAQ;AACzF,MAAI,SAAS,CAAC,gBAAgB,OAAO,UAAU,QAAQ,IAAI,OAAO,KAAK,YAAY;AACnF,MAAI,aAAa,iBAAiB,SAAS,OAAO,yBAAyB,QAAQ,UAAU,IAAI,IAAI;AACrG,MAAI,GAAG,OAAO;AACd,WAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,QAAI,UAAU,CAAE;AAChB,aAAS,KAAK,UAAW,SAAQ,CAAC,IAAI,MAAM,WAAW,CAAA,IAAK,UAAU,CAAC;AACvE,aAAS,KAAK,UAAU,OAAQ,SAAQ,OAAO,CAAC,IAAI,UAAU,OAAO,CAAC;AACtE,YAAQ,iBAAiB,SAAU,GAAG;AAAE,UAAI,KAAM,OAAM,IAAI,UAAU,wDAAwD;AAAG,wBAAkB,KAAK,OAAO,KAAK,IAAI,CAAC;AAAA,IAAI;AAC7K,QAAI,UAAS,GAAI,WAAW,CAAC,GAAG,SAAS,aAAa,EAAE,KAAK,WAAW,KAAK,KAAK,WAAW,IAAK,IAAG,WAAW,GAAG,GAAG,OAAO;AAC7H,QAAI,SAAS,YAAY;AACrB,UAAI,WAAW,OAAQ;AACvB,UAAI,WAAW,QAAQ,OAAO,WAAW,SAAU,OAAM,IAAI,UAAU,iBAAiB;AACxF,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,IAAI,EAAG,cAAa,QAAQ,CAAC;AAAA,IAC/D,WACiB,IAAI,OAAO,MAAM,GAAG;AACzB,UAAI,SAAS,QAAS,cAAa,QAAQ,CAAC;AAAA,UACvC,YAAW,GAAG,IAAI;AAAA,IACnC;AAAA,EACA;AACI,MAAI,OAAQ,QAAO,eAAe,QAAQ,UAAU,MAAM,UAAU;AACpE,SAAO;AACX;AAWA,IAAI,WAAW,MAAM;AACjB,MAAI,cAAc;AAClB,MAAI,6BAA6B,CAAE;AACnC,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,SAAO,MAAMe,iBAAgB,YAAY;AAAA,IACrC,OAAA;AACI,YAAM,YAAY,OAAO,WAAW,cAAc,OAAO,WAAW,OAAO,OAAO,YAAY,OAAO,QAAQ,KAAK,IAAI,IAAI;AAC1Hf,qBAAa,MAAM,MAAM,qBAAqB,EAAE,MAAM,UAAU,MAAM,WAAW,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,aAAa,KAAK,KAAK,SAAO,IAAI,QAAS,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC1OA,qBAAa,MAAM,MAAM,kBAAkB,EAAE,MAAM,UAAU,MAAM,QAAQ,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,UAAU,KAAK,KAAK,SAAO,IAAI,KAAM,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC9NA,qBAAa,MAAM,MAAM,uBAAuB,EAAE,MAAM,UAAU,MAAM,aAAa,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,eAAe,KAAK,KAAK,SAAO,IAAI,UAAW,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAClPA,qBAAa,MAAM,MAAM,iBAAiB,EAAE,MAAM,UAAU,MAAM,OAAO,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,SAAS,KAAK,KAAK,SAAO,IAAI,IAAK,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC1N,UAAI,UAAW,QAAO,eAAe,MAAM,OAAO,UAAU,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,OAAO,WAAW;AAAA,IAClJ;AAAA,IACQ,aAAa,KAAK,YAAY,cAAc;AAmBxC,UAAI;AACJ,UAAI;AACA,kBAAU,MAAM,WAAW,KAAK,eAAe;AAAA,UAC3C;AAAA,QACH,CAAA,GAAG;AAAA,MACpB,SACmB,KAAK;AAER,mBAAW,GAAG;AACd,iBAAS;AAAA,UACL,WAAW;AAAA,UACX,cAAc;AAAA,YACV,qBAAqB;AAAA,YACrB,aAAa;AAAA,YACb,gBAAgB;AAAA,YAChB,cAAc;AAAA,YACd,eAAe;AAAA,YACf,cAAc;AAAA,UACjB;AAAA,QACJ;AAAA,MACjB;AACY,YAAM,UAAU,IAAIe,SAAQ,YAAY,MAAM;AAC9C,YAAM,QAAQ,YAAa;AAC3B,aAAO;AAAA,IACnB;AAAA;AAAA,IAEQ,WAAWhB,oBAAkB,MAAM,0BAA0B,GAAG;AAAA,IAChE,eAAe,IAAI,gBAAiB;AAAA,IACpC;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA,YAAY,YAAY,MAAM;AAC1B,YAAO;AAEP,WAAK,QAAQ;AACb,WAAK,aAAa;AAAA,IAE9B;AAAA,IACQ,MAAM,cAAc;AAChB,WAAK,WAAW,OAAO,IAAI;AAE3B,WAAK,UAAU,MAAM,QAAQ,KAAK,IAAI;AACtC,YAAM,iBAAiB,KAAK,aAAa,IAAI,KAAK,OAAO;AACzD,qBAAe,KAAK,UAAU,CAAC,EAAE,OAAM,MAAO;AAC1C,aAAK,QAAQ,MAAM;AAAA,MACnC,CAAa;AAAA,IACb;AAAA;AAAA,IAEQ,IAAI,eAAe;AACf,aAAO,KAAK,MAAM;AAAA,IAC9B;AAAA,IACQ,IAAI,WAAW;AACX,aAAO,KAAK;AAAA,IACxB;AAAA,IACQ,IAAI,QAAQ;AACR,aAAO,KAAK,YAAY;AAAA,IACpC;AAAA,IACQ,IAAI,KAAK;AACL,aAAO,KAAK,MAAM;AAAA,IAC9B;AAAA;AAAA,IAEQ,QAAQ,QAAQ;AACZ,WAAK,UAAU;AACf,WAAK,aAAa,EAAG;AAAA,IACjC;AAAA,IACQ,OAAO,SAAS;AACZ,WAAK,WAAW,OAAO,OAAO;AAAA,IAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQQ,MAAM,KAAK,QAAQ,QAAQ;AACvB,aAAO,MAAM,KAAK,WAAW,KAAK,QAAQ,MAAM;AAAA,IAC5D;AAAA,IACQ,MAAM,UAAU,QAAQ;AACpB,YAAM,KAAK,KAAK,qBAAqB;AAAA,QACjC;AAAA,MAChB,CAAa;AAAA,IACb;AAAA,IACQ,MAAM,MAAM;AACR,UAAI;AACA,cAAM,KAAK,KAAK,eAAe,EAAE;AAAA,MACjD,UACoB;AACJ,aAAK,QAAQ,wBAAwB;AAAA,MACrD;AAAA,IACA;AAAA,IACQ,EAAE,sBAAsB,CAAC,eAAe,GAAG,mBAAmB,CAAC,gBAAgB,aAAW;AAElF,aAAO,QAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,wBAAwB,CAAC,gBAAgB,aAAW;AAErD,aAAO,QAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,kBAAkB,CAAC,gBAAgB,aAAW;AAE/C,aAAO,QAAQ;AAAA,IAC/B,CAAa,CAAC,GAAG,kBAAkB;AACvB,WAAK,YACD;AACJ,WAAK,KAAK,SAAS,EAAE,QAAQ,KAAK,SAAS;AAC3C,WAAK,aAAa,QAAS;AAC3B,YAAM,aAAa,EAAG;AAAA,IAClC;AAAA,EACK;AACL,GAAI;ACjMJ;AAAA;AAAA;AAAA;AAAA;AASO,MAAM,mBAAmB,OAAO;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY,SAAS,MAAM,SAAS,cAAc;AAC9C,UAAM,MAAM,SAAS,YAAY;AACjC,SAAK,WAAW;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAII,MAAM,OAAO,SAAS;AAClB,UAAM,KAAK,SAAS,WAAW,KAAK,oCAAoC;AAAA,MACpE,SAAS,KAAK,SAAS;AAAA,MACvB,QAAQ,QAAQ;AAAA,MAChB,UAAU,QAAQ;AAAA,IAC9B,CAAS;AAAA,EACT;AACA;ACzBO,MAAM,iBAAiB;AAAA,EAC1B;AAAA,EACA,YAAY,iBAAiB;AACzB,SAAK,mBAAmB;AAAA,EAChC;AAAA,EACI,MAAM,gBAAgB,UAAU;AAC5B,UAAM,KAAK,iBAAiB,WAAW,KAAK,+BAA+B;AAAA,MACvE,SAAS,KAAK,iBAAiB;AAAA,MAC/B,UAAU,SAAS,SAAS,SAAS,SAC/B;AAAA,QACE,OAAO,SAAS;AAAA,QAChB,QAAQ,SAAS;AAAA,MACrC,IACkB;AAAA,MACN,kBAAkB,SAAS,oBACrB,SAAS,oBACT;AAAA,IAClB,CAAS;AAAA,EACT;AACA;ACtBA;AAAA;AAAA;AAAA;AAAA;AAeO,MAAM,mBAAmB;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,eAAe,oBAAI,IAAK;AAAA,EACxB;AAAA,EACA,YAAY,OAAO,MAAM,OAAO;AAC5B,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,YAAY;AAAA,MACb,MAAM,gBAAgB,KAAK,OAAO,GAAG,wBAAwB,KAAK,IAAI;AAAA,MACtE,SAAS,gBAAgB,KAAK,OAAO,GAAG,wBAAwB,KAAK,IAAI;AAAA,MACzE,QAAQ,gBAAgB,KAAK,OAAO,GAAG,wBAAwB,KAAK,IAAI;AAAA,IAC3E;AAAA,EACT;AAAA,EACI,MAAM,SAAS;AACX,UAAM,aAAa,KAAK;AACxB,UAAM,mBAAmB,KAAK;AAE9B,eAAW,GAAGK,sBAAkB,OAAO,WAAW,SAAS,KAAK,uBAAuB;AACvF,eAAW,GAAGA,sBAAkB,OAAO,WAAW,SAAS,KAAK,qBAAqB;AACrF,eAAW,GAAGA,sBAAkB,OAAO,WAAW,SAAS,KAAK,oBAAoB;AACpF,UAAM,sBAAsB,kBAAkB,oBAAoB,CAAC,UAAU,aAAa,eAAe;AACrG,UAAI,KAAK;AACT,aAAO,OAAO,YAAY;AAAA,QACtB,CAAC,YAAY,MAAM,CAAC,GAAG,YAAa,MAAM;AACtC,iBAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,qBAAS,CAAC,IAAI,IAAI,CAAC;AACnB,wBAAY,CAAC,IAAI,OAAO,CAAC;AACzB,uBAAW,CAAC,IAAI,MAAM,CAAC;AACvB,cAAE;AAAA,UAC1B,CAAqB;AAAA,QACJ;AAAA,MACjB,CAAa;AAAA,IACb,GAAW,EAAE,MAAM,KAAK,UAAU,KAAK,IAAI,EAAC,CAAE,CAAC;AACvC,UAAM,EAAE,OAAQ,IAAG,MAAM,WAAW,KAAK,2BAA2B;AAAA,MAChE;AAAA,MACA,WAAW;AAAA,MACX,UAAU,CAAC,KAAK,OAAO,KAAI,EAAG,UAAW,EAAC,GAAG;AAAA,IACzD,CAAS;AACD,SAAK,mBAAmB,OAAO;AAC/B,UAAM,QAAQ,IAAI,KAAK,OAClB,KAAI,EACJ,OAAM,EACN,IAAI,OAAO,UAAU;AACtB,aAAO,MAAM,WAAW,KAAK,uBAAuB;AAAA,QAChD;AAAA,QACA,WAAW;AAAA,QACX,cAAc;AAAA,QACd,QAAQ,MAAM,UAAW,EAAC,MAAM;AAAA,MAChD,CAAa;AAAA,IACb,CAAS,CAAC;AAAA,EACV;AAAA,EACI,0BAA0B,OAAO,WAAW;AACxC,QAAI,OAAO,YAAY,KAAK,UAAU,MAAM;AACxC;AAAA,IACZ;AACQ,UAAM,aAAa,KAAK;AACxB,UAAM,EAAE,WAAW,YAAW,IAAK,KAAK,4BAA4B,MAAM;AAC1E,UAAM,OAAO,YAAY,QAAQ,CAAC;AAClC,WAAO,IAAI;AACX,QAAI;AACA,YAAM,SAAS,MAAM,KAAK,OAAO,GAAG,iBAAiB,YAAY,IAAI,CAAC;AACtE,YAAM,WAAW,KAAK,uBAAuB;AAAA,QACzC,qBAAqB,kBAAkB,CAAC,CAAC,GAAG,OAAO,GAAGY,YAAW;AAC7D,kBAAQA,OAAM;AAAA,QAClC,CAAiB;AAAA,QACD,WAAW;AAAA,UACN,MAAM,UAAU,QAAQ,aAAc;AAAA,UACvC,eAAe,qBAAqB,MAAM;AAAA,QAC7C;AAAA,QACD,cAAc;AAAA,QACd,QAAQ;AAAA,UACJ,OAAO,OAAO,OAAO;AAAA,QACxB;AAAA,MACjB,CAAa;AAAA,IACb,SACe,OAAO;AACV,UAAI;AACA,YAAI,iBAAiB,OAAO;AACxB,gBAAM,WAAW,KAAK,uBAAuB;AAAA,YACzC,qBAAqB,kBAAkB,CAAC,CAAC,GAAG,MAAM,GAAG,MAAM,SAAS,UAAU;AAC1E,oBAAMC,SAAQ,IAAI,MAAM,OAAO;AAC/B,cAAAA,OAAM,OAAO;AACb,kBAAI,OAAO;AACP,gBAAAA,OAAM,QAAQ;AAAA,cAC9C;AAC4B,qBAAOA,MAAK;AAAA,YACxC,CAAyB;AAAA,YACD,WAAW;AAAA,cACN,MAAM,UAAU,OAAO,aAAc;AAAA,cACtC,eAAe,qBAAqB,MAAM,IAAI;AAAA,cAC9C,eAAe,qBAAqB,MAAM,OAAO;AAAA,cACjD,eAAe,qBAAqB,MAAM,KAAK;AAAA,YAClD;AAAA,YACD,cAAc;AAAA,YACd,QAAQ;AAAA,cACJ,OAAO,OAAO,OAAO;AAAA,YACxB;AAAA,UACzB,CAAqB;AAAA,QACrB,OACqB;AACD,gBAAM,WAAW,KAAK,uBAAuB;AAAA,YACzC,qBAAqB,kBAAkB,CAAC,CAAC,GAAG,MAAM,GAAGA,WAAU;AAC3D,qBAAOA,MAAK;AAAA,YACxC,CAAyB;AAAA,YACD,WAAW;AAAA,cACN,MAAM,UAAU,OAAO,aAAc;AAAA,cACtC,eAAe,qBAAqB,KAAK;AAAA,YAC5C;AAAA,YACD,cAAc;AAAA,YACd,QAAQ;AAAA,cACJ,OAAO,OAAO,OAAO;AAAA,YACxB;AAAA,UACzB,CAAqB;AAAA,QACrB;AAAA,MACA,SACmBA,QAAO;AACV,mBAAWA,MAAK;AAAA,MAChC;AAAA,IACA;AAAA,EACK;AAAA,EACD,IAAI,cAAc;AACd,WAAO,KAAK,OAAO,QAAO,EAAG;AAAA,EACrC;AAAA,EACI,IAAI,oBAAoB;AACpB,WAAO;AAAA,MACH;AAAA,QACI,MAAM;AAAA,QACN,OAAO;AAAA,UACH,SAAS,KAAK,UAAU;AAAA,UACxB,WAAW;AAAA,QACd;AAAA,MACJ;AAAA,MACD;AAAA,QACI,MAAM;AAAA,QACN,OAAO;AAAA,UACH,SAAS,KAAK,UAAU;AAAA,UACxB,WAAW;AAAA,QACd;AAAA,MACJ;AAAA,MACD;AAAA,QACI,MAAM;AAAA,QACN,OAAO;AAAA,UACH,SAAS,KAAK,UAAU;AAAA,UACxB,WAAW;AAAA,QACd;AAAA,MACJ;AAAA,IACJ;AAAA,EACT;AAAA,EACI,wBAAwB,CAAC,WAAW;AAChC,QAAI,OAAO,YAAY,KAAK,UAAU,SAAS;AAC3C;AAAA,IACZ;AACQ,UAAM,EAAE,WAAW,YAAW,IAAK,KAAK,4BAA4B,MAAM;AAC1E,cAAU,QAAQ,QAAQ,WAAW;AAAA,EACxC;AAAA,EACD,uBAAuB,CAAC,WAAW;AAC/B,QAAI,OAAO,YAAY,KAAK,UAAU,QAAQ;AAC1C;AAAA,IACZ;AACQ,UAAM,EAAE,WAAW,YAAW,IAAK,KAAK,4BAA4B,MAAM;AAC1E,cAAU,OAAO,QAAQ,WAAW;AAAA,EACvC;AAAA,EACD,4BAA4B,QAAQ;AAChC,UAAM,EAAE,MAAM,OAAM,IAAK;AACzB,WAAO,KAAK,SAAS,OAAO;AAC5B,WAAO,KAAK,KAAK;AACjB,UAAM,iBAAiB,KAAK,MAAM,CAAC;AACnC,WAAO,cAAc;AACrB,WAAO,eAAe,SAAS,QAAQ;AACvC,WAAO,OAAO,eAAe,UAAU,QAAQ;AAC/C,QAAI,aAAa,KAAK,aAAa,IAAI,OAAO,KAAK;AACnD,QAAI,CAAC,YAAY;AACb,mBAAa,oBAAI,IAAK;AACtB,WAAK,aAAa,IAAI,OAAO,OAAO,UAAU;AAAA,IAC1D;AACQ,UAAM,WAAW,eAAe;AAChC,QAAI,YAAY,WAAW,IAAI,QAAQ;AACvC,QAAI,CAAC,WAAW;AACZ,kBAAY;AAAA,QACR,SAAS,IAAI,SAAU;AAAA,QACvB,QAAQ,IAAI,SAAU;AAAA,MACzB;AACD,iBAAW,IAAI,UAAU,SAAS;AAAA,IAC9C;AACQ,WAAO,EAAE,WAAW,aAAa,KAAM;AAAA,EAC/C;AAAA,EACI,CAAC,OAAO,OAAO,IAAI;AACf,SAAK,KAAK,OAAO,YAAY,EAAC,EAAG,MAAM,UAAU;AAAA,EACzD;AAAA,EACI,OAAO,OAAO,YAAY,IAAI;AAC1B,QAAI,KAAK,kBAAkB;AACvB,YAAM,KAAK,YAAY,KAAK,8BAA8B;AAAA,QACtD,QAAQ,KAAK;AAAA,MAC7B,CAAa;AAAA,IACb;AAAA,EACA;AACA;AClNO,SAAS,kBAAkB,OAAO;AACrC,MAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,UAAM,gBAAgB,MAAM,KAAK,eAAa;AAC1C,aAAO,cAAc;AAAA,IACxB,CAAA,IACK,SACA;AACN,UAAM,mBAAmB,MAAM,OAAO,CAAC,KAAK,cAAc;AACtD,UAAI,cAAc,gBAAgB;AAC9B,eAAO;AAAA,MACvB,WACqB,QAAQ,kBAAkB,cAAc,gBAAgB;AAC7D,eAAO;AAAA,MACvB;AACY,aAAO;AAAA,IACV,GAAE,IAAI;AACP,WAAO,CAAC,eAAe,gBAAgB;AAAA,EAC/C;AACI,MAAI,UAAU,kBAAkB,UAAU,gBAAgB;AACtD,WAAO,CAAC,QAAQ,KAAK;AAAA,EAC7B;AACI,SAAO,CAAC,OAAO,IAAI;AACvB;AAIO,MAAM,4BAA4B,oBAAI,IAAI;AAAA,EAC7C;AAAA,IAAC;AAAA,IAAQ;AAAA;AAAA,EAA8D;AAAA,EACvE;AAAA,IAAC;AAAA,IAAoB;AAAA;AAAA,EAAoE;AAC7F,CAAC;AACM,SAAS,sBAAsB,OAAO;AACzC,QAAM,aAAa,kBAAkB,KAAK;AAC1C,QAAM,YAAY,0BAA0B,IAAI,WAAW,CAAC,CAAC;AAC7D,SAAO,CAAC,WAAW,WAAW,CAAC,CAAC;AACpC;AAIO,MAAM,6BAA6B,oBAAI,IAAI;AAAA,EAC9C,CAAC,QAAQ,sBAAsB;AAAA,EAC/B,CAAC,oBAAoB,kCAAkC;AAC3D,CAAC;AAIM,SAAS,sBAAsB,OAAO;AACzC,QAAM,aAAa,kBAAkB,KAAK;AAC1C,QAAM,YAAY,2BAA2B,IAAI,WAAW,CAAC,CAAC;AAC9D,SAAO,CAAC,WAAW,WAAW,CAAC,CAAC;AACpC;AAIO,SAAS,uBAAuB,SAAS,IAAI;AAChD,SAAOC,GAAW,WAAS;AACvB,QAAI,iBAAiB,eAAe;AAChC,YAAM,WAAW,OAAO,OAAO;AAAA,IAC3C,WACiB,iBAAiB,cAAc;AACpC,YAAM,UAAU,yBAAyB,EAAE;AAAA,IACvD;AACQ,UAAM;AAAA,EACd,CAAK;AACL;ACpEA;AAAA;AAAA;AAAA;AAAA;AAcY,MAAC,eAAe,OAAO,aAAa;AAOpC,MAAC,oBAAoB,OAAO,kBAAkB;AAInD,MAAM,gBAAgBC,QAAM;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,MAAM,OAElB,OAAO,iBAAiB;AACpB,UAAM,eAAe;AACrB,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,MAAM,WAAW,IAAI;AAAA,EAClC;AAAA,EACI,IAAI,cAAc;AACd,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,MAAM,eAAe,iBAAiB,MAAM;AACxC,mBAAe,6BAA6B,KAAK,eAAe,MAAM,YAAY;AAClF,WAAO,MAAM,KAAK,MAAM,eAAe,cAAc,GAAG,IAAI;AAAA,EACpE;AAAA,EACI,MAAM,SAAS,iBAAiB,MAAM;AAClC,mBAAe,6BAA6B,KAAK,SAAS,MAAM,YAAY;AAC5E,WAAO,MAAM,KAAK,MAAM,SAAS,cAAc,GAAG,IAAI;AAAA,EAC9D;AAAA,EACI,MAAM,YAAY,QAAQ;AACtB,WAAQ,MAAM,KAAK,eAAe,UAAQ;AACtC,aAAO;AAAA,IACV,GAAE,MAAM;AAAA,EACjB;AAAA,EACI,MAAM,eAAe,QAAQ;AACzB,QAAI,OAAO,UAAU,MAAM;AACvB,aAAO;AAAA,IACnB;AACQ,UAAM,oBAAoB,MAAM,KAAK,eAAe,UAAQ;AACxD,aAAO;AAAA,IACV,GAAE,MAAM;AACT,UAAM,OAAO,QAAS;AACtB,WAAO;AAAA,EACf;AAAA,EACI,MAAM,iBAAiB,eAAe;AAClC,UAAM,EAAE,OAAM,IAAK,MAAM,KAAK,YAAY,OAAO,KAAK,mBAAmB;AAAA,MACrE;AAAA,IACZ,CAAS;AACD,WAAO,IAAI,kBAAkB,MAAM;AAAA,MAC/B,QAAQ,OAAO;AAAA,MACf,MAAM;AAAA,IAClB,CAAS;AAAA,EACT;AACA;ACzEA;AAAA;AAAA;AAAA;AAAA;AAKA,IAAI,oBAAwD,SAAU,SAAS,cAAc,OAAO;AAChG,MAAI,WAAW,UAAU,SAAS;AAClC,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC1C,YAAQ,WAAW,aAAa,CAAC,EAAE,KAAK,SAAS,KAAK,IAAI,aAAa,CAAC,EAAE,KAAK,OAAO;AAAA,EAC9F;AACI,SAAO,WAAW,QAAQ;AAC9B;AACA,IAAI,eAA8C,SAAU,MAAM,cAAc,YAAY,WAAW,cAAc,mBAAmB;AACpI,WAAS,OAAO,GAAG;AAAE,QAAI,MAAM,UAAU,OAAO,MAAM,WAAY,OAAM,IAAI,UAAU,mBAAmB;AAAG,WAAO;AAAA,EAAE;AACrH,MAAI,OAAO,UAAU,MAAM,MAAM,SAAS,WAAW,QAAQ,SAAS,WAAW,QAAQ;AACzF,MAAI,SAAS,CAAC,gBAAgB,OAAO,UAAU,QAAQ,IAAI,OAAO,KAAK,YAAY;AACnF,MAAI,aAAa,iBAAiB,SAAS,OAAO,yBAAyB,QAAQ,UAAU,IAAI,IAAI;AACrG,MAAI,GAAG,OAAO;AACd,WAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,QAAI,UAAU,CAAE;AAChB,aAAS,KAAK,UAAW,SAAQ,CAAC,IAAI,MAAM,WAAW,CAAA,IAAK,UAAU,CAAC;AACvE,aAAS,KAAK,UAAU,OAAQ,SAAQ,OAAO,CAAC,IAAI,UAAU,OAAO,CAAC;AACtE,YAAQ,iBAAiB,SAAU,GAAG;AAAE,UAAI,KAAM,OAAM,IAAI,UAAU,wDAAwD;AAAG,wBAAkB,KAAK,OAAO,KAAK,IAAI,CAAC;AAAA,IAAI;AAC7K,QAAI,UAAS,GAAI,WAAW,CAAC,GAAG,SAAS,aAAa,EAAE,KAAK,WAAW,KAAK,KAAK,WAAW,IAAK,IAAG,WAAW,GAAG,GAAG,OAAO;AAC7H,QAAI,SAAS,YAAY;AACrB,UAAI,WAAW,OAAQ;AACvB,UAAI,WAAW,QAAQ,OAAO,WAAW,SAAU,OAAM,IAAI,UAAU,iBAAiB;AACxF,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,IAAI,EAAG,cAAa,QAAQ,CAAC;AAAA,IAC/D,WACiB,IAAI,OAAO,MAAM,GAAG;AACzB,UAAI,SAAS,QAAS,cAAa,QAAQ,CAAC;AAAA,UACvC,YAAW,GAAG,IAAI;AAAA,IACnC;AAAA,EACA;AACI,MAAI,OAAQ,QAAO,eAAe,QAAQ,UAAU,MAAM,UAAU;AACpE,SAAO;AACX;AAeG,IAAC,aAAa,MAAM;AACnB,MAAI,cAAc;AAClB,MAAI,6BAA6B,CAAE;AACnC,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,SAAO,MAAM,kBAAkB,YAAY;AAAA,IACvC,OAAA;AACI,YAAM,YAAY,OAAO,WAAW,cAAc,OAAO,WAAW,OAAO,OAAO,YAAY,OAAO,QAAQ,KAAK,IAAI,IAAI;AAC1H,mBAAa,MAAM,MAAM,kBAAkB,EAAE,MAAM,UAAU,MAAM,QAAQ,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,UAAU,KAAK,KAAK,SAAO,IAAI,KAAM,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC9N,mBAAa,MAAM,MAAM,wBAAwB,EAAE,MAAM,UAAU,MAAM,cAAc,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,gBAAgB,KAAK,KAAK,SAAO,IAAI,WAAY,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AACtP,mBAAa,MAAM,MAAM,+BAA+B,EAAE,MAAM,UAAU,MAAM,qBAAqB,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,uBAAuB,KAAK,KAAK,SAAO,IAAI,kBAAmB,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAClR,UAAI,UAAW,QAAO,eAAe,MAAM,OAAO,UAAU,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,OAAO,WAAW;AAAA,IAClJ;AAAA,IACQ,SAAS,kBAAkB,MAAM,0BAA0B,GAAG;AAAA,IAC9D;AAAA,IACA;AAAA,IACA,iBAAiB,SAAS,OAAQ;AAAA,IAClC,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,IACA,YAAY,MAAM,SAAS,iBAAiB,UAAU;AAClD,YAAO;AACP,WAAK,QAAQ;AACb,WAAK,WAAW;AAChB,WAAK,mBAAmB;AACxB,WAAK,MAAM,KAAK,SAAS;AACzB,WAAK,YAAY,YAAY;AAC7B,WAAK,YAAY;AAAA,QACb,CAAC,YAAY,GAAG,IAAI,QAAQ,QAAW,MAAM,SAAS,eAAe;AAAA,QACrE,CAAC,iBAAiB,GAAG,IAAI,QAAQ,oBAAoB,MAAM,QAAQ,sBAAuB,GAAE,eAAe;AAAA,MAC9G;AAAA,IACb;AAAA,IACQ,IAAI,SAAS;AACT,aAAO,KAAK,QAAO,EAAG;AAAA,IAClC;AAAA,IACQ,YAAY;AACR,aAAO,KAAK,UAAU,YAAY;AAAA,IAC9C;AAAA,IACQ,gBAAgB;AACZ,aAAO,KAAK,UAAU,iBAAiB;AAAA,IACnD;AAAA,IACQ,OAAO;AACH,aAAO,KAAK;AAAA,IACxB;AAAA,IACQ,aAAa;AACT,YAAM,IAAI,qBAAsB;AAAA,IAC5C;AAAA,IACQ,MAAM;AACF,aAAO,KAAK,SAAS;AAAA,IACjC;AAAA,IACQ,cAAc;AACV,aAAO,KAAK,MAAM,MAAM,KAAK,aAAa,EAAE;AAAA,IACxD;AAAA,IACQ,cAAc;AACV,aAAO,KAAK,MAAM,YAAY,KAAK,SAAS,EAAE;AAAA,IAC1D;AAAA,IACQ,MAAM,KAAK,KAAK,UAAU,IAAI;AAC1B,YAAM,EAAE,YAAY,QAAQ,SAAS,KAAK,KAAK,iBAAiB,kBAAmB,EAAA,IAAM;AACzF,YAAM,CAAC,WAAW,WAAW,IAAI,sBAAsB,SAAS;AAChE,YAAM,UAAUC,GAAIC,EAAK,KAAK,SAAS,WAAW,KAAK,4BAA4B;AAAA,QAC/E,SAAS,KAAK,SAAS;AAAA,QACvB;AAAA,QACA,MAAM;AAAA,MACtB,CAAa,CAAC,GAAG,GAAI,gBAAgB,OACnB;AAAA,QACE,KAAK,MAAM,oBAAoB;AAAA,UAC3B,SAAS;AAAA,UACT,aAAa,gBAAgB,iBAAiB,IAAI;AAAA,UAClD,UAAU;AAAA,QAClC,CAAqB;AAAA,MACrB,IACkB,CAAE,CAAC,EAAE,KAAKC,EAAI,CAAC,CAAC,EAAE,QAAAN,QAAM,CAAE,MAAM;AAClC,eAAOA;AAAA,MACV,CAAA,GAAGO,GAAS,QAAQ,EAAE,GAAGF,EAAK,KAAK,eAAe,aAAc,CAAA,CAAC,GAAG,uBAAuB,KAAK,EAAE,CAAC;AACpG,YAAM,SAAS,MAAMG,GAAe,OAAO;AAC3C,aAAO,KAAK,MAAM,sBAAsB,OAAO,UAAU;AAAA,IACrE;AAAA,IACQ,MAAM,WAAW,MAAM,UAAU,IAAI;AACjC,YAAM,EAAE,YAAY,QAAQ,SAAS,KAAK,KAAK,iBAAiB,kBAAmB,EAAA,IAAM;AACzF,YAAM,CAAC,WAAW,WAAW,IAAI,sBAAsB,SAAS;AAChE,YAAM,UAAUJ,GAAIK,GAAS;AAAA,QACzB,iBAAiB,KAAK,UAAU,SAAS,EAAE,KAAKC,IAAO;AAAA,QACvDL,EAAK,KAAK,gBAAgB,IAAI,CAAC;AAAA,MAC/C,CAAa,EAAE,KAAKC,EAAI,MAAM;AACd,eAAO;AAAA,MACvB,CAAa,CAAC,GAAG,GAAI,gBAAgB,OACnB;AAAA,QACE,KAAK,MAAM,oBAAoB;AAAA,UAC3B,SAAS;AAAA,UACT,aAAa,gBAAgB,iBAAiB,IAAI;AAAA,UAClD,UAAU;AAAA,QAClC,CAAqB;AAAA,MACrB,IACkB,CAAE,CAAC,EAAE,KAAKC,GAAS,QAAQ,EAAE,GAAGF,EAAK,KAAK,eAAe,aAAY,CAAE,CAAC,GAAG,uBAAuB,cAAc,EAAE,CAAC;AACzH,YAAMG,GAAe,OAAO;AAAA,IACxC;AAAA,IACQ,UAAU;AACN,aAAO,KAAK;AAAA,IACxB;AAAA,IACQ,MAAM,kBAAkB,UAAU,IAAI;AAClC,YAAM,EAAE,YAAY,QAAQ,SAAS,KAAK,KAAK,iBAAiB,kBAAmB,EAAA,IAAM;AACzF,YAAM,CAAC,gBAAgB,WAAW,IAAI,sBAAsB,SAAS;AACrE,YAAM,cAAcG,GAAMF,GAAS;AAAA,QAC/B,iBAAiB,KAAK,UAAUrB,sBAAkB,gBAAgB,WAAW,iBAAiB,EAAE,KAAKsB,IAAO;AAAA,QAC5G,iBAAiB,KAAK,UAAU,cAAc,EAAE,KAAKA,IAAO;AAAA,MAC/D,CAAA,GAAG,iBAAiB,KAAK,UAAUtB,SAAAA,aAAkB,gBAAgB,WAAW,iBAAiB,CAAC,EAAE,KAAKkB,EAAI,CAAAN,YAAU;AACpH,YAAI,MAAM,QAAQA,OAAM,GAAG;AACvB,iBAAO,EAAE,QAAQA,QAAO,CAAC,EAAG;AAAA,QAChD;AACgB,eAAO,EAAE,QAAAA,QAAQ;AAAA,MACjC,CAAa,CAAC;AACF,YAAM,UAAUI,GAAI,aAAa,GAAI,gBAAgB,OAC/C;AAAA,QACE,KAAK,MAAM,oBAAoB;AAAA,UAC3B,SAAS;AAAA,UACT,aAAa,gBAAgB,iBAAiB,IAAI;AAAA,UAClD,UAAU;AAAA,QAClC,CAAqB;AAAA,MACrB,IACkB,CAAE,CAAC,EAAE,KAAKE,EAAI,CAAC,CAAC,EAAE,QAAAN,QAAM,CAAE,MAAM;AAClC,eAAOA;AAAA,MACvB,CAAa,GAAGO,GAAS,QAAQ,EAAE,GAAGF,EAAK,KAAK,eAAe,aAAc,CAAA,CAAC,CAAC;AACnE,YAAM,SAAS,MAAMG,GAAe,OAAO;AAC3C,aAAO,KAAK,MAAM,sBAAsB,OAAO,UAAU;AAAA,IACrE;AAAA,IACQ,sBAAsB;AAClB,YAAM,IAAI,qBAAsB;AAAA,IAC5C;AAAA,IACQ,IAAI,WAAW;AACX,aAAO,KAAK;AAAA,IACxB;AAAA,IACQ,EAAE,mBAAmB,CAAC,eAAe,GAAG,yBAAyB,CAAC,eAAe,GAAG,gCAAgC,CAAC,eAAe,GAAG,cAAa,IAAK;AACrJ,UAAI,KAAK,WAAW;AAChB;AAAA,MAChB;AACY,WAAK,YAAY;AACjB,WAAK,eAAe,OAAO,IAAI,MAAM,gBAAgB,CAAC;AACtD,WAAK,SAAS,QAAS;AACvB,WAAK,UAAU,YAAY,EAAE,aAAa,EAAG;AAC7C,WAAK,UAAU,iBAAiB,EAAE,aAAa,EAAG;AAAA,IAC9D;AAAA,IACQ,oBAAoB,oBAAI,IAAK;AAAA,IAC7B,MAAM,eAAe,MAAM,OAAO;AAC9B,UAAI,KAAK,kBAAkB,IAAI,IAAI,GAAG;AAClC,cAAM,IAAI,MAAM,wCAAwC,IAAI,iBAAiB,IAAI,oBAAoB;AAAA,MACrH;AACY,YAAM,aAAa,IAAI,mBAAmB,MAAM,MAAM,KAAK;AAC3D,WAAK,kBAAkB,IAAI,MAAM,UAAU;AAC3C,UAAI;AACA,cAAM,WAAW,OAAQ;AAAA,MACzC,SACmB,OAAO;AACV,aAAK,kBAAkB,OAAO,IAAI;AAClC,cAAM;AAAA,MACtB;AAAA,IACA;AAAA,IACQ,gBAAgB,UAAU,SAAS;AAC/B,UAAI,SAAS,WAAW,MAAM,GAAG;AAC7B,cAAM,IAAI,qBAAqB,0CAA0C;AAAA,MACzF;AACY,aAAO,MAAM,gBAAgB,UAAU,OAAO;AAAA,IAC1D;AAAA,EACK;AACL,GAAC;ACzND;AAAA;AAAA;AAAA;AAAA;AAOA,IAAI;AAAA,CACH,SAAUI,oBAAmB;AAC1B,EAAAA,mBAAkB,MAAM,IAAI;AAC5B,EAAAA,mBAAkB,KAAK,IAAI;AAC3B,EAAAA,mBAAkB,SAAS,IAAI;AAC/B,EAAAA,mBAAkB,OAAO,IAAI;AACjC,GAAG,sBAAsB,oBAAoB,CAAA,EAAG;AAChD,IAAI;AAAA,CACH,SAAUC,aAAY;AACnB,EAAAA,YAAW,OAAO,IAAI;AACtB,EAAAA,YAAW,SAAS,IAAI;AACxB,EAAAA,YAAW,OAAO,IAAI;AACtB,EAAAA,YAAW,WAAW,IAAI;AAC1B,EAAAA,YAAW,aAAa,IAAI;AAC5B,EAAAA,YAAW,aAAa,IAAI;AAC5B,EAAAA,YAAW,QAAQ,IAAI;AAC3B,GAAG,eAAe,aAAa,CAAA,EAAG;AAClC,MAAM,kBAAkB,CAAC,QAAQ;AAC7B,UAAQ,KAAG;AAAA,IACP,KAAK;AAAA,IACL,KAAK;AACD,YAAM;AACN;AAAA,EACZ;AAEI,MAAI,CAAC,GAAG,GAAG,EAAE,WAAW,GAAG;AACvB,WAAO;AAAA,EACf;AACI,UAAQ,KAAG;AAAA,IACP,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX;AACI,YAAM,IAAI,MAAM,iBAAiB,GAAG,GAAG;AAAA,EACnD;AACA;AAIO,MAAM,qBAAqB,SAAS;AAAA,EACvC;AAAA,EACA,YAAY,MAAM;AACd,UAAO;AACP,SAAK,QAAQ;AAAA,EACrB;AAAA,EACI,MAAM,KAAK,KAAK,UAAU;AACtB,UAAM,KAAK,MAAM,WAAW,KAAK,wBAAwB;AAAA,MACrD,SAAS,KAAK,MAAM,UAAW,EAAC;AAAA,MAChC,SAAS;AAAA,QACL;AAAA,UACI,MAAM,kBAAkB;AAAA,UACxB,IAAI;AAAA,UACJ,SAAS;AAAA,YACL;AAAA,cACI,MAAM,WAAW;AAAA,cACjB,OAAO,gBAAgB,GAAG;AAAA,YAC7B;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACb,CAAS;AAAA,EACT;AAAA,EACI,MAAM,GAAG,KAAK;AACV,UAAM,KAAK,MAAM,WAAW,KAAK,wBAAwB;AAAA,MACrD,SAAS,KAAK,MAAM,UAAW,EAAC;AAAA,MAChC,SAAS;AAAA,QACL;AAAA,UACI,MAAM,kBAAkB;AAAA,UACxB,IAAI;AAAA,UACJ,SAAS;AAAA,YACL;AAAA,cACI,MAAM,WAAW;AAAA,cACjB,OAAO,gBAAgB,GAAG;AAAA,YAC7B;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACb,CAAS;AAAA,EACT;AAAA,EACI,MAAM,MAAM,KAAK,UAAU,IAAI;AAC3B,UAAM,EAAE,QAAQ,EAAC,IAAK;AACtB,UAAM,UAAU;AAAA,MACZ;AAAA,QACI,MAAM,WAAW;AAAA,QACjB,OAAO,gBAAgB,GAAG;AAAA,MAC7B;AAAA,IACJ;AACD,QAAI,QAAQ,GAAG;AACX,cAAQ,KAAK;AAAA,QACT,MAAM,WAAW;AAAA,QACjB,UAAU;AAAA,MAC1B,CAAa;AAAA,IACb;AACQ,YAAQ,KAAK;AAAA,MACT,MAAM,WAAW;AAAA,MACjB,OAAO,gBAAgB,GAAG;AAAA,IACtC,CAAS;AACD,UAAM,KAAK,MAAM,WAAW,KAAK,wBAAwB;AAAA,MACrD,SAAS,KAAK,MAAM,UAAW,EAAC;AAAA,MAChC,SAAS;AAAA,QACL;AAAA,UACI,MAAM,kBAAkB;AAAA,UACxB,IAAI;AAAA,UACJ;AAAA,QACH;AAAA,MACJ;AAAA,IACb,CAAS;AAAA,EACT;AAAA,EACI,MAAM,KAAK,MAAM,UAAU,IAAI;AAC3B,UAAM,EAAE,QAAQ,EAAC,IAAK;AAGtB,UAAM,SAAS,CAAC,GAAG,IAAI,EAAE,IAAI,eAAe;AAC5C,UAAM,UAAU,CAAE;AAClB,QAAI,SAAS,GAAG;AACZ,iBAAW,SAAS,QAAQ;AACxB,gBAAQ,KAAK;AAAA,UACT,MAAM,WAAW;AAAA,UACjB;AAAA,QACpB,GAAmB;AAAA,UACC,MAAM,WAAW;AAAA,UACjB;AAAA,QACpB,CAAiB;AAAA,MACjB;AAAA,IACA,OACa;AACD,iBAAW,SAAS,QAAQ;AACxB,gBAAQ,KAAK;AAAA,UACT,MAAM,WAAW;AAAA,UACjB;AAAA,QACpB,GAAmB;AAAA,UACC,MAAM,WAAW;AAAA,UACjB,UAAU;AAAA,QAC9B,GAAmB;AAAA,UACC,MAAM,WAAW;AAAA,UACjB;AAAA,QACpB,CAAiB;AAAA,MACjB;AAAA,IACA;AACQ,UAAM,KAAK,MAAM,WAAW,KAAK,wBAAwB;AAAA,MACrD,SAAS,KAAK,MAAM,UAAW,EAAC;AAAA,MAChC,SAAS;AAAA,QACL;AAAA,UACI,MAAM,kBAAkB;AAAA,UACxB,IAAI;AAAA,UACJ;AAAA,QACH;AAAA,MACJ;AAAA,IACb,CAAS;AAAA,EACT;AAAA,EACI,MAAM,cAAc,MAAM;AAEtB,QAAI,CAAC,GAAG,IAAI,EAAE,SAAS,GAAG;AACtB,YAAM,IAAI,MAAM,oCAAoC;AAAA,IAChE;AACQ,UAAM,QAAQ,MAAM,KAAK,MAAM,aAAc;AAC7C,UAAM,MAAM,cAAa,EAAG,SAAS,OAAOC,UAAS;AACjD,eAAS,YAAY,cAAc,OAAOA,KAAI;AAAA,IACjD,GAAE,IAAI;AAAA,EACf;AACA;AACA,MAAM,gBAAgB,CAAC,WAAW;AAC9B,UAAQ,QAAM;AAAA,IACV,KAAK,YAAY;AACb,aAAO;AAAA,IACX,KAAK,YAAY;AACb,aAAO;AAAA,IACX,KAAK,YAAY;AACb,aAAO;AAAA,IACX,KAAK,YAAY;AACb,aAAO;AAAA,IACX,KAAK,YAAY;AACb,aAAO;AAAA,EACnB;AACA;AAIO,MAAM,kBAAkB,MAAM;AAAA,EACjC;AAAA,EACA,iBAAiB,EAAE,GAAG,GAAG,GAAG,EAAG;AAAA,EAC/B,YAAY,SAAS;AACjB,UAAO;AACP,SAAK,WAAW;AAAA,EACxB;AAAA,EACI,MAAM,QAAQ;AACV,SAAK,iBAAiB,EAAE,GAAG,GAAG,GAAG,EAAG;AACpC,UAAM,KAAK,SAAS,WAAW,KAAK,wBAAwB;AAAA,MACxD,SAAS,KAAK,SAAS;AAAA,IACnC,CAAS;AAAA,EACT;AAAA,EACI,MAAM,KAAK,GAAG,GAAG,UAAU,CAAA,GAAI;AAC3B,UAAM,OAAO,KAAK;AAClB,UAAM,KAAK;AAAA,MACP,GAAG,KAAK,MAAM,CAAC;AAAA,MACf,GAAG,KAAK,MAAM,CAAC;AAAA,IAClB;AACD,UAAM,UAAU,CAAE;AAClB,UAAM,QAAQ,QAAQ,SAAS;AAC/B,aAAS,IAAI,GAAG,IAAI,OAAO,EAAE,GAAG;AAC5B,cAAQ,KAAK;AAAA,QACT,MAAM,WAAW;AAAA,QACjB,GAAG,KAAK,KAAK,GAAG,IAAI,KAAK,MAAM,IAAI;AAAA,QACnC,GAAG,KAAK,KAAK,GAAG,IAAI,KAAK,MAAM,IAAI;AAAA,QACnC,QAAQ,QAAQ;AAAA,MAChC,CAAa;AAAA,IACb;AACQ,YAAQ,KAAK;AAAA,MACT,MAAM,WAAW;AAAA,MACjB,GAAG;AAAA,MACH,QAAQ,QAAQ;AAAA,IAC5B,CAAS;AAED,SAAK,iBAAiB;AACtB,UAAM,KAAK,SAAS,WAAW,KAAK,wBAAwB;AAAA,MACxD,SAAS,KAAK,SAAS;AAAA,MACvB,SAAS;AAAA,QACL;AAAA,UACI,MAAM,kBAAkB;AAAA,UACxB,IAAI;AAAA,UACJ;AAAA,QACH;AAAA,MACJ;AAAA,IACb,CAAS;AAAA,EACT;AAAA,EACI,MAAM,KAAK,UAAU,IAAI;AACrB,UAAM,KAAK,SAAS,WAAW,KAAK,wBAAwB;AAAA,MACxD,SAAS,KAAK,SAAS;AAAA,MACvB,SAAS;AAAA,QACL;AAAA,UACI,MAAM,kBAAkB;AAAA,UACxB,IAAI;AAAA,UACJ,SAAS;AAAA,YACL;AAAA,cACI,MAAM,WAAW;AAAA,cACjB,QAAQ,cAAc,QAAQ,UAAU,YAAY,IAAI;AAAA,YAC3D;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACb,CAAS;AAAA,EACT;AAAA,EACI,MAAM,GAAG,UAAU,IAAI;AACnB,UAAM,KAAK,SAAS,WAAW,KAAK,wBAAwB;AAAA,MACxD,SAAS,KAAK,SAAS;AAAA,MACvB,SAAS;AAAA,QACL;AAAA,UACI,MAAM,kBAAkB;AAAA,UACxB,IAAI;AAAA,UACJ,SAAS;AAAA,YACL;AAAA,cACI,MAAM,WAAW;AAAA,cACjB,QAAQ,cAAc,QAAQ,UAAU,YAAY,IAAI;AAAA,YAC3D;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACb,CAAS;AAAA,EACT;AAAA,EACI,MAAM,MAAM,GAAG,GAAG,UAAU,CAAA,GAAI;AAC5B,UAAM,UAAU;AAAA,MACZ;AAAA,QACI,MAAM,WAAW;AAAA,QACjB,GAAG,KAAK,MAAM,CAAC;AAAA,QACf,GAAG,KAAK,MAAM,CAAC;AAAA,QACf,QAAQ,QAAQ;AAAA,MACnB;AAAA,IACJ;AACD,UAAM,oBAAoB;AAAA,MACtB,MAAM,WAAW;AAAA,MACjB,QAAQ,cAAc,QAAQ,UAAU,YAAY,IAAI;AAAA,IAC3D;AACD,UAAM,kBAAkB;AAAA,MACpB,MAAM,WAAW;AAAA,MACjB,QAAQ,kBAAkB;AAAA,IAC7B;AACD,aAAS,IAAI,GAAG,KAAK,QAAQ,SAAS,IAAI,EAAE,GAAG;AAC3C,cAAQ,KAAK,mBAAmB,eAAe;AAAA,IAC3D;AACQ,YAAQ,KAAK,iBAAiB;AAC9B,QAAI,QAAQ,OAAO;AACf,cAAQ,KAAK;AAAA,QACT,MAAM,WAAW;AAAA,QACjB,UAAU,QAAQ;AAAA,MAClC,CAAa;AAAA,IACb;AACQ,YAAQ,KAAK,eAAe;AAC5B,UAAM,KAAK,SAAS,WAAW,KAAK,wBAAwB;AAAA,MACxD,SAAS,KAAK,SAAS;AAAA,MACvB,SAAS;AAAA,QACL;AAAA,UACI,MAAM,kBAAkB;AAAA,UACxB,IAAI;AAAA,UACJ;AAAA,QACH;AAAA,MACJ;AAAA,IACb,CAAS;AAAA,EACT;AAAA,EACI,MAAM,MAAM,UAAU,IAAI;AACtB,UAAM,KAAK,SAAS,WAAW,KAAK,wBAAwB;AAAA,MACxD,SAAS,KAAK,SAAS;AAAA,MACvB,SAAS;AAAA,QACL;AAAA,UACI,MAAM,kBAAkB;AAAA,UACxB,IAAI;AAAA,UACJ,SAAS;AAAA,YACL;AAAA,cACI,MAAM,WAAW;AAAA,cACjB,GAAI,KAAK,kBAAkB;AAAA,gBACvB,GAAG;AAAA,gBACH,GAAG;AAAA,cACnC;AAAA,cAC4B,QAAQ,QAAQ,UAAU;AAAA,cAC1B,QAAQ,QAAQ,UAAU;AAAA,YAC7B;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACb,CAAS;AAAA,EACT;AAAA,EACI,OAAO;AACH,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,WAAW;AACP,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,YAAY;AACR,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,OAAO;AACH,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,cAAc;AACV,UAAM,IAAI,qBAAsB;AAAA,EACxC;AACA;AAIO,MAAM,wBAAwB,YAAY;AAAA,EAC7C;AAAA,EACA,YAAY,SAAS;AACjB,UAAO;AACP,SAAK,WAAW;AAAA,EACxB;AAAA,EACI,MAAM,WAAW,GAAG,GAAG,UAAU,CAAA,GAAI;AACjC,UAAM,KAAK,SAAS,WAAW,KAAK,wBAAwB;AAAA,MACxD,SAAS,KAAK,SAAS;AAAA,MACvB,SAAS;AAAA,QACL;AAAA,UACI,MAAM,kBAAkB;AAAA,UACxB,IAAI;AAAA,UACJ,YAAY;AAAA,YACR,aAAa;AAAA,UAChB;AAAA,UACD,SAAS;AAAA,YACL;AAAA,cACI,MAAM,WAAW;AAAA,cACjB,GAAG,KAAK,MAAM,CAAC;AAAA,cACf,GAAG,KAAK,MAAM,CAAC;AAAA,cACf,QAAQ,QAAQ;AAAA,YACnB;AAAA,YACD;AAAA,cACI,MAAM,WAAW;AAAA,cACjB,QAAQ;AAAA,YACX;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACb,CAAS;AAAA,EACT;AAAA,EACI,MAAM,UAAU,GAAG,GAAG,UAAU,CAAA,GAAI;AAChC,UAAM,KAAK,SAAS,WAAW,KAAK,wBAAwB;AAAA,MACxD,SAAS,KAAK,SAAS;AAAA,MACvB,SAAS;AAAA,QACL;AAAA,UACI,MAAM,kBAAkB;AAAA,UACxB,IAAI;AAAA,UACJ,YAAY;AAAA,YACR,aAAa;AAAA,UAChB;AAAA,UACD,SAAS;AAAA,YACL;AAAA,cACI,MAAM,WAAW;AAAA,cACjB,GAAG,KAAK,MAAM,CAAC;AAAA,cACf,GAAG,KAAK,MAAM,CAAC;AAAA,cACf,QAAQ,QAAQ;AAAA,YACnB;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACb,CAAS;AAAA,EACT;AAAA,EACI,MAAM,WAAW;AACb,UAAM,KAAK,SAAS,WAAW,KAAK,wBAAwB;AAAA,MACxD,SAAS,KAAK,SAAS;AAAA,MACvB,SAAS;AAAA,QACL;AAAA,UACI,MAAM,kBAAkB;AAAA,UACxB,IAAI;AAAA,UACJ,YAAY;AAAA,YACR,aAAa;AAAA,UAChB;AAAA,UACD,SAAS;AAAA,YACL;AAAA,cACI,MAAM,WAAW;AAAA,cACjB,QAAQ;AAAA,YACX;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACb,CAAS;AAAA,EACT;AACA;AC1mBO,MAAM,wBAAwB,YAAY;AAAA,EAC7C,YAAY;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,WAAW,CAAE;AAAA,EACb;AAAA,EACA;AAAA,EACA,YAAY,OAAO,OAAO,gBAAgB,CAAA,GAAI;AAC1C,UAAO;AACP,SAAK,OAAO,MAAM,QAAQ;AAC1B,SAAK,gBAAgB,MAAM,UAAU,KAAK,YAAa;AACvD,SAAK,UAAU,MAAM,QAAQ;AAC7B,SAAK,YAAY;AACjB,SAAK,aAAa,MAAM;AACxB,SAAK,SAAS;AACd,SAAK,aAAa,MAAM,QAAQ;AAChC,SAAK,iBAAiB;AACtB,SAAK,gBAAgB,MAAM;AAC3B,eAAW,UAAU,MAAM,QAAQ,SAAS;AAGxC,UAAI,OAAO,MAAM,SAAS,UAAU;AAChC,aAAK,SAAS,OAAO,KAAK,YAAW,CAAE,IAAI,OAAO,MAAM;AAAA,MACxE;AAAA,IACA;AAAA,EACA;AAAA,EACI,IAAI,SAAS;AACT,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,MAAM;AACF,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,eAAe;AACX,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,SAAS;AACL,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,WAAW;AACP,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,cAAc;AACV,WAAO,KAAK,cAAc;AAAA,EAClC;AAAA,EACI,MAAM,gBAAgB;AAClB,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,UAAU;AACN,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,WAAW;AACP,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,sBAAsB;AAClB,WAAO,QAAQ,KAAK,aAAa;AAAA,EACzC;AAAA,EACI,YAAY;AACR,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,gBAAgB;AACZ,WAAO,KAAK,eAAe,MAAO;AAAA,EAC1C;AAAA,EACI,uBAAuB,gBAAgB;AAEnC,SAAK,eAAgB;AAAA,EAC7B;AAAA,EACI,QAAQ;AACJ,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,2BAA2B;AACvB,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,SAAS,aAAa,IAAI;AACtB,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,qBAAqB;AACjB,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,mBAAmB;AACf,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,2BAA2B;AACvB,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,+BAA+B;AAC3B,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,wBAAwB;AACpB,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,QAAQ;AACJ,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,QAAQ,WAAW,WAAW;AAC1B,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,UAAU;AACN,UAAM,IAAI,qBAAsB;AAAA,EACxC;AACA;ACvGO,MAAM,yBAAyB,aAAa;AAAA,EAC/C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,WAAW,CAAE;AAAA,EACb;AAAA,EACA,YAAY,SAAS,EAAE,YAAY;AAC/B,UAAO;AACP,SAAK,WAAW;AAChB,SAAK,iBAAiB;AAAA,MAClB,IAAI;AAAA,MACJ,MAAM;AAAA,IACT;AACD,SAAK,OAAO,SAAS;AACrB,SAAK,aAAa,SAAS;AAC3B,SAAK,UAAU,SAAS;AACxB,SAAK,cAAc,SAAS;AAE5B,SAAK,WAAW;AAEhB,eAAW,UAAU,SAAS,WAAW,CAAA,GAAI;AAGzC,UAAI,OAAO,MAAM,SAAS,UAAU;AAChC,aAAK,SAAS,OAAO,KAAK,YAAW,CAAE,IAAI,OAAO,MAAM;AAAA,MACxE;AAAA,IACA;AAAA,EACA;AAAA,EACI,gBAAgB;AACZ,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,MAAM;AACF,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,SAAS;AACL,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,aAAa;AACT,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,UAAU;AACN,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,UAAU;AACN,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,YAAY;AACR,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,SAAS;AACL,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,QAAQ;AACJ,WAAO,KAAK,SAAS,MAAO;AAAA,EACpC;AAAA,EACI,oBAAoB;AAChB,WAAO;AAAA,EACf;AAAA,EACI,kBAAkB;AACd,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,SAAS;AACL,UAAM,IAAI,qBAAsB;AAAA,EACxC;AACA;ACxEA;AAAA;AAAA;AAAA;AAAA;AAaO,MAAM,2BAA2B,aAAa;AAAA,EACjD;AAAA,EACA;AAAA,EACA,iBAAiB,IAAI,gBAAiB;AAAA,EACtC,cAAc,oBAAI,IAAK;AAAA,EACvB,iBAAiB,oBAAI,IAAK;AAAA,EAC1B,YAAY,YAAY,MAAM;AAC1B,UAAO;AACP,SAAK,cAAc;AACnB,SAAK,QAAQ;AAEb,SAAK,eAAe,IAAI,IAAI,kBAAkB,KAAK,aAAa,6BAA6B,KAAK,qBAAqB,KAAK,IAAI,CAAC,CAAC;AAClI,SAAK,eAAe,IAAI,IAAI,kBAAkB,KAAK,aAAa,2BAA2B,KAAK,mBAAmB,KAAK,IAAI,CAAC,CAAC;AAC9H,SAAK,eAAe,IAAI,IAAI,kBAAkB,KAAK,aAAa,6BAA6B,KAAK,qBAAqB,KAAK,IAAI,CAAC,CAAC;AAClI,SAAK,eAAe,IAAI,IAAI,kBAAkB,KAAK,aAAa,sBAAsB,KAAK,cAAc,KAAK,IAAI,CAAC,CAAC;AAAA,EAC5H;AAAA,EACI,qBAAqB,OAAO;AACxB,UAAM,QAAQ,KAAK,MAAM,MAAM,MAAM,WAAW,EAAE;AAClD,QAAI,CAAC,OAAO;AACR;AAAA,IACZ;AACQ,UAAM,UAAU,KAAK,YAAY,IAAI,MAAM,QAAQ,OAAO;AAC1D,QAAI;AACJ,QAAI,SAAS;AACT,cAAQ,eAAe,KAAK,OAAO;AACnC,sBAAgB,IAAI,gBAAgB,OAAO,OAAO,QAAQ,cAAc;AAAA,IACpF,OACa;AACD,sBAAgB,IAAI,gBAAgB,OAAO,OAAO,CAAA,CAAE;AAAA,IAChE;AACQ,SAAK,YAAY,IAAI,MAAM,QAAQ,SAAS,aAAa;AACzD,SAAK,KAAK,oBAAoB,SAAS,aAAa;AAAA,EAC5D;AAAA,EACI,mBAAmB,QAAQ;AAAA,EAAA;AAAA,EAC3B,qBAAqB,OAAO;AACxB,UAAM,UAAU,KAAK,YAAY,IAAI,MAAM,QAAQ,OAAO;AAC1D,QAAI,CAAC,SAAS;AACV;AAAA,IACZ;AACQ,UAAM,WAAW,IAAI,iBAAiB,SAAS,KAAK;AACpD,YAAQ,YAAY;AACpB,QAAI,MAAM,YAAY;AAClB,WAAK,eAAe,IAAI,MAAM,YAAY,QAAQ;AAAA,IAC9D;AACQ,QAAI,SAAS,aAAa;AACtB,WAAK,KAAK,oBAAoB,wBAAwB,OAAO;AAAA,IACzE;AACQ,SAAK,KAAK,oBAAoB,UAAU,QAAQ;AAChD,SAAK,KAAK,oBAAoB,iBAAiB,OAAO;AAAA,EAC9D;AAAA,EACI,cAAc,OAAO;AACjB,UAAM,UAAU,KAAK,YAAY,IAAI,MAAM,QAAQ,OAAO;AAC1D,QAAI,CAAC,SAAS;AACV;AAAA,IACZ;AACQ,YAAQ,eAAe,MAAM;AAC7B,SAAK,KAAK,oBAAoB,eAAe,OAAO;AACpD,SAAK,YAAY,OAAO,MAAM,QAAQ,OAAO;AAAA,EACrD;AAAA,EACI,sBAAsB,cAAc;AAChC,QAAI,CAAC,cAAc;AACf,aAAO;AAAA,IACnB;AACQ,UAAM,WAAW,KAAK,eAAe,IAAI,YAAY;AACrD,WAAO,YAAY;AAAA,EAC3B;AAAA,EACI,wBAAwB;AACpB,QAAI,yBAAyB;AAC7B,eAAW,WAAW,KAAK,YAAY,OAAM,GAAI;AAC7C,UAAI,CAAC,QAAQ,cAAc,QAAQ,cAAc;AAC7C;AAAA,MAChB;AAAA,IACA;AACQ,WAAO;AAAA,EACf;AAAA,EACI,0BAA0B,OAAO;AAC7B,eAAW,CAAC,IAAI,OAAO,KAAK,KAAK,YAAY,WAAW;AACpD,UAAI,QAAQ,MAAO,MAAK,OAAO;AAC3B,aAAK,YAAY,OAAO,EAAE;AAAA,MAC1C;AAAA,IACA;AACQ,eAAW,CAAC,IAAI,QAAQ,KAAK,KAAK,eAAe,WAAW;AACxD,UAAI,SAAS,MAAO,MAAK,OAAO;AAC5B,aAAK,eAAe,OAAO,EAAE;AAAA,MAC7C;AAAA,IACA;AAAA,EACA;AAAA,EACI,UAAU;AACN,SAAK,mBAAoB;AACzB,SAAK,YAAY,MAAO;AACxB,SAAK,eAAe,MAAO;AAC3B,SAAK,eAAe,QAAS;AAAA,EACrC;AACA;AC1GA;AAAA;AAAA;AAAA;AAAA;AAKA,IAAI,0BAAoE,SAAU,KAAK,OAAO,OAAO;AACjG,MAAI,UAAU,QAAQ,UAAU,QAAQ;AACpC,QAAI,OAAO,UAAU,YAAY,OAAO,UAAU,WAAY,OAAM,IAAI,UAAU,kBAAkB;AACpG,QAAI;AACJ,QAAI,OAAO;AACP,UAAI,CAAC,OAAO,aAAc,OAAM,IAAI,UAAU,qCAAqC;AACnF,gBAAU,MAAM,OAAO,YAAY;AAAA,IAC/C;AACQ,QAAI,YAAY,QAAQ;AACpB,UAAI,CAAC,OAAO,QAAS,OAAM,IAAI,UAAU,gCAAgC;AACzE,gBAAU,MAAM,OAAO,OAAO;AAAA,IAC1C;AACQ,QAAI,OAAO,YAAY,WAAY,OAAM,IAAI,UAAU,wBAAwB;AAC/E,QAAI,MAAM,KAAK,EAAE,OAAc,SAAkB,OAAc;AAAA,EACvE,WACa,OAAO;AACZ,QAAI,MAAM,KAAK,EAAE,OAAO,KAAI,CAAE;AAAA,EACtC;AACI,SAAO;AACX;AACA,IAAI,qBAA2D,yBAAU3B,kBAAiB;AACtF,SAAO,SAAU,KAAK;AAClB,aAAS,KAAK,GAAG;AACb,UAAI,QAAQ,IAAI,WAAW,IAAIA,iBAAgB,GAAG,IAAI,OAAO,0CAA0C,IAAI;AAC3G,UAAI,WAAW;AAAA,IAC3B;AACQ,aAAS,OAAO;AACZ,aAAO,IAAI,MAAM,QAAQ;AACrB,YAAI,MAAM,IAAI,MAAM,IAAK;AACzB,YAAI;AACA,cAAI,SAAS,IAAI,WAAW,IAAI,QAAQ,KAAK,IAAI,KAAK;AACtD,cAAI,IAAI,MAAO,QAAO,QAAQ,QAAQ,MAAM,EAAE,KAAK,MAAM,SAAS,GAAG;AAAE,iBAAK,CAAC;AAAG,mBAAO,KAAM;AAAA,WAAG;AAAA,QACpH,SACuB,GAAG;AACN,eAAK,CAAC;AAAA,QAC1B;AAAA,MACA;AACY,UAAI,IAAI,SAAU,OAAM,IAAI;AAAA,IACxC;AACQ,WAAO,KAAM;AAAA,EAChB;AACL,EAAG,OAAO,oBAAoB,aAAa,kBAAkB,SAAU,OAAO,YAAY,SAAS;AAC/F,MAAI,IAAI,IAAI,MAAM,OAAO;AACzB,SAAO,EAAE,OAAO,mBAAmB,EAAE,QAAQ,OAAO,EAAE,aAAa,YAAY;AACnF,CAAC;AA6BM,MAAM,iBAAiB,KAAK;AAAA,EAC/B;AAAA,EACA;AAAA,EACA,aAAa,IAAI,UAAW;AAAA,EAC5B;AAAA,EACA,YAAY;AAAA,EACZ,kBAAkB,SAAS,OAAQ;AAAA,EACnC,oBAAoB,oBAAI,IAAI;AAAA,IACxB,CAAC,kBAAkB,KAAK,iBAAiB,KAAK,IAAI,CAAC;AAAA,IACnD,CAAC,wBAAwB,KAAK,eAAe,KAAK,IAAI,CAAC;AAAA,IACvD;AAAA,MACI;AAAA,MACA,KAAK,0BAA0B,KAAK,IAAI;AAAA,IAC3C;AAAA,IACD;AAAA,MACI;AAAA,MACA,KAAK,yBAAyB,KAAK,IAAI;AAAA,IAC1C;AAAA,IACD,CAAC,oCAAoC,KAAK,UAAU,KAAK,IAAI,CAAC;AAAA,EACtE,CAAK;AAAA,EACD,wBAAwB;AAAA,IACpB;AAAA,MACI,oBAAoB;AAAA,MACpB,CAAC,YAAY;AACT,aAAK,KAAK,WAAmC,OAAO;AAAA,MACvD;AAAA,IACJ;AAAA,IACD;AAAA,MACI,oBAAoB;AAAA,MACpB,CAAC,YAAY;AACT,aAAK,KAAK,0BAAiE,OAAO;AAAA,MACrF;AAAA,IACJ;AAAA,IACD;AAAA,MACI,oBAAoB;AAAA,MACpB,CAAC,YAAY;AACT,aAAK,KAAK,iBAA+C,OAAO;AAAA,MACnE;AAAA,IACJ;AAAA,IACD;AAAA,MACI,oBAAoB;AAAA,MACpB,CAAC,YAAY;AACT,aAAK,KAAK,mBAAmD,OAAO;AAAA,MACvE;AAAA,IACJ;AAAA,IACD;AAAA,MACI,oBAAoB;AAAA,MACpB,CAAC,aAAa;AACV,aAAK,KAAK,YAAqC,QAAQ;AAAA,MAC1D;AAAA,IACJ;AAAA,EACJ;AAAA,EACD,yBAAyB,oBAAI,IAAI;AAAA,IAC7B,CAAC,qBAAqB,SAAS,KAAK,kBAAkB,KAAK,IAAI,CAAC;AAAA,IAChE,CAAC,qBAAqB,WAAW,KAAK,oBAAoB,KAAK,IAAI,CAAC;AAAA,EAC5E,CAAK;AAAA,EACD;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,UAAU;AACN,WAAO,KAAK,YAAY,QAAS,EAAC;AAAA,EAC1C;AAAA,EACI,YAAY,iBAAiB,gBAAgB,QAAQ;AACjD,UAAO;AACP,SAAK,mBAAmB;AACxB,SAAK,kBAAkB;AACvB,SAAK,UAAU;AACf,SAAK,cAAc,gBAAgB;AACnC,eAAW,CAAC,OAAO,UAAU,KAAK,KAAK,wBAAwB;AAC3D,WAAK,iBAAiB,GAAG,OAAO,UAAU;AAAA,IACtD;AACQ,SAAK,kBAAkB,IAAI,mBAAmB,KAAK,aAAa,IAAI;AACpE,eAAW,CAAC,OAAO,UAAU,KAAK,KAAK,mBAAmB;AACtD,WAAK,YAAY,GAAG,OAAO,UAAU;AAAA,IACjD;AACQ,eAAW,CAAC,OAAO,UAAU,KAAK,KAAK,uBAAuB;AAE1D,WAAK,gBAAgB,GAAG,OAAO,UAAU;AAAA,IACrD;AACQ,UAAM,QAAQ,IAAI,UAAU,MAAM,KAAK,kBAAkB,KAAK,kBAAkB,KAAK,iBAAiB,MAAM;AAC5G,SAAK,WAAW,SAAS,KAAK;AAC9B,SAAK,KAAK,iBAA+C,KAAK;AAE9D,SAAK,iBAAiB,IAAI,cAAc,KAAK,YAAY,QAAS,EAAC,UAAU;AAC7E,SAAK,WAAW,IAAI,QAAQ,KAAK,YAAY,QAAS,EAAC,UAAU;AACjE,SAAK,YAAY,IAAI,SAAS,KAAK,YAAY,QAAS,EAAC,UAAU;AACnE,SAAK,uBAAuB,IAAI4B,mBAAoB,KAAK,YAAY,QAAS,EAAC,UAAU;AACzF,SAAK,oBAAoB,IAAI,iBAAiB,eAAe;AAC7D,SAAK,SAAS,IAAI,UAAU,KAAK,UAAS,EAAG,SAAS;AACtD,SAAK,eAAe,IAAI,gBAAgB,KAAK,UAAS,EAAG,SAAS;AAClE,SAAK,YAAY,IAAI,aAAa,IAAI;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA,EAII,IAAI,aAAa;AACb,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,MAAM,aAAa,WAAW,mBAAmB;AAE7C,UAAM,KAAK,UAAU,KAAK,gCAAgC;AAAA,MACtD;AAAA,MACA;AAAA,IACZ,CAAS;AAAA,EACT;AAAA,EACI,MAAM,aAAa,SAAS;AAExB,UAAM,KAAK,QAAS,EAAC,KAAK,qBAAqB,EAAE,SAAS;AAAA,EAClE;AAAA,EACI,MAAM,aAAa,iBAAiB;AAChC,WAAO,CAAC,gBAAgB,UAAU,iCAAiC;AACnE,WAAO,gBAAgB,IAAI,4DAA4D;AACvF,UAAM,WAAW,MAAM,KAAK,UAAW,EAAC,OAAO,KAAK,wBAAwB;AAAA,MACxE,mBAAmB,gBAAgB;AAAA,IAC/C,CAAS;AACD,WAAO,iBAAiB,KAAK,UAAW,EAAC,UAAS,GAAI;AAAA,MAClD,MAAM;AAAA,MACN,QAAQ,SAAS,QAAQ;AAAA,IACrC,CAAS;AAAA,EACT;AAAA,EACI,mBAAmB,gBAAgB;AAC/B,SAAK,kBAAkB;AAAA,EAC/B;AAAA,EACI,IAAI,gBAAgB;AAChB,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,IAAI,UAAU;AACV,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,IAAI,WAAW;AACX,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,IAAI,QAAQ;AACR,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,IAAI,cAAc;AACd,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,IAAI,WAAW;AACX,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,UAAU;AACN,WAAO,KAAK,eAAgB,EAAC,QAAS;AAAA,EAC9C;AAAA,EACI,iBAAiB;AACb,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,YAAY;AACR,UAAM,YAAY,KAAK,WAAW,aAAc;AAChD,WAAO,WAAW,kCAAkC;AACpD,WAAO;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,EAII,MAAM,eAAe;AACjB,UAAM,QAAQ,EAAE,OAAO,CAAA,GAAI,OAAO,QAAQ,UAAU,MAAO;AAC3D,QAAI;AACA,YAAM,QAAQ,wBAAwB,OAAO,MAAM,KAAK,UAAS,EAC5D,cAAa,EACb,eAAe,MAAM;AACtB,YAAIC;AACJ,YAAI,MAAM;AACV,eAAO,KAAK,SAAS,yBAAyB,mBAAmB;AAC7D,UAAAA,SAAQ,IAAI,SAAS;AACrB,gBAAMA,OAAM;AAAA,QAChC;AACgB,eAAOA;AAAA,MACV,CAAA,GAAG,KAAK;AACT,UAAI,EAAE,iBAAiB,oBAAoB;AACvC,eAAO,KAAK,UAAW;AAAA,MACvC;AACY,aAAO,MAAM,MAAM,aAAc;AAAA,IAC7C,SACe,KAAK;AACR,YAAM,QAAQ;AACd,YAAM,WAAW;AAAA,IAC7B,UACgB;AACJ,yBAAmB,KAAK;AAAA,IACpC;AAAA,EACA;AAAA,EACI,SAAS;AACL,WAAO,MAAM,KAAK,KAAK,WAAW,OAAM,CAAE;AAAA,EAClD;AAAA,EACI,MAAM,SAAS;AACX,WAAO,KAAK,WAAW,QAAQ,WAAW,EAAE,KAAK;AAAA,EACzD;AAAA,EACI,YAAY,SAAS;AACjB,WAAO,KAAK,WAAW,YAAY,OAAO;AAAA,EAClD;AAAA,EACI,eAAe,MAAM;AACjB,UAAM,QAAQ,KAAK,MAAM,KAAK,OAAO;AACrC,QAAI,SAAS,KAAK,UAAS,MAAO,OAAO;AACrC,WAAK,KAAK,QAA6B,MAAS;AAAA,IAC5D;AAAA,EACA;AAAA,EACI,0BAA0B,MAAM;AAC5B,UAAM,QAAQ,KAAK,MAAM,KAAK,OAAO;AACrC,QAAI,OAAO;AACP,WAAK,KAAK,kBAAiD,KAAK;AAAA,IAC5E;AAAA,EACA;AAAA,EACI,yBAAyB,MAAM;AAC3B,UAAM,QAAQ,KAAK,MAAM,KAAK,OAAO;AACrC,QAAI,OAAO;AACP,YAAM,qBAAqB;AAC3B,UAAI,KAAK,UAAW,MAAK,OAAO;AAC5B,aAAK,KAAK,oBAAqD,MAAS;AAAA,MACxF;AACY,WAAK,KAAK,kBAAiD,KAAK;AAAA,IAC5E;AAAA,EACA;AAAA,EACI,kBAAkB,SAAS;AACvB,QAAI,CAAC,KAAK,MAAM,QAAQ,EAAE,MACrB,KAAK,MAAM,QAAQ,UAAU,EAAE,KAAK,CAAC,KAAK,WAAW,aAAY,IAAK;AACvE,YAAM,QAAQ,IAAI,UAAU,MAAM,SAAS,KAAK,kBAAkB,QAAQ,MAAM;AAChF,WAAK,WAAW,SAAS,KAAK;AAC9B,UAAI,UAAU,KAAK,aAAa;AAC5B,aAAK,KAAK,iBAA+C,KAAK;AAAA,MAC9E;AAAA,IACA;AAAA,EACA;AAAA,EACI,oBAAoB,SAAS;AACzB,UAAM,QAAQ,KAAK,MAAM,QAAQ,EAAE;AACnC,QAAI,OAAO;AACP,UAAI,UAAU,KAAK,aAAa;AAC5B,aAAK,KAAK,SAA+B,MAAS;AAAA,MAClE;AACY,WAAK,yBAAyB,KAAK;AAAA,IAC/C;AAAA,EACA;AAAA,EACI,yBAAyB,OAAO;AAC5B,eAAW,SAAS,MAAM,eAAe;AACrC,WAAK,yBAAyB,KAAK;AAAA,IAC/C;AACQ,UAAM,aAAa,EAAG;AACtB,SAAK,gBAAgB,0BAA0B,KAAK;AACpD,SAAK,WAAW,YAAY,KAAK;AACjC,SAAK,KAAK,iBAA+C,KAAK;AAAA,EACtE;AAAA,EACI,iBAAiB,OAAO;AACpB,UAAM,QAAQ,KAAK,MAAM,MAAM,OAAO,OAAO;AAC7C,QAAI,CAAC,OAAO;AACR;AAAA,IACZ;AACQ,QAAI,kBAAkB,KAAK,GAAG;AAC1B,YAAM,OAAO,MAAM,KAAK,IAAI,SAAO;AAC/B,eAAO,iBAAiB,MAAM,UAAS,GAAI,GAAG;AAAA,MAC9D,CAAa;AACD,YAAM,OAAO,KACR,OAAO,CAAC,OAAO,QAAQ;AACxB,cAAM,cAAc,IAAI,mBAClB,iBAAiB,YAAY,IAAI,YAAa,CAAA,IAC9C,IAAI,SAAU;AACpB,eAAO,GAAG,KAAK,IAAI,WAAW;AAAA,MAC9C,GAAe,EAAE,EACA,MAAM,CAAC;AACZ,WAAK,KAAK,WAAmC,IAAI,eAAe,MAAM,QAAQ,MAAM,MAAM,uBAAuB,MAAM,UAAU,CAAC,CAAC;AAAA,IAC/I,WACiB,qBAAqB,KAAK,GAAG;AAClC,YAAM,QAAQ,IAAI,MAAM,MAAM,QAAQ,EAAE;AACxC,YAAM,gBAAgB,MAAM,QAAQ,MAAM,IAAI,EAAE;AAChD,YAAM,eAAe,MAAM,MAAM,MAAM,IAAI,EAAE,OAAO,GAAG,aAAa;AACpE,YAAM,aAAa,CAAE;AACrB,UAAI,MAAM,YAAY;AAClB,mBAAWA,UAAS,MAAM,WAAW,YAAY;AAE7C,qBAAW,KAAK,UAAUA,OAAM,gBAAgB,aAAa,KAAKA,OAAM,GAAG,IAAIA,OAAM,aAAa,CAAC,IAAIA,OAAM,eAAe,CAAC,GAAG;AAChI,cAAI,WAAW,UAAU,MAAM,iBAAiB;AAC5C;AAAA,UACxB;AAAA,QACA;AAAA,MACA;AACY,YAAM,QAAQ,CAAC,GAAG,cAAc,GAAG,UAAU,EAAE,KAAK,IAAI;AACxD,WAAK,KAAK,aAAuC,KAAK;AAAA,IAClE,OACa;AACD,iBAAW,iCAAiC,MAAM,IAAI,YAAY,MAAM,IAAI,gBAAgB,MAAM,KAAK,GAAG;AAAA,IACtH;AAAA,EACA;AAAA,EACI,UAAU,OAAO;AACb,UAAM,QAAQ,KAAK,MAAM,MAAM,OAAO;AACtC,QAAI,CAAC,OAAO;AACR;AAAA,IACZ;AACQ,UAAM,OAAO,mBAAmB,MAAM,IAAI;AAC1C,UAAM,SAAS,IAAI,WAAW,MAAM,QAAO,GAAI,MAAM,MAAM,SAAS,MAAM,YAAY;AACtF,SAAK,KAAK,UAAiC,MAAM;AAAA,EACzD;AAAA,EACI,sBAAsB,IAAI;AACtB,WAAO,KAAK,gBAAgB,sBAAsB,EAAE;AAAA,EAC5D;AAAA,EACI,WAAW;AACP,WAAO,KAAK,gBAAgB,SAAU;AAAA,EAC9C;AAAA,EACI,MAAM,MAAM,SAAS;AACjB,QAAI,KAAK,gBAAgB,YAAY;AACjC;AAAA,IACZ;AACQ,SAAK,gBAAgB,OAAO,IAAI,iBAAiB,cAAc,CAAC;AAChE,SAAK,gBAAgB,QAAS;AAC9B,UAAM,KAAK,YAAY,KAAK,yBAAyB;AAAA,MACjD,SAAS,KAAK,UAAS,EAAG;AAAA,MAC1B,cAAc,SAAS,mBAAmB;AAAA,IACtD,CAAS;AACD,SAAK,KAAK,SAA+B,MAAS;AAClD,SAAK,mBAAoB;AAAA,EACjC;AAAA,EACI,MAAM,OAAO,UAAU,IAAI;AACvB,UAAM,EAAE,YAAY,QAAQ,SAAS,KAAK,KAAK,iBAAiB,kBAAmB,EAAA,IAAM;AACzF,UAAM,CAAC,WAAW,WAAW,IAAI,sBAAsB,SAAS;AAChE,UAAM,UAAUZ,GAAIC,EAAK,KAAK,YAAY,KAAK,0BAA0B;AAAA,MACrE,SAAS,KAAK,UAAS,EAAG;AAAA,MAC1B,MAAM;AAAA,IAClB,CAAS,CAAC,GAAG,GAAI,gBAAgB,OACnB;AAAA,MACE,KAAK,oBAAoB;AAAA,QACrB,SAAS;AAAA,QACT,aAAa,gBAAgB,iBAAiB,IAAI;AAAA,QAClD,UAAU;AAAA,MAC9B,CAAiB;AAAA,IACjB,IACc,CAAE,CAAC,EAAE,KAAKC,EAAI,CAAC,CAAC,EAAE,QAAAN,QAAM,CAAE,MAAM;AAClC,aAAOA;AAAA,IACnB,CAAS,GAAGO,GAAS,QAAQ,EAAE,GAAGF,EAAK,KAAK,gBAAgB,aAAc,CAAA,CAAC,GAAG,uBAAuB,KAAK,IAAG,GAAI,EAAE,CAAC;AAC5G,UAAM,SAAS,MAAMG,GAAe,OAAO;AAC3C,WAAO,KAAK,sBAAsB,OAAO,UAAU;AAAA,EAC3D;AAAA,EACI,4BAA4BlB,UAAS;AACjC,SAAK,iBAAiB,4BAA4BA,QAAO;AAAA,EACjE;AAAA,EACI,kBAAkBA,UAAS;AACvB,SAAK,iBAAiB,kBAAkBA,QAAO;AAAA,EACvD;AAAA,EACI,oBAAoB;AAChB,WAAO,KAAK,iBAAiB,QAAS;AAAA,EAC9C;AAAA,EACI,sBAAsB;AAClB,WAAO,KAAK,qBAAqB;AAAA,EACzC;AAAA,EACI,MAAM,eAAe,SAAS;AAC1B,WAAO,MAAM,KAAK,qBAAqB,eAAe,OAAO;AAAA,EACrE;AAAA,EACI,MAAM,qBAAqB,SAAS;AAChC,WAAO,MAAM,KAAK,qBAAqB,qBAAqB,OAAO;AAAA,EAC3E;AAAA,EACI,MAAM,iBAAiB,MAAM;AACzB,WAAO,MAAM,KAAK,qBAAqB,iBAAiB,IAAI;AAAA,EACpE;AAAA,EACI,MAAM,qBAAqB,QAAQ;AAC/B,WAAO,MAAM,KAAK,qBAAqB,qBAAqB,MAAM;AAAA,EAC1E;AAAA,EACI,MAAM,qBAAqB,UAAU;AACjC,WAAO,MAAM,KAAK,qBAAqB,qBAAqB,QAAQ;AAAA,EAC5E;AAAA,EACI,MAAM,gBAAgB,YAAY;AAC9B,WAAO,MAAM,KAAK,qBAAqB,gBAAgB,UAAU;AAAA,EACzE;AAAA,EACI,MAAM,iBAAiB,WAAW;AAC9B,WAAO,MAAM,KAAK,qBAAqB,iBAAiB,SAAS;AAAA,EACzE;AAAA,EACI,MAAM,wBAAwB,MAAM;AAChC,WAAO,MAAM,KAAK,qBAAqB,wBAAwB,IAAI;AAAA,EAC3E;AAAA,EACI,MAAM,YAAY,UAAU;AACxB,QAAI,CAAC,KAAK,iBAAiB,eAAe;AACtC,YAAM,KAAK,kBAAkB,gBAAgB,QAAQ;AACrD,WAAK,YAAY;AACjB;AAAA,IACZ;AACQ,UAAM,cAAc,MAAM,KAAK,qBAAqB,gBAAgB,QAAQ;AAC5E,SAAK,YAAY;AACjB,QAAI,aAAa;AACb,YAAM,KAAK,OAAQ;AAAA,IAC/B;AAAA,EACA;AAAA,EACI,WAAW;AACP,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,MAAM,IAAI,UAAU,IAAI;AACpB,UAAM,EAAE,SAAS,KAAK,KAAK,iBAAiB,WAAW,OAAO,OAAS,IAAK;AAC5E,UAAM,EAAE,iBAAiB,YAAY,QAAQ,WAAW,OAAO,QAAQ,YAAY,QAAQ,OAAO,kBAAiB,IAAM,gBAAgB,SAAS,IAAI;AACtJ,UAAM,aAAa,SAAS,OAAO,MAAM,IAAI,IAAI,CAAE;AACnD,UAAM,EAAE,OAAQ,IAAG,MAAMkB,GAAeH,EAAK,KAAK,YAAY,KAAK,yBAAyB;AAAA,MACxF,SAAS,KAAK,UAAS,EAAG;AAAA,MAC1B;AAAA,MACA;AAAA,MACA,aAAa,YAAY,cAAc;AAAA,MACvC,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACH;AAAA,MACD;AAAA,MACA;AAAA,MACA,aAAa,CAAC;AAAA,IAC1B,CAAS,CAAC,EAAE,KAAKE,GAAS,QAAQ,EAAE,CAAC,CAAC,CAAC;AAC/B,UAAM,SAAS,OAAO,KAAK,OAAO,MAAM,QAAQ;AAChD,UAAM,KAAK,wBAAwB,MAAM,MAAM;AAC/C,WAAO;AAAA,EACf;AAAA,EACI,MAAM,gBAAgB,SAAS;AAC3B,UAAM,SAAS,MAAM,KAAK,IAAI,OAAO;AACrC,QAAI;AACA,YAAM,EAAE,SAAQ,IAAK,MAAM,OAAO,QAAQ;AAC1C,aAAO,SAAS,KAAK,MAAM;AAAA,IACvC,SACe,OAAO;AACV,UAAI,iBAAiB,WAAW;AAC5B,cAAM,IAAI,MAAM,uDAAuD;AAAA,MACvF;AACY,YAAM;AAAA,IAClB;AAAA,EACA;AAAA,EACI,MAAM,YAAY,SAAS;AACvB,UAAM,EAAE,MAAM,MAAM,uBAAuB,QAAS,IAAG;AACvD,QAAI,QAAQ,mBAAmB,UAAa,QAAQ,gBAAgB;AAChE,YAAM,IAAI,qBAAqB,yCAAyC;AAAA,IACpF;AACQ,QAAI,QAAQ,qBAAqB,UAAa,QAAQ,kBAAkB;AACpE,YAAM,IAAI,qBAAqB,2CAA2C;AAAA,IACtF;AACQ,QAAI,QAAQ,gBAAgB,UAAa,CAAC,QAAQ,aAAa;AAC3D,YAAM,IAAI,qBAAqB,sCAAsC;AAAA,IACjF;AACQ,QAAI,SAAS,UAAa,KAAK,UAAU,UAAa,KAAK,UAAU,GAAG;AACpE,YAAM,IAAI,qBAAqB,0CAA0C;AAAA,IACrF;AACQ,QAAI;AACJ,QAAI,MAAM;AACN,UAAI,uBAAuB;AACvB,cAAM;AAAA,MACtB,OACiB;AAID,cAAM,CAAC,UAAU,OAAO,IAAI,MAAM,KAAK,SAAS,MAAM;AAClD,cAAI,CAAC,OAAO,gBAAgB;AACxB,kBAAM,IAAI,MAAM,yCAAyC;AAAA,UACjF;AACoB,iBAAO;AAAA,YACH,OAAO,eAAe;AAAA,YACtB,OAAO,eAAe;AAAA,UACzB;AAAA,QACrB,CAAiB;AACD,cAAM;AAAA,UACF,GAAG;AAAA,UACH,GAAG,KAAK,IAAI;AAAA,UACZ,GAAG,KAAK,IAAI;AAAA,QACf;AAAA,MACjB;AAAA,IACA;AACQ,UAAM,EAAE,QAAQ,EAAE,KAAM,EAAA,IAAM,MAAM,KAAK,YAAY,KAAK,qCAAqC;AAAA,MAC3F,SAAS,KAAK,UAAS,EAAG;AAAA,MAC1B,QAAQ,wBAAwB,aAAa;AAAA,MAC7C,QAAQ;AAAA,QACJ,MAAM,SAAS,IAAI;AAAA,QACnB,GAAI,YAAY,SAAY,EAAE,SAAS,UAAU,IAAK,IAAG;MAC5D;AAAA,MACD,GAAI,MAAM,EAAE,MAAM,EAAE,MAAM,OAAO,GAAG,MAAO,IAAG;IAC1D,CAAS;AACD,WAAO;AAAA,EACf;AAAA,EACI,MAAM,mBAAmB;AACrB,UAAM,EAAE,UAAS,IAAK,MAAM,KAAK,UAAS,EACrC,QAAO,EACP,WAAW,KAAK,yBAAyB;AAAA,MAC1C,UAAU,KAAK,UAAS,EAAG;AAAA,MAC3B,SAAS;AAAA,IACrB,CAAS;AACD,WAAO,IAAI,kBAAkB,KAAK,UAAS,EAAG,QAAS,GAAE,SAAS;AAAA,EAC1E;AAAA,EACI,MAAM,eAAe;AACjB,UAAM,KAAK,YAAY,KAAK,4BAA4B;AAAA,MACpD,SAAS,KAAK,UAAS,EAAG;AAAA,IACtC,CAAS;AAAA,EACT;AAAA,EACI,MAAM,sBAAsB,iBAAiB,MAAM;AAC/C,UAAM,aAAa,qBAAqB,cAAc,GAAG,IAAI;AAC7D,UAAM,EAAE,OAAM,IAAK,MAAM,KAAK,YAAY,KAAK,2BAA2B;AAAA,MACtE,qBAAqB;AAAA,MACrB,UAAU,CAAC,KAAK,UAAS,EAAG,GAAG;AAAA,IAC3C,CAAS;AACD,WAAO,EAAE,YAAY,OAAO,OAAQ;AAAA,EAC5C;AAAA,EACI,MAAM,oCAAoC,IAAI;AAC1C,UAAM,KAAK,YAAY,KAAK,8BAA8B;AAAA,MACtD,QAAQ;AAAA,IACpB,CAAS;AAAA,EACT;AAAA,EACI,MAAM,eAAe,MAAM,cAAc;AACrC,WAAO,MAAM,KAAK,UAAW,EAAC,eAAe,MAAM,aAAa,eAAe,aAAa,UAAU,YAAY;AAAA,EAC1H;AAAA,EACI,4BAA4B;AACxB,WAAO;AAAA,EACf;AAAA,EACI,MAAM,gBAAgB,SAAS;AAE3B,UAAM,KAAK,UAAU,KAAK,4BAA4B;AAAA,MAClD,eAAe,CAAC;AAAA,IAC5B,CAAS;AAAA,EACT;AAAA,EACI,0BAA0B;AACtB,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,SAAS;AACL,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,qBAAqB;AACjB,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,UAAU;AACN,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,yBAAyB;AACrB,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,sBAAsB;AAClB,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,yBAAyB;AACrB,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,iBAAiB;AACb,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,2BAA2B;AACvB,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,UAAU;AACN,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,YAAY;AACR,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,eAAe;AACX,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,wBAAwB;AAEpB,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,eAAe;AACX,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,sBAAsB;AAClB,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,UAAU;AACN,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,MAAM,OAAO,UAAU,IAAI;AACvB,WAAO,MAAM,KAAK,IAAI,IAAI,OAAO;AAAA,EACzC;AAAA,EACI,MAAM,UAAU,UAAU,IAAI;AAC1B,WAAO,MAAM,KAAK,IAAI,GAAI,OAAO;AAAA,EACzC;AAAA,EACI,MAAM,IAAI,OAAO,SAAS;AACtB,QAAI;AACA,YAAM,SAAS,MAAM,QAAQ,IAAI;AAAA,QAC7B,KAAK,kBAAkB,OAAO;AAAA,QAC9B,KAAK,YAAY,KAAK,mCAAmC;AAAA,UACrD;AAAA,UACA,SAAS,KAAK,UAAS,EAAG;AAAA,QAC9C,CAAiB;AAAA,MACjB,CAAa;AACD,aAAO,OAAO,CAAC;AAAA,IAC3B,SACe,KAAK;AAER,UAAI,YAAY,GAAG,GAAG;AAClB,YAAI,IAAI,QAAQ,SAAS,uBAAuB,GAAG;AAC/C,iBAAO;AAAA,QAC3B;AAAA,MACA;AACY,YAAM;AAAA,IAClB;AAAA,EACA;AAAA,EACI,sBAAsB;AAClB,UAAM,IAAI,qBAAsB;AAAA,EACxC;AACA;AACA,SAAS,kBAAkB,OAAO;AAC9B,SAAO,MAAM,SAAS;AAC1B;AACA,SAAS,qBAAqB,OAAO;AACjC,SAAO,MAAM,SAAS;AAC1B;AACA,SAAS,uBAAuB,YAAY;AACxC,QAAM,sBAAsB,CAAE;AAC9B,MAAI,YAAY;AACZ,eAAW,aAAa,WAAW,YAAY;AAC3C,0BAAoB,KAAK;AAAA,QACrB,KAAK,UAAU;AAAA,QACf,YAAY,UAAU;AAAA,QACtB,cAAc,UAAU;AAAA,MACxC,CAAa;AAAA,IACb;AAAA,EACA;AACI,SAAO;AACX;AACA,SAAS,qBAAqB,QAAQ,MAAM;AACxC,SAAO,UAAU,iBAAiB,KAAK,GAAG,IAAI,CAAC;AACnD;ACjrBA;AAAA;AAAA;AAAA;AAAA;AAYO,MAAM,mBAAmB,OAAO;AAAA,EACnC;AAAA,EACA,YAAY,gBAAgB;AACxB,UAAO;AACP,SAAK,kBAAkB;AAAA,EAC/B;AAAA,EACI,mBAAmB,gBAAgB;AAC/B,SAAK,kBAAkB;AAAA,EAC/B;AAAA,EACI,SAAS;AACL,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,UAAU;AACN,WAAO,KAAK,gBAAgB,QAAS;AAAA,EAC7C;AAAA,EACI,iBAAiB;AACb,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,SAAS;AACL,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,mBAAmB;AACf,UAAM,IAAI,qBAAsB;AAAA,EACxC;AACA;AAIO,MAAM,0BAA0B,OAAO;AAAA,EAC1C;AAAA,EACA,YAAY,SAAS;AACjB,UAAO;AACP,SAAK,WAAW;AAAA,EACxB;AAAA,EACI,MAAM;AACF,WAAO;AAAA,EACf;AAAA,EACI,OAAO;AACH,WAAO,WAAW;AAAA,EAC1B;AAAA,EACI,SAAS;AACL,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,UAAU;AACN,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,iBAAiB;AACb,WAAO,KAAK,SAAS,sBAAuB;AAAA,EACpD;AAAA,EACI,SAAS;AACL,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,mBAAmB;AACf,UAAM,IAAI,qBAAsB;AAAA,EACxC;AACA;AAIO,MAAM,kCAAkC,WAAW;AAAA,EACtD;AAAA,EACA,YAAY,gBAAgB,iBAAiB;AACzC,UAAM,cAAc;AACpB,SAAK,mBAAmB;AAAA,EAChC;AAAA,EACI,MAAM;AACF,WAAO,KAAK,iBAAiB;AAAA,EACrC;AAAA,EACI,MAAM,mBAAmB;AACrB,UAAM,EAAE,UAAS,IAAK,MAAM,KAAK,iBAAiB,WAAW,KAAK,yBAAyB;AAAA,MACvF,UAAU,KAAK,iBAAiB;AAAA,MAChC,SAAS;AAAA,IACrB,CAAS;AACD,WAAO,IAAI,kBAAkB,KAAK,kBAAkB,SAAS;AAAA,EACrE;AAAA,EACI,OAAO;AACH,WAAO,WAAW;AAAA,EAC1B;AACA;AAIO,MAAM,uBAAuB,0BAA0B;AAAA,EAC1D;AAAA,EACA,YAAY,gBAAgB,iBAAiB;AACzC,UAAM,gBAAgB,eAAe;AACrC,SAAK,QAAQ,IAAI,SAAS,iBAAiB,gBAAgB,IAAI;AAAA,EACvE;AAAA,EACI,MAAM,OAAO;AACT,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,mBAAmB,gBAAgB;AAC/B,UAAM,mBAAmB,cAAc;AACvC,SAAK,MAAM,mBAAmB,cAAc;AAAA,EACpD;AACA;AC3GA;AAAA;AAAA;AAAA;AAAA;AAeO,MAAM,oBAAoBT,UAAQ;AAAA,EACrC,WAAW;AAAA;AAAA,EAEX,OAAO,mBAAmB;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACH;AAAA,EACD,OAAO,qBAAqB;AAAA;AAAA,IAExB;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA,IACA;AAAA,EACH;AAAA,EACD,aAAa,OAAO,MAAM;AACtB,UAAM,UAAU,MAAM,QAAQ,KAAK,KAAK,YAAY;AAAA,MAChD,aAAa;AAAA,QACT,qBAAqB,KAAK;AAAA,QAC1B,cAAc;AAAA,MACjB;AAAA,IACb,CAAS;AACD,UAAM,QAAQ,UAAU,QAAQ,aAAa,YAAY,kBAAiB,EAAG,SAAS,SAAS,IACzF,YAAY,mBACZ,CAAC,GAAG,YAAY,kBAAkB,GAAG,YAAY,kBAAkB,CAAC;AAC1E,UAAM,UAAU,IAAI,YAAY,QAAQ,SAAS,IAAI;AACrD,YAAQ,YAAa;AACrB,UAAM,QAAQ,SAAU;AACxB,WAAO;AAAA,EACf;AAAA,EACI;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,WAAW,oBAAI,IAAK;AAAA,EACpB,mBAAmB,oBAAI,QAAS;AAAA,EAChC;AAAA,EACA,2BAA2B,oBAAI,IAAI;AAAA,IAC/B,CAAC,kCAAkC,KAAK,kBAAkB,KAAK,IAAI,CAAC;AAAA,IACpE,CAAC,oCAAoC,KAAK,oBAAoB,KAAK,IAAI,CAAC;AAAA,IACxE,CAAC,oCAAoC,KAAK,oBAAoB,KAAK,IAAI,CAAC;AAAA,IACxE,CAAC,qCAAqC,KAAK,qBAAqB,KAAK,IAAI,CAAC;AAAA,IAC1E,CAAC,qCAAqC,KAAK,qBAAqB,KAAK,IAAI,CAAC;AAAA,EAClF,CAAK;AAAA,EACD,YAAY,aAAa,MAAM;AAC3B,UAAO;AACP,SAAK,WAAW,KAAK;AACrB,SAAK,iBAAiB,KAAK;AAC3B,SAAK,eAAe;AACpB,SAAK,mBAAmB,KAAK;AAC7B,SAAK,iBAAiB,IAAI,kBAAkB,IAAI;AAChD,eAAW,WAAW,KAAK,aAAa,cAAc;AAClD,WAAK,sBAAsB,OAAO;AAAA,IAC9C;AAAA,EACA;AAAA,EACI,cAAc;AACV,SAAK,aAAa,KAAK,gBAAgB,MAAM;AACzC,WAAK,KAAK,gBAAgD,MAAS;AAAA,IAC/E,CAAS;AACD,SAAK,UAAU,KAAK,SAAS,MAAM;AAC/B,WAAK,aAAa,QAAQ,2BAA2B,IAAI;AACzD,WAAK,WAAW,QAAS;AAAA,IACrC,CAAS;AACD,eAAW,CAAC,WAAW,OAAO,KAAK,KAAK,0BAA0B;AAC9D,WAAK,WAAW,GAAG,WAAW,OAAO;AAAA,IACjD;AAAA,EACA;AAAA,EACI,IAAI,eAAe;AACf,WAAO,KAAK,aAAa,QAAQ,aAAa;AAAA,EACtD;AAAA,EACI,IAAI,kBAAkB;AAClB,WAAO,KAAK,aAAa,QAAQ,aAAa;AAAA,EACtD;AAAA,EACI,YAAY;AACR,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,sBAAsB,aAAa;AAC/B,UAAM,iBAAiB,IAAI,mBAAmB,MAAM,aAAa;AAAA,MAC7D,iBAAiB,KAAK;AAAA,IAClC,CAAS;AACD,SAAK,iBAAiB,IAAI,aAAa,cAAc;AACrD,WAAO;AAAA,EACf;AAAA,EACI,oBAAoB,OAAO;AACvB,UAAM,SAAS,KAAK,SAAS,IAAI,MAAM,OAAO;AAC9C,QAAI,QAAQ;AACR,WAAK,KAAK,iBAAkD,MAAM;AAClE,aAAO,eAAgB,EAAC,KAAK,iBAAyD,MAAM;AAAA,IACxG;AAAA,EACA;AAAA,EACI,qBAAqB,OAAO;AACxB,UAAM,SAAS,KAAK,SAAS,IAAI,MAAM,OAAO;AAC9C,QAAI,QAAQ;AACR,WAAK,KAAK,iBAAkD,MAAM;AAClE,aAAO,eAAgB,EAAC,KAAK,iBAAyD,MAAM;AAAA,IACxG;AAAA,EACA;AAAA,EACI,kBAAkB,OAAO;AACrB,UAAM,UAAU,IAAIF,kBAAgB,KAAK,YAAY,OAAO,KAAK,YAAY;AAC7E,SAAK,WAAW,yBAAyB,OAAO;AAChD,UAAM,iBAAiB,MAAM,gBAAgB,YACvC,KAAK,sBAAqB,IAC1B,KAAK,gBAAe,EAAG,KAAK,CAAAqB,oBAAkB;AAC5C,aAAOA,gBAAe,OAAO,MAAM;AAAA,IACnD,CAAa;AACL,QAAI,CAAC,gBAAgB;AACjB,YAAM,IAAI,MAAM,0BAA0B;AAAA,IACtD;AACQ,UAAM,SAAS,CAAC,QAAQ,SAClB,IAAI,eAAe,gBAAgB,OAAO,IAC1C,IAAI,0BAA0B,gBAAgB,OAAO;AAC3D,SAAK,SAAS,IAAI,MAAM,SAAS,MAAM;AACvC,SAAK,KAAK,iBAAkD,MAAM;AAClE,WAAO,eAAgB,EAAC,KAAK,iBAAyD,MAAM;AAC5F,QAAI,QAAQ,QAAQ;AAChB,YAAM,WAAW,KAAK,WAAW,mBAAmB,QAAQ,MAAM;AAClE,eAAS,KAAK,qBAAqB,SAAS,OAAO;AAAA,IAC/D;AAAA,EACA;AAAA,EACI,MAAM,WAAW;AACb,UAAM,EAAE,OAAM,IAAK,MAAM,KAAK,WAAW,KAAK,2BAA2B,EAAE;AAC3E,eAAW,WAAW,OAAO,UAAU;AACnC,WAAK,kBAAkB,OAAO;AAAA,IAC1C;AAAA,EACA;AAAA,EACI,MAAM,oBAAoB,OAAO;AAC7B,UAAM,UAAU,KAAK,WAAW,mBAAmB,MAAM,OAAO;AAChE,UAAM,kBAAkB,KAAK,WAAW,mBAAmB,MAAM,OAAO;AACxE,oBAAgB,KAAK,qBAAqB,WAAW,OAAO;AAC5D,UAAM,SAAS,KAAK,SAAS,IAAI,MAAM,OAAO;AAC9C,UAAM,OAAO,MAAM,QAAQ,KAAM;AACjC,UAAM,MAAM,QAAQ,MAAM,UAAU;AACpC,SAAK,SAAS,OAAO,MAAM,OAAO;AAClC,QAAI,QAAQ;AACR,WAAK,KAAK,mBAAsD,MAAM;AACtE,aAAO,eAAgB,EAAC,KAAK,mBAA6D,MAAM;AAAA,IAC5G;AAAA,EACA;AAAA,EACI,IAAI,aAAa;AAEb,WAAO,KAAK,aAAa,QAAQ;AAAA,EACzC;AAAA,EACI,aAAa;AACT,WAAO,KAAK,WAAW;AAAA,EAC/B;AAAA,EACI,MAAM,QAAQ;AACV,eAAW,CAAC,WAAW,OAAO,KAAK,KAAK,0BAA0B;AAC9D,WAAK,WAAW,IAAI,WAAW,OAAO;AAAA,IAClD;AACQ,QAAI,KAAK,WAAW,QAAQ;AACxB;AAAA,IACZ;AACQ,QAAI;AACA,YAAM,KAAK,aAAa,MAAO;AAC/B,YAAM,KAAK,gBAAgB,KAAK,IAAI;AAAA,IAChD,SACe,OAAO;AAEV,iBAAW,KAAK;AAAA,IAC5B,UACgB;AACJ,WAAK,WAAW,QAAS;AAAA,IACrC;AAAA,EACA;AAAA,EACI,IAAI,YAAY;AACZ,WAAO,CAAC,KAAK,aAAa;AAAA,EAClC;AAAA,EACI,UAAU;AACN,WAAO,KAAK,YAAY;AAAA,EAChC;AAAA,EACI,MAAM,8BAA8B,UAAU;AAC1C,UAAM,cAAc,MAAM,KAAK,aAAa,kBAAmB;AAC/D,WAAO,KAAK,sBAAsB,WAAW;AAAA,EACrD;AAAA,EACI,MAAM,UAAU;AACZ,WAAO,GAAG,KAAK,YAAY,IAAI,KAAK,eAAe;AAAA,EAC3D;AAAA,EACI,kBAAkB;AACd,WAAO,CAAC,GAAG,KAAK,aAAa,YAAY,EAAE,IAAI,aAAW;AACtD,aAAO,KAAK,iBAAiB,IAAI,OAAO;AAAA,IACpD,CAAS;AAAA,EACT;AAAA,EACI,wBAAwB;AACpB,WAAO,KAAK,iBAAiB,IAAI,KAAK,aAAa,kBAAkB;AAAA,EAC7E;AAAA,EACI,UAAU;AACN,WAAO,KAAK,sBAAuB,EAAC,QAAS;AAAA,EACrD;AAAA,EACI,UAAU;AACN,WAAO,CAAC,KAAK,gBAAgB,GAAG,MAAM,KAAK,KAAK,SAAS,OAAM,CAAE,CAAC;AAAA,EAC1E;AAAA,EACI,eAAe,IAAI;AACf,UAAM,SAAS,KAAK,SAAS,IAAI,EAAE;AACnC,QAAI,CAAC,QAAQ;AACT,YAAM,IAAI,MAAM,kBAAkB;AAAA,IAC9C;AACQ,WAAO;AAAA,EACf;AAAA,EACI,SAAS;AACL,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,MAAM,aAAa;AACf,QAAI;AACA,YAAM,KAAK,aAAa,QAAQ,IAAK;AAAA,IACjD,SACe,OAAO;AAEV,iBAAW,KAAK;AAAA,IAC5B,UACgB;AACJ,WAAK,WAAW,QAAS;AAAA,IACrC;AAAA,EACA;AAAA,EACI,IAAI,YAAY;AACZ,WAAO;AAAA,MACH,uBAAuB,KAAK,WAAW,yBAA0B;AAAA,IACpE;AAAA,EACT;AACA;", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30]}
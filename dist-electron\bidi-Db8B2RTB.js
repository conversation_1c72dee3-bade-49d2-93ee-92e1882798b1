var __defProp = Object.defineProperty;
var __typeError = (msg) => {
  throw TypeError(msg);
};
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
var __accessCheck = (obj, member, msg) => member.has(obj) || __typeError("Cannot " + msg);
var __privateGet = (obj, member, getter) => (__accessCheck(obj, member, "read from private field"), getter ? getter.call(obj) : member.get(obj));
var __privateAdd = (obj, member, value) => member.has(obj) ? __typeError("Cannot add the same private member more than once") : member instanceof WeakSet ? member.add(obj) : member.set(obj, value);
var __privateSet = (obj, member, value, setter) => (__accessCheck(obj, member, "write to private field"), setter ? setter.call(obj, value) : member.set(obj, value), value);
var __privateMethod = (obj, member, method) => (__accessCheck(obj, member, "access private method"), method);
var _disposed, _sandbox, _remoteValue, _id, _sandbox2, _BidiRealm_instances, evaluate_fn, _context, _sessionId, _detached, _id2, _url, _cdpSession, _parent, _browserName, _BrowsingContext_instances, updateUrl_fn, _a, _url2, _transport, _delay, _timeout, _closed, _callbacks, _browsingContexts, _emitters, _BidiConnection_instances, maybeEmitOnContext_fn, _cdp, _adapters, _browserCdpConnection, _closed2, _client, _browserClient, _forwardMessage, _onMessage, _workers, _workers2, _browser, _connection, _defaultViewport, _userContext, _context2, _browsingContext, _frame, _apply, _channels, _callerInfos, _preloadScriptId, _handleArgumentsMessage, _ExposeableFunction_instances, connection_get, channelArguments_get, _handleResolveMessage, _handleRejectMessage, getCallbacksAndRemoteValue_fn, _frame2, _page, _context3, _lastMovePoint, _context4, _url3, _resourceType, _method, _postData, _headers, _initiator, _frame3, _request, _remoteAddress, _status, _statusText, _url4, _fromCache, _headers2, _timings, _connection2, _page2, _subscriptions, _requestMap, _navigationMap, _BidiNetworkManager_instances, onBeforeRequestSent_fn, onResponseStarted_fn, onResponseCompleted_fn, onFetchError_fn, _accessibility, _connection3, _frameTree, _networkManager, _viewport, _closedDeferred, _subscribedEvents, _networkManagerEvents, _browsingContextEvents, _tracing, _coverage, _cdpEmulationManager, _emulationManager, _mouse, _touchscreen, _keyboard, _browsingContext2, _browserContext, _target, _BidiPage_instances, onFrameLoaded_fn, onFrameFragmentNavigated_fn, onFrameDOMContentLoaded_fn, onContextCreated_fn, onContextDestroyed_fn, removeFramesRecursively_fn, onLogEntryAdded_fn, onDialog_fn, go_fn, _browser2, _page3, _process, _closeCallback, _browserCore, _defaultViewport2, _targets, _browserContexts, _browserTarget, _connectionEventHandlers, _BidiBrowser_instances, initialize_fn, browserName_get, browserVersion_get, createBrowserContext_fn, onContextDomLoaded_fn, onContextNavigation_fn, onContextCreated_fn2, getTree_fn, onContextDestroyed_fn2;
import { d as debugError, P as PuppeteerURL, J as JSHandle, U as UnsupportedOperation, E as ElementHandle, t as throwIfDisposed, i as isPlainObject, a as isRegExp, b as isDate, L as LazyArg, c as EventEmitter, p as protocol, s as scriptInjector, g as getSourceUrlComment, e as getSourcePuppeteerURLIfAvailable, f as isString, h as stringifyFunction, j as disposeSymbol, S as SOURCE_URL_REGEX, C as CDPSession, D as Deferred, T as TargetCloseError, k as CallbackRegistry, l as assert, m as debug, B as BidiMapper, n as DisposableStack, o as inertIfDisposed, q as BrowserContext, r as Dialog, u as interpolateFunction, W as Wr, v as ProtocolError, w as TimeoutError, R as Realm$1, x as withSourcePuppeteerURLIfNone, y as UTILITY_WORLD_NAME, z as Ee, N as NETWORK_IDLE_TIME, F, A as k, G as Fe, H as timeout, I as me, K as be, M as fromEmitterEvent, O as Pe, Q as we, V as throwIfDetached, X as Frame, Y as Mouse, Z as MouseButton, _ as Touchscreen, $ as Keyboard, a0 as HTTPRequest, a1 as HTTPResponse, a2 as EventSubscription, a3 as NetworkManagerEvent, a4 as Page, a5 as FrameTree, a6 as Accessibility, a7 as Tracing, a8 as Coverage, a9 as EmulationManager$1, aa as ConsoleMessage, ab as validateDialogType, ac as parsePDFOptions, ad as isErrorLike, ae as evaluationString, af as Target, ag as TargetType, ah as Browser$1 } from "./main-Be7A0SP-.js";
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
class BidiDeserializer {
  static deserializeNumber(value) {
    switch (value) {
      case "-0":
        return -0;
      case "NaN":
        return NaN;
      case "Infinity":
        return Infinity;
      case "-Infinity":
        return -Infinity;
      default:
        return value;
    }
  }
  static deserializeLocalValue(result) {
    var _a2, _b, _c, _d;
    switch (result.type) {
      case "array":
        return (_a2 = result.value) == null ? void 0 : _a2.map((value) => {
          return BidiDeserializer.deserializeLocalValue(value);
        });
      case "set":
        return (_b = result.value) == null ? void 0 : _b.reduce((acc, value) => {
          return acc.add(BidiDeserializer.deserializeLocalValue(value));
        }, /* @__PURE__ */ new Set());
      case "object":
        return (_c = result.value) == null ? void 0 : _c.reduce((acc, tuple) => {
          const { key, value } = BidiDeserializer.deserializeTuple(tuple);
          acc[key] = value;
          return acc;
        }, {});
      case "map":
        return (_d = result.value) == null ? void 0 : _d.reduce((acc, tuple) => {
          const { key, value } = BidiDeserializer.deserializeTuple(tuple);
          return acc.set(key, value);
        }, /* @__PURE__ */ new Map());
      case "promise":
        return {};
      case "regexp":
        return new RegExp(result.value.pattern, result.value.flags);
      case "date":
        return new Date(result.value);
      case "undefined":
        return void 0;
      case "null":
        return null;
      case "number":
        return BidiDeserializer.deserializeNumber(result.value);
      case "bigint":
        return BigInt(result.value);
      case "boolean":
        return Boolean(result.value);
      case "string":
        return result.value;
    }
    debugError(`Deserialization of type ${result.type} not supported.`);
    return void 0;
  }
  static deserializeTuple([serializedKey, serializedValue]) {
    const key = typeof serializedKey === "string" ? serializedKey : BidiDeserializer.deserializeLocalValue(serializedKey);
    const value = BidiDeserializer.deserializeLocalValue(serializedValue);
    return { key, value };
  }
  static deserialize(result) {
    if (!result) {
      debugError("Service did not produce a result.");
      return void 0;
    }
    return BidiDeserializer.deserializeLocalValue(result);
  }
}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
async function releaseReference(client, remoteReference) {
  if (!remoteReference.handle) {
    return;
  }
  await client.connection.send("script.disown", {
    target: client.target,
    handles: [remoteReference.handle]
  }).catch((error) => {
    debugError(error);
  });
}
function createEvaluationError(details) {
  if (details.exception.type !== "error") {
    return BidiDeserializer.deserialize(details.exception);
  }
  const [name = "", ...parts] = details.text.split(": ");
  const message = parts.join(": ");
  const error = new Error(message);
  error.name = name;
  const stackLines = [];
  if (details.stackTrace && stackLines.length < Error.stackTraceLimit) {
    for (const frame of details.stackTrace.callFrames.reverse()) {
      if (PuppeteerURL.isPuppeteerURL(frame.url) && frame.url !== PuppeteerURL.INTERNAL_URL) {
        const url = PuppeteerURL.parse(frame.url);
        stackLines.unshift(`    at ${frame.functionName || url.functionName} (${url.functionName} at ${url.siteString}, <anonymous>:${frame.lineNumber}:${frame.columnNumber})`);
      } else {
        stackLines.push(`    at ${frame.functionName || "<anonymous>"} (${frame.url}:${frame.lineNumber}:${frame.columnNumber})`);
      }
      if (stackLines.length >= Error.stackTraceLimit) {
        break;
      }
    }
  }
  error.stack = [details.text, ...stackLines].join("\n");
  return error;
}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
class BidiJSHandle extends JSHandle {
  constructor(sandbox, remoteValue) {
    super();
    __privateAdd(this, _disposed, false);
    __privateAdd(this, _sandbox);
    __privateAdd(this, _remoteValue);
    __privateSet(this, _sandbox, sandbox);
    __privateSet(this, _remoteValue, remoteValue);
  }
  context() {
    return this.realm.environment.context();
  }
  get realm() {
    return __privateGet(this, _sandbox);
  }
  get disposed() {
    return __privateGet(this, _disposed);
  }
  async jsonValue() {
    return await this.evaluate((value) => {
      return value;
    });
  }
  asElement() {
    return null;
  }
  async dispose() {
    if (__privateGet(this, _disposed)) {
      return;
    }
    __privateSet(this, _disposed, true);
    if ("handle" in __privateGet(this, _remoteValue)) {
      await releaseReference(this.context(), __privateGet(this, _remoteValue));
    }
  }
  get isPrimitiveValue() {
    switch (__privateGet(this, _remoteValue).type) {
      case "string":
      case "number":
      case "bigint":
      case "boolean":
      case "undefined":
      case "null":
        return true;
      default:
        return false;
    }
  }
  toString() {
    if (this.isPrimitiveValue) {
      return "JSHandle:" + BidiDeserializer.deserialize(__privateGet(this, _remoteValue));
    }
    return "JSHandle@" + __privateGet(this, _remoteValue).type;
  }
  get id() {
    return "handle" in __privateGet(this, _remoteValue) ? __privateGet(this, _remoteValue).handle : void 0;
  }
  remoteValue() {
    return __privateGet(this, _remoteValue);
  }
  remoteObject() {
    throw new UnsupportedOperation("Not available in WebDriver BiDi");
  }
}
_disposed = new WeakMap();
_sandbox = new WeakMap();
_remoteValue = new WeakMap();
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
var __runInitializers$9 = function(thisArg, initializers, value) {
  var useValue = arguments.length > 2;
  for (var i = 0; i < initializers.length; i++) {
    value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
  }
  return useValue ? value : void 0;
};
var __esDecorate$9 = function(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
  function accept(f) {
    if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected");
    return f;
  }
  var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
  var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
  var _, done = false;
  for (var i = decorators.length - 1; i >= 0; i--) {
    var context = {};
    for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
    for (var p in contextIn.access) context.access[p] = contextIn.access[p];
    context.addInitializer = function(f) {
      if (done) throw new TypeError("Cannot add initializers after decoration has completed");
      extraInitializers.push(accept(f || null));
    };
    var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
    if (kind === "accessor") {
      if (result === void 0) continue;
      if (result === null || typeof result !== "object") throw new TypeError("Object expected");
      if (_ = accept(result.get)) descriptor.get = _;
      if (_ = accept(result.set)) descriptor.set = _;
      if (_ = accept(result.init)) initializers.unshift(_);
    } else if (_ = accept(result)) {
      if (kind === "field") initializers.unshift(_);
      else descriptor[key] = _;
    }
  }
  if (target) Object.defineProperty(target, contextIn.name, descriptor);
  done = true;
};
var __addDisposableResource$2 = function(env, value, async) {
  if (value !== null && value !== void 0) {
    if (typeof value !== "object" && typeof value !== "function") throw new TypeError("Object expected.");
    var dispose;
    if (async) {
      if (!Symbol.asyncDispose) throw new TypeError("Symbol.asyncDispose is not defined.");
      dispose = value[Symbol.asyncDispose];
    }
    if (dispose === void 0) {
      if (!Symbol.dispose) throw new TypeError("Symbol.dispose is not defined.");
      dispose = value[Symbol.dispose];
    }
    if (typeof dispose !== "function") throw new TypeError("Object not disposable.");
    env.stack.push({ value, dispose, async });
  } else if (async) {
    env.stack.push({ async: true });
  }
  return value;
};
var __disposeResources$2 = /* @__PURE__ */ function(SuppressedError2) {
  return function(env) {
    function fail(e) {
      env.error = env.hasError ? new SuppressedError2(e, env.error, "An error was suppressed during disposal.") : e;
      env.hasError = true;
    }
    function next() {
      while (env.stack.length) {
        var rec = env.stack.pop();
        try {
          var result = rec.dispose && rec.dispose.call(rec.value);
          if (rec.async) return Promise.resolve(result).then(next, function(e) {
            fail(e);
            return next();
          });
        } catch (e) {
          fail(e);
        }
      }
      if (env.hasError) throw env.error;
    }
    return next();
  };
}(typeof SuppressedError === "function" ? SuppressedError : function(error, suppressed, message) {
  var e = new Error(message);
  return e.name = "SuppressedError", e.error = error, e.suppressed = suppressed, e;
});
let BidiElementHandle = (() => {
  var _a3;
  var _a2;
  let _classSuper = ElementHandle;
  let _instanceExtraInitializers = [];
  let _autofill_decorators;
  let _contentFrame_decorators;
  return _a3 = class extends _classSuper {
    constructor(sandbox, remoteValue) {
      super(new BidiJSHandle(sandbox, remoteValue));
      __runInitializers$9(this, _instanceExtraInitializers);
    }
    get realm() {
      return this.handle.realm;
    }
    get frame() {
      return this.realm.environment;
    }
    context() {
      return this.handle.context();
    }
    get isPrimitiveValue() {
      return this.handle.isPrimitiveValue;
    }
    remoteValue() {
      return this.handle.remoteValue();
    }
    async autofill(data) {
      const client = this.frame.client;
      const nodeInfo = await client.send("DOM.describeNode", {
        objectId: this.handle.id
      });
      const fieldId = nodeInfo.node.backendNodeId;
      const frameId = this.frame._id;
      await client.send("Autofill.trigger", {
        fieldId,
        frameId,
        card: data.creditCard
      });
    }
    async contentFrame() {
      const env_1 = { stack: [], error: void 0, hasError: false };
      try {
        const handle = __addDisposableResource$2(env_1, await this.evaluateHandle((element) => {
          if (element instanceof HTMLIFrameElement) {
            return element.contentWindow;
          }
          return;
        }), false);
        const value = handle.remoteValue();
        if (value.type === "window") {
          return this.frame.page().frame(value.value.context);
        }
        return null;
      } catch (e_1) {
        env_1.error = e_1;
        env_1.hasError = true;
      } finally {
        __disposeResources$2(env_1);
      }
    }
    uploadFile() {
      throw new UnsupportedOperation();
    }
  }, (() => {
    const _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;
    _autofill_decorators = [throwIfDisposed()];
    _contentFrame_decorators = [throwIfDisposed(), (_a2 = ElementHandle).bindIsolatedHandle.bind(_a2)];
    __esDecorate$9(_a3, null, _autofill_decorators, { kind: "method", name: "autofill", static: false, private: false, access: { has: (obj) => "autofill" in obj, get: (obj) => obj.autofill }, metadata: _metadata }, null, _instanceExtraInitializers);
    __esDecorate$9(_a3, null, _contentFrame_decorators, { kind: "method", name: "contentFrame", static: false, private: false, access: { has: (obj) => "contentFrame" in obj, get: (obj) => obj.contentFrame }, metadata: _metadata }, null, _instanceExtraInitializers);
    if (_metadata) Object.defineProperty(_a3, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
  })(), _a3;
})();
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
class UnserializableError extends Error {
}
class BidiSerializer {
  static serializeNumber(arg) {
    let value;
    if (Object.is(arg, -0)) {
      value = "-0";
    } else if (Object.is(arg, Infinity)) {
      value = "Infinity";
    } else if (Object.is(arg, -Infinity)) {
      value = "-Infinity";
    } else if (Object.is(arg, NaN)) {
      value = "NaN";
    } else {
      value = arg;
    }
    return {
      type: "number",
      value
    };
  }
  static serializeObject(arg) {
    if (arg === null) {
      return {
        type: "null"
      };
    } else if (Array.isArray(arg)) {
      const parsedArray = arg.map((subArg) => {
        return BidiSerializer.serializeRemoteValue(subArg);
      });
      return {
        type: "array",
        value: parsedArray
      };
    } else if (isPlainObject(arg)) {
      try {
        JSON.stringify(arg);
      } catch (error) {
        if (error instanceof TypeError && error.message.startsWith("Converting circular structure to JSON")) {
          error.message += " Recursive objects are not allowed.";
        }
        throw error;
      }
      const parsedObject = [];
      for (const key in arg) {
        parsedObject.push([
          BidiSerializer.serializeRemoteValue(key),
          BidiSerializer.serializeRemoteValue(arg[key])
        ]);
      }
      return {
        type: "object",
        value: parsedObject
      };
    } else if (isRegExp(arg)) {
      return {
        type: "regexp",
        value: {
          pattern: arg.source,
          flags: arg.flags
        }
      };
    } else if (isDate(arg)) {
      return {
        type: "date",
        value: arg.toISOString()
      };
    }
    throw new UnserializableError("Custom object sterilization not possible. Use plain objects instead.");
  }
  static serializeRemoteValue(arg) {
    switch (typeof arg) {
      case "symbol":
      case "function":
        throw new UnserializableError(`Unable to serializable ${typeof arg}`);
      case "object":
        return BidiSerializer.serializeObject(arg);
      case "undefined":
        return {
          type: "undefined"
        };
      case "number":
        return BidiSerializer.serializeNumber(arg);
      case "bigint":
        return {
          type: "bigint",
          value: arg.toString()
        };
      case "string":
        return {
          type: "string",
          value: arg
        };
      case "boolean":
        return {
          type: "boolean",
          value: arg
        };
    }
  }
  static async serialize(sandbox, arg) {
    if (arg instanceof LazyArg) {
      arg = await arg.get(sandbox.realm);
    }
    const objectHandle = arg && (arg instanceof BidiJSHandle || arg instanceof BidiElementHandle) ? arg : null;
    if (objectHandle) {
      if (objectHandle.realm.environment.context() !== sandbox.environment.context()) {
        throw new Error("JSHandles can be evaluated only in the context they were created!");
      }
      if (objectHandle.disposed) {
        throw new Error("JSHandle is disposed!");
      }
      return objectHandle.remoteValue();
    }
    return BidiSerializer.serializeRemoteValue(arg);
  }
}
class BidiRealm extends EventEmitter {
  constructor(connection) {
    super();
    __privateAdd(this, _BidiRealm_instances);
    __publicField(this, "connection");
    __privateAdd(this, _id);
    __privateAdd(this, _sandbox2);
    __publicField(this, "handleRealmDestroyed", async (params) => {
      if (params.realm === __privateGet(this, _id)) {
        this.internalPuppeteerUtil = void 0;
        __privateGet(this, _sandbox2).environment.clearDocumentHandle();
      }
    });
    __publicField(this, "handleRealmCreated", (params) => {
      if (params.type === "window" && params.context === __privateGet(this, _sandbox2).environment._id && params.sandbox === __privateGet(this, _sandbox2).name) {
        __privateSet(this, _id, params.realm);
        void __privateGet(this, _sandbox2).taskManager.rerunAll();
      }
    });
    __publicField(this, "internalPuppeteerUtil");
    this.connection = connection;
  }
  get target() {
    return {
      context: __privateGet(this, _sandbox2).environment._id,
      sandbox: __privateGet(this, _sandbox2).name
    };
  }
  setSandbox(sandbox) {
    __privateSet(this, _sandbox2, sandbox);
    this.connection.on(protocol.ChromiumBidi.Script.EventNames.RealmCreated, this.handleRealmCreated);
    this.connection.on(protocol.ChromiumBidi.Script.EventNames.RealmDestroyed, this.handleRealmDestroyed);
  }
  get puppeteerUtil() {
    const promise = Promise.resolve();
    scriptInjector.inject((script) => {
      if (this.internalPuppeteerUtil) {
        void this.internalPuppeteerUtil.then((handle) => {
          void handle.dispose();
        });
      }
      this.internalPuppeteerUtil = promise.then(() => {
        return this.evaluateHandle(script);
      });
    }, !this.internalPuppeteerUtil);
    return this.internalPuppeteerUtil;
  }
  async evaluateHandle(pageFunction, ...args) {
    return await __privateMethod(this, _BidiRealm_instances, evaluate_fn).call(this, false, pageFunction, ...args);
  }
  async evaluate(pageFunction, ...args) {
    return await __privateMethod(this, _BidiRealm_instances, evaluate_fn).call(this, true, pageFunction, ...args);
  }
  [disposeSymbol]() {
    this.connection.off(protocol.ChromiumBidi.Script.EventNames.RealmCreated, this.handleRealmCreated);
    this.connection.off(protocol.ChromiumBidi.Script.EventNames.RealmDestroyed, this.handleRealmDestroyed);
  }
}
_id = new WeakMap();
_sandbox2 = new WeakMap();
_BidiRealm_instances = new WeakSet();
evaluate_fn = async function(returnByValue, pageFunction, ...args) {
  var _a2;
  const sourceUrlComment = getSourceUrlComment(((_a2 = getSourcePuppeteerURLIfAvailable(pageFunction)) == null ? void 0 : _a2.toString()) ?? PuppeteerURL.INTERNAL_URL);
  const sandbox = __privateGet(this, _sandbox2);
  let responsePromise;
  const resultOwnership = returnByValue ? "none" : "root";
  const serializationOptions = returnByValue ? {} : {
    maxObjectDepth: 0,
    maxDomDepth: 0
  };
  if (isString(pageFunction)) {
    const expression = SOURCE_URL_REGEX.test(pageFunction) ? pageFunction : `${pageFunction}
${sourceUrlComment}
`;
    responsePromise = this.connection.send("script.evaluate", {
      expression,
      target: this.target,
      resultOwnership,
      awaitPromise: true,
      userActivation: true,
      serializationOptions
    });
  } else {
    let functionDeclaration = stringifyFunction(pageFunction);
    functionDeclaration = SOURCE_URL_REGEX.test(functionDeclaration) ? functionDeclaration : `${functionDeclaration}
${sourceUrlComment}
`;
    responsePromise = this.connection.send("script.callFunction", {
      functionDeclaration,
      arguments: args.length ? await Promise.all(args.map((arg) => {
        return BidiSerializer.serialize(sandbox, arg);
      })) : [],
      target: this.target,
      resultOwnership,
      awaitPromise: true,
      userActivation: true,
      serializationOptions
    });
  }
  const { result } = await responsePromise;
  if ("type" in result && result.type === "exception") {
    throw createEvaluationError(result.exceptionDetails);
  }
  return returnByValue ? BidiDeserializer.deserialize(result.result) : createBidiHandle(sandbox, result.result);
};
function createBidiHandle(sandbox, result) {
  if (result.type === "node" || result.type === "window") {
    return new BidiElementHandle(sandbox, result);
  }
  return new BidiJSHandle(sandbox, result);
}
const cdpSessions = /* @__PURE__ */ new Map();
class CdpSessionWrapper extends CDPSession {
  constructor(context, sessionId) {
    super();
    __privateAdd(this, _context);
    __privateAdd(this, _sessionId, Deferred.create());
    __privateAdd(this, _detached, false);
    __privateSet(this, _context, context);
    if (!__privateGet(this, _context).supportsCdp()) {
      return;
    }
    if (sessionId) {
      __privateGet(this, _sessionId).resolve(sessionId);
      cdpSessions.set(sessionId, this);
    } else {
      context.connection.send("cdp.getSession", {
        context: context.id
      }).then((session) => {
        __privateGet(this, _sessionId).resolve(session.result.session);
        cdpSessions.set(session.result.session, this);
      }).catch((err) => {
        __privateGet(this, _sessionId).reject(err);
      });
    }
  }
  connection() {
    return void 0;
  }
  async send(method, ...paramArgs) {
    if (!__privateGet(this, _context).supportsCdp()) {
      throw new UnsupportedOperation("CDP support is required for this feature. The current browser does not support CDP.");
    }
    if (__privateGet(this, _detached)) {
      throw new TargetCloseError(`Protocol error (${method}): Session closed. Most likely the page has been closed.`);
    }
    const session = await __privateGet(this, _sessionId).valueOrThrow();
    const { result } = await __privateGet(this, _context).connection.send("cdp.sendCommand", {
      method,
      params: paramArgs[0],
      session
    });
    return result.result;
  }
  async detach() {
    cdpSessions.delete(this.id());
    if (!__privateGet(this, _detached) && __privateGet(this, _context).supportsCdp()) {
      await __privateGet(this, _context).cdpSession.send("Target.detachFromTarget", {
        sessionId: this.id()
      });
    }
    __privateSet(this, _detached, true);
  }
  id() {
    const val = __privateGet(this, _sessionId).value();
    return val instanceof Error || val === void 0 ? "" : val;
  }
}
_context = new WeakMap();
_sessionId = new WeakMap();
_detached = new WeakMap();
var BrowsingContextEvent;
(function(BrowsingContextEvent2) {
  BrowsingContextEvent2.Created = Symbol("BrowsingContext.created");
  BrowsingContextEvent2.Destroyed = Symbol("BrowsingContext.destroyed");
})(BrowsingContextEvent || (BrowsingContextEvent = {}));
let BrowsingContext$1 = (_a = class extends BidiRealm {
  constructor(connection, info, browserName) {
    super(connection);
    __privateAdd(this, _BrowsingContext_instances);
    __privateAdd(this, _id2);
    __privateAdd(this, _url);
    __privateAdd(this, _cdpSession);
    __privateAdd(this, _parent);
    __privateAdd(this, _browserName, "");
    __privateSet(this, _id2, info.context);
    __privateSet(this, _url, info.url);
    __privateSet(this, _parent, info.parent);
    __privateSet(this, _browserName, browserName);
    __privateSet(this, _cdpSession, new CdpSessionWrapper(this, void 0));
    this.on("browsingContext.domContentLoaded", __privateMethod(this, _BrowsingContext_instances, updateUrl_fn).bind(this));
    this.on("browsingContext.fragmentNavigated", __privateMethod(this, _BrowsingContext_instances, updateUrl_fn).bind(this));
    this.on("browsingContext.load", __privateMethod(this, _BrowsingContext_instances, updateUrl_fn).bind(this));
  }
  supportsCdp() {
    return !__privateGet(this, _browserName).toLowerCase().includes("firefox");
  }
  createRealmForSandbox() {
    return new BidiRealm(this.connection);
  }
  get url() {
    return __privateGet(this, _url);
  }
  get id() {
    return __privateGet(this, _id2);
  }
  get parent() {
    return __privateGet(this, _parent);
  }
  get cdpSession() {
    return __privateGet(this, _cdpSession);
  }
  async sendCdpCommand(method, ...paramArgs) {
    return await __privateGet(this, _cdpSession).send(method, ...paramArgs);
  }
  dispose() {
    this.removeAllListeners();
    this.connection.unregisterBrowsingContexts(__privateGet(this, _id2));
    void __privateGet(this, _cdpSession).detach().catch(debugError);
  }
}, _id2 = new WeakMap(), _url = new WeakMap(), _cdpSession = new WeakMap(), _parent = new WeakMap(), _browserName = new WeakMap(), _BrowsingContext_instances = new WeakSet(), updateUrl_fn = function(info) {
  __privateSet(this, _url, info.url);
}, _a);
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
const debugProtocolSend = debug("puppeteer:webDriverBiDi:SEND ►");
const debugProtocolReceive = debug("puppeteer:webDriverBiDi:RECV ◀");
class BidiConnection extends EventEmitter {
  constructor(url, transport, delay = 0, timeout2) {
    super();
    __privateAdd(this, _BidiConnection_instances);
    __privateAdd(this, _url2);
    __privateAdd(this, _transport);
    __privateAdd(this, _delay);
    __privateAdd(this, _timeout, 0);
    __privateAdd(this, _closed, false);
    __privateAdd(this, _callbacks, new CallbackRegistry());
    __privateAdd(this, _browsingContexts, /* @__PURE__ */ new Map());
    __privateAdd(this, _emitters, []);
    __privateSet(this, _url2, url);
    __privateSet(this, _delay, delay);
    __privateSet(this, _timeout, timeout2 ?? 18e4);
    __privateSet(this, _transport, transport);
    __privateGet(this, _transport).onmessage = this.onMessage.bind(this);
    __privateGet(this, _transport).onclose = this.unbind.bind(this);
  }
  get closed() {
    return __privateGet(this, _closed);
  }
  get url() {
    return __privateGet(this, _url2);
  }
  pipeTo(emitter) {
    __privateGet(this, _emitters).push(emitter);
  }
  emit(type, event) {
    for (const emitter of __privateGet(this, _emitters)) {
      emitter.emit(type, event);
    }
    return super.emit(type, event);
  }
  send(method, params) {
    assert(!__privateGet(this, _closed), "Protocol error: Connection closed.");
    return __privateGet(this, _callbacks).create(method, __privateGet(this, _timeout), (id) => {
      const stringifiedMessage = JSON.stringify({
        id,
        method,
        params
      });
      debugProtocolSend(stringifiedMessage);
      __privateGet(this, _transport).send(stringifiedMessage);
    });
  }
  /**
   * @internal
   */
  async onMessage(message) {
    var _a2;
    if (__privateGet(this, _delay)) {
      await new Promise((f) => {
        return setTimeout(f, __privateGet(this, _delay));
      });
    }
    debugProtocolReceive(message);
    const object = JSON.parse(message);
    if ("type" in object) {
      switch (object.type) {
        case "success":
          __privateGet(this, _callbacks).resolve(object.id, object);
          return;
        case "error":
          if (object.id === null) {
            break;
          }
          __privateGet(this, _callbacks).reject(object.id, createProtocolError(object), object.message);
          return;
        case "event":
          if (isCdpEvent(object)) {
            (_a2 = cdpSessions.get(object.params.session)) == null ? void 0 : _a2.emit(object.params.event, object.params.params);
            return;
          }
          __privateMethod(this, _BidiConnection_instances, maybeEmitOnContext_fn).call(this, object);
          this.emit(object.method, object.params);
          return;
      }
    }
    if ("id" in object) {
      __privateGet(this, _callbacks).reject(object.id, `Protocol Error. Message is not in BiDi protocol format: '${message}'`, object.message);
    }
    debugError(object);
  }
  registerBrowsingContexts(context) {
    __privateGet(this, _browsingContexts).set(context.id, context);
  }
  getBrowsingContext(contextId) {
    const currentContext = __privateGet(this, _browsingContexts).get(contextId);
    if (!currentContext) {
      throw new Error(`BrowsingContext ${contextId} does not exist.`);
    }
    return currentContext;
  }
  getTopLevelContext(contextId) {
    let currentContext = __privateGet(this, _browsingContexts).get(contextId);
    if (!currentContext) {
      throw new Error(`BrowsingContext ${contextId} does not exist.`);
    }
    while (currentContext.parent) {
      contextId = currentContext.parent;
      currentContext = __privateGet(this, _browsingContexts).get(contextId);
      if (!currentContext) {
        throw new Error(`BrowsingContext ${contextId} does not exist.`);
      }
    }
    return currentContext;
  }
  unregisterBrowsingContexts(id) {
    __privateGet(this, _browsingContexts).delete(id);
  }
  /**
   * Unbinds the connection, but keeps the transport open. Useful when the transport will
   * be reused by other connection e.g. with different protocol.
   * @internal
   */
  unbind() {
    if (__privateGet(this, _closed)) {
      return;
    }
    __privateSet(this, _closed, true);
    __privateGet(this, _transport).onmessage = () => {
    };
    __privateGet(this, _transport).onclose = () => {
    };
    __privateGet(this, _browsingContexts).clear();
    __privateGet(this, _callbacks).clear();
  }
  /**
   * Unbinds the connection and closes the transport.
   */
  dispose() {
    this.unbind();
    __privateGet(this, _transport).close();
  }
  getPendingProtocolErrors() {
    return __privateGet(this, _callbacks).getPendingProtocolErrors();
  }
}
_url2 = new WeakMap();
_transport = new WeakMap();
_delay = new WeakMap();
_timeout = new WeakMap();
_closed = new WeakMap();
_callbacks = new WeakMap();
_browsingContexts = new WeakMap();
_emitters = new WeakMap();
_BidiConnection_instances = new WeakSet();
maybeEmitOnContext_fn = function(event) {
  let context;
  if ("context" in event.params && event.params.context !== null) {
    context = __privateGet(this, _browsingContexts).get(event.params.context);
  } else if ("source" in event.params && event.params.source.context !== void 0) {
    context = __privateGet(this, _browsingContexts).get(event.params.source.context);
  }
  context == null ? void 0 : context.emit(event.method, event.params);
};
function createProtocolError(object) {
  let message = `${object.error} ${object.message}`;
  if (object.stacktrace) {
    message += ` ${object.stacktrace}`;
  }
  return message;
}
function isCdpEvent(event) {
  return event.method.startsWith("cdp.");
}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
const bidiServerLogger = (prefix, ...args) => {
  debug(`bidi:${prefix}`)(args);
};
async function connectBidiOverCdp(cdp, options) {
  const transportBiDi = new NoOpTransport();
  const cdpConnectionAdapter = new CdpConnectionAdapter(cdp);
  const pptrTransport = {
    send(message) {
      transportBiDi.emitMessage(JSON.parse(message));
    },
    close() {
      bidiServer.close();
      cdpConnectionAdapter.close();
      cdp.dispose();
    },
    onmessage(_message) {
    }
  };
  transportBiDi.on("bidiResponse", (message) => {
    pptrTransport.onmessage(JSON.stringify(message));
  });
  const pptrBiDiConnection = new BidiConnection(cdp.url(), pptrTransport);
  const bidiServer = await BidiMapper.BidiServer.createAndStart(
    transportBiDi,
    cdpConnectionAdapter,
    // TODO: most likely need a little bit of refactoring
    cdpConnectionAdapter.browserClient(),
    "",
    options,
    void 0,
    bidiServerLogger
  );
  return pptrBiDiConnection;
}
class CdpConnectionAdapter {
  constructor(cdp) {
    __privateAdd(this, _cdp);
    __privateAdd(this, _adapters, /* @__PURE__ */ new Map());
    __privateAdd(this, _browserCdpConnection);
    __privateSet(this, _cdp, cdp);
    __privateSet(this, _browserCdpConnection, new CDPClientAdapter(cdp));
  }
  browserClient() {
    return __privateGet(this, _browserCdpConnection);
  }
  getCdpClient(id) {
    const session = __privateGet(this, _cdp).session(id);
    if (!session) {
      throw new Error(`Unknown CDP session with id ${id}`);
    }
    if (!__privateGet(this, _adapters).has(session)) {
      const adapter = new CDPClientAdapter(session, id, __privateGet(this, _browserCdpConnection));
      __privateGet(this, _adapters).set(session, adapter);
      return adapter;
    }
    return __privateGet(this, _adapters).get(session);
  }
  close() {
    __privateGet(this, _browserCdpConnection).close();
    for (const adapter of __privateGet(this, _adapters).values()) {
      adapter.close();
    }
  }
}
_cdp = new WeakMap();
_adapters = new WeakMap();
_browserCdpConnection = new WeakMap();
class CDPClientAdapter extends BidiMapper.EventEmitter {
  constructor(client, sessionId, browserClient) {
    super();
    __privateAdd(this, _closed2, false);
    __privateAdd(this, _client);
    __publicField(this, "sessionId");
    __privateAdd(this, _browserClient);
    __privateAdd(this, _forwardMessage, (method, event) => {
      this.emit(method, event);
    });
    __privateSet(this, _client, client);
    this.sessionId = sessionId;
    __privateSet(this, _browserClient, browserClient);
    __privateGet(this, _client).on("*", __privateGet(this, _forwardMessage));
  }
  browserClient() {
    return __privateGet(this, _browserClient);
  }
  async sendCommand(method, ...params) {
    if (__privateGet(this, _closed2)) {
      return;
    }
    try {
      return await __privateGet(this, _client).send(method, ...params);
    } catch (err) {
      if (__privateGet(this, _closed2)) {
        return;
      }
      throw err;
    }
  }
  close() {
    __privateGet(this, _client).off("*", __privateGet(this, _forwardMessage));
    __privateSet(this, _closed2, true);
  }
  isCloseError(error) {
    return error instanceof TargetCloseError;
  }
}
_closed2 = new WeakMap();
_client = new WeakMap();
_browserClient = new WeakMap();
_forwardMessage = new WeakMap();
class NoOpTransport extends BidiMapper.EventEmitter {
  constructor() {
    super(...arguments);
    __privateAdd(this, _onMessage, async (_m) => {
      return;
    });
  }
  emitMessage(message) {
    void __privateGet(this, _onMessage).call(this, message);
  }
  setOnMessage(onMessage) {
    __privateSet(this, _onMessage, onMessage);
  }
  async sendMessage(message) {
    this.emit("bidiResponse", message);
  }
  close() {
    __privateSet(this, _onMessage, async (_m) => {
      return;
    });
  }
}
_onMessage = new WeakMap();
/**
 * @license
 * Copyright 2024 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
var __runInitializers$8 = function(thisArg, initializers, value) {
  var useValue = arguments.length > 2;
  for (var i = 0; i < initializers.length; i++) {
    value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
  }
  return useValue ? value : void 0;
};
var __esDecorate$8 = function(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
  function accept(f) {
    if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected");
    return f;
  }
  var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
  var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
  var _, done = false;
  for (var i = decorators.length - 1; i >= 0; i--) {
    var context = {};
    for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
    for (var p in contextIn.access) context.access[p] = contextIn.access[p];
    context.addInitializer = function(f) {
      if (done) throw new TypeError("Cannot add initializers after decoration has completed");
      extraInitializers.push(accept(f || null));
    };
    var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
    if (kind === "accessor") {
      if (result === void 0) continue;
      if (result === null || typeof result !== "object") throw new TypeError("Object expected");
      if (_ = accept(result.get)) descriptor.get = _;
      if (_ = accept(result.set)) descriptor.set = _;
      if (_ = accept(result.init)) initializers.unshift(_);
    } else if (_ = accept(result)) {
      if (kind === "field") initializers.unshift(_);
      else descriptor[key] = _;
    }
  }
  if (target) Object.defineProperty(target, contextIn.name, descriptor);
  done = true;
};
let Navigation = (() => {
  var _request2, _browsingContext3, _disposables, _id3, _Navigation_instances, initialize_fn2, session_get, _a2;
  let _classSuper = EventEmitter;
  let _instanceExtraInitializers = [];
  let _dispose_decorators;
  return _a2 = class extends _classSuper {
    // keep-sorted end
    constructor(context) {
      super();
      __privateAdd(this, _Navigation_instances);
      // keep-sorted start
      __privateAdd(this, _request2, (__runInitializers$8(this, _instanceExtraInitializers), void 0));
      __privateAdd(this, _browsingContext3);
      __privateAdd(this, _disposables, new DisposableStack());
      __privateAdd(this, _id3, new Deferred());
      __privateSet(this, _browsingContext3, context);
    }
    static from(context) {
      var _a3;
      const navigation = new _a2(context);
      __privateMethod(_a3 = navigation, _Navigation_instances, initialize_fn2).call(_a3);
      return navigation;
    }
    get disposed() {
      return __privateGet(this, _disposables).disposed;
    }
    get request() {
      return __privateGet(this, _request2);
    }
    // keep-sorted end
    dispose() {
      this[disposeSymbol]();
    }
    [(_dispose_decorators = [inertIfDisposed], disposeSymbol)]() {
      __privateGet(this, _disposables).dispose();
      super[disposeSymbol]();
    }
  }, _request2 = new WeakMap(), _browsingContext3 = new WeakMap(), _disposables = new WeakMap(), _id3 = new WeakMap(), _Navigation_instances = new WeakSet(), initialize_fn2 = function() {
    const browsingContextEmitter = __privateGet(this, _disposables).use(new EventEmitter(__privateGet(this, _browsingContext3)));
    browsingContextEmitter.once("closed", () => {
      this.emit("failed", {
        url: __privateGet(this, _browsingContext3).url,
        timestamp: /* @__PURE__ */ new Date()
      });
      this.dispose();
    });
    __privateGet(this, _browsingContext3).on("request", ({ request }) => {
      if (request.navigation === __privateGet(this, _id3).value()) {
        __privateSet(this, _request2, request);
        this.emit("request", request);
      }
    });
    const sessionEmitter = __privateGet(this, _disposables).use(new EventEmitter(__privateGet(this, _Navigation_instances, session_get)));
    for (const eventName of [
      "browsingContext.domContentLoaded",
      "browsingContext.load"
    ]) {
      sessionEmitter.on(eventName, (info) => {
        if (info.context !== __privateGet(this, _browsingContext3).id) {
          return;
        }
        if (!info.navigation) {
          return;
        }
        if (!__privateGet(this, _id3).resolved()) {
          __privateGet(this, _id3).resolve(info.navigation);
        }
      });
    }
    for (const [eventName, event] of [
      ["browsingContext.fragmentNavigated", "fragment"],
      ["browsingContext.navigationFailed", "failed"],
      ["browsingContext.navigationAborted", "aborted"]
    ]) {
      sessionEmitter.on(eventName, (info) => {
        if (info.context !== __privateGet(this, _browsingContext3).id) {
          return;
        }
        if (!info.navigation) {
          return;
        }
        if (!__privateGet(this, _id3).resolved()) {
          __privateGet(this, _id3).resolve(info.navigation);
        }
        if (__privateGet(this, _id3).value() !== info.navigation) {
          return;
        }
        this.emit(event, {
          url: info.url,
          timestamp: new Date(info.timestamp)
        });
        this.dispose();
      });
    }
  }, session_get = function() {
    return __privateGet(this, _browsingContext3).userContext.browser.session;
  }, (() => {
    const _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;
    __esDecorate$8(_a2, null, _dispose_decorators, { kind: "method", name: "dispose", static: false, private: false, access: { has: (obj) => "dispose" in obj, get: (obj) => obj.dispose }, metadata: _metadata }, null, _instanceExtraInitializers);
    if (_metadata) Object.defineProperty(_a2, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
  })(), _a2;
})();
/**
 * @license
 * Copyright 2024 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
var __runInitializers$7 = function(thisArg, initializers, value) {
  var useValue = arguments.length > 2;
  for (var i = 0; i < initializers.length; i++) {
    value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
  }
  return useValue ? value : void 0;
};
var __esDecorate$7 = function(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
  function accept(f) {
    if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected");
    return f;
  }
  var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
  var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
  var _, done = false;
  for (var i = decorators.length - 1; i >= 0; i--) {
    var context = {};
    for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
    for (var p in contextIn.access) context.access[p] = contextIn.access[p];
    context.addInitializer = function(f) {
      if (done) throw new TypeError("Cannot add initializers after decoration has completed");
      extraInitializers.push(accept(f || null));
    };
    var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
    if (kind === "accessor") {
      if (result === void 0) continue;
      if (result === null || typeof result !== "object") throw new TypeError("Object expected");
      if (_ = accept(result.get)) descriptor.get = _;
      if (_ = accept(result.set)) descriptor.set = _;
      if (_ = accept(result.init)) initializers.unshift(_);
    } else if (_ = accept(result)) {
      if (kind === "field") initializers.unshift(_);
      else descriptor[key] = _;
    }
  }
  if (target) Object.defineProperty(target, contextIn.name, descriptor);
  done = true;
};
let Realm = (() => {
  var _reason, _a2;
  let _classSuper = EventEmitter;
  let _instanceExtraInitializers = [];
  let _dispose_decorators;
  let _disown_decorators;
  let _callFunction_decorators;
  let _evaluate_decorators;
  return _a2 = class extends _classSuper {
    // keep-sorted end
    constructor(id, origin) {
      super();
      // keep-sorted start
      __privateAdd(this, _reason, (__runInitializers$7(this, _instanceExtraInitializers), void 0));
      __publicField(this, "disposables", new DisposableStack());
      __publicField(this, "id");
      __publicField(this, "origin");
      this.id = id;
      this.origin = origin;
    }
    initialize() {
      const sessionEmitter = this.disposables.use(new EventEmitter(this.session));
      sessionEmitter.on("script.realmDestroyed", (info) => {
        if (info.realm !== this.id) {
          return;
        }
        this.dispose("Realm already destroyed.");
      });
    }
    // keep-sorted start block=yes
    get disposed() {
      return __privateGet(this, _reason) !== void 0;
    }
    get target() {
      return { realm: this.id };
    }
    // keep-sorted end
    dispose(reason) {
      __privateSet(this, _reason, reason);
      this[disposeSymbol]();
    }
    async disown(handles) {
      await this.session.send("script.disown", {
        target: this.target,
        handles
      });
    }
    async callFunction(functionDeclaration, awaitPromise, options = {}) {
      const { result } = await this.session.send("script.callFunction", {
        functionDeclaration,
        awaitPromise,
        target: this.target,
        ...options
      });
      return result;
    }
    async evaluate(expression, awaitPromise, options = {}) {
      const { result } = await this.session.send("script.evaluate", {
        expression,
        awaitPromise,
        target: this.target,
        ...options
      });
      return result;
    }
    [(_dispose_decorators = [inertIfDisposed], _disown_decorators = [throwIfDisposed((realm) => {
      return __privateGet(realm, _reason);
    })], _callFunction_decorators = [throwIfDisposed((realm) => {
      return __privateGet(realm, _reason);
    })], _evaluate_decorators = [throwIfDisposed((realm) => {
      return __privateGet(realm, _reason);
    })], disposeSymbol)]() {
      __privateGet(this, _reason) ?? __privateSet(this, _reason, "Realm already destroyed, probably because all associated browsing contexts closed.");
      this.emit("destroyed", { reason: __privateGet(this, _reason) });
      this.disposables.dispose();
      super[disposeSymbol]();
    }
  }, _reason = new WeakMap(), (() => {
    const _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;
    __esDecorate$7(_a2, null, _dispose_decorators, { kind: "method", name: "dispose", static: false, private: false, access: { has: (obj) => "dispose" in obj, get: (obj) => obj.dispose }, metadata: _metadata }, null, _instanceExtraInitializers);
    __esDecorate$7(_a2, null, _disown_decorators, { kind: "method", name: "disown", static: false, private: false, access: { has: (obj) => "disown" in obj, get: (obj) => obj.disown }, metadata: _metadata }, null, _instanceExtraInitializers);
    __esDecorate$7(_a2, null, _callFunction_decorators, { kind: "method", name: "callFunction", static: false, private: false, access: { has: (obj) => "callFunction" in obj, get: (obj) => obj.callFunction }, metadata: _metadata }, null, _instanceExtraInitializers);
    __esDecorate$7(_a2, null, _evaluate_decorators, { kind: "method", name: "evaluate", static: false, private: false, access: { has: (obj) => "evaluate" in obj, get: (obj) => obj.evaluate }, metadata: _metadata }, null, _instanceExtraInitializers);
    if (_metadata) Object.defineProperty(_a2, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
  })(), _a2;
})();
const _WindowRealm = class _WindowRealm extends Realm {
  constructor(context, sandbox) {
    super("", "");
    // keep-sorted start
    __publicField(this, "browsingContext");
    __publicField(this, "sandbox");
    // keep-sorted end
    __privateAdd(this, _workers, {
      dedicated: /* @__PURE__ */ new Map(),
      shared: /* @__PURE__ */ new Map()
    });
    this.browsingContext = context;
    this.sandbox = sandbox;
  }
  static from(context, sandbox) {
    const realm = new _WindowRealm(context, sandbox);
    realm.initialize();
    return realm;
  }
  initialize() {
    super.initialize();
    const sessionEmitter = this.disposables.use(new EventEmitter(this.session));
    sessionEmitter.on("script.realmCreated", (info) => {
      if (info.type !== "window") {
        return;
      }
      this.id = info.realm;
      this.origin = info.origin;
    });
    sessionEmitter.on("script.realmCreated", (info) => {
      if (info.type !== "dedicated-worker") {
        return;
      }
      if (!info.owners.includes(this.id)) {
        return;
      }
      const realm = DedicatedWorkerRealm.from(this, info.realm, info.origin);
      __privateGet(this, _workers).dedicated.set(realm.id, realm);
      const realmEmitter = this.disposables.use(new EventEmitter(realm));
      realmEmitter.once("destroyed", () => {
        realmEmitter.removeAllListeners();
        __privateGet(this, _workers).dedicated.delete(realm.id);
      });
      this.emit("worker", realm);
    });
    this.browsingContext.userContext.browser.on("sharedworker", ({ realm }) => {
      if (!realm.owners.has(this)) {
        return;
      }
      __privateGet(this, _workers).shared.set(realm.id, realm);
      const realmEmitter = this.disposables.use(new EventEmitter(realm));
      realmEmitter.once("destroyed", () => {
        realmEmitter.removeAllListeners();
        __privateGet(this, _workers).shared.delete(realm.id);
      });
      this.emit("sharedworker", realm);
    });
  }
  get session() {
    return this.browsingContext.userContext.browser.session;
  }
  get target() {
    return { context: this.browsingContext.id, sandbox: this.sandbox };
  }
};
_workers = new WeakMap();
let WindowRealm = _WindowRealm;
const _DedicatedWorkerRealm = class _DedicatedWorkerRealm extends Realm {
  // keep-sorted end
  constructor(owner, id, origin) {
    super(id, origin);
    // keep-sorted start
    __privateAdd(this, _workers2, /* @__PURE__ */ new Map());
    __publicField(this, "owners");
    this.owners = /* @__PURE__ */ new Set([owner]);
  }
  static from(owner, id, origin) {
    const realm = new _DedicatedWorkerRealm(owner, id, origin);
    realm.initialize();
    return realm;
  }
  initialize() {
    super.initialize();
    const sessionEmitter = this.disposables.use(new EventEmitter(this.session));
    sessionEmitter.on("script.realmCreated", (info) => {
      if (info.type !== "dedicated-worker") {
        return;
      }
      if (!info.owners.includes(this.id)) {
        return;
      }
      const realm = _DedicatedWorkerRealm.from(this, info.realm, info.origin);
      __privateGet(this, _workers2).set(realm.id, realm);
      const realmEmitter = this.disposables.use(new EventEmitter(realm));
      realmEmitter.once("destroyed", () => {
        __privateGet(this, _workers2).delete(realm.id);
      });
      this.emit("worker", realm);
    });
  }
  get session() {
    return this.owners.values().next().value.session;
  }
};
_workers2 = new WeakMap();
let DedicatedWorkerRealm = _DedicatedWorkerRealm;
/**
 * @license
 * Copyright 2024 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
var __runInitializers$6 = function(thisArg, initializers, value) {
  var useValue = arguments.length > 2;
  for (var i = 0; i < initializers.length; i++) {
    value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
  }
  return useValue ? value : void 0;
};
var __esDecorate$6 = function(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
  function accept(f) {
    if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected");
    return f;
  }
  var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
  var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
  var _, done = false;
  for (var i = decorators.length - 1; i >= 0; i--) {
    var context = {};
    for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
    for (var p in contextIn.access) context.access[p] = contextIn.access[p];
    context.addInitializer = function(f) {
      if (done) throw new TypeError("Cannot add initializers after decoration has completed");
      extraInitializers.push(accept(f || null));
    };
    var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
    if (kind === "accessor") {
      if (result === void 0) continue;
      if (result === null || typeof result !== "object") throw new TypeError("Object expected");
      if (_ = accept(result.get)) descriptor.get = _;
      if (_ = accept(result.set)) descriptor.set = _;
      if (_ = accept(result.init)) initializers.unshift(_);
    } else if (_ = accept(result)) {
      if (kind === "field") initializers.unshift(_);
      else descriptor[key] = _;
    }
  }
  if (target) Object.defineProperty(target, contextIn.name, descriptor);
  done = true;
};
let Request = (() => {
  var _error, _redirect, _response, _browsingContext3, _disposables, _event, _Request_instances, initialize_fn2, session_get, _a2;
  let _classSuper = EventEmitter;
  let _instanceExtraInitializers = [];
  let _dispose_decorators;
  return _a2 = class extends _classSuper {
    // keep-sorted end
    constructor(browsingContext, event) {
      super();
      __privateAdd(this, _Request_instances);
      // keep-sorted start
      __privateAdd(this, _error, (__runInitializers$6(this, _instanceExtraInitializers), void 0));
      __privateAdd(this, _redirect);
      __privateAdd(this, _response);
      __privateAdd(this, _browsingContext3);
      __privateAdd(this, _disposables, new DisposableStack());
      __privateAdd(this, _event);
      __privateSet(this, _browsingContext3, browsingContext);
      __privateSet(this, _event, event);
    }
    static from(browsingContext, event) {
      var _a3;
      const request = new _a2(browsingContext, event);
      __privateMethod(_a3 = request, _Request_instances, initialize_fn2).call(_a3);
      return request;
    }
    get disposed() {
      return __privateGet(this, _disposables).disposed;
    }
    get error() {
      return __privateGet(this, _error);
    }
    get headers() {
      return __privateGet(this, _event).request.headers;
    }
    get id() {
      return __privateGet(this, _event).request.request;
    }
    get initiator() {
      return __privateGet(this, _event).initiator;
    }
    get method() {
      return __privateGet(this, _event).request.method;
    }
    get navigation() {
      return __privateGet(this, _event).navigation ?? void 0;
    }
    get redirect() {
      return this.redirect;
    }
    get response() {
      return __privateGet(this, _response);
    }
    get url() {
      return __privateGet(this, _event).request.url;
    }
    // keep-sorted end
    dispose() {
      this[disposeSymbol]();
    }
    [(_dispose_decorators = [inertIfDisposed], disposeSymbol)]() {
      __privateGet(this, _disposables).dispose();
      super[disposeSymbol]();
    }
  }, _error = new WeakMap(), _redirect = new WeakMap(), _response = new WeakMap(), _browsingContext3 = new WeakMap(), _disposables = new WeakMap(), _event = new WeakMap(), _Request_instances = new WeakSet(), initialize_fn2 = function() {
    const browsingContextEmitter = __privateGet(this, _disposables).use(new EventEmitter(__privateGet(this, _browsingContext3)));
    browsingContextEmitter.once("closed", ({ reason }) => {
      __privateSet(this, _error, reason);
      this.emit("error", __privateGet(this, _error));
      this.dispose();
    });
    const sessionEmitter = __privateGet(this, _disposables).use(new EventEmitter(__privateGet(this, _Request_instances, session_get)));
    sessionEmitter.on("network.beforeRequestSent", (event) => {
      if (event.context !== __privateGet(this, _browsingContext3).id) {
        return;
      }
      if (event.request.request !== this.id) {
        return;
      }
      __privateSet(this, _redirect, _a2.from(__privateGet(this, _browsingContext3), event));
      this.emit("redirect", __privateGet(this, _redirect));
      this.dispose();
    });
    sessionEmitter.on("network.fetchError", (event) => {
      if (event.context !== __privateGet(this, _browsingContext3).id) {
        return;
      }
      if (event.request.request !== this.id) {
        return;
      }
      __privateSet(this, _error, event.errorText);
      this.emit("error", __privateGet(this, _error));
      this.dispose();
    });
    sessionEmitter.on("network.responseCompleted", (event) => {
      if (event.context !== __privateGet(this, _browsingContext3).id) {
        return;
      }
      if (event.request.request !== this.id) {
        return;
      }
      __privateSet(this, _response, event.response);
      this.emit("success", __privateGet(this, _response));
      this.dispose();
    });
  }, session_get = function() {
    return __privateGet(this, _browsingContext3).userContext.browser.session;
  }, (() => {
    const _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;
    __esDecorate$6(_a2, null, _dispose_decorators, { kind: "method", name: "dispose", static: false, private: false, access: { has: (obj) => "dispose" in obj, get: (obj) => obj.dispose }, metadata: _metadata }, null, _instanceExtraInitializers);
    if (_metadata) Object.defineProperty(_a2, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
  })(), _a2;
})();
/**
 * @license
 * Copyright 2024 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
var __runInitializers$5 = function(thisArg, initializers, value) {
  var useValue = arguments.length > 2;
  for (var i = 0; i < initializers.length; i++) {
    value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
  }
  return useValue ? value : void 0;
};
var __esDecorate$5 = function(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
  function accept(f) {
    if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected");
    return f;
  }
  var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
  var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
  var _, done = false;
  for (var i = decorators.length - 1; i >= 0; i--) {
    var context = {};
    for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
    for (var p in contextIn.access) context.access[p] = contextIn.access[p];
    context.addInitializer = function(f) {
      if (done) throw new TypeError("Cannot add initializers after decoration has completed");
      extraInitializers.push(accept(f || null));
    };
    var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
    if (kind === "accessor") {
      if (result === void 0) continue;
      if (result === null || typeof result !== "object") throw new TypeError("Object expected");
      if (_ = accept(result.get)) descriptor.get = _;
      if (_ = accept(result.set)) descriptor.set = _;
      if (_ = accept(result.init)) initializers.unshift(_);
    } else if (_ = accept(result)) {
      if (kind === "field") initializers.unshift(_);
      else descriptor[key] = _;
    }
  }
  if (target) Object.defineProperty(target, contextIn.name, descriptor);
  done = true;
};
let UserPrompt = (() => {
  var _reason, _result, _disposables, _UserPrompt_instances, initialize_fn2, session_get, _a2;
  let _classSuper = EventEmitter;
  let _instanceExtraInitializers = [];
  let _dispose_decorators;
  let _handle_decorators;
  return _a2 = class extends _classSuper {
    // keep-sorted end
    constructor(context, info) {
      super();
      __privateAdd(this, _UserPrompt_instances);
      // keep-sorted start
      __privateAdd(this, _reason, (__runInitializers$5(this, _instanceExtraInitializers), void 0));
      __privateAdd(this, _result);
      __privateAdd(this, _disposables, new DisposableStack());
      __publicField(this, "browsingContext");
      __publicField(this, "info");
      this.browsingContext = context;
      this.info = info;
    }
    static from(browsingContext, info) {
      var _a3;
      const userPrompt = new _a2(browsingContext, info);
      __privateMethod(_a3 = userPrompt, _UserPrompt_instances, initialize_fn2).call(_a3);
      return userPrompt;
    }
    get closed() {
      return __privateGet(this, _reason) !== void 0;
    }
    get disposed() {
      return this.closed;
    }
    get handled() {
      return __privateGet(this, _result) !== void 0;
    }
    get result() {
      return __privateGet(this, _result);
    }
    // keep-sorted end
    dispose(reason) {
      __privateSet(this, _reason, reason);
      this[disposeSymbol]();
    }
    async handle(options = {}) {
      await __privateGet(this, _UserPrompt_instances, session_get).send("browsingContext.handleUserPrompt", {
        ...options,
        context: this.info.context
      });
      return __privateGet(this, _result);
    }
    [(_dispose_decorators = [inertIfDisposed], _handle_decorators = [throwIfDisposed((prompt) => {
      return __privateGet(prompt, _reason);
    })], disposeSymbol)]() {
      __privateGet(this, _reason) ?? __privateSet(this, _reason, "User prompt already closed, probably because the associated browsing context was destroyed.");
      this.emit("closed", { reason: __privateGet(this, _reason) });
      __privateGet(this, _disposables).dispose();
      super[disposeSymbol]();
    }
  }, _reason = new WeakMap(), _result = new WeakMap(), _disposables = new WeakMap(), _UserPrompt_instances = new WeakSet(), initialize_fn2 = function() {
    const browserContextEmitter = __privateGet(this, _disposables).use(new EventEmitter(this.browsingContext));
    browserContextEmitter.once("closed", ({ reason }) => {
      this.dispose(`User prompt already closed: ${reason}`);
    });
    const sessionEmitter = __privateGet(this, _disposables).use(new EventEmitter(__privateGet(this, _UserPrompt_instances, session_get)));
    sessionEmitter.on("browsingContext.userPromptClosed", (parameters) => {
      if (parameters.context !== this.browsingContext.id) {
        return;
      }
      __privateSet(this, _result, parameters);
      this.emit("handled", parameters);
      this.dispose("User prompt already handled.");
    });
  }, session_get = function() {
    return this.browsingContext.userContext.browser.session;
  }, (() => {
    const _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;
    __esDecorate$5(_a2, null, _dispose_decorators, { kind: "method", name: "dispose", static: false, private: false, access: { has: (obj) => "dispose" in obj, get: (obj) => obj.dispose }, metadata: _metadata }, null, _instanceExtraInitializers);
    __esDecorate$5(_a2, null, _handle_decorators, { kind: "method", name: "handle", static: false, private: false, access: { has: (obj) => "handle" in obj, get: (obj) => obj.handle }, metadata: _metadata }, null, _instanceExtraInitializers);
    if (_metadata) Object.defineProperty(_a2, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
  })(), _a2;
})();
/**
 * @license
 * Copyright 2024 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
var __runInitializers$4 = function(thisArg, initializers, value) {
  var useValue = arguments.length > 2;
  for (var i = 0; i < initializers.length; i++) {
    value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
  }
  return useValue ? value : void 0;
};
var __esDecorate$4 = function(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
  function accept(f) {
    if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected");
    return f;
  }
  var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
  var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
  var _, done = false;
  for (var i = decorators.length - 1; i >= 0; i--) {
    var context = {};
    for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
    for (var p in contextIn.access) context.access[p] = contextIn.access[p];
    context.addInitializer = function(f) {
      if (done) throw new TypeError("Cannot add initializers after decoration has completed");
      extraInitializers.push(accept(f || null));
    };
    var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
    if (kind === "accessor") {
      if (result === void 0) continue;
      if (result === null || typeof result !== "object") throw new TypeError("Object expected");
      if (_ = accept(result.get)) descriptor.get = _;
      if (_ = accept(result.set)) descriptor.set = _;
      if (_ = accept(result.init)) initializers.unshift(_);
    } else if (_ = accept(result)) {
      if (kind === "field") initializers.unshift(_);
      else descriptor[key] = _;
    }
  }
  if (target) Object.defineProperty(target, contextIn.name, descriptor);
  done = true;
};
let BrowsingContext = (() => {
  var _navigation, _reason, _url5, _children, _disposables, _realms, _requests, _BrowsingContext_instances2, initialize_fn2, session_get, _a2;
  let _classSuper = EventEmitter;
  let _instanceExtraInitializers = [];
  let _dispose_decorators;
  let _activate_decorators;
  let _captureScreenshot_decorators;
  let _close_decorators;
  let _traverseHistory_decorators;
  let _navigate_decorators;
  let _reload_decorators;
  let _print_decorators;
  let _handleUserPrompt_decorators;
  let _setViewport_decorators;
  let _performActions_decorators;
  let _releaseActions_decorators;
  let _createWindowRealm_decorators;
  let _addPreloadScript_decorators;
  let _removePreloadScript_decorators;
  return _a2 = class extends _classSuper {
    // keep-sorted end
    constructor(context, parent, id, url) {
      super();
      __privateAdd(this, _BrowsingContext_instances2);
      // keep-sorted start
      __privateAdd(this, _navigation, (__runInitializers$4(this, _instanceExtraInitializers), void 0));
      __privateAdd(this, _reason);
      __privateAdd(this, _url5);
      __privateAdd(this, _children, /* @__PURE__ */ new Map());
      __privateAdd(this, _disposables, new DisposableStack());
      __privateAdd(this, _realms, /* @__PURE__ */ new Map());
      __privateAdd(this, _requests, /* @__PURE__ */ new Map());
      __publicField(this, "defaultRealm");
      __publicField(this, "id");
      __publicField(this, "parent");
      __publicField(this, "userContext");
      __privateSet(this, _url5, url);
      this.id = id;
      this.parent = parent;
      this.userContext = context;
      this.defaultRealm = WindowRealm.from(this);
    }
    static from(userContext, parent, id, url) {
      var _a3;
      const browsingContext = new _a2(userContext, parent, id, url);
      __privateMethod(_a3 = browsingContext, _BrowsingContext_instances2, initialize_fn2).call(_a3);
      return browsingContext;
    }
    get children() {
      return __privateGet(this, _children).values();
    }
    get closed() {
      return __privateGet(this, _reason) !== void 0;
    }
    get disposed() {
      return this.closed;
    }
    get realms() {
      return __privateGet(this, _realms).values();
    }
    get top() {
      let context = this;
      for (let { parent } = context; parent; { parent } = context) {
        context = parent;
      }
      return context;
    }
    get url() {
      return __privateGet(this, _url5);
    }
    // keep-sorted end
    dispose(reason) {
      __privateSet(this, _reason, reason);
      this[disposeSymbol]();
    }
    async activate() {
      await __privateGet(this, _BrowsingContext_instances2, session_get).send("browsingContext.activate", {
        context: this.id
      });
    }
    async captureScreenshot(options = {}) {
      const { result: { data } } = await __privateGet(this, _BrowsingContext_instances2, session_get).send("browsingContext.captureScreenshot", {
        context: this.id,
        ...options
      });
      return data;
    }
    async close(promptUnload) {
      await Promise.all([...__privateGet(this, _children).values()].map(async (child) => {
        await child.close(promptUnload);
      }));
      await __privateGet(this, _BrowsingContext_instances2, session_get).send("browsingContext.close", {
        context: this.id,
        promptUnload
      });
    }
    async traverseHistory(delta) {
      await __privateGet(this, _BrowsingContext_instances2, session_get).send("browsingContext.traverseHistory", {
        context: this.id,
        delta
      });
    }
    async navigate(url, wait) {
      await __privateGet(this, _BrowsingContext_instances2, session_get).send("browsingContext.navigate", {
        context: this.id,
        url,
        wait
      });
      return await new Promise((resolve) => {
        this.once("navigation", ({ navigation }) => {
          resolve(navigation);
        });
      });
    }
    async reload(options = {}) {
      await __privateGet(this, _BrowsingContext_instances2, session_get).send("browsingContext.reload", {
        context: this.id,
        ...options
      });
      return await new Promise((resolve) => {
        this.once("navigation", ({ navigation }) => {
          resolve(navigation);
        });
      });
    }
    async print(options = {}) {
      const { result: { data } } = await __privateGet(this, _BrowsingContext_instances2, session_get).send("browsingContext.print", {
        context: this.id,
        ...options
      });
      return data;
    }
    async handleUserPrompt(options = {}) {
      await __privateGet(this, _BrowsingContext_instances2, session_get).send("browsingContext.handleUserPrompt", {
        context: this.id,
        ...options
      });
    }
    async setViewport(options = {}) {
      await __privateGet(this, _BrowsingContext_instances2, session_get).send("browsingContext.setViewport", {
        context: this.id,
        ...options
      });
    }
    async performActions(actions) {
      await __privateGet(this, _BrowsingContext_instances2, session_get).send("input.performActions", {
        context: this.id,
        actions
      });
    }
    async releaseActions() {
      await __privateGet(this, _BrowsingContext_instances2, session_get).send("input.releaseActions", {
        context: this.id
      });
    }
    createWindowRealm(sandbox) {
      return WindowRealm.from(this, sandbox);
    }
    async addPreloadScript(functionDeclaration, options = {}) {
      return await this.userContext.browser.addPreloadScript(functionDeclaration, {
        ...options,
        contexts: [this, ...options.contexts ?? []]
      });
    }
    async removePreloadScript(script) {
      await this.userContext.browser.removePreloadScript(script);
    }
    [(_dispose_decorators = [inertIfDisposed], _activate_decorators = [throwIfDisposed((context) => {
      return __privateGet(context, _reason);
    })], _captureScreenshot_decorators = [throwIfDisposed((context) => {
      return __privateGet(context, _reason);
    })], _close_decorators = [throwIfDisposed((context) => {
      return __privateGet(context, _reason);
    })], _traverseHistory_decorators = [throwIfDisposed((context) => {
      return __privateGet(context, _reason);
    })], _navigate_decorators = [throwIfDisposed((context) => {
      return __privateGet(context, _reason);
    })], _reload_decorators = [throwIfDisposed((context) => {
      return __privateGet(context, _reason);
    })], _print_decorators = [throwIfDisposed((context) => {
      return __privateGet(context, _reason);
    })], _handleUserPrompt_decorators = [throwIfDisposed((context) => {
      return __privateGet(context, _reason);
    })], _setViewport_decorators = [throwIfDisposed((context) => {
      return __privateGet(context, _reason);
    })], _performActions_decorators = [throwIfDisposed((context) => {
      return __privateGet(context, _reason);
    })], _releaseActions_decorators = [throwIfDisposed((context) => {
      return __privateGet(context, _reason);
    })], _createWindowRealm_decorators = [throwIfDisposed((context) => {
      return __privateGet(context, _reason);
    })], _addPreloadScript_decorators = [throwIfDisposed((context) => {
      return __privateGet(context, _reason);
    })], _removePreloadScript_decorators = [throwIfDisposed((context) => {
      return __privateGet(context, _reason);
    })], disposeSymbol)]() {
      __privateGet(this, _reason) ?? __privateSet(this, _reason, "Browsing context already closed, probably because the user context closed.");
      this.emit("closed", { reason: __privateGet(this, _reason) });
      __privateGet(this, _disposables).dispose();
      super[disposeSymbol]();
    }
  }, _navigation = new WeakMap(), _reason = new WeakMap(), _url5 = new WeakMap(), _children = new WeakMap(), _disposables = new WeakMap(), _realms = new WeakMap(), _requests = new WeakMap(), _BrowsingContext_instances2 = new WeakSet(), initialize_fn2 = function() {
    const userContextEmitter = __privateGet(this, _disposables).use(new EventEmitter(this.userContext));
    userContextEmitter.once("closed", ({ reason }) => {
      this.dispose(`Browsing context already closed: ${reason}`);
    });
    const sessionEmitter = __privateGet(this, _disposables).use(new EventEmitter(__privateGet(this, _BrowsingContext_instances2, session_get)));
    sessionEmitter.on("browsingContext.contextCreated", (info) => {
      if (info.parent !== this.id) {
        return;
      }
      const browsingContext = _a2.from(this.userContext, this, info.context, info.url);
      __privateGet(this, _children).set(info.context, browsingContext);
      const browsingContextEmitter = __privateGet(this, _disposables).use(new EventEmitter(browsingContext));
      browsingContextEmitter.once("closed", () => {
        browsingContextEmitter.removeAllListeners();
        __privateGet(this, _children).delete(browsingContext.id);
      });
      this.emit("browsingcontext", { browsingContext });
    });
    sessionEmitter.on("browsingContext.contextDestroyed", (info) => {
      if (info.context !== this.id) {
        return;
      }
      this.dispose("Browsing context already closed.");
    });
    sessionEmitter.on("browsingContext.domContentLoaded", (info) => {
      if (info.context !== this.id) {
        return;
      }
      __privateSet(this, _url5, info.url);
      this.emit("DOMContentLoaded", void 0);
    });
    sessionEmitter.on("browsingContext.load", (info) => {
      if (info.context !== this.id) {
        return;
      }
      __privateSet(this, _url5, info.url);
      this.emit("load", void 0);
    });
    sessionEmitter.on("browsingContext.navigationStarted", (info) => {
      if (info.context !== this.id) {
        return;
      }
      __privateSet(this, _url5, info.url);
      __privateGet(this, _requests).clear();
      __privateSet(this, _navigation, Navigation.from(this));
      const navigationEmitter = __privateGet(this, _disposables).use(new EventEmitter(__privateGet(this, _navigation)));
      for (const eventName of ["fragment", "failed", "aborted"]) {
        navigationEmitter.once(eventName, ({ url }) => {
          navigationEmitter[disposeSymbol]();
          __privateSet(this, _url5, url);
        });
      }
      this.emit("navigation", { navigation: __privateGet(this, _navigation) });
    });
    sessionEmitter.on("network.beforeRequestSent", (event) => {
      if (event.context !== this.id) {
        return;
      }
      if (__privateGet(this, _requests).has(event.request.request)) {
        return;
      }
      const request = Request.from(this, event);
      __privateGet(this, _requests).set(request.id, request);
      this.emit("request", { request });
    });
    sessionEmitter.on("log.entryAdded", (entry) => {
      if (entry.source.context !== this.id) {
        return;
      }
      this.emit("log", { entry });
    });
    sessionEmitter.on("browsingContext.userPromptOpened", (info) => {
      if (info.context !== this.id) {
        return;
      }
      const userPrompt = UserPrompt.from(this, info);
      this.emit("userprompt", { userPrompt });
    });
  }, session_get = function() {
    return this.userContext.browser.session;
  }, (() => {
    const _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;
    __esDecorate$4(_a2, null, _dispose_decorators, { kind: "method", name: "dispose", static: false, private: false, access: { has: (obj) => "dispose" in obj, get: (obj) => obj.dispose }, metadata: _metadata }, null, _instanceExtraInitializers);
    __esDecorate$4(_a2, null, _activate_decorators, { kind: "method", name: "activate", static: false, private: false, access: { has: (obj) => "activate" in obj, get: (obj) => obj.activate }, metadata: _metadata }, null, _instanceExtraInitializers);
    __esDecorate$4(_a2, null, _captureScreenshot_decorators, { kind: "method", name: "captureScreenshot", static: false, private: false, access: { has: (obj) => "captureScreenshot" in obj, get: (obj) => obj.captureScreenshot }, metadata: _metadata }, null, _instanceExtraInitializers);
    __esDecorate$4(_a2, null, _close_decorators, { kind: "method", name: "close", static: false, private: false, access: { has: (obj) => "close" in obj, get: (obj) => obj.close }, metadata: _metadata }, null, _instanceExtraInitializers);
    __esDecorate$4(_a2, null, _traverseHistory_decorators, { kind: "method", name: "traverseHistory", static: false, private: false, access: { has: (obj) => "traverseHistory" in obj, get: (obj) => obj.traverseHistory }, metadata: _metadata }, null, _instanceExtraInitializers);
    __esDecorate$4(_a2, null, _navigate_decorators, { kind: "method", name: "navigate", static: false, private: false, access: { has: (obj) => "navigate" in obj, get: (obj) => obj.navigate }, metadata: _metadata }, null, _instanceExtraInitializers);
    __esDecorate$4(_a2, null, _reload_decorators, { kind: "method", name: "reload", static: false, private: false, access: { has: (obj) => "reload" in obj, get: (obj) => obj.reload }, metadata: _metadata }, null, _instanceExtraInitializers);
    __esDecorate$4(_a2, null, _print_decorators, { kind: "method", name: "print", static: false, private: false, access: { has: (obj) => "print" in obj, get: (obj) => obj.print }, metadata: _metadata }, null, _instanceExtraInitializers);
    __esDecorate$4(_a2, null, _handleUserPrompt_decorators, { kind: "method", name: "handleUserPrompt", static: false, private: false, access: { has: (obj) => "handleUserPrompt" in obj, get: (obj) => obj.handleUserPrompt }, metadata: _metadata }, null, _instanceExtraInitializers);
    __esDecorate$4(_a2, null, _setViewport_decorators, { kind: "method", name: "setViewport", static: false, private: false, access: { has: (obj) => "setViewport" in obj, get: (obj) => obj.setViewport }, metadata: _metadata }, null, _instanceExtraInitializers);
    __esDecorate$4(_a2, null, _performActions_decorators, { kind: "method", name: "performActions", static: false, private: false, access: { has: (obj) => "performActions" in obj, get: (obj) => obj.performActions }, metadata: _metadata }, null, _instanceExtraInitializers);
    __esDecorate$4(_a2, null, _releaseActions_decorators, { kind: "method", name: "releaseActions", static: false, private: false, access: { has: (obj) => "releaseActions" in obj, get: (obj) => obj.releaseActions }, metadata: _metadata }, null, _instanceExtraInitializers);
    __esDecorate$4(_a2, null, _createWindowRealm_decorators, { kind: "method", name: "createWindowRealm", static: false, private: false, access: { has: (obj) => "createWindowRealm" in obj, get: (obj) => obj.createWindowRealm }, metadata: _metadata }, null, _instanceExtraInitializers);
    __esDecorate$4(_a2, null, _addPreloadScript_decorators, { kind: "method", name: "addPreloadScript", static: false, private: false, access: { has: (obj) => "addPreloadScript" in obj, get: (obj) => obj.addPreloadScript }, metadata: _metadata }, null, _instanceExtraInitializers);
    __esDecorate$4(_a2, null, _removePreloadScript_decorators, { kind: "method", name: "removePreloadScript", static: false, private: false, access: { has: (obj) => "removePreloadScript" in obj, get: (obj) => obj.removePreloadScript }, metadata: _metadata }, null, _instanceExtraInitializers);
    if (_metadata) Object.defineProperty(_a2, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
  })(), _a2;
})();
/**
 * @license
 * Copyright 2024 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
var __runInitializers$3 = function(thisArg, initializers, value) {
  var useValue = arguments.length > 2;
  for (var i = 0; i < initializers.length; i++) {
    value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
  }
  return useValue ? value : void 0;
};
var __esDecorate$3 = function(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
  function accept(f) {
    if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected");
    return f;
  }
  var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
  var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
  var _, done = false;
  for (var i = decorators.length - 1; i >= 0; i--) {
    var context = {};
    for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
    for (var p in contextIn.access) context.access[p] = contextIn.access[p];
    context.addInitializer = function(f) {
      if (done) throw new TypeError("Cannot add initializers after decoration has completed");
      extraInitializers.push(accept(f || null));
    };
    var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
    if (kind === "accessor") {
      if (result === void 0) continue;
      if (result === null || typeof result !== "object") throw new TypeError("Object expected");
      if (_ = accept(result.get)) descriptor.get = _;
      if (_ = accept(result.set)) descriptor.set = _;
      if (_ = accept(result.init)) initializers.unshift(_);
    } else if (_ = accept(result)) {
      if (kind === "field") initializers.unshift(_);
      else descriptor[key] = _;
    }
  }
  if (target) Object.defineProperty(target, contextIn.name, descriptor);
  done = true;
};
let UserContext = (() => {
  var _a2, _reason, _browsingContexts2, _disposables, _id3, _UserContext_instances, initialize_fn2, session_get;
  let _classSuper = EventEmitter;
  let _instanceExtraInitializers = [];
  let _dispose_decorators;
  let _createBrowsingContext_decorators;
  let _remove_decorators;
  return _a2 = class extends _classSuper {
    // keep-sorted end
    constructor(browser, id) {
      super();
      __privateAdd(this, _UserContext_instances);
      // keep-sorted start
      __privateAdd(this, _reason, (__runInitializers$3(this, _instanceExtraInitializers), void 0));
      // Note these are only top-level contexts.
      __privateAdd(this, _browsingContexts2, /* @__PURE__ */ new Map());
      __privateAdd(this, _disposables, new DisposableStack());
      __privateAdd(this, _id3);
      __publicField(this, "browser");
      __privateSet(this, _id3, id);
      this.browser = browser;
    }
    static create(browser, id) {
      var _a3;
      const context = new _a2(browser, id);
      __privateMethod(_a3 = context, _UserContext_instances, initialize_fn2).call(_a3);
      return context;
    }
    get browsingContexts() {
      return __privateGet(this, _browsingContexts2).values();
    }
    get closed() {
      return __privateGet(this, _reason) !== void 0;
    }
    get disposed() {
      return this.closed;
    }
    get id() {
      return __privateGet(this, _id3);
    }
    // keep-sorted end
    dispose(reason) {
      __privateSet(this, _reason, reason);
      this[disposeSymbol]();
    }
    async createBrowsingContext(type, options = {}) {
      var _a3;
      const { result: { context: contextId } } = await __privateGet(this, _UserContext_instances, session_get).send("browsingContext.create", {
        type,
        ...options,
        referenceContext: (_a3 = options.referenceContext) == null ? void 0 : _a3.id,
        userContext: __privateGet(this, _id3)
      });
      const browsingContext = __privateGet(this, _browsingContexts2).get(contextId);
      assert(browsingContext, "The WebDriver BiDi implementation is failing to create a browsing context correctly.");
      return browsingContext;
    }
    async remove() {
      try {
        await __privateGet(this, _UserContext_instances, session_get).send("browser.removeUserContext", {
          userContext: __privateGet(this, _id3)
        });
      } finally {
        this.dispose("User context already closed.");
      }
    }
    [(_dispose_decorators = [inertIfDisposed], _createBrowsingContext_decorators = [throwIfDisposed((context) => {
      return __privateGet(context, _reason);
    })], _remove_decorators = [throwIfDisposed((context) => {
      return __privateGet(context, _reason);
    })], disposeSymbol)]() {
      __privateGet(this, _reason) ?? __privateSet(this, _reason, "User context already closed, probably because the browser disconnected/closed.");
      this.emit("closed", { reason: __privateGet(this, _reason) });
      __privateGet(this, _disposables).dispose();
      super[disposeSymbol]();
    }
  }, _reason = new WeakMap(), _browsingContexts2 = new WeakMap(), _disposables = new WeakMap(), _id3 = new WeakMap(), _UserContext_instances = new WeakSet(), initialize_fn2 = function() {
    const browserEmitter = __privateGet(this, _disposables).use(new EventEmitter(this.browser));
    browserEmitter.once("closed", ({ reason }) => {
      this.dispose(`User context already closed: ${reason}`);
    });
    const sessionEmitter = __privateGet(this, _disposables).use(new EventEmitter(__privateGet(this, _UserContext_instances, session_get)));
    sessionEmitter.on("browsingContext.contextCreated", (info) => {
      if (info.parent) {
        return;
      }
      if (info.userContext !== __privateGet(this, _id3)) {
        return;
      }
      const browsingContext = BrowsingContext.from(this, void 0, info.context, info.url);
      __privateGet(this, _browsingContexts2).set(browsingContext.id, browsingContext);
      const browsingContextEmitter = __privateGet(this, _disposables).use(new EventEmitter(browsingContext));
      browsingContextEmitter.on("closed", () => {
        browsingContextEmitter.removeAllListeners();
        __privateGet(this, _browsingContexts2).delete(browsingContext.id);
      });
      this.emit("browsingcontext", { browsingContext });
    });
  }, session_get = function() {
    return this.browser.session;
  }, (() => {
    const _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;
    __esDecorate$3(_a2, null, _dispose_decorators, { kind: "method", name: "dispose", static: false, private: false, access: { has: (obj) => "dispose" in obj, get: (obj) => obj.dispose }, metadata: _metadata }, null, _instanceExtraInitializers);
    __esDecorate$3(_a2, null, _createBrowsingContext_decorators, { kind: "method", name: "createBrowsingContext", static: false, private: false, access: { has: (obj) => "createBrowsingContext" in obj, get: (obj) => obj.createBrowsingContext }, metadata: _metadata }, null, _instanceExtraInitializers);
    __esDecorate$3(_a2, null, _remove_decorators, { kind: "method", name: "remove", static: false, private: false, access: { has: (obj) => "remove" in obj, get: (obj) => obj.remove }, metadata: _metadata }, null, _instanceExtraInitializers);
    if (_metadata) Object.defineProperty(_a2, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
  })(), __publicField(_a2, "DEFAULT", "default"), _a2;
})();
/**
 * @license
 * Copyright 2022 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
class BidiBrowserContext extends BrowserContext {
  constructor(browser, userContext, options) {
    super();
    __privateAdd(this, _browser);
    __privateAdd(this, _connection);
    __privateAdd(this, _defaultViewport);
    __privateAdd(this, _userContext);
    __privateSet(this, _browser, browser);
    __privateSet(this, _userContext, userContext);
    __privateSet(this, _connection, __privateGet(this, _browser).connection);
    __privateSet(this, _defaultViewport, options.defaultViewport);
  }
  targets() {
    return __privateGet(this, _browser).targets().filter((target) => {
      return target.browserContext() === this;
    });
  }
  waitForTarget(predicate, options = {}) {
    return __privateGet(this, _browser).waitForTarget((target) => {
      return target.browserContext() === this && predicate(target);
    }, options);
  }
  get connection() {
    return __privateGet(this, _connection);
  }
  async newPage() {
    const { result } = await __privateGet(this, _connection).send("browsingContext.create", {
      type: "tab",
      userContext: __privateGet(this, _userContext).id
    });
    const target = __privateGet(this, _browser)._getTargetById(result.context);
    target._setBrowserContext(this);
    const page = await target.page();
    if (!page) {
      throw new Error("Page is not found");
    }
    if (__privateGet(this, _defaultViewport)) {
      try {
        await page.setViewport(__privateGet(this, _defaultViewport));
      } catch {
      }
    }
    return page;
  }
  async close() {
    if (!this.isIncognito()) {
      throw new Error("Default context cannot be closed!");
    }
    try {
      await __privateGet(this, _userContext).remove();
    } catch (error) {
      debugError(error);
    }
  }
  browser() {
    return __privateGet(this, _browser);
  }
  async pages() {
    const results = await Promise.all([...this.targets()].map((t) => {
      return t.page();
    }));
    return results.filter((p) => {
      return p !== null;
    });
  }
  isIncognito() {
    return __privateGet(this, _userContext).id !== UserContext.DEFAULT;
  }
  overridePermissions() {
    throw new UnsupportedOperation();
  }
  clearPermissionOverrides() {
    throw new UnsupportedOperation();
  }
  get id() {
    if (__privateGet(this, _userContext).id === "default") {
      return void 0;
    }
    return __privateGet(this, _userContext).id;
  }
}
_browser = new WeakMap();
_connection = new WeakMap();
_defaultViewport = new WeakMap();
_userContext = new WeakMap();
/**
 * @license
 * Copyright 2024 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
var __runInitializers$2 = function(thisArg, initializers, value) {
  var useValue = arguments.length > 2;
  for (var i = 0; i < initializers.length; i++) {
    value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
  }
  return useValue ? value : void 0;
};
var __esDecorate$2 = function(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
  function accept(f) {
    if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected");
    return f;
  }
  var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
  var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
  var _, done = false;
  for (var i = decorators.length - 1; i >= 0; i--) {
    var context = {};
    for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
    for (var p in contextIn.access) context.access[p] = contextIn.access[p];
    context.addInitializer = function(f) {
      if (done) throw new TypeError("Cannot add initializers after decoration has completed");
      extraInitializers.push(accept(f || null));
    };
    var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
    if (kind === "accessor") {
      if (result === void 0) continue;
      if (result === null || typeof result !== "object") throw new TypeError("Object expected");
      if (_ = accept(result.get)) descriptor.get = _;
      if (_ = accept(result.set)) descriptor.set = _;
      if (_ = accept(result.init)) initializers.unshift(_);
    } else if (_ = accept(result)) {
      if (kind === "field") initializers.unshift(_);
      else descriptor[key] = _;
    }
  }
  if (target) Object.defineProperty(target, contextIn.name, descriptor);
  done = true;
};
var __addDisposableResource$1 = function(env, value, async) {
  if (value !== null && value !== void 0) {
    if (typeof value !== "object" && typeof value !== "function") throw new TypeError("Object expected.");
    var dispose;
    if (async) {
      if (!Symbol.asyncDispose) throw new TypeError("Symbol.asyncDispose is not defined.");
      dispose = value[Symbol.asyncDispose];
    }
    if (dispose === void 0) {
      if (!Symbol.dispose) throw new TypeError("Symbol.dispose is not defined.");
      dispose = value[Symbol.dispose];
    }
    if (typeof dispose !== "function") throw new TypeError("Object not disposable.");
    env.stack.push({ value, dispose, async });
  } else if (async) {
    env.stack.push({ async: true });
  }
  return value;
};
var __disposeResources$1 = /* @__PURE__ */ function(SuppressedError2) {
  return function(env) {
    function fail(e) {
      env.error = env.hasError ? new SuppressedError2(e, env.error, "An error was suppressed during disposal.") : e;
      env.hasError = true;
    }
    function next() {
      while (env.stack.length) {
        var rec = env.stack.pop();
        try {
          var result = rec.dispose && rec.dispose.call(rec.value);
          if (rec.async) return Promise.resolve(result).then(next, function(e) {
            fail(e);
            return next();
          });
        } catch (e) {
          fail(e);
        }
      }
      if (env.hasError) throw env.error;
    }
    return next();
  };
}(typeof SuppressedError === "function" ? SuppressedError : function(error, suppressed, message) {
  var e = new Error(message);
  return e.name = "SuppressedError", e.error = error, e.suppressed = suppressed, e;
});
let Browser = (() => {
  var _closed3, _reason, _disposables, _userContexts, _Browser_instances, initialize_fn2, syncUserContexts_fn, syncBrowsingContexts_fn, _a2;
  let _classSuper = EventEmitter;
  let _instanceExtraInitializers = [];
  let _dispose_decorators;
  let _close_decorators;
  let _addPreloadScript_decorators;
  let _removePreloadScript_decorators;
  let _createUserContext_decorators;
  return _a2 = class extends _classSuper {
    // keep-sorted end
    constructor(session) {
      super();
      __privateAdd(this, _Browser_instances);
      // keep-sorted start
      __privateAdd(this, _closed3, (__runInitializers$2(this, _instanceExtraInitializers), false));
      __privateAdd(this, _reason);
      __privateAdd(this, _disposables, new DisposableStack());
      __privateAdd(this, _userContexts, /* @__PURE__ */ new Map());
      __publicField(this, "session");
      this.session = session;
      __privateGet(this, _userContexts).set(UserContext.DEFAULT, UserContext.create(this, UserContext.DEFAULT));
    }
    static async from(session) {
      var _a3;
      const browser = new _a2(session);
      await __privateMethod(_a3 = browser, _Browser_instances, initialize_fn2).call(_a3);
      return browser;
    }
    // keep-sorted start block=yes
    get closed() {
      return __privateGet(this, _closed3);
    }
    get defaultUserContext() {
      return __privateGet(this, _userContexts).get(UserContext.DEFAULT);
    }
    get disconnected() {
      return __privateGet(this, _reason) !== void 0;
    }
    get disposed() {
      return this.disconnected;
    }
    get userContexts() {
      return __privateGet(this, _userContexts).values();
    }
    // keep-sorted end
    dispose(reason, closed = false) {
      __privateSet(this, _closed3, closed);
      __privateSet(this, _reason, reason);
      this[disposeSymbol]();
    }
    async close() {
      try {
        await this.session.send("browser.close", {});
      } finally {
        this.dispose("Browser already closed.", true);
      }
    }
    async addPreloadScript(functionDeclaration, options = {}) {
      var _a3;
      const { result: { script } } = await this.session.send("script.addPreloadScript", {
        functionDeclaration,
        ...options,
        contexts: (_a3 = options.contexts) == null ? void 0 : _a3.map((context) => {
          return context.id;
        })
      });
      return script;
    }
    async removePreloadScript(script) {
      await this.session.send("script.removePreloadScript", {
        script
      });
    }
    async createUserContext() {
      const { result: { userContext: context } } = await this.session.send("browser.createUserContext", {});
      const userContext = UserContext.create(this, context);
      __privateGet(this, _userContexts).set(userContext.id, userContext);
      const userContextEmitter = __privateGet(this, _disposables).use(new EventEmitter(userContext));
      userContextEmitter.once("closed", () => {
        userContextEmitter.removeAllListeners();
        __privateGet(this, _userContexts).delete(context);
      });
      return userContext;
    }
    [(_dispose_decorators = [inertIfDisposed], _close_decorators = [throwIfDisposed((browser) => {
      return __privateGet(browser, _reason);
    })], _addPreloadScript_decorators = [throwIfDisposed((browser) => {
      return __privateGet(browser, _reason);
    })], _removePreloadScript_decorators = [throwIfDisposed((browser) => {
      return __privateGet(browser, _reason);
    })], _createUserContext_decorators = [throwIfDisposed((browser) => {
      return __privateGet(browser, _reason);
    })], disposeSymbol)]() {
      __privateGet(this, _reason) ?? __privateSet(this, _reason, "Browser was disconnected, probably because the session ended.");
      if (this.closed) {
        this.emit("closed", { reason: __privateGet(this, _reason) });
      }
      this.emit("disconnected", { reason: __privateGet(this, _reason) });
      __privateGet(this, _disposables).dispose();
      super[disposeSymbol]();
    }
  }, _closed3 = new WeakMap(), _reason = new WeakMap(), _disposables = new WeakMap(), _userContexts = new WeakMap(), _Browser_instances = new WeakSet(), initialize_fn2 = async function() {
    const sessionEmitter = __privateGet(this, _disposables).use(new EventEmitter(this.session));
    sessionEmitter.once("ended", ({ reason }) => {
      this.dispose(reason);
    });
    sessionEmitter.on("script.realmCreated", (info) => {
      if (info.type === "shared-worker") ;
    });
    await __privateMethod(this, _Browser_instances, syncUserContexts_fn).call(this);
    await __privateMethod(this, _Browser_instances, syncBrowsingContexts_fn).call(this);
  }, syncUserContexts_fn = async function() {
    const { result: { userContexts } } = await this.session.send("browser.getUserContexts", {});
    for (const context of userContexts) {
      if (context.userContext === UserContext.DEFAULT) {
        continue;
      }
      __privateGet(this, _userContexts).set(context.userContext, UserContext.create(this, context.userContext));
    }
  }, syncBrowsingContexts_fn = async function() {
    const contextIds = /* @__PURE__ */ new Set();
    let contexts;
    {
      const env_1 = { stack: [], error: void 0, hasError: false };
      try {
        const sessionEmitter = __addDisposableResource$1(env_1, new EventEmitter(this.session), false);
        sessionEmitter.on("browsingContext.contextCreated", (info) => {
          contextIds.add(info.context);
        });
        sessionEmitter.on("browsingContext.contextDestroyed", (info) => {
          contextIds.delete(info.context);
        });
        const { result } = await this.session.send("browsingContext.getTree", {});
        contexts = result.contexts;
      } catch (e_1) {
        env_1.error = e_1;
        env_1.hasError = true;
      } finally {
        __disposeResources$1(env_1);
      }
    }
    for (const info of contexts) {
      if (contextIds.has(info.context)) {
        this.session.emit("browsingContext.contextCreated", info);
      }
      if (info.children) {
        contexts.push(...info.children);
      }
    }
  }, (() => {
    const _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;
    __esDecorate$2(_a2, null, _dispose_decorators, { kind: "method", name: "dispose", static: false, private: false, access: { has: (obj) => "dispose" in obj, get: (obj) => obj.dispose }, metadata: _metadata }, null, _instanceExtraInitializers);
    __esDecorate$2(_a2, null, _close_decorators, { kind: "method", name: "close", static: false, private: false, access: { has: (obj) => "close" in obj, get: (obj) => obj.close }, metadata: _metadata }, null, _instanceExtraInitializers);
    __esDecorate$2(_a2, null, _addPreloadScript_decorators, { kind: "method", name: "addPreloadScript", static: false, private: false, access: { has: (obj) => "addPreloadScript" in obj, get: (obj) => obj.addPreloadScript }, metadata: _metadata }, null, _instanceExtraInitializers);
    __esDecorate$2(_a2, null, _removePreloadScript_decorators, { kind: "method", name: "removePreloadScript", static: false, private: false, access: { has: (obj) => "removePreloadScript" in obj, get: (obj) => obj.removePreloadScript }, metadata: _metadata }, null, _instanceExtraInitializers);
    __esDecorate$2(_a2, null, _createUserContext_decorators, { kind: "method", name: "createUserContext", static: false, private: false, access: { has: (obj) => "createUserContext" in obj, get: (obj) => obj.createUserContext }, metadata: _metadata }, null, _instanceExtraInitializers);
    if (_metadata) Object.defineProperty(_a2, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
  })(), _a2;
})();
/**
 * @license
 * Copyright 2024 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
var __runInitializers$1 = function(thisArg, initializers, value) {
  var useValue = arguments.length > 2;
  for (var i = 0; i < initializers.length; i++) {
    value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
  }
  return useValue ? value : void 0;
};
var __esDecorate$1 = function(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
  function accept(f) {
    if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected");
    return f;
  }
  var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
  var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
  var _, done = false;
  for (var i = decorators.length - 1; i >= 0; i--) {
    var context = {};
    for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
    for (var p in contextIn.access) context.access[p] = contextIn.access[p];
    context.addInitializer = function(f) {
      if (done) throw new TypeError("Cannot add initializers after decoration has completed");
      extraInitializers.push(accept(f || null));
    };
    var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
    if (kind === "accessor") {
      if (result === void 0) continue;
      if (result === null || typeof result !== "object") throw new TypeError("Object expected");
      if (_ = accept(result.get)) descriptor.get = _;
      if (_ = accept(result.set)) descriptor.set = _;
      if (_ = accept(result.init)) initializers.unshift(_);
    } else if (_ = accept(result)) {
      if (kind === "field") initializers.unshift(_);
      else descriptor[key] = _;
    }
  }
  if (target) Object.defineProperty(target, contextIn.name, descriptor);
  done = true;
};
let Session = (() => {
  var _reason, _disposables, _info, _Session_instances, initialize_fn2, _a2;
  let _classSuper = EventEmitter;
  let _instanceExtraInitializers = [];
  let _dispose_decorators;
  let _send_decorators;
  let _subscribe_decorators;
  let _end_decorators;
  return _a2 = class extends _classSuper {
    // keep-sorted end
    constructor(connection, info) {
      super();
      __privateAdd(this, _Session_instances);
      // keep-sorted start
      __privateAdd(this, _reason, (__runInitializers$1(this, _instanceExtraInitializers), void 0));
      __privateAdd(this, _disposables, new DisposableStack());
      __privateAdd(this, _info);
      __publicField(this, "browser");
      __publicField(this, "connection");
      __privateSet(this, _info, info);
      this.connection = connection;
    }
    static async from(connection, capabilities) {
      var _a3;
      let result;
      try {
        result = (await connection.send("session.new", {
          capabilities
        })).result;
      } catch (err) {
        debugError(err);
        result = {
          sessionId: "",
          capabilities: {
            acceptInsecureCerts: false,
            browserName: "",
            browserVersion: "",
            platformName: "",
            setWindowRect: false,
            webSocketUrl: ""
          }
        };
      }
      const session = new _a2(connection, result);
      await __privateMethod(_a3 = session, _Session_instances, initialize_fn2).call(_a3);
      return session;
    }
    // keep-sorted start block=yes
    get capabilities() {
      return __privateGet(this, _info).capabilities;
    }
    get disposed() {
      return this.ended;
    }
    get ended() {
      return __privateGet(this, _reason) !== void 0;
    }
    get id() {
      return __privateGet(this, _info).sessionId;
    }
    // keep-sorted end
    dispose(reason) {
      __privateSet(this, _reason, reason);
      this[disposeSymbol]();
    }
    pipeTo(emitter) {
      this.connection.pipeTo(emitter);
    }
    /**
     * Currently, there is a 1:1 relationship between the session and the
     * session. In the future, we might support multiple sessions and in that
     * case we always needs to make sure that the session for the right session
     * object is used, so we implement this method here, although it's not defined
     * in the spec.
     */
    async send(method, params) {
      return await this.connection.send(method, params);
    }
    async subscribe(events) {
      await this.send("session.subscribe", {
        events
      });
    }
    async end() {
      try {
        await this.send("session.end", {});
      } finally {
        this.dispose(`Session already ended.`);
      }
    }
    [(_dispose_decorators = [inertIfDisposed], _send_decorators = [throwIfDisposed((session) => {
      return __privateGet(session, _reason);
    })], _subscribe_decorators = [throwIfDisposed((session) => {
      return __privateGet(session, _reason);
    })], _end_decorators = [throwIfDisposed((session) => {
      return __privateGet(session, _reason);
    })], disposeSymbol)]() {
      __privateGet(this, _reason) ?? __privateSet(this, _reason, "Session already destroyed, probably because the connection broke.");
      this.emit("ended", { reason: __privateGet(this, _reason) });
      __privateGet(this, _disposables).dispose();
      super[disposeSymbol]();
    }
  }, _reason = new WeakMap(), _disposables = new WeakMap(), _info = new WeakMap(), _Session_instances = new WeakSet(), initialize_fn2 = async function() {
    this.connection.pipeTo(this);
    this.browser = await Browser.from(this);
    const browserEmitter = __privateGet(this, _disposables).use(this.browser);
    browserEmitter.once("closed", ({ reason }) => {
      this.dispose(reason);
    });
  }, (() => {
    const _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;
    __esDecorate$1(_a2, null, _dispose_decorators, { kind: "method", name: "dispose", static: false, private: false, access: { has: (obj) => "dispose" in obj, get: (obj) => obj.dispose }, metadata: _metadata }, null, _instanceExtraInitializers);
    __esDecorate$1(_a2, null, _send_decorators, { kind: "method", name: "send", static: false, private: false, access: { has: (obj) => "send" in obj, get: (obj) => obj.send }, metadata: _metadata }, null, _instanceExtraInitializers);
    __esDecorate$1(_a2, null, _subscribe_decorators, { kind: "method", name: "subscribe", static: false, private: false, access: { has: (obj) => "subscribe" in obj, get: (obj) => obj.subscribe }, metadata: _metadata }, null, _instanceExtraInitializers);
    __esDecorate$1(_a2, null, _end_decorators, { kind: "method", name: "end", static: false, private: false, access: { has: (obj) => "end" in obj, get: (obj) => obj.end }, metadata: _metadata }, null, _instanceExtraInitializers);
    if (_metadata) Object.defineProperty(_a2, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
  })(), _a2;
})();
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
class BidiDialog extends Dialog {
  /**
   * @internal
   */
  constructor(context, type, message, defaultValue) {
    super(type, message, defaultValue);
    __privateAdd(this, _context2);
    __privateSet(this, _context2, context);
  }
  /**
   * @internal
   */
  async handle(options) {
    await __privateGet(this, _context2).connection.send("browsingContext.handleUserPrompt", {
      context: __privateGet(this, _context2).id,
      accept: options.accept,
      userText: options.text
    });
  }
}
_context2 = new WeakMap();
class EmulationManager {
  constructor(browsingContext) {
    __privateAdd(this, _browsingContext);
    __privateSet(this, _browsingContext, browsingContext);
  }
  async emulateViewport(viewport) {
    await __privateGet(this, _browsingContext).connection.send("browsingContext.setViewport", {
      context: __privateGet(this, _browsingContext).id,
      viewport: viewport.width && viewport.height ? {
        width: viewport.width,
        height: viewport.height
      } : null,
      devicePixelRatio: viewport.deviceScaleFactor ? viewport.deviceScaleFactor : null
    });
  }
}
_browsingContext = new WeakMap();
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
class ExposeableFunction {
  constructor(frame, name, apply) {
    __privateAdd(this, _ExposeableFunction_instances);
    __privateAdd(this, _frame);
    __publicField(this, "name");
    __privateAdd(this, _apply);
    __privateAdd(this, _channels);
    __privateAdd(this, _callerInfos, /* @__PURE__ */ new Map());
    __privateAdd(this, _preloadScriptId);
    __privateAdd(this, _handleArgumentsMessage, async (params) => {
      var _a2;
      if (params.channel !== __privateGet(this, _channels).args) {
        return;
      }
      const connection = __privateGet(this, _ExposeableFunction_instances, connection_get);
      const { callbacks, remoteValue } = __privateMethod(this, _ExposeableFunction_instances, getCallbacksAndRemoteValue_fn).call(this, params);
      const args = (_a2 = remoteValue.value) == null ? void 0 : _a2[1];
      assert(args);
      try {
        const result = await __privateGet(this, _apply).call(this, ...BidiDeserializer.deserialize(args));
        await connection.send("script.callFunction", {
          functionDeclaration: stringifyFunction(([_, resolve], result2) => {
            resolve(result2);
          }),
          arguments: [
            await callbacks.resolve.valueOrThrow(),
            BidiSerializer.serializeRemoteValue(result)
          ],
          awaitPromise: false,
          target: {
            realm: params.source.realm
          }
        });
      } catch (error) {
        try {
          if (error instanceof Error) {
            await connection.send("script.callFunction", {
              functionDeclaration: stringifyFunction(([_, reject], name, message, stack) => {
                const error2 = new Error(message);
                error2.name = name;
                if (stack) {
                  error2.stack = stack;
                }
                reject(error2);
              }),
              arguments: [
                await callbacks.reject.valueOrThrow(),
                BidiSerializer.serializeRemoteValue(error.name),
                BidiSerializer.serializeRemoteValue(error.message),
                BidiSerializer.serializeRemoteValue(error.stack)
              ],
              awaitPromise: false,
              target: {
                realm: params.source.realm
              }
            });
          } else {
            await connection.send("script.callFunction", {
              functionDeclaration: stringifyFunction(([_, reject], error2) => {
                reject(error2);
              }),
              arguments: [
                await callbacks.reject.valueOrThrow(),
                BidiSerializer.serializeRemoteValue(error)
              ],
              awaitPromise: false,
              target: {
                realm: params.source.realm
              }
            });
          }
        } catch (error2) {
          debugError(error2);
        }
      }
    });
    __privateAdd(this, _handleResolveMessage, (params) => {
      if (params.channel !== __privateGet(this, _channels).resolve) {
        return;
      }
      const { callbacks, remoteValue } = __privateMethod(this, _ExposeableFunction_instances, getCallbacksAndRemoteValue_fn).call(this, params);
      callbacks.resolve.resolve(remoteValue);
    });
    __privateAdd(this, _handleRejectMessage, (params) => {
      if (params.channel !== __privateGet(this, _channels).reject) {
        return;
      }
      const { callbacks, remoteValue } = __privateMethod(this, _ExposeableFunction_instances, getCallbacksAndRemoteValue_fn).call(this, params);
      callbacks.reject.resolve(remoteValue);
    });
    __privateSet(this, _frame, frame);
    this.name = name;
    __privateSet(this, _apply, apply);
    __privateSet(this, _channels, {
      args: `__puppeteer__${__privateGet(this, _frame)._id}_page_exposeFunction_${this.name}_args`,
      resolve: `__puppeteer__${__privateGet(this, _frame)._id}_page_exposeFunction_${this.name}_resolve`,
      reject: `__puppeteer__${__privateGet(this, _frame)._id}_page_exposeFunction_${this.name}_reject`
    });
  }
  async expose() {
    const connection = __privateGet(this, _ExposeableFunction_instances, connection_get);
    const channelArguments = __privateGet(this, _ExposeableFunction_instances, channelArguments_get);
    connection.on(protocol.ChromiumBidi.Script.EventNames.Message, __privateGet(this, _handleArgumentsMessage));
    connection.on(protocol.ChromiumBidi.Script.EventNames.Message, __privateGet(this, _handleResolveMessage));
    connection.on(protocol.ChromiumBidi.Script.EventNames.Message, __privateGet(this, _handleRejectMessage));
    const functionDeclaration = stringifyFunction(interpolateFunction((sendArgs, sendResolve, sendReject) => {
      let id = 0;
      Object.assign(globalThis, {
        [PLACEHOLDER("name")]: function(...args) {
          return new Promise((resolve, reject) => {
            sendArgs([id, args]);
            sendResolve([id, resolve]);
            sendReject([id, reject]);
            ++id;
          });
        }
      });
    }, { name: JSON.stringify(this.name) }));
    const { result } = await connection.send("script.addPreloadScript", {
      functionDeclaration,
      arguments: channelArguments,
      contexts: [__privateGet(this, _frame).page().mainFrame()._id]
    });
    __privateSet(this, _preloadScriptId, result.script);
    await Promise.all(__privateGet(this, _frame).page().frames().map(async (frame) => {
      return await connection.send("script.callFunction", {
        functionDeclaration,
        arguments: channelArguments,
        awaitPromise: false,
        target: frame.mainRealm().realm.target
      });
    }));
  }
  [Symbol.dispose]() {
    void this[Symbol.asyncDispose]().catch(debugError);
  }
  async [Symbol.asyncDispose]() {
    if (__privateGet(this, _preloadScriptId)) {
      await __privateGet(this, _ExposeableFunction_instances, connection_get).send("script.removePreloadScript", {
        script: __privateGet(this, _preloadScriptId)
      });
    }
  }
}
_frame = new WeakMap();
_apply = new WeakMap();
_channels = new WeakMap();
_callerInfos = new WeakMap();
_preloadScriptId = new WeakMap();
_handleArgumentsMessage = new WeakMap();
_ExposeableFunction_instances = new WeakSet();
connection_get = function() {
  return __privateGet(this, _frame).context().connection;
};
channelArguments_get = function() {
  return [
    {
      type: "channel",
      value: {
        channel: __privateGet(this, _channels).args,
        ownership: "root"
      }
    },
    {
      type: "channel",
      value: {
        channel: __privateGet(this, _channels).resolve,
        ownership: "root"
      }
    },
    {
      type: "channel",
      value: {
        channel: __privateGet(this, _channels).reject,
        ownership: "root"
      }
    }
  ];
};
_handleResolveMessage = new WeakMap();
_handleRejectMessage = new WeakMap();
getCallbacksAndRemoteValue_fn = function(params) {
  const { data, source } = params;
  assert(data.type === "array");
  assert(data.value);
  const callerIdRemote = data.value[0];
  assert(callerIdRemote);
  assert(callerIdRemote.type === "number");
  assert(typeof callerIdRemote.value === "number");
  let bindingMap = __privateGet(this, _callerInfos).get(source.realm);
  if (!bindingMap) {
    bindingMap = /* @__PURE__ */ new Map();
    __privateGet(this, _callerInfos).set(source.realm, bindingMap);
  }
  const callerId = callerIdRemote.value;
  let callbacks = bindingMap.get(callerId);
  if (!callbacks) {
    callbacks = {
      resolve: new Deferred(),
      reject: new Deferred()
    };
    bindingMap.set(callerId, callbacks);
  }
  return { callbacks, remoteValue: data };
};
function getBiDiLifeCycles(event) {
  if (Array.isArray(event)) {
    const pageLifeCycle = event.some((lifeCycle) => {
      return lifeCycle !== "domcontentloaded";
    }) ? "load" : "domcontentloaded";
    const networkLifeCycle = event.reduce((acc, lifeCycle) => {
      if (lifeCycle === "networkidle0") {
        return lifeCycle;
      } else if (acc !== "networkidle0" && lifeCycle === "networkidle2") {
        return lifeCycle;
      }
      return acc;
    }, null);
    return [pageLifeCycle, networkLifeCycle];
  }
  if (event === "networkidle0" || event === "networkidle2") {
    return ["load", event];
  }
  return [event, null];
}
const lifeCycleToReadinessState = /* @__PURE__ */ new Map([
  [
    "load",
    "complete"
    /* Bidi.BrowsingContext.ReadinessState.Complete */
  ],
  [
    "domcontentloaded",
    "interactive"
    /* Bidi.BrowsingContext.ReadinessState.Interactive */
  ]
]);
function getBiDiReadinessState(event) {
  const lifeCycles = getBiDiLifeCycles(event);
  const readiness = lifeCycleToReadinessState.get(lifeCycles[0]);
  return [readiness, lifeCycles[1]];
}
const lifeCycleToSubscribedEvent = /* @__PURE__ */ new Map([
  ["load", "browsingContext.load"],
  ["domcontentloaded", "browsingContext.domContentLoaded"]
]);
function getBiDiLifecycleEvent(event) {
  const lifeCycles = getBiDiLifeCycles(event);
  const bidiEvent = lifeCycleToSubscribedEvent.get(lifeCycles[0]);
  return [bidiEvent, lifeCycles[1]];
}
function rewriteNavigationError(message, ms) {
  return Wr((error) => {
    if (error instanceof ProtocolError) {
      error.message += ` at ${message}`;
    } else if (error instanceof TimeoutError) {
      error.message = `Navigation timeout of ${ms} ms exceeded`;
    }
    throw error;
  });
}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
const MAIN_SANDBOX = Symbol("mainSandbox");
const PUPPETEER_SANDBOX = Symbol("puppeteerSandbox");
class Sandbox extends Realm$1 {
  constructor(name, frame, realm, timeoutSettings) {
    super(timeoutSettings);
    __publicField(this, "name");
    __publicField(this, "realm");
    __privateAdd(this, _frame2);
    this.name = name;
    this.realm = realm;
    __privateSet(this, _frame2, frame);
    this.realm.setSandbox(this);
  }
  get environment() {
    return __privateGet(this, _frame2);
  }
  async evaluateHandle(pageFunction, ...args) {
    pageFunction = withSourcePuppeteerURLIfNone(this.evaluateHandle.name, pageFunction);
    return await this.realm.evaluateHandle(pageFunction, ...args);
  }
  async evaluate(pageFunction, ...args) {
    pageFunction = withSourcePuppeteerURLIfNone(this.evaluate.name, pageFunction);
    return await this.realm.evaluate(pageFunction, ...args);
  }
  async adoptHandle(handle) {
    return await this.evaluateHandle((node) => {
      return node;
    }, handle);
  }
  async transferHandle(handle) {
    if (handle.realm === this) {
      return handle;
    }
    const transferredHandle = await this.evaluateHandle((node) => {
      return node;
    }, handle);
    await handle.dispose();
    return transferredHandle;
  }
  async adoptBackendNode(backendNodeId) {
    const { object } = await this.environment.client.send("DOM.resolveNode", {
      backendNodeId
    });
    return new BidiElementHandle(this, {
      handle: object.objectId,
      type: "node"
    });
  }
}
_frame2 = new WeakMap();
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
var __runInitializers = function(thisArg, initializers, value) {
  var useValue = arguments.length > 2;
  for (var i = 0; i < initializers.length; i++) {
    value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
  }
  return useValue ? value : void 0;
};
var __esDecorate = function(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
  function accept(f) {
    if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected");
    return f;
  }
  var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
  var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
  var _, done = false;
  for (var i = decorators.length - 1; i >= 0; i--) {
    var context = {};
    for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
    for (var p in contextIn.access) context.access[p] = contextIn.access[p];
    context.addInitializer = function(f) {
      if (done) throw new TypeError("Cannot add initializers after decoration has completed");
      extraInitializers.push(accept(f || null));
    };
    var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
    if (kind === "accessor") {
      if (result === void 0) continue;
      if (result === null || typeof result !== "object") throw new TypeError("Object expected");
      if (_ = accept(result.get)) descriptor.get = _;
      if (_ = accept(result.set)) descriptor.set = _;
      if (_ = accept(result.init)) initializers.unshift(_);
    } else if (_ = accept(result)) {
      if (kind === "field") initializers.unshift(_);
      else descriptor[key] = _;
    }
  }
  if (target) Object.defineProperty(target, contextIn.name, descriptor);
  done = true;
};
let BidiFrame = (() => {
  var _page4, _context5, _timeoutSettings, _abortDeferred, _disposed2, _exposedFunctions, _a2;
  let _classSuper = Frame;
  let _instanceExtraInitializers = [];
  let _goto_decorators;
  let _setContent_decorators;
  let _waitForNavigation_decorators;
  return _a2 = class extends _classSuper {
    constructor(page, context, timeoutSettings, parentId) {
      super();
      __privateAdd(this, _page4, (__runInitializers(this, _instanceExtraInitializers), void 0));
      __privateAdd(this, _context5);
      __privateAdd(this, _timeoutSettings);
      __privateAdd(this, _abortDeferred, Deferred.create());
      __privateAdd(this, _disposed2, false);
      __publicField(this, "sandboxes");
      __publicField(this, "_id");
      __privateAdd(this, _exposedFunctions, /* @__PURE__ */ new Map());
      __privateSet(this, _page4, page);
      __privateSet(this, _context5, context);
      __privateSet(this, _timeoutSettings, timeoutSettings);
      this._id = __privateGet(this, _context5).id;
      this._parentId = parentId ?? void 0;
      this.sandboxes = {
        [MAIN_SANDBOX]: new Sandbox(void 0, this, context, timeoutSettings),
        [PUPPETEER_SANDBOX]: new Sandbox(UTILITY_WORLD_NAME, this, context.createRealmForSandbox(), timeoutSettings)
      };
    }
    get client() {
      return this.context().cdpSession;
    }
    mainRealm() {
      return this.sandboxes[MAIN_SANDBOX];
    }
    isolatedRealm() {
      return this.sandboxes[PUPPETEER_SANDBOX];
    }
    page() {
      return __privateGet(this, _page4);
    }
    isOOPFrame() {
      throw new UnsupportedOperation();
    }
    url() {
      return __privateGet(this, _context5).url;
    }
    parentFrame() {
      return __privateGet(this, _page4).frame(this._parentId ?? "");
    }
    childFrames() {
      return __privateGet(this, _page4).childFrames(__privateGet(this, _context5).id);
    }
    async goto(url, options = {}) {
      const { waitUntil = "load", timeout: ms = __privateGet(this, _timeoutSettings).navigationTimeout() } = options;
      const [readiness, networkIdle] = getBiDiReadinessState(waitUntil);
      const result$ = Ee(F(__privateGet(this, _context5).connection.send("browsingContext.navigate", {
        context: __privateGet(this, _context5).id,
        url,
        wait: readiness
      })), ...networkIdle !== null ? [
        __privateGet(this, _page4).waitForNetworkIdle$({
          timeout: ms,
          concurrency: networkIdle === "networkidle2" ? 2 : 0,
          idleTime: NETWORK_IDLE_TIME
        })
      ] : []).pipe(k(([{ result: result2 }]) => {
        return result2;
      }), Fe(timeout(ms), F(__privateGet(this, _abortDeferred).valueOrThrow())), rewriteNavigationError(url, ms));
      const result = await me(result$);
      return __privateGet(this, _page4).getNavigationResponse(result.navigation);
    }
    async setContent(html, options = {}) {
      const { waitUntil = "load", timeout: ms = __privateGet(this, _timeoutSettings).navigationTimeout() } = options;
      const [waitEvent, networkIdle] = getBiDiLifecycleEvent(waitUntil);
      const result$ = Ee(be([
        fromEmitterEvent(__privateGet(this, _context5), waitEvent).pipe(Pe()),
        F(this.setFrameContent(html))
      ]).pipe(k(() => {
        return null;
      })), ...networkIdle !== null ? [
        __privateGet(this, _page4).waitForNetworkIdle$({
          timeout: ms,
          concurrency: networkIdle === "networkidle2" ? 2 : 0,
          idleTime: NETWORK_IDLE_TIME
        })
      ] : []).pipe(Fe(timeout(ms), F(__privateGet(this, _abortDeferred).valueOrThrow())), rewriteNavigationError("setContent", ms));
      await me(result$);
    }
    context() {
      return __privateGet(this, _context5);
    }
    async waitForNavigation(options = {}) {
      const { waitUntil = "load", timeout: ms = __privateGet(this, _timeoutSettings).navigationTimeout() } = options;
      const [waitUntilEvent, networkIdle] = getBiDiLifecycleEvent(waitUntil);
      const navigation$ = we(be([
        fromEmitterEvent(__privateGet(this, _context5), protocol.ChromiumBidi.BrowsingContext.EventNames.NavigationStarted).pipe(Pe()),
        fromEmitterEvent(__privateGet(this, _context5), waitUntilEvent).pipe(Pe())
      ]), fromEmitterEvent(__privateGet(this, _context5), protocol.ChromiumBidi.BrowsingContext.EventNames.FragmentNavigated)).pipe(k((result2) => {
        if (Array.isArray(result2)) {
          return { result: result2[1] };
        }
        return { result: result2 };
      }));
      const result$ = Ee(navigation$, ...networkIdle !== null ? [
        __privateGet(this, _page4).waitForNetworkIdle$({
          timeout: ms,
          concurrency: networkIdle === "networkidle2" ? 2 : 0,
          idleTime: NETWORK_IDLE_TIME
        })
      ] : []).pipe(k(([{ result: result2 }]) => {
        return result2;
      }), Fe(timeout(ms), F(__privateGet(this, _abortDeferred).valueOrThrow())));
      const result = await me(result$);
      return __privateGet(this, _page4).getNavigationResponse(result.navigation);
    }
    waitForDevicePrompt() {
      throw new UnsupportedOperation();
    }
    get detached() {
      return __privateGet(this, _disposed2);
    }
    [(_goto_decorators = [throwIfDetached], _setContent_decorators = [throwIfDetached], _waitForNavigation_decorators = [throwIfDetached], disposeSymbol)]() {
      if (__privateGet(this, _disposed2)) {
        return;
      }
      __privateSet(this, _disposed2, true);
      __privateGet(this, _abortDeferred).reject(new Error("Frame detached"));
      __privateGet(this, _context5).dispose();
      this.sandboxes[MAIN_SANDBOX][disposeSymbol]();
      this.sandboxes[PUPPETEER_SANDBOX][disposeSymbol]();
    }
    async exposeFunction(name, apply) {
      if (__privateGet(this, _exposedFunctions).has(name)) {
        throw new Error(`Failed to add page binding with name ${name}: globalThis['${name}'] already exists!`);
      }
      const exposeable = new ExposeableFunction(this, name, apply);
      __privateGet(this, _exposedFunctions).set(name, exposeable);
      try {
        await exposeable.expose();
      } catch (error) {
        __privateGet(this, _exposedFunctions).delete(name);
        throw error;
      }
    }
    waitForSelector(selector, options) {
      if (selector.startsWith("aria")) {
        throw new UnsupportedOperation("ARIA selector is not supported for BiDi!");
      }
      return super.waitForSelector(selector, options);
    }
  }, _page4 = new WeakMap(), _context5 = new WeakMap(), _timeoutSettings = new WeakMap(), _abortDeferred = new WeakMap(), _disposed2 = new WeakMap(), _exposedFunctions = new WeakMap(), (() => {
    const _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;
    __esDecorate(_a2, null, _goto_decorators, { kind: "method", name: "goto", static: false, private: false, access: { has: (obj) => "goto" in obj, get: (obj) => obj.goto }, metadata: _metadata }, null, _instanceExtraInitializers);
    __esDecorate(_a2, null, _setContent_decorators, { kind: "method", name: "setContent", static: false, private: false, access: { has: (obj) => "setContent" in obj, get: (obj) => obj.setContent }, metadata: _metadata }, null, _instanceExtraInitializers);
    __esDecorate(_a2, null, _waitForNavigation_decorators, { kind: "method", name: "waitForNavigation", static: false, private: false, access: { has: (obj) => "waitForNavigation" in obj, get: (obj) => obj.waitForNavigation }, metadata: _metadata }, null, _instanceExtraInitializers);
    if (_metadata) Object.defineProperty(_a2, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
  })(), _a2;
})();
/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
var SourceActionsType;
(function(SourceActionsType2) {
  SourceActionsType2["None"] = "none";
  SourceActionsType2["Key"] = "key";
  SourceActionsType2["Pointer"] = "pointer";
  SourceActionsType2["Wheel"] = "wheel";
})(SourceActionsType || (SourceActionsType = {}));
var ActionType;
(function(ActionType2) {
  ActionType2["Pause"] = "pause";
  ActionType2["KeyDown"] = "keyDown";
  ActionType2["KeyUp"] = "keyUp";
  ActionType2["PointerUp"] = "pointerUp";
  ActionType2["PointerDown"] = "pointerDown";
  ActionType2["PointerMove"] = "pointerMove";
  ActionType2["Scroll"] = "scroll";
})(ActionType || (ActionType = {}));
const getBidiKeyValue = (key) => {
  switch (key) {
    case "\r":
    case "\n":
      key = "Enter";
      break;
  }
  if ([...key].length === 1) {
    return key;
  }
  switch (key) {
    case "Cancel":
      return "";
    case "Help":
      return "";
    case "Backspace":
      return "";
    case "Tab":
      return "";
    case "Clear":
      return "";
    case "Enter":
      return "";
    case "Shift":
    case "ShiftLeft":
      return "";
    case "Control":
    case "ControlLeft":
      return "";
    case "Alt":
    case "AltLeft":
      return "";
    case "Pause":
      return "";
    case "Escape":
      return "";
    case "PageUp":
      return "";
    case "PageDown":
      return "";
    case "End":
      return "";
    case "Home":
      return "";
    case "ArrowLeft":
      return "";
    case "ArrowUp":
      return "";
    case "ArrowRight":
      return "";
    case "ArrowDown":
      return "";
    case "Insert":
      return "";
    case "Delete":
      return "";
    case "NumpadEqual":
      return "";
    case "Numpad0":
      return "";
    case "Numpad1":
      return "";
    case "Numpad2":
      return "";
    case "Numpad3":
      return "";
    case "Numpad4":
      return "";
    case "Numpad5":
      return "";
    case "Numpad6":
      return "";
    case "Numpad7":
      return "";
    case "Numpad8":
      return "";
    case "Numpad9":
      return "";
    case "NumpadMultiply":
      return "";
    case "NumpadAdd":
      return "";
    case "NumpadSubtract":
      return "";
    case "NumpadDecimal":
      return "";
    case "NumpadDivide":
      return "";
    case "F1":
      return "";
    case "F2":
      return "";
    case "F3":
      return "";
    case "F4":
      return "";
    case "F5":
      return "";
    case "F6":
      return "";
    case "F7":
      return "";
    case "F8":
      return "";
    case "F9":
      return "";
    case "F10":
      return "";
    case "F11":
      return "";
    case "F12":
      return "";
    case "Meta":
    case "MetaLeft":
      return "";
    case "ShiftRight":
      return "";
    case "ControlRight":
      return "";
    case "AltRight":
      return "";
    case "MetaRight":
      return "";
    case "Digit0":
      return "0";
    case "Digit1":
      return "1";
    case "Digit2":
      return "2";
    case "Digit3":
      return "3";
    case "Digit4":
      return "4";
    case "Digit5":
      return "5";
    case "Digit6":
      return "6";
    case "Digit7":
      return "7";
    case "Digit8":
      return "8";
    case "Digit9":
      return "9";
    case "KeyA":
      return "a";
    case "KeyB":
      return "b";
    case "KeyC":
      return "c";
    case "KeyD":
      return "d";
    case "KeyE":
      return "e";
    case "KeyF":
      return "f";
    case "KeyG":
      return "g";
    case "KeyH":
      return "h";
    case "KeyI":
      return "i";
    case "KeyJ":
      return "j";
    case "KeyK":
      return "k";
    case "KeyL":
      return "l";
    case "KeyM":
      return "m";
    case "KeyN":
      return "n";
    case "KeyO":
      return "o";
    case "KeyP":
      return "p";
    case "KeyQ":
      return "q";
    case "KeyR":
      return "r";
    case "KeyS":
      return "s";
    case "KeyT":
      return "t";
    case "KeyU":
      return "u";
    case "KeyV":
      return "v";
    case "KeyW":
      return "w";
    case "KeyX":
      return "x";
    case "KeyY":
      return "y";
    case "KeyZ":
      return "z";
    case "Semicolon":
      return ";";
    case "Equal":
      return "=";
    case "Comma":
      return ",";
    case "Minus":
      return "-";
    case "Period":
      return ".";
    case "Slash":
      return "/";
    case "Backquote":
      return "`";
    case "BracketLeft":
      return "[";
    case "Backslash":
      return "\\";
    case "BracketRight":
      return "]";
    case "Quote":
      return '"';
    default:
      throw new Error(`Unknown key: "${key}"`);
  }
};
class BidiKeyboard extends Keyboard {
  constructor(page) {
    super();
    __privateAdd(this, _page);
    __privateSet(this, _page, page);
  }
  async down(key, _options) {
    await __privateGet(this, _page).connection.send("input.performActions", {
      context: __privateGet(this, _page).mainFrame()._id,
      actions: [
        {
          type: SourceActionsType.Key,
          id: "__puppeteer_keyboard",
          actions: [
            {
              type: ActionType.KeyDown,
              value: getBidiKeyValue(key)
            }
          ]
        }
      ]
    });
  }
  async up(key) {
    await __privateGet(this, _page).connection.send("input.performActions", {
      context: __privateGet(this, _page).mainFrame()._id,
      actions: [
        {
          type: SourceActionsType.Key,
          id: "__puppeteer_keyboard",
          actions: [
            {
              type: ActionType.KeyUp,
              value: getBidiKeyValue(key)
            }
          ]
        }
      ]
    });
  }
  async press(key, options = {}) {
    const { delay = 0 } = options;
    const actions = [
      {
        type: ActionType.KeyDown,
        value: getBidiKeyValue(key)
      }
    ];
    if (delay > 0) {
      actions.push({
        type: ActionType.Pause,
        duration: delay
      });
    }
    actions.push({
      type: ActionType.KeyUp,
      value: getBidiKeyValue(key)
    });
    await __privateGet(this, _page).connection.send("input.performActions", {
      context: __privateGet(this, _page).mainFrame()._id,
      actions: [
        {
          type: SourceActionsType.Key,
          id: "__puppeteer_keyboard",
          actions
        }
      ]
    });
  }
  async type(text, options = {}) {
    const { delay = 0 } = options;
    const values = [...text].map(getBidiKeyValue);
    const actions = [];
    if (delay <= 0) {
      for (const value of values) {
        actions.push({
          type: ActionType.KeyDown,
          value
        }, {
          type: ActionType.KeyUp,
          value
        });
      }
    } else {
      for (const value of values) {
        actions.push({
          type: ActionType.KeyDown,
          value
        }, {
          type: ActionType.Pause,
          duration: delay
        }, {
          type: ActionType.KeyUp,
          value
        });
      }
    }
    await __privateGet(this, _page).connection.send("input.performActions", {
      context: __privateGet(this, _page).mainFrame()._id,
      actions: [
        {
          type: SourceActionsType.Key,
          id: "__puppeteer_keyboard",
          actions
        }
      ]
    });
  }
  async sendCharacter(char) {
    if ([...char].length > 1) {
      throw new Error("Cannot send more than 1 character.");
    }
    const frame = await __privateGet(this, _page).focusedFrame();
    await frame.isolatedRealm().evaluate(async (char2) => {
      document.execCommand("insertText", false, char2);
    }, char);
  }
}
_page = new WeakMap();
const getBidiButton = (button) => {
  switch (button) {
    case MouseButton.Left:
      return 0;
    case MouseButton.Middle:
      return 1;
    case MouseButton.Right:
      return 2;
    case MouseButton.Back:
      return 3;
    case MouseButton.Forward:
      return 4;
  }
};
class BidiMouse extends Mouse {
  constructor(context) {
    super();
    __privateAdd(this, _context3);
    __privateAdd(this, _lastMovePoint, { x: 0, y: 0 });
    __privateSet(this, _context3, context);
  }
  async reset() {
    __privateSet(this, _lastMovePoint, { x: 0, y: 0 });
    await __privateGet(this, _context3).connection.send("input.releaseActions", {
      context: __privateGet(this, _context3).id
    });
  }
  async move(x, y, options = {}) {
    const from = __privateGet(this, _lastMovePoint);
    const to = {
      x: Math.round(x),
      y: Math.round(y)
    };
    const actions = [];
    const steps = options.steps ?? 0;
    for (let i = 0; i < steps; ++i) {
      actions.push({
        type: ActionType.PointerMove,
        x: from.x + (to.x - from.x) * (i / steps),
        y: from.y + (to.y - from.y) * (i / steps),
        origin: options.origin
      });
    }
    actions.push({
      type: ActionType.PointerMove,
      ...to,
      origin: options.origin
    });
    __privateSet(this, _lastMovePoint, to);
    await __privateGet(this, _context3).connection.send("input.performActions", {
      context: __privateGet(this, _context3).id,
      actions: [
        {
          type: SourceActionsType.Pointer,
          id: "__puppeteer_mouse",
          actions
        }
      ]
    });
  }
  async down(options = {}) {
    await __privateGet(this, _context3).connection.send("input.performActions", {
      context: __privateGet(this, _context3).id,
      actions: [
        {
          type: SourceActionsType.Pointer,
          id: "__puppeteer_mouse",
          actions: [
            {
              type: ActionType.PointerDown,
              button: getBidiButton(options.button ?? MouseButton.Left)
            }
          ]
        }
      ]
    });
  }
  async up(options = {}) {
    await __privateGet(this, _context3).connection.send("input.performActions", {
      context: __privateGet(this, _context3).id,
      actions: [
        {
          type: SourceActionsType.Pointer,
          id: "__puppeteer_mouse",
          actions: [
            {
              type: ActionType.PointerUp,
              button: getBidiButton(options.button ?? MouseButton.Left)
            }
          ]
        }
      ]
    });
  }
  async click(x, y, options = {}) {
    const actions = [
      {
        type: ActionType.PointerMove,
        x: Math.round(x),
        y: Math.round(y),
        origin: options.origin
      }
    ];
    const pointerDownAction = {
      type: ActionType.PointerDown,
      button: getBidiButton(options.button ?? MouseButton.Left)
    };
    const pointerUpAction = {
      type: ActionType.PointerUp,
      button: pointerDownAction.button
    };
    for (let i = 1; i < (options.count ?? 1); ++i) {
      actions.push(pointerDownAction, pointerUpAction);
    }
    actions.push(pointerDownAction);
    if (options.delay) {
      actions.push({
        type: ActionType.Pause,
        duration: options.delay
      });
    }
    actions.push(pointerUpAction);
    await __privateGet(this, _context3).connection.send("input.performActions", {
      context: __privateGet(this, _context3).id,
      actions: [
        {
          type: SourceActionsType.Pointer,
          id: "__puppeteer_mouse",
          actions
        }
      ]
    });
  }
  async wheel(options = {}) {
    await __privateGet(this, _context3).connection.send("input.performActions", {
      context: __privateGet(this, _context3).id,
      actions: [
        {
          type: SourceActionsType.Wheel,
          id: "__puppeteer_wheel",
          actions: [
            {
              type: ActionType.Scroll,
              ...__privateGet(this, _lastMovePoint) ?? {
                x: 0,
                y: 0
              },
              deltaX: options.deltaX ?? 0,
              deltaY: options.deltaY ?? 0
            }
          ]
        }
      ]
    });
  }
  drag() {
    throw new UnsupportedOperation();
  }
  dragOver() {
    throw new UnsupportedOperation();
  }
  dragEnter() {
    throw new UnsupportedOperation();
  }
  drop() {
    throw new UnsupportedOperation();
  }
  dragAndDrop() {
    throw new UnsupportedOperation();
  }
}
_context3 = new WeakMap();
_lastMovePoint = new WeakMap();
class BidiTouchscreen extends Touchscreen {
  constructor(context) {
    super();
    __privateAdd(this, _context4);
    __privateSet(this, _context4, context);
  }
  async touchStart(x, y, options = {}) {
    await __privateGet(this, _context4).connection.send("input.performActions", {
      context: __privateGet(this, _context4).id,
      actions: [
        {
          type: SourceActionsType.Pointer,
          id: "__puppeteer_finger",
          parameters: {
            pointerType: "touch"
          },
          actions: [
            {
              type: ActionType.PointerMove,
              x: Math.round(x),
              y: Math.round(y),
              origin: options.origin
            },
            {
              type: ActionType.PointerDown,
              button: 0
            }
          ]
        }
      ]
    });
  }
  async touchMove(x, y, options = {}) {
    await __privateGet(this, _context4).connection.send("input.performActions", {
      context: __privateGet(this, _context4).id,
      actions: [
        {
          type: SourceActionsType.Pointer,
          id: "__puppeteer_finger",
          parameters: {
            pointerType: "touch"
          },
          actions: [
            {
              type: ActionType.PointerMove,
              x: Math.round(x),
              y: Math.round(y),
              origin: options.origin
            }
          ]
        }
      ]
    });
  }
  async touchEnd() {
    await __privateGet(this, _context4).connection.send("input.performActions", {
      context: __privateGet(this, _context4).id,
      actions: [
        {
          type: SourceActionsType.Pointer,
          id: "__puppeteer_finger",
          parameters: {
            pointerType: "touch"
          },
          actions: [
            {
              type: ActionType.PointerUp,
              button: 0
            }
          ]
        }
      ]
    });
  }
}
_context4 = new WeakMap();
class BidiHTTPRequest extends HTTPRequest {
  constructor(event, frame, redirectChain = []) {
    super();
    __publicField(this, "_response", null);
    __publicField(this, "_redirectChain");
    __publicField(this, "_navigationId");
    __privateAdd(this, _url3);
    __privateAdd(this, _resourceType);
    __privateAdd(this, _method);
    __privateAdd(this, _postData);
    __privateAdd(this, _headers, {});
    __privateAdd(this, _initiator);
    __privateAdd(this, _frame3);
    __privateSet(this, _url3, event.request.url);
    __privateSet(this, _resourceType, event.initiator.type.toLowerCase());
    __privateSet(this, _method, event.request.method);
    __privateSet(this, _postData, void 0);
    __privateSet(this, _initiator, event.initiator);
    __privateSet(this, _frame3, frame);
    this._requestId = event.request.request;
    this._redirectChain = redirectChain;
    this._navigationId = event.navigation;
    for (const header of event.request.headers) {
      if (header.value.type === "string") {
        __privateGet(this, _headers)[header.name.toLowerCase()] = header.value.value;
      }
    }
  }
  get client() {
    throw new UnsupportedOperation();
  }
  url() {
    return __privateGet(this, _url3);
  }
  resourceType() {
    return __privateGet(this, _resourceType);
  }
  method() {
    return __privateGet(this, _method);
  }
  postData() {
    return __privateGet(this, _postData);
  }
  hasPostData() {
    return __privateGet(this, _postData) !== void 0;
  }
  async fetchPostData() {
    return __privateGet(this, _postData);
  }
  headers() {
    return __privateGet(this, _headers);
  }
  response() {
    return this._response;
  }
  isNavigationRequest() {
    return Boolean(this._navigationId);
  }
  initiator() {
    return __privateGet(this, _initiator);
  }
  redirectChain() {
    return this._redirectChain.slice();
  }
  enqueueInterceptAction(pendingHandler) {
    void pendingHandler();
  }
  frame() {
    return __privateGet(this, _frame3);
  }
  continueRequestOverrides() {
    throw new UnsupportedOperation();
  }
  continue(_overrides = {}) {
    throw new UnsupportedOperation();
  }
  responseForRequest() {
    throw new UnsupportedOperation();
  }
  abortErrorReason() {
    throw new UnsupportedOperation();
  }
  interceptResolutionState() {
    throw new UnsupportedOperation();
  }
  isInterceptResolutionHandled() {
    throw new UnsupportedOperation();
  }
  finalizeInterceptions() {
    throw new UnsupportedOperation();
  }
  abort() {
    throw new UnsupportedOperation();
  }
  respond(_response, _priority) {
    throw new UnsupportedOperation();
  }
  failure() {
    throw new UnsupportedOperation();
  }
}
_url3 = new WeakMap();
_resourceType = new WeakMap();
_method = new WeakMap();
_postData = new WeakMap();
_headers = new WeakMap();
_initiator = new WeakMap();
_frame3 = new WeakMap();
class BidiHTTPResponse extends HTTPResponse {
  constructor(request, { response }) {
    super();
    __privateAdd(this, _request);
    __privateAdd(this, _remoteAddress);
    __privateAdd(this, _status);
    __privateAdd(this, _statusText);
    __privateAdd(this, _url4);
    __privateAdd(this, _fromCache);
    __privateAdd(this, _headers2, {});
    __privateAdd(this, _timings);
    __privateSet(this, _request, request);
    __privateSet(this, _remoteAddress, {
      ip: "",
      port: -1
    });
    __privateSet(this, _url4, response.url);
    __privateSet(this, _fromCache, response.fromCache);
    __privateSet(this, _status, response.status);
    __privateSet(this, _statusText, response.statusText);
    __privateSet(this, _timings, null);
    for (const header of response.headers || []) {
      if (header.value.type === "string") {
        __privateGet(this, _headers2)[header.name.toLowerCase()] = header.value.value;
      }
    }
  }
  remoteAddress() {
    return __privateGet(this, _remoteAddress);
  }
  url() {
    return __privateGet(this, _url4);
  }
  status() {
    return __privateGet(this, _status);
  }
  statusText() {
    return __privateGet(this, _statusText);
  }
  headers() {
    return __privateGet(this, _headers2);
  }
  request() {
    return __privateGet(this, _request);
  }
  fromCache() {
    return __privateGet(this, _fromCache);
  }
  timing() {
    return __privateGet(this, _timings);
  }
  frame() {
    return __privateGet(this, _request).frame();
  }
  fromServiceWorker() {
    return false;
  }
  securityDetails() {
    throw new UnsupportedOperation();
  }
  buffer() {
    throw new UnsupportedOperation();
  }
}
_request = new WeakMap();
_remoteAddress = new WeakMap();
_status = new WeakMap();
_statusText = new WeakMap();
_url4 = new WeakMap();
_fromCache = new WeakMap();
_headers2 = new WeakMap();
_timings = new WeakMap();
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
class BidiNetworkManager extends EventEmitter {
  constructor(connection, page) {
    super();
    __privateAdd(this, _BidiNetworkManager_instances);
    __privateAdd(this, _connection2);
    __privateAdd(this, _page2);
    __privateAdd(this, _subscriptions, new DisposableStack());
    __privateAdd(this, _requestMap, /* @__PURE__ */ new Map());
    __privateAdd(this, _navigationMap, /* @__PURE__ */ new Map());
    __privateSet(this, _connection2, connection);
    __privateSet(this, _page2, page);
    __privateGet(this, _subscriptions).use(new EventSubscription(__privateGet(this, _connection2), "network.beforeRequestSent", __privateMethod(this, _BidiNetworkManager_instances, onBeforeRequestSent_fn).bind(this)));
    __privateGet(this, _subscriptions).use(new EventSubscription(__privateGet(this, _connection2), "network.responseStarted", __privateMethod(this, _BidiNetworkManager_instances, onResponseStarted_fn).bind(this)));
    __privateGet(this, _subscriptions).use(new EventSubscription(__privateGet(this, _connection2), "network.responseCompleted", __privateMethod(this, _BidiNetworkManager_instances, onResponseCompleted_fn).bind(this)));
    __privateGet(this, _subscriptions).use(new EventSubscription(__privateGet(this, _connection2), "network.fetchError", __privateMethod(this, _BidiNetworkManager_instances, onFetchError_fn).bind(this)));
  }
  getNavigationResponse(navigationId) {
    if (!navigationId) {
      return null;
    }
    const response = __privateGet(this, _navigationMap).get(navigationId);
    return response ?? null;
  }
  inFlightRequestsCount() {
    let inFlightRequestCounter = 0;
    for (const request of __privateGet(this, _requestMap).values()) {
      if (!request.response() || request._failureText) {
        inFlightRequestCounter++;
      }
    }
    return inFlightRequestCounter;
  }
  clearMapAfterFrameDispose(frame) {
    for (const [id, request] of __privateGet(this, _requestMap).entries()) {
      if (request.frame() === frame) {
        __privateGet(this, _requestMap).delete(id);
      }
    }
    for (const [id, response] of __privateGet(this, _navigationMap).entries()) {
      if (response.frame() === frame) {
        __privateGet(this, _navigationMap).delete(id);
      }
    }
  }
  dispose() {
    this.removeAllListeners();
    __privateGet(this, _requestMap).clear();
    __privateGet(this, _navigationMap).clear();
    __privateGet(this, _subscriptions).dispose();
  }
}
_connection2 = new WeakMap();
_page2 = new WeakMap();
_subscriptions = new WeakMap();
_requestMap = new WeakMap();
_navigationMap = new WeakMap();
_BidiNetworkManager_instances = new WeakSet();
onBeforeRequestSent_fn = function(event) {
  const frame = __privateGet(this, _page2).frame(event.context ?? "");
  if (!frame) {
    return;
  }
  const request = __privateGet(this, _requestMap).get(event.request.request);
  let upsertRequest;
  if (request) {
    request._redirectChain.push(request);
    upsertRequest = new BidiHTTPRequest(event, frame, request._redirectChain);
  } else {
    upsertRequest = new BidiHTTPRequest(event, frame, []);
  }
  __privateGet(this, _requestMap).set(event.request.request, upsertRequest);
  this.emit(NetworkManagerEvent.Request, upsertRequest);
};
onResponseStarted_fn = function(_event) {
};
onResponseCompleted_fn = function(event) {
  const request = __privateGet(this, _requestMap).get(event.request.request);
  if (!request) {
    return;
  }
  const response = new BidiHTTPResponse(request, event);
  request._response = response;
  if (event.navigation) {
    __privateGet(this, _navigationMap).set(event.navigation, response);
  }
  if (response.fromCache()) {
    this.emit(NetworkManagerEvent.RequestServedFromCache, request);
  }
  this.emit(NetworkManagerEvent.Response, response);
  this.emit(NetworkManagerEvent.RequestFinished, request);
};
onFetchError_fn = function(event) {
  const request = __privateGet(this, _requestMap).get(event.request.request);
  if (!request) {
    return;
  }
  request._failureText = event.errorText;
  this.emit(NetworkManagerEvent.RequestFailed, request);
  __privateGet(this, _requestMap).delete(event.request.request);
};
/**
 * @license
 * Copyright 2022 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
var __addDisposableResource = function(env, value, async) {
  if (value !== null && value !== void 0) {
    if (typeof value !== "object" && typeof value !== "function") throw new TypeError("Object expected.");
    var dispose;
    if (async) {
      if (!Symbol.asyncDispose) throw new TypeError("Symbol.asyncDispose is not defined.");
      dispose = value[Symbol.asyncDispose];
    }
    if (dispose === void 0) {
      if (!Symbol.dispose) throw new TypeError("Symbol.dispose is not defined.");
      dispose = value[Symbol.dispose];
    }
    if (typeof dispose !== "function") throw new TypeError("Object not disposable.");
    env.stack.push({ value, dispose, async });
  } else if (async) {
    env.stack.push({ async: true });
  }
  return value;
};
var __disposeResources = /* @__PURE__ */ function(SuppressedError2) {
  return function(env) {
    function fail(e) {
      env.error = env.hasError ? new SuppressedError2(e, env.error, "An error was suppressed during disposal.") : e;
      env.hasError = true;
    }
    function next() {
      while (env.stack.length) {
        var rec = env.stack.pop();
        try {
          var result = rec.dispose && rec.dispose.call(rec.value);
          if (rec.async) return Promise.resolve(result).then(next, function(e) {
            fail(e);
            return next();
          });
        } catch (e) {
          fail(e);
        }
      }
      if (env.hasError) throw env.error;
    }
    return next();
  };
}(typeof SuppressedError === "function" ? SuppressedError : function(error, suppressed, message) {
  var e = new Error(message);
  return e.name = "SuppressedError", e.error = error, e.suppressed = suppressed, e;
});
class BidiPage extends Page {
  constructor(browsingContext, browserContext, target) {
    super();
    __privateAdd(this, _BidiPage_instances);
    __privateAdd(this, _accessibility);
    __privateAdd(this, _connection3);
    __privateAdd(this, _frameTree, new FrameTree());
    __privateAdd(this, _networkManager);
    __privateAdd(this, _viewport, null);
    __privateAdd(this, _closedDeferred, Deferred.create());
    __privateAdd(this, _subscribedEvents, /* @__PURE__ */ new Map([
      ["log.entryAdded", __privateMethod(this, _BidiPage_instances, onLogEntryAdded_fn).bind(this)],
      ["browsingContext.load", __privateMethod(this, _BidiPage_instances, onFrameLoaded_fn).bind(this)],
      [
        "browsingContext.fragmentNavigated",
        __privateMethod(this, _BidiPage_instances, onFrameFragmentNavigated_fn).bind(this)
      ],
      [
        "browsingContext.domContentLoaded",
        __privateMethod(this, _BidiPage_instances, onFrameDOMContentLoaded_fn).bind(this)
      ],
      ["browsingContext.userPromptOpened", __privateMethod(this, _BidiPage_instances, onDialog_fn).bind(this)]
    ]));
    __privateAdd(this, _networkManagerEvents, [
      [
        NetworkManagerEvent.Request,
        (request) => {
          this.emit("request", request);
        }
      ],
      [
        NetworkManagerEvent.RequestServedFromCache,
        (request) => {
          this.emit("requestservedfromcache", request);
        }
      ],
      [
        NetworkManagerEvent.RequestFailed,
        (request) => {
          this.emit("requestfailed", request);
        }
      ],
      [
        NetworkManagerEvent.RequestFinished,
        (request) => {
          this.emit("requestfinished", request);
        }
      ],
      [
        NetworkManagerEvent.Response,
        (response) => {
          this.emit("response", response);
        }
      ]
    ]);
    __privateAdd(this, _browsingContextEvents, /* @__PURE__ */ new Map([
      [BrowsingContextEvent.Created, __privateMethod(this, _BidiPage_instances, onContextCreated_fn).bind(this)],
      [BrowsingContextEvent.Destroyed, __privateMethod(this, _BidiPage_instances, onContextDestroyed_fn).bind(this)]
    ]));
    __privateAdd(this, _tracing);
    __privateAdd(this, _coverage);
    __privateAdd(this, _cdpEmulationManager);
    __privateAdd(this, _emulationManager);
    __privateAdd(this, _mouse);
    __privateAdd(this, _touchscreen);
    __privateAdd(this, _keyboard);
    __privateAdd(this, _browsingContext2);
    __privateAdd(this, _browserContext);
    __privateAdd(this, _target);
    __privateSet(this, _browsingContext2, browsingContext);
    __privateSet(this, _browserContext, browserContext);
    __privateSet(this, _target, target);
    __privateSet(this, _connection3, browsingContext.connection);
    for (const [event, subscriber] of __privateGet(this, _browsingContextEvents)) {
      __privateGet(this, _browsingContext2).on(event, subscriber);
    }
    __privateSet(this, _networkManager, new BidiNetworkManager(__privateGet(this, _connection3), this));
    for (const [event, subscriber] of __privateGet(this, _subscribedEvents)) {
      __privateGet(this, _connection3).on(event, subscriber);
    }
    for (const [event, subscriber] of __privateGet(this, _networkManagerEvents)) {
      __privateGet(this, _networkManager).on(event, subscriber);
    }
    const frame = new BidiFrame(this, __privateGet(this, _browsingContext2), this._timeoutSettings, __privateGet(this, _browsingContext2).parent);
    __privateGet(this, _frameTree).addFrame(frame);
    this.emit("frameattached", frame);
    __privateSet(this, _accessibility, new Accessibility(this.mainFrame().context().cdpSession));
    __privateSet(this, _tracing, new Tracing(this.mainFrame().context().cdpSession));
    __privateSet(this, _coverage, new Coverage(this.mainFrame().context().cdpSession));
    __privateSet(this, _cdpEmulationManager, new EmulationManager$1(this.mainFrame().context().cdpSession));
    __privateSet(this, _emulationManager, new EmulationManager(browsingContext));
    __privateSet(this, _mouse, new BidiMouse(this.mainFrame().context()));
    __privateSet(this, _touchscreen, new BidiTouchscreen(this.mainFrame().context()));
    __privateSet(this, _keyboard, new BidiKeyboard(this));
  }
  _client() {
    return this.mainFrame().context().cdpSession;
  }
  /**
   * @internal
   */
  get connection() {
    return __privateGet(this, _connection3);
  }
  async setUserAgent(userAgent, userAgentMetadata) {
    await this._client().send("Network.setUserAgentOverride", {
      userAgent,
      userAgentMetadata
    });
  }
  async setBypassCSP(enabled) {
    await this._client().send("Page.setBypassCSP", { enabled });
  }
  async queryObjects(prototypeHandle) {
    assert(!prototypeHandle.disposed, "Prototype JSHandle is disposed!");
    assert(prototypeHandle.id, "Prototype JSHandle must not be referencing primitive value");
    const response = await this.mainFrame().client.send("Runtime.queryObjects", {
      prototypeObjectId: prototypeHandle.id
    });
    return createBidiHandle(this.mainFrame().mainRealm(), {
      type: "array",
      handle: response.objects.objectId
    });
  }
  _setBrowserContext(browserContext) {
    __privateSet(this, _browserContext, browserContext);
  }
  get accessibility() {
    return __privateGet(this, _accessibility);
  }
  get tracing() {
    return __privateGet(this, _tracing);
  }
  get coverage() {
    return __privateGet(this, _coverage);
  }
  get mouse() {
    return __privateGet(this, _mouse);
  }
  get touchscreen() {
    return __privateGet(this, _touchscreen);
  }
  get keyboard() {
    return __privateGet(this, _keyboard);
  }
  browser() {
    return this.browserContext().browser();
  }
  browserContext() {
    return __privateGet(this, _browserContext);
  }
  mainFrame() {
    const mainFrame = __privateGet(this, _frameTree).getMainFrame();
    assert(mainFrame, "Requesting main frame too early!");
    return mainFrame;
  }
  /**
   * @internal
   */
  async focusedFrame() {
    const env_1 = { stack: [], error: void 0, hasError: false };
    try {
      const frame = __addDisposableResource(env_1, await this.mainFrame().isolatedRealm().evaluateHandle(() => {
        let frame2;
        let win = window;
        while ((win == null ? void 0 : win.document.activeElement) instanceof HTMLIFrameElement) {
          frame2 = win.document.activeElement;
          win = frame2.contentWindow;
        }
        return frame2;
      }), false);
      if (!(frame instanceof BidiElementHandle)) {
        return this.mainFrame();
      }
      return await frame.contentFrame();
    } catch (e_1) {
      env_1.error = e_1;
      env_1.hasError = true;
    } finally {
      __disposeResources(env_1);
    }
  }
  frames() {
    return Array.from(__privateGet(this, _frameTree).frames());
  }
  frame(frameId) {
    return __privateGet(this, _frameTree).getById(frameId ?? "") || null;
  }
  childFrames(frameId) {
    return __privateGet(this, _frameTree).childFrames(frameId);
  }
  getNavigationResponse(id) {
    return __privateGet(this, _networkManager).getNavigationResponse(id);
  }
  isClosed() {
    return __privateGet(this, _closedDeferred).finished();
  }
  async close(options) {
    if (__privateGet(this, _closedDeferred).finished()) {
      return;
    }
    __privateGet(this, _closedDeferred).reject(new TargetCloseError("Page closed!"));
    __privateGet(this, _networkManager).dispose();
    await __privateGet(this, _connection3).send("browsingContext.close", {
      context: this.mainFrame()._id,
      promptUnload: (options == null ? void 0 : options.runBeforeUnload) ?? false
    });
    this.emit("close", void 0);
    this.removeAllListeners();
  }
  async reload(options = {}) {
    const { waitUntil = "load", timeout: ms = this._timeoutSettings.navigationTimeout() } = options;
    const [readiness, networkIdle] = getBiDiReadinessState(waitUntil);
    const result$ = Ee(F(__privateGet(this, _connection3).send("browsingContext.reload", {
      context: this.mainFrame()._id,
      wait: readiness
    })), ...networkIdle !== null ? [
      this.waitForNetworkIdle$({
        timeout: ms,
        concurrency: networkIdle === "networkidle2" ? 2 : 0,
        idleTime: NETWORK_IDLE_TIME
      })
    ] : []).pipe(k(([{ result: result2 }]) => {
      return result2;
    }), Fe(timeout(ms), F(__privateGet(this, _closedDeferred).valueOrThrow())), rewriteNavigationError(this.url(), ms));
    const result = await me(result$);
    return this.getNavigationResponse(result.navigation);
  }
  setDefaultNavigationTimeout(timeout2) {
    this._timeoutSettings.setDefaultNavigationTimeout(timeout2);
  }
  setDefaultTimeout(timeout2) {
    this._timeoutSettings.setDefaultTimeout(timeout2);
  }
  getDefaultTimeout() {
    return this._timeoutSettings.timeout();
  }
  isJavaScriptEnabled() {
    return __privateGet(this, _cdpEmulationManager).javascriptEnabled;
  }
  async setGeolocation(options) {
    return await __privateGet(this, _cdpEmulationManager).setGeolocation(options);
  }
  async setJavaScriptEnabled(enabled) {
    return await __privateGet(this, _cdpEmulationManager).setJavaScriptEnabled(enabled);
  }
  async emulateMediaType(type) {
    return await __privateGet(this, _cdpEmulationManager).emulateMediaType(type);
  }
  async emulateCPUThrottling(factor) {
    return await __privateGet(this, _cdpEmulationManager).emulateCPUThrottling(factor);
  }
  async emulateMediaFeatures(features) {
    return await __privateGet(this, _cdpEmulationManager).emulateMediaFeatures(features);
  }
  async emulateTimezone(timezoneId) {
    return await __privateGet(this, _cdpEmulationManager).emulateTimezone(timezoneId);
  }
  async emulateIdleState(overrides) {
    return await __privateGet(this, _cdpEmulationManager).emulateIdleState(overrides);
  }
  async emulateVisionDeficiency(type) {
    return await __privateGet(this, _cdpEmulationManager).emulateVisionDeficiency(type);
  }
  async setViewport(viewport) {
    if (!__privateGet(this, _browsingContext2).supportsCdp()) {
      await __privateGet(this, _emulationManager).emulateViewport(viewport);
      __privateSet(this, _viewport, viewport);
      return;
    }
    const needsReload = await __privateGet(this, _cdpEmulationManager).emulateViewport(viewport);
    __privateSet(this, _viewport, viewport);
    if (needsReload) {
      await this.reload();
    }
  }
  viewport() {
    return __privateGet(this, _viewport);
  }
  async pdf(options = {}) {
    const { timeout: ms = this._timeoutSettings.timeout(), path = void 0 } = options;
    const { printBackground: background, margin, landscape, width, height, pageRanges: ranges, scale, preferCSSPageSize } = parsePDFOptions(options, "cm");
    const pageRanges = ranges ? ranges.split(", ") : [];
    const { result } = await me(F(__privateGet(this, _connection3).send("browsingContext.print", {
      context: this.mainFrame()._id,
      background,
      margin,
      orientation: landscape ? "landscape" : "portrait",
      page: {
        width,
        height
      },
      pageRanges,
      scale,
      shrinkToFit: !preferCSSPageSize
    })).pipe(Fe(timeout(ms))));
    const buffer = Buffer.from(result.data, "base64");
    await this._maybeWriteBufferToFile(path, buffer);
    return buffer;
  }
  async createPDFStream(options) {
    const buffer = await this.pdf(options);
    try {
      const { Readable } = await import("stream");
      return Readable.from(buffer);
    } catch (error) {
      if (error instanceof TypeError) {
        throw new Error("Can only pass a file path in a Node-like environment.");
      }
      throw error;
    }
  }
  async _screenshot(options) {
    const { clip, type, captureBeyondViewport, quality } = options;
    if (options.omitBackground !== void 0 && options.omitBackground) {
      throw new UnsupportedOperation(`BiDi does not support 'omitBackground'.`);
    }
    if (options.optimizeForSpeed !== void 0 && options.optimizeForSpeed) {
      throw new UnsupportedOperation(`BiDi does not support 'optimizeForSpeed'.`);
    }
    if (options.fromSurface !== void 0 && !options.fromSurface) {
      throw new UnsupportedOperation(`BiDi does not support 'fromSurface'.`);
    }
    if (clip !== void 0 && clip.scale !== void 0 && clip.scale !== 1) {
      throw new UnsupportedOperation(`BiDi does not support 'scale' in 'clip'.`);
    }
    let box;
    if (clip) {
      if (captureBeyondViewport) {
        box = clip;
      } else {
        const [pageLeft, pageTop] = await this.evaluate(() => {
          if (!window.visualViewport) {
            throw new Error("window.visualViewport is not supported.");
          }
          return [
            window.visualViewport.pageLeft,
            window.visualViewport.pageTop
          ];
        });
        box = {
          ...clip,
          x: clip.x - pageLeft,
          y: clip.y - pageTop
        };
      }
    }
    const { result: { data } } = await __privateGet(this, _connection3).send("browsingContext.captureScreenshot", {
      context: this.mainFrame()._id,
      origin: captureBeyondViewport ? "document" : "viewport",
      format: {
        type: `image/${type}`,
        ...quality !== void 0 ? { quality: quality / 100 } : {}
      },
      ...box ? { clip: { type: "box", ...box } } : {}
    });
    return data;
  }
  async createCDPSession() {
    const { sessionId } = await this.mainFrame().context().cdpSession.send("Target.attachToTarget", {
      targetId: this.mainFrame()._id,
      flatten: true
    });
    return new CdpSessionWrapper(this.mainFrame().context(), sessionId);
  }
  async bringToFront() {
    await __privateGet(this, _connection3).send("browsingContext.activate", {
      context: this.mainFrame()._id
    });
  }
  async evaluateOnNewDocument(pageFunction, ...args) {
    const expression = evaluationExpression(pageFunction, ...args);
    const { result } = await __privateGet(this, _connection3).send("script.addPreloadScript", {
      functionDeclaration: expression,
      contexts: [this.mainFrame()._id]
    });
    return { identifier: result.script };
  }
  async removeScriptToEvaluateOnNewDocument(id) {
    await __privateGet(this, _connection3).send("script.removePreloadScript", {
      script: id
    });
  }
  async exposeFunction(name, pptrFunction) {
    return await this.mainFrame().exposeFunction(name, "default" in pptrFunction ? pptrFunction.default : pptrFunction);
  }
  isDragInterceptionEnabled() {
    return false;
  }
  async setCacheEnabled(enabled) {
    await this._client().send("Network.setCacheDisabled", {
      cacheDisabled: !enabled
    });
  }
  isServiceWorkerBypassed() {
    throw new UnsupportedOperation();
  }
  target() {
    return __privateGet(this, _target);
  }
  waitForFileChooser() {
    throw new UnsupportedOperation();
  }
  workers() {
    throw new UnsupportedOperation();
  }
  setRequestInterception() {
    throw new UnsupportedOperation();
  }
  setDragInterception() {
    throw new UnsupportedOperation();
  }
  setBypassServiceWorker() {
    throw new UnsupportedOperation();
  }
  setOfflineMode() {
    throw new UnsupportedOperation();
  }
  emulateNetworkConditions() {
    throw new UnsupportedOperation();
  }
  cookies() {
    throw new UnsupportedOperation();
  }
  setCookie() {
    throw new UnsupportedOperation();
  }
  deleteCookie() {
    throw new UnsupportedOperation();
  }
  removeExposedFunction() {
    throw new UnsupportedOperation();
  }
  authenticate() {
    throw new UnsupportedOperation();
  }
  setExtraHTTPHeaders() {
    throw new UnsupportedOperation();
  }
  metrics() {
    throw new UnsupportedOperation();
  }
  async goBack(options = {}) {
    return await __privateMethod(this, _BidiPage_instances, go_fn).call(this, -1, options);
  }
  async goForward(options = {}) {
    return await __privateMethod(this, _BidiPage_instances, go_fn).call(this, 1, options);
  }
  waitForDevicePrompt() {
    throw new UnsupportedOperation();
  }
}
_accessibility = new WeakMap();
_connection3 = new WeakMap();
_frameTree = new WeakMap();
_networkManager = new WeakMap();
_viewport = new WeakMap();
_closedDeferred = new WeakMap();
_subscribedEvents = new WeakMap();
_networkManagerEvents = new WeakMap();
_browsingContextEvents = new WeakMap();
_tracing = new WeakMap();
_coverage = new WeakMap();
_cdpEmulationManager = new WeakMap();
_emulationManager = new WeakMap();
_mouse = new WeakMap();
_touchscreen = new WeakMap();
_keyboard = new WeakMap();
_browsingContext2 = new WeakMap();
_browserContext = new WeakMap();
_target = new WeakMap();
_BidiPage_instances = new WeakSet();
onFrameLoaded_fn = function(info) {
  const frame = this.frame(info.context);
  if (frame && this.mainFrame() === frame) {
    this.emit("load", void 0);
  }
};
onFrameFragmentNavigated_fn = function(info) {
  const frame = this.frame(info.context);
  if (frame) {
    this.emit("framenavigated", frame);
  }
};
onFrameDOMContentLoaded_fn = function(info) {
  const frame = this.frame(info.context);
  if (frame) {
    frame._hasStartedLoading = true;
    if (this.mainFrame() === frame) {
      this.emit("domcontentloaded", void 0);
    }
    this.emit("framenavigated", frame);
  }
};
onContextCreated_fn = function(context) {
  if (!this.frame(context.id) && (this.frame(context.parent ?? "") || !__privateGet(this, _frameTree).getMainFrame())) {
    const frame = new BidiFrame(this, context, this._timeoutSettings, context.parent);
    __privateGet(this, _frameTree).addFrame(frame);
    if (frame !== this.mainFrame()) {
      this.emit("frameattached", frame);
    }
  }
};
onContextDestroyed_fn = function(context) {
  const frame = this.frame(context.id);
  if (frame) {
    if (frame === this.mainFrame()) {
      this.emit("close", void 0);
    }
    __privateMethod(this, _BidiPage_instances, removeFramesRecursively_fn).call(this, frame);
  }
};
removeFramesRecursively_fn = function(frame) {
  for (const child of frame.childFrames()) {
    __privateMethod(this, _BidiPage_instances, removeFramesRecursively_fn).call(this, child);
  }
  frame[disposeSymbol]();
  __privateGet(this, _networkManager).clearMapAfterFrameDispose(frame);
  __privateGet(this, _frameTree).removeFrame(frame);
  this.emit("framedetached", frame);
};
onLogEntryAdded_fn = function(event) {
  const frame = this.frame(event.source.context);
  if (!frame) {
    return;
  }
  if (isConsoleLogEntry(event)) {
    const args = event.args.map((arg) => {
      return createBidiHandle(frame.mainRealm(), arg);
    });
    const text = args.reduce((value, arg) => {
      const parsedValue = arg.isPrimitiveValue ? BidiDeserializer.deserialize(arg.remoteValue()) : arg.toString();
      return `${value} ${parsedValue}`;
    }, "").slice(1);
    this.emit("console", new ConsoleMessage(event.method, text, args, getStackTraceLocations(event.stackTrace)));
  } else if (isJavaScriptLogEntry(event)) {
    const error = new Error(event.text ?? "");
    const messageHeight = error.message.split("\n").length;
    const messageLines = error.stack.split("\n").splice(0, messageHeight);
    const stackLines = [];
    if (event.stackTrace) {
      for (const frame2 of event.stackTrace.callFrames) {
        stackLines.push(`    at ${frame2.functionName || "<anonymous>"} (${frame2.url}:${frame2.lineNumber + 1}:${frame2.columnNumber + 1})`);
        if (stackLines.length >= Error.stackTraceLimit) {
          break;
        }
      }
    }
    error.stack = [...messageLines, ...stackLines].join("\n");
    this.emit("pageerror", error);
  } else {
    debugError(`Unhandled LogEntry with type "${event.type}", text "${event.text}" and level "${event.level}"`);
  }
};
onDialog_fn = function(event) {
  const frame = this.frame(event.context);
  if (!frame) {
    return;
  }
  const type = validateDialogType(event.type);
  const dialog = new BidiDialog(frame.context(), type, event.message, event.defaultValue);
  this.emit("dialog", dialog);
};
go_fn = async function(delta, options) {
  try {
    const result = await Promise.all([
      this.waitForNavigation(options),
      __privateGet(this, _connection3).send("browsingContext.traverseHistory", {
        delta,
        context: this.mainFrame()._id
      })
    ]);
    return result[0];
  } catch (err) {
    if (isErrorLike(err)) {
      if (err.message.includes("no such history entry")) {
        return null;
      }
    }
    throw err;
  }
};
function isConsoleLogEntry(event) {
  return event.type === "console";
}
function isJavaScriptLogEntry(event) {
  return event.type === "javascript";
}
function getStackTraceLocations(stackTrace) {
  const stackTraceLocations = [];
  if (stackTrace) {
    for (const callFrame of stackTrace.callFrames) {
      stackTraceLocations.push({
        url: callFrame.url,
        lineNumber: callFrame.lineNumber,
        columnNumber: callFrame.columnNumber
      });
    }
  }
  return stackTraceLocations;
}
function evaluationExpression(fun, ...args) {
  return `() => {${evaluationString(fun, ...args)}}`;
}
/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
class BidiTarget extends Target {
  constructor(browserContext) {
    super();
    __publicField(this, "_browserContext");
    this._browserContext = browserContext;
  }
  _setBrowserContext(browserContext) {
    this._browserContext = browserContext;
  }
  asPage() {
    throw new UnsupportedOperation();
  }
  browser() {
    return this._browserContext.browser();
  }
  browserContext() {
    return this._browserContext;
  }
  opener() {
    throw new UnsupportedOperation();
  }
  createCDPSession() {
    throw new UnsupportedOperation();
  }
}
class BiDiBrowserTarget extends Target {
  constructor(browser) {
    super();
    __privateAdd(this, _browser2);
    __privateSet(this, _browser2, browser);
  }
  url() {
    return "";
  }
  type() {
    return TargetType.BROWSER;
  }
  asPage() {
    throw new UnsupportedOperation();
  }
  browser() {
    return __privateGet(this, _browser2);
  }
  browserContext() {
    return __privateGet(this, _browser2).defaultBrowserContext();
  }
  opener() {
    throw new UnsupportedOperation();
  }
  createCDPSession() {
    throw new UnsupportedOperation();
  }
}
_browser2 = new WeakMap();
class BiDiBrowsingContextTarget extends BidiTarget {
  constructor(browserContext, browsingContext) {
    super(browserContext);
    __publicField(this, "_browsingContext");
    this._browsingContext = browsingContext;
  }
  url() {
    return this._browsingContext.url;
  }
  async createCDPSession() {
    const { sessionId } = await this._browsingContext.cdpSession.send("Target.attachToTarget", {
      targetId: this._browsingContext.id,
      flatten: true
    });
    return new CdpSessionWrapper(this._browsingContext, sessionId);
  }
  type() {
    return TargetType.PAGE;
  }
}
class BiDiPageTarget extends BiDiBrowsingContextTarget {
  constructor(browserContext, browsingContext) {
    super(browserContext, browsingContext);
    __privateAdd(this, _page3);
    __privateSet(this, _page3, new BidiPage(browsingContext, browserContext, this));
  }
  async page() {
    return __privateGet(this, _page3);
  }
  _setBrowserContext(browserContext) {
    super._setBrowserContext(browserContext);
    __privateGet(this, _page3)._setBrowserContext(browserContext);
  }
}
_page3 = new WeakMap();
/**
 * @license
 * Copyright 2022 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
const _BidiBrowser = class _BidiBrowser extends Browser$1 {
  constructor(browserCore, opts) {
    super();
    __privateAdd(this, _BidiBrowser_instances);
    __publicField(this, "protocol", "webDriverBiDi");
    __privateAdd(this, _process);
    __privateAdd(this, _closeCallback);
    __privateAdd(this, _browserCore);
    __privateAdd(this, _defaultViewport2);
    __privateAdd(this, _targets, /* @__PURE__ */ new Map());
    __privateAdd(this, _browserContexts, /* @__PURE__ */ new WeakMap());
    __privateAdd(this, _browserTarget);
    __privateAdd(this, _connectionEventHandlers, /* @__PURE__ */ new Map([
      ["browsingContext.contextCreated", __privateMethod(this, _BidiBrowser_instances, onContextCreated_fn2).bind(this)],
      ["browsingContext.contextDestroyed", __privateMethod(this, _BidiBrowser_instances, onContextDestroyed_fn2).bind(this)],
      ["browsingContext.domContentLoaded", __privateMethod(this, _BidiBrowser_instances, onContextDomLoaded_fn).bind(this)],
      ["browsingContext.fragmentNavigated", __privateMethod(this, _BidiBrowser_instances, onContextNavigation_fn).bind(this)],
      ["browsingContext.navigationStarted", __privateMethod(this, _BidiBrowser_instances, onContextNavigation_fn).bind(this)]
    ]));
    __privateSet(this, _process, opts.process);
    __privateSet(this, _closeCallback, opts.closeCallback);
    __privateSet(this, _browserCore, browserCore);
    __privateSet(this, _defaultViewport2, opts.defaultViewport);
    __privateSet(this, _browserTarget, new BiDiBrowserTarget(this));
    for (const context of __privateGet(this, _browserCore).userContexts) {
      __privateMethod(this, _BidiBrowser_instances, createBrowserContext_fn).call(this, context);
    }
  }
  static async create(opts) {
    var _a2, _b;
    const session = await Session.from(opts.connection, {
      alwaysMatch: {
        acceptInsecureCerts: opts.ignoreHTTPSErrors,
        webSocketUrl: true
      }
    });
    await session.subscribe(session.capabilities.browserName.toLocaleLowerCase().includes("firefox") ? _BidiBrowser.subscribeModules : [..._BidiBrowser.subscribeModules, ..._BidiBrowser.subscribeCdpEvents]);
    const browser = new _BidiBrowser(session.browser, opts);
    __privateMethod(_a2 = browser, _BidiBrowser_instances, initialize_fn).call(_a2);
    await __privateMethod(_b = browser, _BidiBrowser_instances, getTree_fn).call(_b);
    return browser;
  }
  userAgent() {
    throw new UnsupportedOperation();
  }
  get connection() {
    return __privateGet(this, _browserCore).session.connection;
  }
  wsEndpoint() {
    return this.connection.url;
  }
  async close() {
    var _a2;
    for (const [eventName, handler] of __privateGet(this, _connectionEventHandlers)) {
      this.connection.off(eventName, handler);
    }
    if (this.connection.closed) {
      return;
    }
    try {
      await __privateGet(this, _browserCore).close();
      await ((_a2 = __privateGet(this, _closeCallback)) == null ? void 0 : _a2.call(null));
    } catch (error) {
      debugError(error);
    } finally {
      this.connection.dispose();
    }
  }
  get connected() {
    return !__privateGet(this, _browserCore).disposed;
  }
  process() {
    return __privateGet(this, _process) ?? null;
  }
  async createIncognitoBrowserContext(_options) {
    const userContext = await __privateGet(this, _browserCore).createUserContext();
    return __privateMethod(this, _BidiBrowser_instances, createBrowserContext_fn).call(this, userContext);
  }
  async version() {
    return `${__privateGet(this, _BidiBrowser_instances, browserName_get)}/${__privateGet(this, _BidiBrowser_instances, browserVersion_get)}`;
  }
  browserContexts() {
    return [...__privateGet(this, _browserCore).userContexts].map((context) => {
      return __privateGet(this, _browserContexts).get(context);
    });
  }
  defaultBrowserContext() {
    return __privateGet(this, _browserContexts).get(__privateGet(this, _browserCore).defaultUserContext);
  }
  newPage() {
    return this.defaultBrowserContext().newPage();
  }
  targets() {
    return [__privateGet(this, _browserTarget), ...Array.from(__privateGet(this, _targets).values())];
  }
  _getTargetById(id) {
    const target = __privateGet(this, _targets).get(id);
    if (!target) {
      throw new Error("Target not found");
    }
    return target;
  }
  target() {
    return __privateGet(this, _browserTarget);
  }
  async disconnect() {
    try {
      await __privateGet(this, _browserCore).session.end();
    } catch (error) {
      debugError(error);
    } finally {
      this.connection.dispose();
    }
  }
  get debugInfo() {
    return {
      pendingProtocolErrors: this.connection.getPendingProtocolErrors()
    };
  }
};
_process = new WeakMap();
_closeCallback = new WeakMap();
_browserCore = new WeakMap();
_defaultViewport2 = new WeakMap();
_targets = new WeakMap();
_browserContexts = new WeakMap();
_browserTarget = new WeakMap();
_connectionEventHandlers = new WeakMap();
_BidiBrowser_instances = new WeakSet();
initialize_fn = function() {
  var _a2;
  __privateGet(this, _browserCore).once("disconnected", () => {
    this.emit("disconnected", void 0);
  });
  (_a2 = __privateGet(this, _process)) == null ? void 0 : _a2.once("close", () => {
    __privateGet(this, _browserCore).dispose("Browser process exited.", true);
    this.connection.dispose();
  });
  for (const [eventName, handler] of __privateGet(this, _connectionEventHandlers)) {
    this.connection.on(eventName, handler);
  }
};
browserName_get = function() {
  return __privateGet(this, _browserCore).session.capabilities.browserName;
};
browserVersion_get = function() {
  return __privateGet(this, _browserCore).session.capabilities.browserVersion;
};
createBrowserContext_fn = function(userContext) {
  const browserContext = new BidiBrowserContext(this, userContext, {
    defaultViewport: __privateGet(this, _defaultViewport2)
  });
  __privateGet(this, _browserContexts).set(userContext, browserContext);
  return browserContext;
};
onContextDomLoaded_fn = function(event) {
  const target = __privateGet(this, _targets).get(event.context);
  if (target) {
    this.emit("targetchanged", target);
    target.browserContext().emit("targetchanged", target);
  }
};
onContextNavigation_fn = function(event) {
  const target = __privateGet(this, _targets).get(event.context);
  if (target) {
    this.emit("targetchanged", target);
    target.browserContext().emit("targetchanged", target);
  }
};
onContextCreated_fn2 = function(event) {
  const context = new BrowsingContext$1(this.connection, event, __privateGet(this, _BidiBrowser_instances, browserName_get));
  this.connection.registerBrowsingContexts(context);
  const browserContext = event.userContext === "default" ? this.defaultBrowserContext() : this.browserContexts().find((browserContext2) => {
    return browserContext2.id === event.userContext;
  });
  if (!browserContext) {
    throw new Error("Missing browser contexts");
  }
  const target = !context.parent ? new BiDiPageTarget(browserContext, context) : new BiDiBrowsingContextTarget(browserContext, context);
  __privateGet(this, _targets).set(event.context, target);
  this.emit("targetcreated", target);
  target.browserContext().emit("targetcreated", target);
  if (context.parent) {
    const topLevel = this.connection.getTopLevelContext(context.parent);
    topLevel.emit(BrowsingContextEvent.Created, context);
  }
};
getTree_fn = async function() {
  const { result } = await this.connection.send("browsingContext.getTree", {});
  for (const context of result.contexts) {
    __privateMethod(this, _BidiBrowser_instances, onContextCreated_fn2).call(this, context);
  }
};
onContextDestroyed_fn2 = async function(event) {
  const context = this.connection.getBrowsingContext(event.context);
  const topLevelContext = this.connection.getTopLevelContext(event.context);
  topLevelContext.emit(BrowsingContextEvent.Destroyed, context);
  const target = __privateGet(this, _targets).get(event.context);
  const page = await (target == null ? void 0 : target.page());
  await (page == null ? void 0 : page.close().catch(debugError));
  __privateGet(this, _targets).delete(event.context);
  if (target) {
    this.emit("targetdestroyed", target);
    target.browserContext().emit("targetdestroyed", target);
  }
};
// TODO: Update generator to include fully module
__publicField(_BidiBrowser, "subscribeModules", [
  "browsingContext",
  "network",
  "log",
  "script"
]);
__publicField(_BidiBrowser, "subscribeCdpEvents", [
  // Coverage
  "cdp.Debugger.scriptParsed",
  "cdp.CSS.styleSheetAdded",
  "cdp.Runtime.executionContextsCleared",
  // Tracing
  "cdp.Tracing.tracingComplete",
  // TODO: subscribe to all CDP events in the future.
  "cdp.Network.requestWillBeSent",
  "cdp.Debugger.scriptParsed",
  "cdp.Page.screencastFrame"
]);
let BidiBrowser = _BidiBrowser;
export {
  BiDiBrowserTarget,
  BiDiBrowsingContextTarget,
  BiDiPageTarget,
  BidiBrowser,
  BidiBrowserContext,
  BidiConnection,
  BidiElementHandle,
  BidiFrame,
  BidiHTTPRequest,
  BidiHTTPResponse,
  BidiJSHandle,
  BidiKeyboard,
  BidiMouse,
  BidiNetworkManager,
  BidiPage,
  BidiRealm,
  BidiTarget,
  BidiTouchscreen,
  BrowsingContext$1 as BrowsingContext,
  BrowsingContextEvent,
  CdpSessionWrapper,
  MAIN_SANDBOX,
  PUPPETEER_SANDBOX,
  Sandbox,
  cdpSessions,
  connectBidiOverCdp,
  createBidiHandle
};
//# sourceMappingURL=bidi-Db8B2RTB.js.map

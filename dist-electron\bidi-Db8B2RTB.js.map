{"version": 3, "file": "bidi-Db8B2RTB.js", "sources": ["../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/Deserializer.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/util.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/JSHandle.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/ElementHandle.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/Serializer.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/Realm.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/BrowsingContext.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/Connection.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/BidiOverCdp.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/core/Navigation.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/core/Realm.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/core/Request.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/core/UserPrompt.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/core/BrowsingContext.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/core/UserContext.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/BrowserContext.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/core/Browser.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/core/Session.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/Dialog.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/EmulationManager.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/ExposedFunction.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/lifecycle.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/Sandbox.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/Frame.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/Input.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/HTTPRequest.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/HTTPResponse.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/NetworkManager.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/Page.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/Target.js", "../node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/bidi/Browser.js"], "sourcesContent": ["/**\n * @license\n * Copyright 2023 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nimport { debugError } from '../common/util.js';\n/**\n * @internal\n */\nexport class BidiDeserializer {\n    static deserializeNumber(value) {\n        switch (value) {\n            case '-0':\n                return -0;\n            case 'NaN':\n                return NaN;\n            case 'Infinity':\n                return Infinity;\n            case '-Infinity':\n                return -Infinity;\n            default:\n                return value;\n        }\n    }\n    static deserializeLocalValue(result) {\n        switch (result.type) {\n            case 'array':\n                return result.value?.map(value => {\n                    return BidiDeserializer.deserializeLocalValue(value);\n                });\n            case 'set':\n                return result.value?.reduce((acc, value) => {\n                    return acc.add(BidiDeserializer.deserializeLocalValue(value));\n                }, new Set());\n            case 'object':\n                return result.value?.reduce((acc, tuple) => {\n                    const { key, value } = BidiDeserializer.deserializeTuple(tuple);\n                    acc[key] = value;\n                    return acc;\n                }, {});\n            case 'map':\n                return result.value?.reduce((acc, tuple) => {\n                    const { key, value } = BidiDeserializer.deserializeTuple(tuple);\n                    return acc.set(key, value);\n                }, new Map());\n            case 'promise':\n                return {};\n            case 'regexp':\n                return new RegExp(result.value.pattern, result.value.flags);\n            case 'date':\n                return new Date(result.value);\n            case 'undefined':\n                return undefined;\n            case 'null':\n                return null;\n            case 'number':\n                return BidiDeserializer.deserializeNumber(result.value);\n            case 'bigint':\n                return BigInt(result.value);\n            case 'boolean':\n                return Boolean(result.value);\n            case 'string':\n                return result.value;\n        }\n        debugError(`Deserialization of type ${result.type} not supported.`);\n        return undefined;\n    }\n    static deserializeTuple([serializedKey, serializedValue]) {\n        const key = typeof serializedKey === 'string'\n            ? serializedKey\n            : BidiDeserializer.deserializeLocalValue(serializedKey);\n        const value = BidiDeserializer.deserializeLocalValue(serializedValue);\n        return { key, value };\n    }\n    static deserialize(result) {\n        if (!result) {\n            debugError('Service did not produce a result.');\n            return undefined;\n        }\n        return BidiDeserializer.deserializeLocalValue(result);\n    }\n}\n//# sourceMappingURL=Deserializer.js.map", "/**\n * @license\n * Copyright 2023 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nimport { PuppeteerURL, debugError } from '../common/util.js';\nimport { BidiDeserializer } from './Deserializer.js';\n/**\n * @internal\n */\nexport async function releaseReference(client, remoteReference) {\n    if (!remoteReference.handle) {\n        return;\n    }\n    await client.connection\n        .send('script.disown', {\n        target: client.target,\n        handles: [remoteReference.handle],\n    })\n        .catch(error => {\n        // Exceptions might happen in case of a page been navigated or closed.\n        // Swallow these since they are harmless and we don't leak anything in this case.\n        debugError(error);\n    });\n}\n/**\n * @internal\n */\nexport function createEvaluationError(details) {\n    if (details.exception.type !== 'error') {\n        return BidiDeserializer.deserialize(details.exception);\n    }\n    const [name = '', ...parts] = details.text.split(': ');\n    const message = parts.join(': ');\n    const error = new Error(message);\n    error.name = name;\n    // The first line is this function which we ignore.\n    const stackLines = [];\n    if (details.stackTrace && stackLines.length < Error.stackTraceLimit) {\n        for (const frame of details.stackTrace.callFrames.reverse()) {\n            if (PuppeteerURL.isPuppeteerURL(frame.url) &&\n                frame.url !== PuppeteerURL.INTERNAL_URL) {\n                const url = PuppeteerURL.parse(frame.url);\n                stackLines.unshift(`    at ${frame.functionName || url.functionName} (${url.functionName} at ${url.siteString}, <anonymous>:${frame.lineNumber}:${frame.columnNumber})`);\n            }\n            else {\n                stackLines.push(`    at ${frame.functionName || '<anonymous>'} (${frame.url}:${frame.lineNumber}:${frame.columnNumber})`);\n            }\n            if (stackLines.length >= Error.stackTraceLimit) {\n                break;\n            }\n        }\n    }\n    error.stack = [details.text, ...stackLines].join('\\n');\n    return error;\n}\n//# sourceMappingURL=util.js.map", "/**\n * @license\n * Copyright 2023 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nimport { JSHandle } from '../api/JSHandle.js';\nimport { UnsupportedOperation } from '../common/Errors.js';\nimport { BidiDeserializer } from './Deserializer.js';\nimport { releaseReference } from './util.js';\n/**\n * @internal\n */\nexport class BidiJSHandle extends JSHandle {\n    #disposed = false;\n    #sandbox;\n    #remoteValue;\n    constructor(sandbox, remoteValue) {\n        super();\n        this.#sandbox = sandbox;\n        this.#remoteValue = remoteValue;\n    }\n    context() {\n        return this.realm.environment.context();\n    }\n    get realm() {\n        return this.#sandbox;\n    }\n    get disposed() {\n        return this.#disposed;\n    }\n    async jsonValue() {\n        return await this.evaluate(value => {\n            return value;\n        });\n    }\n    asElement() {\n        return null;\n    }\n    async dispose() {\n        if (this.#disposed) {\n            return;\n        }\n        this.#disposed = true;\n        if ('handle' in this.#remoteValue) {\n            await releaseReference(this.context(), this.#remoteValue);\n        }\n    }\n    get isPrimitiveValue() {\n        switch (this.#remoteValue.type) {\n            case 'string':\n            case 'number':\n            case 'bigint':\n            case 'boolean':\n            case 'undefined':\n            case 'null':\n                return true;\n            default:\n                return false;\n        }\n    }\n    toString() {\n        if (this.isPrimitiveValue) {\n            return 'JSHandle:' + BidiDeserializer.deserialize(this.#remoteValue);\n        }\n        return 'JSHandle@' + this.#remoteValue.type;\n    }\n    get id() {\n        return 'handle' in this.#remoteValue ? this.#remoteValue.handle : undefined;\n    }\n    remoteValue() {\n        return this.#remoteValue;\n    }\n    remoteObject() {\n        throw new UnsupportedOperation('Not available in WebDriver BiDi');\n    }\n}\n//# sourceMappingURL=JSHandle.js.map", "/**\n * @license\n * Copyright 2023 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nvar __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {\n    var useValue = arguments.length > 2;\n    for (var i = 0; i < initializers.length; i++) {\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n    }\n    return useValue ? value : void 0;\n};\nvar __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n    var _, done = false;\n    for (var i = decorators.length - 1; i >= 0; i--) {\n        var context = {};\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n        if (kind === \"accessor\") {\n            if (result === void 0) continue;\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n            if (_ = accept(result.get)) descriptor.get = _;\n            if (_ = accept(result.set)) descriptor.set = _;\n            if (_ = accept(result.init)) initializers.unshift(_);\n        }\n        else if (_ = accept(result)) {\n            if (kind === \"field\") initializers.unshift(_);\n            else descriptor[key] = _;\n        }\n    }\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\n    done = true;\n};\nvar __addDisposableResource = (this && this.__addDisposableResource) || function (env, value, async) {\n    if (value !== null && value !== void 0) {\n        if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n        var dispose;\n        if (async) {\n            if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n            dispose = value[Symbol.asyncDispose];\n        }\n        if (dispose === void 0) {\n            if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n            dispose = value[Symbol.dispose];\n        }\n        if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n        env.stack.push({ value: value, dispose: dispose, async: async });\n    }\n    else if (async) {\n        env.stack.push({ async: true });\n    }\n    return value;\n};\nvar __disposeResources = (this && this.__disposeResources) || (function (SuppressedError) {\n    return function (env) {\n        function fail(e) {\n            env.error = env.hasError ? new SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n            env.hasError = true;\n        }\n        function next() {\n            while (env.stack.length) {\n                var rec = env.stack.pop();\n                try {\n                    var result = rec.dispose && rec.dispose.call(rec.value);\n                    if (rec.async) return Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n                }\n                catch (e) {\n                    fail(e);\n                }\n            }\n            if (env.hasError) throw env.error;\n        }\n        return next();\n    };\n})(typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n    var e = new Error(message);\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n});\nimport { ElementHandle } from '../api/ElementHandle.js';\nimport { UnsupportedOperation } from '../common/Errors.js';\nimport { throwIfDisposed } from '../util/decorators.js';\nimport { BidiJSHandle } from './JSHandle.js';\n/**\n * @internal\n */\nlet BidiElementHandle = (() => {\n    var _a;\n    let _classSuper = ElementHandle;\n    let _instanceExtraInitializers = [];\n    let _autofill_decorators;\n    let _contentFrame_decorators;\n    return class BidiElementHandle extends _classSuper {\n        static {\n            const _metadata = typeof Symbol === \"function\" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;\n            _autofill_decorators = [throwIfDisposed()];\n            _contentFrame_decorators = [throwIfDisposed(), (_a = ElementHandle).bindIsolatedHandle.bind(_a)];\n            __esDecorate(this, null, _autofill_decorators, { kind: \"method\", name: \"autofill\", static: false, private: false, access: { has: obj => \"autofill\" in obj, get: obj => obj.autofill }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _contentFrame_decorators, { kind: \"method\", name: \"contentFrame\", static: false, private: false, access: { has: obj => \"contentFrame\" in obj, get: obj => obj.contentFrame }, metadata: _metadata }, null, _instanceExtraInitializers);\n            if (_metadata) Object.defineProperty(this, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });\n        }\n        constructor(sandbox, remoteValue) {\n            super(new BidiJSHandle(sandbox, remoteValue));\n            __runInitializers(this, _instanceExtraInitializers);\n        }\n        get realm() {\n            return this.handle.realm;\n        }\n        get frame() {\n            return this.realm.environment;\n        }\n        context() {\n            return this.handle.context();\n        }\n        get isPrimitiveValue() {\n            return this.handle.isPrimitiveValue;\n        }\n        remoteValue() {\n            return this.handle.remoteValue();\n        }\n        async autofill(data) {\n            const client = this.frame.client;\n            const nodeInfo = await client.send('DOM.describeNode', {\n                objectId: this.handle.id,\n            });\n            const fieldId = nodeInfo.node.backendNodeId;\n            const frameId = this.frame._id;\n            await client.send('Autofill.trigger', {\n                fieldId,\n                frameId,\n                card: data.creditCard,\n            });\n        }\n        async contentFrame() {\n            const env_1 = { stack: [], error: void 0, hasError: false };\n            try {\n                const handle = __addDisposableResource(env_1, (await this.evaluateHandle(element => {\n                    if (element instanceof HTMLIFrameElement) {\n                        return element.contentWindow;\n                    }\n                    return;\n                })), false);\n                const value = handle.remoteValue();\n                if (value.type === 'window') {\n                    return this.frame.page().frame(value.value.context);\n                }\n                return null;\n            }\n            catch (e_1) {\n                env_1.error = e_1;\n                env_1.hasError = true;\n            }\n            finally {\n                __disposeResources(env_1);\n            }\n        }\n        uploadFile() {\n            throw new UnsupportedOperation();\n        }\n    };\n})();\nexport { BidiElementHandle };\n//# sourceMappingURL=ElementHandle.js.map", "/**\n * @license\n * Copyright 2023 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nimport { LazyArg } from '../common/LazyArg.js';\nimport { isDate, isPlainObject, isRegExp } from '../common/util.js';\nimport { BidiElementHandle } from './ElementHandle.js';\nimport { BidiJSHandle } from './JSHandle.js';\n/**\n * @internal\n */\nclass UnserializableError extends Error {\n}\n/**\n * @internal\n */\nexport class BidiSerializer {\n    static serializeNumber(arg) {\n        let value;\n        if (Object.is(arg, -0)) {\n            value = '-0';\n        }\n        else if (Object.is(arg, Infinity)) {\n            value = 'Infinity';\n        }\n        else if (Object.is(arg, -Infinity)) {\n            value = '-Infinity';\n        }\n        else if (Object.is(arg, NaN)) {\n            value = 'NaN';\n        }\n        else {\n            value = arg;\n        }\n        return {\n            type: 'number',\n            value,\n        };\n    }\n    static serializeObject(arg) {\n        if (arg === null) {\n            return {\n                type: 'null',\n            };\n        }\n        else if (Array.isArray(arg)) {\n            const parsedArray = arg.map(subArg => {\n                return BidiSerializer.serializeRemoteValue(subArg);\n            });\n            return {\n                type: 'array',\n                value: parsedArray,\n            };\n        }\n        else if (isPlainObject(arg)) {\n            try {\n                JSON.stringify(arg);\n            }\n            catch (error) {\n                if (error instanceof TypeError &&\n                    error.message.startsWith('Converting circular structure to JSON')) {\n                    error.message += ' Recursive objects are not allowed.';\n                }\n                throw error;\n            }\n            const parsedObject = [];\n            for (const key in arg) {\n                parsedObject.push([\n                    BidiSerializer.serializeRemoteValue(key),\n                    BidiSerializer.serializeRemoteValue(arg[key]),\n                ]);\n            }\n            return {\n                type: 'object',\n                value: parsedObject,\n            };\n        }\n        else if (isRegExp(arg)) {\n            return {\n                type: 'regexp',\n                value: {\n                    pattern: arg.source,\n                    flags: arg.flags,\n                },\n            };\n        }\n        else if (isDate(arg)) {\n            return {\n                type: 'date',\n                value: arg.toISOString(),\n            };\n        }\n        throw new UnserializableError('Custom object sterilization not possible. Use plain objects instead.');\n    }\n    static serializeRemoteValue(arg) {\n        switch (typeof arg) {\n            case 'symbol':\n            case 'function':\n                throw new UnserializableError(`Unable to serializable ${typeof arg}`);\n            case 'object':\n                return BidiSerializer.serializeObject(arg);\n            case 'undefined':\n                return {\n                    type: 'undefined',\n                };\n            case 'number':\n                return BidiSerializer.serializeNumber(arg);\n            case 'bigint':\n                return {\n                    type: 'bigint',\n                    value: arg.toString(),\n                };\n            case 'string':\n                return {\n                    type: 'string',\n                    value: arg,\n                };\n            case 'boolean':\n                return {\n                    type: 'boolean',\n                    value: arg,\n                };\n        }\n    }\n    static async serialize(sandbox, arg) {\n        if (arg instanceof LazyArg) {\n            arg = await arg.get(sandbox.realm);\n        }\n        // eslint-disable-next-line rulesdir/use-using -- We want this to continue living.\n        const objectHandle = arg && (arg instanceof BidiJSHandle || arg instanceof BidiElementHandle)\n            ? arg\n            : null;\n        if (objectHandle) {\n            if (objectHandle.realm.environment.context() !==\n                sandbox.environment.context()) {\n                throw new Error('JSHandles can be evaluated only in the context they were created!');\n            }\n            if (objectHandle.disposed) {\n                throw new Error('JSHandle is disposed!');\n            }\n            return objectHandle.remoteValue();\n        }\n        return BidiSerializer.serializeRemoteValue(arg);\n    }\n}\n//# sourceMappingURL=Serializer.js.map", "import * as Bidi from 'chromium-bidi/lib/cjs/protocol/protocol.js';\nimport { EventEmitter } from '../common/EventEmitter.js';\nimport { scriptInjector } from '../common/ScriptInjector.js';\nimport { PuppeteerURL, SOURCE_URL_REGEX, getSourcePuppeteerURLIfAvailable, getSourceUrlComment, isString, } from '../common/util.js';\nimport { disposeSymbol } from '../util/disposable.js';\nimport { stringifyFunction } from '../util/Function.js';\nimport { BidiDeserializer } from './Deserializer.js';\nimport { BidiElementHandle } from './ElementHandle.js';\nimport { BidiJSHandle } from './JSHandle.js';\nimport { BidiSerializer } from './Serializer.js';\nimport { createEvaluationError } from './util.js';\n/**\n * @internal\n */\nexport class BidiRealm extends EventEmitter {\n    connection;\n    #id;\n    #sandbox;\n    constructor(connection) {\n        super();\n        this.connection = connection;\n    }\n    get target() {\n        return {\n            context: this.#sandbox.environment._id,\n            sandbox: this.#sandbox.name,\n        };\n    }\n    handleRealmDestroyed = async (params) => {\n        if (params.realm === this.#id) {\n            // Note: The Realm is destroyed, so in theory the handle should be as\n            // well.\n            this.internalPuppeteerUtil = undefined;\n            this.#sandbox.environment.clearDocumentHandle();\n        }\n    };\n    handleRealmCreated = (params) => {\n        if (params.type === 'window' &&\n            params.context === this.#sandbox.environment._id &&\n            params.sandbox === this.#sandbox.name) {\n            this.#id = params.realm;\n            void this.#sandbox.taskManager.rerunAll();\n        }\n    };\n    setSandbox(sandbox) {\n        this.#sandbox = sandbox;\n        this.connection.on(Bidi.ChromiumBidi.Script.EventNames.RealmCreated, this.handleRealmCreated);\n        this.connection.on(Bidi.ChromiumBidi.Script.EventNames.RealmDestroyed, this.handleRealmDestroyed);\n    }\n    internalPuppeteerUtil;\n    get puppeteerUtil() {\n        const promise = Promise.resolve();\n        scriptInjector.inject(script => {\n            if (this.internalPuppeteerUtil) {\n                void this.internalPuppeteerUtil.then(handle => {\n                    void handle.dispose();\n                });\n            }\n            this.internalPuppeteerUtil = promise.then(() => {\n                return this.evaluateHandle(script);\n            });\n        }, !this.internalPuppeteerUtil);\n        return this.internalPuppeteerUtil;\n    }\n    async evaluateHandle(pageFunction, ...args) {\n        return await this.#evaluate(false, pageFunction, ...args);\n    }\n    async evaluate(pageFunction, ...args) {\n        return await this.#evaluate(true, pageFunction, ...args);\n    }\n    async #evaluate(returnByValue, pageFunction, ...args) {\n        const sourceUrlComment = getSourceUrlComment(getSourcePuppeteerURLIfAvailable(pageFunction)?.toString() ??\n            PuppeteerURL.INTERNAL_URL);\n        const sandbox = this.#sandbox;\n        let responsePromise;\n        const resultOwnership = returnByValue\n            ? \"none\" /* Bidi.Script.ResultOwnership.None */\n            : \"root\" /* Bidi.Script.ResultOwnership.Root */;\n        const serializationOptions = returnByValue\n            ? {}\n            : {\n                maxObjectDepth: 0,\n                maxDomDepth: 0,\n            };\n        if (isString(pageFunction)) {\n            const expression = SOURCE_URL_REGEX.test(pageFunction)\n                ? pageFunction\n                : `${pageFunction}\\n${sourceUrlComment}\\n`;\n            responsePromise = this.connection.send('script.evaluate', {\n                expression,\n                target: this.target,\n                resultOwnership,\n                awaitPromise: true,\n                userActivation: true,\n                serializationOptions,\n            });\n        }\n        else {\n            let functionDeclaration = stringifyFunction(pageFunction);\n            functionDeclaration = SOURCE_URL_REGEX.test(functionDeclaration)\n                ? functionDeclaration\n                : `${functionDeclaration}\\n${sourceUrlComment}\\n`;\n            responsePromise = this.connection.send('script.callFunction', {\n                functionDeclaration,\n                arguments: args.length\n                    ? await Promise.all(args.map(arg => {\n                        return BidiSerializer.serialize(sandbox, arg);\n                    }))\n                    : [],\n                target: this.target,\n                resultOwnership,\n                awaitPromise: true,\n                userActivation: true,\n                serializationOptions,\n            });\n        }\n        const { result } = await responsePromise;\n        if ('type' in result && result.type === 'exception') {\n            throw createEvaluationError(result.exceptionDetails);\n        }\n        return returnByValue\n            ? BidiDeserializer.deserialize(result.result)\n            : createBidiHandle(sandbox, result.result);\n    }\n    [disposeSymbol]() {\n        this.connection.off(Bidi.ChromiumBidi.Script.EventNames.RealmCreated, this.handleRealmCreated);\n        this.connection.off(Bidi.ChromiumBidi.Script.EventNames.RealmDestroyed, this.handleRealmDestroyed);\n    }\n}\n/**\n * @internal\n */\nexport function createBidiHandle(sandbox, result) {\n    if (result.type === 'node' || result.type === 'window') {\n        return new BidiElementHandle(sandbox, result);\n    }\n    return new BidiJSHandle(sandbox, result);\n}\n//# sourceMappingURL=Realm.js.map", "import { CDPSession } from '../api/CDPSession.js';\nimport { TargetCloseError, UnsupportedOperation } from '../common/Errors.js';\nimport { debugError } from '../common/util.js';\nimport { Deferred } from '../util/Deferred.js';\nimport { BidiRealm } from './Realm.js';\n/**\n * @internal\n */\nexport const cdpSessions = new Map();\n/**\n * @internal\n */\nexport class CdpSessionWrapper extends CDPSession {\n    #context;\n    #sessionId = Deferred.create();\n    #detached = false;\n    constructor(context, sessionId) {\n        super();\n        this.#context = context;\n        if (!this.#context.supportsCdp()) {\n            return;\n        }\n        if (sessionId) {\n            this.#sessionId.resolve(sessionId);\n            cdpSessions.set(sessionId, this);\n        }\n        else {\n            context.connection\n                .send('cdp.getSession', {\n                context: context.id,\n            })\n                .then(session => {\n                this.#sessionId.resolve(session.result.session);\n                cdpSessions.set(session.result.session, this);\n            })\n                .catch(err => {\n                this.#sessionId.reject(err);\n            });\n        }\n    }\n    connection() {\n        return undefined;\n    }\n    async send(method, ...paramArgs) {\n        if (!this.#context.supportsCdp()) {\n            throw new UnsupportedOperation('CDP support is required for this feature. The current browser does not support CDP.');\n        }\n        if (this.#detached) {\n            throw new TargetCloseError(`Protocol error (${method}): Session closed. Most likely the page has been closed.`);\n        }\n        const session = await this.#sessionId.valueOrThrow();\n        const { result } = await this.#context.connection.send('cdp.sendCommand', {\n            method: method,\n            params: paramArgs[0],\n            session,\n        });\n        return result.result;\n    }\n    async detach() {\n        cdpSessions.delete(this.id());\n        if (!this.#detached && this.#context.supportsCdp()) {\n            await this.#context.cdpSession.send('Target.detachFromTarget', {\n                sessionId: this.id(),\n            });\n        }\n        this.#detached = true;\n    }\n    id() {\n        const val = this.#sessionId.value();\n        return val instanceof Error || val === undefined ? '' : val;\n    }\n}\n/**\n * Internal events that the BrowsingContext class emits.\n *\n * @internal\n */\n// eslint-disable-next-line @typescript-eslint/no-namespace\nexport var BrowsingContextEvent;\n(function (BrowsingContextEvent) {\n    /**\n     * Emitted on the top-level context, when a descendant context is created.\n     */\n    BrowsingContextEvent.Created = Symbol('BrowsingContext.created');\n    /**\n     * Emitted on the top-level context, when a descendant context or the\n     * top-level context itself is destroyed.\n     */\n    BrowsingContextEvent.Destroyed = Symbol('BrowsingContext.destroyed');\n})(BrowsingContextEvent || (BrowsingContextEvent = {}));\n/**\n * @internal\n */\nexport class BrowsingContext extends BidiRealm {\n    #id;\n    #url;\n    #cdpSession;\n    #parent;\n    #browserName = '';\n    constructor(connection, info, browserName) {\n        super(connection);\n        this.#id = info.context;\n        this.#url = info.url;\n        this.#parent = info.parent;\n        this.#browserName = browserName;\n        this.#cdpSession = new CdpSessionWrapper(this, undefined);\n        this.on('browsingContext.domContentLoaded', this.#updateUrl.bind(this));\n        this.on('browsingContext.fragmentNavigated', this.#updateUrl.bind(this));\n        this.on('browsingContext.load', this.#updateUrl.bind(this));\n    }\n    supportsCdp() {\n        return !this.#browserName.toLowerCase().includes('firefox');\n    }\n    #updateUrl(info) {\n        this.#url = info.url;\n    }\n    createRealmForSandbox() {\n        return new BidiRealm(this.connection);\n    }\n    get url() {\n        return this.#url;\n    }\n    get id() {\n        return this.#id;\n    }\n    get parent() {\n        return this.#parent;\n    }\n    get cdpSession() {\n        return this.#cdpSession;\n    }\n    async sendCdpCommand(method, ...paramArgs) {\n        return await this.#cdpSession.send(method, ...paramArgs);\n    }\n    dispose() {\n        this.removeAllListeners();\n        this.connection.unregisterBrowsingContexts(this.#id);\n        void this.#cdpSession.detach().catch(debugError);\n    }\n}\n//# sourceMappingURL=BrowsingContext.js.map", "/**\n * @license\n * Copyright 2017 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nimport { CallbackRegistry } from '../common/CallbackRegistry.js';\nimport { debug } from '../common/Debug.js';\nimport { EventEmitter } from '../common/EventEmitter.js';\nimport { debugError } from '../common/util.js';\nimport { assert } from '../util/assert.js';\nimport { cdpSessions } from './BrowsingContext.js';\nconst debugProtocolSend = debug('puppeteer:webDriverBiDi:SEND ►');\nconst debugProtocolReceive = debug('puppeteer:webDriverBiDi:RECV ◀');\n/**\n * @internal\n */\nexport class BidiConnection extends EventEmitter {\n    #url;\n    #transport;\n    #delay;\n    #timeout = 0;\n    #closed = false;\n    #callbacks = new CallbackRegistry();\n    #browsingContexts = new Map();\n    #emitters = [];\n    constructor(url, transport, delay = 0, timeout) {\n        super();\n        this.#url = url;\n        this.#delay = delay;\n        this.#timeout = timeout ?? 180000;\n        this.#transport = transport;\n        this.#transport.onmessage = this.onMessage.bind(this);\n        this.#transport.onclose = this.unbind.bind(this);\n    }\n    get closed() {\n        return this.#closed;\n    }\n    get url() {\n        return this.#url;\n    }\n    pipeTo(emitter) {\n        this.#emitters.push(emitter);\n    }\n    emit(type, event) {\n        for (const emitter of this.#emitters) {\n            emitter.emit(type, event);\n        }\n        return super.emit(type, event);\n    }\n    send(method, params) {\n        assert(!this.#closed, 'Protocol error: Connection closed.');\n        return this.#callbacks.create(method, this.#timeout, id => {\n            const stringifiedMessage = JSON.stringify({\n                id,\n                method,\n                params,\n            });\n            debugProtocolSend(stringifiedMessage);\n            this.#transport.send(stringifiedMessage);\n        });\n    }\n    /**\n     * @internal\n     */\n    async onMessage(message) {\n        if (this.#delay) {\n            await new Promise(f => {\n                return setTimeout(f, this.#delay);\n            });\n        }\n        debugProtocolReceive(message);\n        const object = JSON.parse(message);\n        if ('type' in object) {\n            switch (object.type) {\n                case 'success':\n                    this.#callbacks.resolve(object.id, object);\n                    return;\n                case 'error':\n                    if (object.id === null) {\n                        break;\n                    }\n                    this.#callbacks.reject(object.id, createProtocolError(object), object.message);\n                    return;\n                case 'event':\n                    if (isCdpEvent(object)) {\n                        cdpSessions\n                            .get(object.params.session)\n                            ?.emit(object.params.event, object.params.params);\n                        return;\n                    }\n                    this.#maybeEmitOnContext(object);\n                    // SAFETY: We know the method and parameter still match here.\n                    this.emit(object.method, object.params);\n                    return;\n            }\n        }\n        // Even if the response in not in BiDi protocol format but `id` is provided, reject\n        // the callback. This can happen if the endpoint supports CDP instead of BiDi.\n        if ('id' in object) {\n            this.#callbacks.reject(object.id, `Protocol Error. Message is not in BiDi protocol format: '${message}'`, object.message);\n        }\n        debugError(object);\n    }\n    #maybeEmitOnContext(event) {\n        let context;\n        // Context specific events\n        if ('context' in event.params && event.params.context !== null) {\n            context = this.#browsingContexts.get(event.params.context);\n            // `log.entryAdded` specific context\n        }\n        else if ('source' in event.params &&\n            event.params.source.context !== undefined) {\n            context = this.#browsingContexts.get(event.params.source.context);\n        }\n        context?.emit(event.method, event.params);\n    }\n    registerBrowsingContexts(context) {\n        this.#browsingContexts.set(context.id, context);\n    }\n    getBrowsingContext(contextId) {\n        const currentContext = this.#browsingContexts.get(contextId);\n        if (!currentContext) {\n            throw new Error(`BrowsingContext ${contextId} does not exist.`);\n        }\n        return currentContext;\n    }\n    getTopLevelContext(contextId) {\n        let currentContext = this.#browsingContexts.get(contextId);\n        if (!currentContext) {\n            throw new Error(`BrowsingContext ${contextId} does not exist.`);\n        }\n        while (currentContext.parent) {\n            contextId = currentContext.parent;\n            currentContext = this.#browsingContexts.get(contextId);\n            if (!currentContext) {\n                throw new Error(`BrowsingContext ${contextId} does not exist.`);\n            }\n        }\n        return currentContext;\n    }\n    unregisterBrowsingContexts(id) {\n        this.#browsingContexts.delete(id);\n    }\n    /**\n     * Unbinds the connection, but keeps the transport open. Useful when the transport will\n     * be reused by other connection e.g. with different protocol.\n     * @internal\n     */\n    unbind() {\n        if (this.#closed) {\n            return;\n        }\n        this.#closed = true;\n        // Both may still be invoked and produce errors\n        this.#transport.onmessage = () => { };\n        this.#transport.onclose = () => { };\n        this.#browsingContexts.clear();\n        this.#callbacks.clear();\n    }\n    /**\n     * Unbinds the connection and closes the transport.\n     */\n    dispose() {\n        this.unbind();\n        this.#transport.close();\n    }\n    getPendingProtocolErrors() {\n        return this.#callbacks.getPendingProtocolErrors();\n    }\n}\n/**\n * @internal\n */\nfunction createProtocolError(object) {\n    let message = `${object.error} ${object.message}`;\n    if (object.stacktrace) {\n        message += ` ${object.stacktrace}`;\n    }\n    return message;\n}\nfunction isCdpEvent(event) {\n    return event.method.startsWith('cdp.');\n}\n//# sourceMappingURL=Connection.js.map", "/**\n * @license\n * Copyright 2023 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nimport * as BidiMapper from 'chromium-bidi/lib/cjs/bidiMapper/BidiMapper.js';\nimport { debug } from '../common/Debug.js';\nimport { TargetCloseError } from '../common/Errors.js';\nimport { BidiConnection } from './Connection.js';\nconst bidiServerLogger = (prefix, ...args) => {\n    debug(`bidi:${prefix}`)(args);\n};\n/**\n * @internal\n */\nexport async function connectBidiOverCdp(cdp, \n// TODO: replace with `BidiMapper.MapperOptions`, once it's exported in\n//  https://github.com/puppeteer/puppeteer/pull/11415.\noptions) {\n    const transportBiDi = new NoOpTransport();\n    const cdpConnectionAdapter = new CdpConnectionAdapter(cdp);\n    const pptrTransport = {\n        send(message) {\n            // Forwards a BiDi command sent by <PERSON><PERSON><PERSON><PERSON> to the input of the BidiServer.\n            transportBiDi.emitMessage(JSON.parse(message));\n        },\n        close() {\n            bidiServer.close();\n            cdpConnectionAdapter.close();\n            cdp.dispose();\n        },\n        onmessage(_message) {\n            // The method is overridden by the Connection.\n        },\n    };\n    transportBiDi.on('bidiResponse', (message) => {\n        // Forwards a BiDi event sent by BidiServer to Puppeteer.\n        pptrTransport.onmessage(JSON.stringify(message));\n    });\n    const pptrBiDiConnection = new BidiConnection(cdp.url(), pptrTransport);\n    const bidiServer = await BidiMapper.BidiServer.createAndStart(transportBiDi, cdpConnectionAdapter, \n    // TODO: most likely need a little bit of refactoring\n    cdpConnectionAdapter.browserClient(), '', options, undefined, bidiServerLogger);\n    return pptrBiDiConnection;\n}\n/**\n * Manages CDPSessions for BidiServer.\n * @internal\n */\nclass CdpConnectionAdapter {\n    #cdp;\n    #adapters = new Map();\n    #browserCdpConnection;\n    constructor(cdp) {\n        this.#cdp = cdp;\n        this.#browserCdpConnection = new CDPClientAdapter(cdp);\n    }\n    browserClient() {\n        return this.#browserCdpConnection;\n    }\n    getCdpClient(id) {\n        const session = this.#cdp.session(id);\n        if (!session) {\n            throw new Error(`Unknown CDP session with id ${id}`);\n        }\n        if (!this.#adapters.has(session)) {\n            const adapter = new CDPClientAdapter(session, id, this.#browserCdpConnection);\n            this.#adapters.set(session, adapter);\n            return adapter;\n        }\n        return this.#adapters.get(session);\n    }\n    close() {\n        this.#browserCdpConnection.close();\n        for (const adapter of this.#adapters.values()) {\n            adapter.close();\n        }\n    }\n}\n/**\n * Wrapper on top of CDPSession/CDPConnection to satisfy CDP interface that\n * BidiServer needs.\n *\n * @internal\n */\nclass CDPClientAdapter extends BidiMapper.EventEmitter {\n    #closed = false;\n    #client;\n    sessionId = undefined;\n    #browserClient;\n    constructor(client, sessionId, browserClient) {\n        super();\n        this.#client = client;\n        this.sessionId = sessionId;\n        this.#browserClient = browserClient;\n        this.#client.on('*', this.#forwardMessage);\n    }\n    browserClient() {\n        return this.#browserClient;\n    }\n    #forwardMessage = (method, event) => {\n        this.emit(method, event);\n    };\n    async sendCommand(method, ...params) {\n        if (this.#closed) {\n            return;\n        }\n        try {\n            return await this.#client.send(method, ...params);\n        }\n        catch (err) {\n            if (this.#closed) {\n                return;\n            }\n            throw err;\n        }\n    }\n    close() {\n        this.#client.off('*', this.#forwardMessage);\n        this.#closed = true;\n    }\n    isCloseError(error) {\n        return error instanceof TargetCloseError;\n    }\n}\n/**\n * This transport is given to the BiDi server instance and allows Puppeteer\n * to send and receive commands to the BiDiServer.\n * @internal\n */\nclass NoOpTransport extends BidiMapper.EventEmitter {\n    #onMessage = async (_m) => {\n        return;\n    };\n    emitMessage(message) {\n        void this.#onMessage(message);\n    }\n    setOnMessage(onMessage) {\n        this.#onMessage = onMessage;\n    }\n    async sendMessage(message) {\n        this.emit('bidiResponse', message);\n    }\n    close() {\n        this.#onMessage = async (_m) => {\n            return;\n        };\n    }\n}\n//# sourceMappingURL=BidiOverCdp.js.map", "/**\n * @license\n * Copyright 2024 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nvar __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {\n    var useValue = arguments.length > 2;\n    for (var i = 0; i < initializers.length; i++) {\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n    }\n    return useValue ? value : void 0;\n};\nvar __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n    var _, done = false;\n    for (var i = decorators.length - 1; i >= 0; i--) {\n        var context = {};\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n        if (kind === \"accessor\") {\n            if (result === void 0) continue;\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n            if (_ = accept(result.get)) descriptor.get = _;\n            if (_ = accept(result.set)) descriptor.set = _;\n            if (_ = accept(result.init)) initializers.unshift(_);\n        }\n        else if (_ = accept(result)) {\n            if (kind === \"field\") initializers.unshift(_);\n            else descriptor[key] = _;\n        }\n    }\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\n    done = true;\n};\nimport { EventEmitter } from '../../common/EventEmitter.js';\nimport { inertIfDisposed } from '../../util/decorators.js';\nimport { Deferred } from '../../util/Deferred.js';\nimport { DisposableStack, disposeSymbol } from '../../util/disposable.js';\n/**\n * @internal\n */\nlet Navigation = (() => {\n    let _classSuper = EventEmitter;\n    let _instanceExtraInitializers = [];\n    let _dispose_decorators;\n    return class Navigation extends _classSuper {\n        static {\n            const _metadata = typeof Symbol === \"function\" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;\n            __esDecorate(this, null, _dispose_decorators, { kind: \"method\", name: \"dispose\", static: false, private: false, access: { has: obj => \"dispose\" in obj, get: obj => obj.dispose }, metadata: _metadata }, null, _instanceExtraInitializers);\n            if (_metadata) Object.defineProperty(this, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });\n        }\n        static from(context) {\n            const navigation = new Navigation(context);\n            navigation.#initialize();\n            return navigation;\n        }\n        // keep-sorted start\n        #request = (__runInitializers(this, _instanceExtraInitializers), void 0);\n        #browsingContext;\n        #disposables = new DisposableStack();\n        #id = new Deferred();\n        // keep-sorted end\n        constructor(context) {\n            super();\n            // keep-sorted start\n            this.#browsingContext = context;\n            // keep-sorted end\n        }\n        #initialize() {\n            const browsingContextEmitter = this.#disposables.use(new EventEmitter(this.#browsingContext));\n            browsingContextEmitter.once('closed', () => {\n                this.emit('failed', {\n                    url: this.#browsingContext.url,\n                    timestamp: new Date(),\n                });\n                this.dispose();\n            });\n            this.#browsingContext.on('request', ({ request }) => {\n                if (request.navigation === this.#id.value()) {\n                    this.#request = request;\n                    this.emit('request', request);\n                }\n            });\n            const sessionEmitter = this.#disposables.use(new EventEmitter(this.#session));\n            // To get the navigation ID if any.\n            for (const eventName of [\n                'browsingContext.domContentLoaded',\n                'browsingContext.load',\n            ]) {\n                sessionEmitter.on(eventName, info => {\n                    if (info.context !== this.#browsingContext.id) {\n                        return;\n                    }\n                    if (!info.navigation) {\n                        return;\n                    }\n                    if (!this.#id.resolved()) {\n                        this.#id.resolve(info.navigation);\n                    }\n                });\n            }\n            for (const [eventName, event] of [\n                ['browsingContext.fragmentNavigated', 'fragment'],\n                ['browsingContext.navigationFailed', 'failed'],\n                ['browsingContext.navigationAborted', 'aborted'],\n            ]) {\n                sessionEmitter.on(eventName, info => {\n                    if (info.context !== this.#browsingContext.id) {\n                        return;\n                    }\n                    if (!info.navigation) {\n                        return;\n                    }\n                    if (!this.#id.resolved()) {\n                        this.#id.resolve(info.navigation);\n                    }\n                    if (this.#id.value() !== info.navigation) {\n                        return;\n                    }\n                    this.emit(event, {\n                        url: info.url,\n                        timestamp: new Date(info.timestamp),\n                    });\n                    this.dispose();\n                });\n            }\n        }\n        // keep-sorted start block=yes\n        get #session() {\n            return this.#browsingContext.userContext.browser.session;\n        }\n        get disposed() {\n            return this.#disposables.disposed;\n        }\n        get request() {\n            return this.#request;\n        }\n        // keep-sorted end\n        dispose() {\n            this[disposeSymbol]();\n        }\n        [(_dispose_decorators = [inertIfDisposed], disposeSymbol)]() {\n            this.#disposables.dispose();\n            super[disposeSymbol]();\n        }\n    };\n})();\nexport { Navigation };\n//# sourceMappingURL=Navigation.js.map", "/**\n * @license\n * Copyright 2024 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nvar __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {\n    var useValue = arguments.length > 2;\n    for (var i = 0; i < initializers.length; i++) {\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n    }\n    return useValue ? value : void 0;\n};\nvar __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n    var _, done = false;\n    for (var i = decorators.length - 1; i >= 0; i--) {\n        var context = {};\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n        if (kind === \"accessor\") {\n            if (result === void 0) continue;\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n            if (_ = accept(result.get)) descriptor.get = _;\n            if (_ = accept(result.set)) descriptor.set = _;\n            if (_ = accept(result.init)) initializers.unshift(_);\n        }\n        else if (_ = accept(result)) {\n            if (kind === \"field\") initializers.unshift(_);\n            else descriptor[key] = _;\n        }\n    }\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\n    done = true;\n};\nimport { EventEmitter } from '../../common/EventEmitter.js';\nimport { inertIfDisposed, throwIfDisposed } from '../../util/decorators.js';\nimport { DisposableStack, disposeSymbol } from '../../util/disposable.js';\n/**\n * @internal\n */\nlet Realm = (() => {\n    let _classSuper = EventEmitter;\n    let _instanceExtraInitializers = [];\n    let _dispose_decorators;\n    let _disown_decorators;\n    let _callFunction_decorators;\n    let _evaluate_decorators;\n    return class Realm extends _classSuper {\n        static {\n            const _metadata = typeof Symbol === \"function\" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;\n            __esDecorate(this, null, _dispose_decorators, { kind: \"method\", name: \"dispose\", static: false, private: false, access: { has: obj => \"dispose\" in obj, get: obj => obj.dispose }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _disown_decorators, { kind: \"method\", name: \"disown\", static: false, private: false, access: { has: obj => \"disown\" in obj, get: obj => obj.disown }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _callFunction_decorators, { kind: \"method\", name: \"callFunction\", static: false, private: false, access: { has: obj => \"callFunction\" in obj, get: obj => obj.callFunction }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _evaluate_decorators, { kind: \"method\", name: \"evaluate\", static: false, private: false, access: { has: obj => \"evaluate\" in obj, get: obj => obj.evaluate }, metadata: _metadata }, null, _instanceExtraInitializers);\n            if (_metadata) Object.defineProperty(this, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });\n        }\n        // keep-sorted start\n        #reason = (__runInitializers(this, _instanceExtraInitializers), void 0);\n        disposables = new DisposableStack();\n        id;\n        origin;\n        // keep-sorted end\n        constructor(id, origin) {\n            super();\n            // keep-sorted start\n            this.id = id;\n            this.origin = origin;\n            // keep-sorted end\n        }\n        initialize() {\n            const sessionEmitter = this.disposables.use(new EventEmitter(this.session));\n            sessionEmitter.on('script.realmDestroyed', info => {\n                if (info.realm !== this.id) {\n                    return;\n                }\n                this.dispose('Realm already destroyed.');\n            });\n        }\n        // keep-sorted start block=yes\n        get disposed() {\n            return this.#reason !== undefined;\n        }\n        get target() {\n            return { realm: this.id };\n        }\n        // keep-sorted end\n        dispose(reason) {\n            this.#reason = reason;\n            this[disposeSymbol]();\n        }\n        async disown(handles) {\n            await this.session.send('script.disown', {\n                target: this.target,\n                handles,\n            });\n        }\n        async callFunction(functionDeclaration, awaitPromise, options = {}) {\n            const { result } = await this.session.send('script.callFunction', {\n                functionDeclaration,\n                awaitPromise,\n                target: this.target,\n                ...options,\n            });\n            return result;\n        }\n        async evaluate(expression, awaitPromise, options = {}) {\n            const { result } = await this.session.send('script.evaluate', {\n                expression,\n                awaitPromise,\n                target: this.target,\n                ...options,\n            });\n            return result;\n        }\n        [(_dispose_decorators = [inertIfDisposed], _disown_decorators = [throwIfDisposed(realm => {\n                // SAFETY: Disposal implies this exists.\n                return realm.#reason;\n            })], _callFunction_decorators = [throwIfDisposed(realm => {\n                // SAFETY: Disposal implies this exists.\n                return realm.#reason;\n            })], _evaluate_decorators = [throwIfDisposed(realm => {\n                // SAFETY: Disposal implies this exists.\n                return realm.#reason;\n            })], disposeSymbol)]() {\n            this.#reason ??=\n                'Realm already destroyed, probably because all associated browsing contexts closed.';\n            this.emit('destroyed', { reason: this.#reason });\n            this.disposables.dispose();\n            super[disposeSymbol]();\n        }\n    };\n})();\nexport { Realm };\n/**\n * @internal\n */\nexport class WindowRealm extends Realm {\n    static from(context, sandbox) {\n        const realm = new WindowRealm(context, sandbox);\n        realm.initialize();\n        return realm;\n    }\n    // keep-sorted start\n    browsingContext;\n    sandbox;\n    // keep-sorted end\n    #workers = {\n        dedicated: new Map(),\n        shared: new Map(),\n    };\n    constructor(context, sandbox) {\n        super('', '');\n        // keep-sorted start\n        this.browsingContext = context;\n        this.sandbox = sandbox;\n        // keep-sorted end\n    }\n    initialize() {\n        super.initialize();\n        const sessionEmitter = this.disposables.use(new EventEmitter(this.session));\n        sessionEmitter.on('script.realmCreated', info => {\n            if (info.type !== 'window') {\n                return;\n            }\n            this.id = info.realm;\n            this.origin = info.origin;\n        });\n        sessionEmitter.on('script.realmCreated', info => {\n            if (info.type !== 'dedicated-worker') {\n                return;\n            }\n            if (!info.owners.includes(this.id)) {\n                return;\n            }\n            const realm = DedicatedWorkerRealm.from(this, info.realm, info.origin);\n            this.#workers.dedicated.set(realm.id, realm);\n            const realmEmitter = this.disposables.use(new EventEmitter(realm));\n            realmEmitter.once('destroyed', () => {\n                realmEmitter.removeAllListeners();\n                this.#workers.dedicated.delete(realm.id);\n            });\n            this.emit('worker', realm);\n        });\n        this.browsingContext.userContext.browser.on('sharedworker', ({ realm }) => {\n            if (!realm.owners.has(this)) {\n                return;\n            }\n            this.#workers.shared.set(realm.id, realm);\n            const realmEmitter = this.disposables.use(new EventEmitter(realm));\n            realmEmitter.once('destroyed', () => {\n                realmEmitter.removeAllListeners();\n                this.#workers.shared.delete(realm.id);\n            });\n            this.emit('sharedworker', realm);\n        });\n    }\n    get session() {\n        return this.browsingContext.userContext.browser.session;\n    }\n    get target() {\n        return { context: this.browsingContext.id, sandbox: this.sandbox };\n    }\n}\n/**\n * @internal\n */\nexport class DedicatedWorkerRealm extends Realm {\n    static from(owner, id, origin) {\n        const realm = new DedicatedWorkerRealm(owner, id, origin);\n        realm.initialize();\n        return realm;\n    }\n    // keep-sorted start\n    #workers = new Map();\n    owners;\n    // keep-sorted end\n    constructor(owner, id, origin) {\n        super(id, origin);\n        this.owners = new Set([owner]);\n    }\n    initialize() {\n        super.initialize();\n        const sessionEmitter = this.disposables.use(new EventEmitter(this.session));\n        sessionEmitter.on('script.realmCreated', info => {\n            if (info.type !== 'dedicated-worker') {\n                return;\n            }\n            if (!info.owners.includes(this.id)) {\n                return;\n            }\n            const realm = DedicatedWorkerRealm.from(this, info.realm, info.origin);\n            this.#workers.set(realm.id, realm);\n            const realmEmitter = this.disposables.use(new EventEmitter(realm));\n            realmEmitter.once('destroyed', () => {\n                this.#workers.delete(realm.id);\n            });\n            this.emit('worker', realm);\n        });\n    }\n    get session() {\n        // SAFETY: At least one owner will exist.\n        return this.owners.values().next().value.session;\n    }\n}\n/**\n * @internal\n */\nexport class SharedWorkerRealm extends Realm {\n    static from(owners, id, origin) {\n        const realm = new SharedWorkerRealm(owners, id, origin);\n        realm.initialize();\n        return realm;\n    }\n    // keep-sorted start\n    #workers = new Map();\n    owners;\n    // keep-sorted end\n    constructor(owners, id, origin) {\n        super(id, origin);\n        this.owners = new Set(owners);\n    }\n    initialize() {\n        super.initialize();\n        const sessionEmitter = this.disposables.use(new EventEmitter(this.session));\n        sessionEmitter.on('script.realmCreated', info => {\n            if (info.type !== 'dedicated-worker') {\n                return;\n            }\n            if (!info.owners.includes(this.id)) {\n                return;\n            }\n            const realm = DedicatedWorkerRealm.from(this, info.realm, info.origin);\n            this.#workers.set(realm.id, realm);\n            const realmEmitter = this.disposables.use(new EventEmitter(realm));\n            realmEmitter.once('destroyed', () => {\n                this.#workers.delete(realm.id);\n            });\n            this.emit('worker', realm);\n        });\n    }\n    get session() {\n        // SAFETY: At least one owner will exist.\n        return this.owners.values().next().value.session;\n    }\n}\n//# sourceMappingURL=Realm.js.map", "/**\n * @license\n * Copyright 2024 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nvar __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {\n    var useValue = arguments.length > 2;\n    for (var i = 0; i < initializers.length; i++) {\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n    }\n    return useValue ? value : void 0;\n};\nvar __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n    var _, done = false;\n    for (var i = decorators.length - 1; i >= 0; i--) {\n        var context = {};\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n        if (kind === \"accessor\") {\n            if (result === void 0) continue;\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n            if (_ = accept(result.get)) descriptor.get = _;\n            if (_ = accept(result.set)) descriptor.set = _;\n            if (_ = accept(result.init)) initializers.unshift(_);\n        }\n        else if (_ = accept(result)) {\n            if (kind === \"field\") initializers.unshift(_);\n            else descriptor[key] = _;\n        }\n    }\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\n    done = true;\n};\nimport { EventEmitter } from '../../common/EventEmitter.js';\nimport { inertIfDisposed } from '../../util/decorators.js';\nimport { DisposableStack, disposeSymbol } from '../../util/disposable.js';\n/**\n * @internal\n */\nlet Request = (() => {\n    var _a;\n    let _classSuper = EventEmitter;\n    let _instanceExtraInitializers = [];\n    let _dispose_decorators;\n    return class Request extends _classSuper {\n        static {\n            const _metadata = typeof Symbol === \"function\" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;\n            __esDecorate(this, null, _dispose_decorators, { kind: \"method\", name: \"dispose\", static: false, private: false, access: { has: obj => \"dispose\" in obj, get: obj => obj.dispose }, metadata: _metadata }, null, _instanceExtraInitializers);\n            if (_metadata) Object.defineProperty(this, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });\n        }\n        static from(browsingContext, event) {\n            const request = new Request(browsingContext, event);\n            request.#initialize();\n            return request;\n        }\n        // keep-sorted start\n        #error = (__runInitializers(this, _instanceExtraInitializers), void 0);\n        #redirect;\n        #response;\n        #browsingContext;\n        #disposables = new DisposableStack();\n        #event;\n        // keep-sorted end\n        constructor(browsingContext, event) {\n            super();\n            // keep-sorted start\n            this.#browsingContext = browsingContext;\n            this.#event = event;\n            // keep-sorted end\n        }\n        #initialize() {\n            const browsingContextEmitter = this.#disposables.use(new EventEmitter(this.#browsingContext));\n            browsingContextEmitter.once('closed', ({ reason }) => {\n                this.#error = reason;\n                this.emit('error', this.#error);\n                this.dispose();\n            });\n            const sessionEmitter = this.#disposables.use(new EventEmitter(this.#session));\n            sessionEmitter.on('network.beforeRequestSent', event => {\n                if (event.context !== this.#browsingContext.id) {\n                    return;\n                }\n                if (event.request.request !== this.id) {\n                    return;\n                }\n                this.#redirect = Request.from(this.#browsingContext, event);\n                this.emit('redirect', this.#redirect);\n                this.dispose();\n            });\n            sessionEmitter.on('network.fetchError', event => {\n                if (event.context !== this.#browsingContext.id) {\n                    return;\n                }\n                if (event.request.request !== this.id) {\n                    return;\n                }\n                this.#error = event.errorText;\n                this.emit('error', this.#error);\n                this.dispose();\n            });\n            sessionEmitter.on('network.responseCompleted', event => {\n                if (event.context !== this.#browsingContext.id) {\n                    return;\n                }\n                if (event.request.request !== this.id) {\n                    return;\n                }\n                this.#response = event.response;\n                this.emit('success', this.#response);\n                this.dispose();\n            });\n        }\n        // keep-sorted start block=yes\n        get #session() {\n            return this.#browsingContext.userContext.browser.session;\n        }\n        get disposed() {\n            return this.#disposables.disposed;\n        }\n        get error() {\n            return this.#error;\n        }\n        get headers() {\n            return this.#event.request.headers;\n        }\n        get id() {\n            return this.#event.request.request;\n        }\n        get initiator() {\n            return this.#event.initiator;\n        }\n        get method() {\n            return this.#event.request.method;\n        }\n        get navigation() {\n            return this.#event.navigation ?? undefined;\n        }\n        get redirect() {\n            return this.redirect;\n        }\n        get response() {\n            return this.#response;\n        }\n        get url() {\n            return this.#event.request.url;\n        }\n        // keep-sorted end\n        dispose() {\n            this[disposeSymbol]();\n        }\n        [(_dispose_decorators = [inertIfDisposed], disposeSymbol)]() {\n            this.#disposables.dispose();\n            super[disposeSymbol]();\n        }\n    };\n})();\nexport { Request };\n//# sourceMappingURL=Request.js.map", "/**\n * @license\n * Copyright 2024 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nvar __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {\n    var useValue = arguments.length > 2;\n    for (var i = 0; i < initializers.length; i++) {\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n    }\n    return useValue ? value : void 0;\n};\nvar __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n    var _, done = false;\n    for (var i = decorators.length - 1; i >= 0; i--) {\n        var context = {};\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n        if (kind === \"accessor\") {\n            if (result === void 0) continue;\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n            if (_ = accept(result.get)) descriptor.get = _;\n            if (_ = accept(result.set)) descriptor.set = _;\n            if (_ = accept(result.init)) initializers.unshift(_);\n        }\n        else if (_ = accept(result)) {\n            if (kind === \"field\") initializers.unshift(_);\n            else descriptor[key] = _;\n        }\n    }\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\n    done = true;\n};\nimport { EventEmitter } from '../../common/EventEmitter.js';\nimport { inertIfDisposed, throwIfDisposed } from '../../util/decorators.js';\nimport { DisposableStack, disposeSymbol } from '../../util/disposable.js';\n/**\n * @internal\n */\nlet UserPrompt = (() => {\n    let _classSuper = EventEmitter;\n    let _instanceExtraInitializers = [];\n    let _dispose_decorators;\n    let _handle_decorators;\n    return class UserPrompt extends _classSuper {\n        static {\n            const _metadata = typeof Symbol === \"function\" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;\n            __esDecorate(this, null, _dispose_decorators, { kind: \"method\", name: \"dispose\", static: false, private: false, access: { has: obj => \"dispose\" in obj, get: obj => obj.dispose }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _handle_decorators, { kind: \"method\", name: \"handle\", static: false, private: false, access: { has: obj => \"handle\" in obj, get: obj => obj.handle }, metadata: _metadata }, null, _instanceExtraInitializers);\n            if (_metadata) Object.defineProperty(this, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });\n        }\n        static from(browsingContext, info) {\n            const userPrompt = new UserPrompt(browsingContext, info);\n            userPrompt.#initialize();\n            return userPrompt;\n        }\n        // keep-sorted start\n        #reason = (__runInitializers(this, _instanceExtraInitializers), void 0);\n        #result;\n        #disposables = new DisposableStack();\n        browsingContext;\n        info;\n        // keep-sorted end\n        constructor(context, info) {\n            super();\n            // keep-sorted start\n            this.browsingContext = context;\n            this.info = info;\n            // keep-sorted end\n        }\n        #initialize() {\n            const browserContextEmitter = this.#disposables.use(new EventEmitter(this.browsingContext));\n            browserContextEmitter.once('closed', ({ reason }) => {\n                this.dispose(`User prompt already closed: ${reason}`);\n            });\n            const sessionEmitter = this.#disposables.use(new EventEmitter(this.#session));\n            sessionEmitter.on('browsingContext.userPromptClosed', parameters => {\n                if (parameters.context !== this.browsingContext.id) {\n                    return;\n                }\n                this.#result = parameters;\n                this.emit('handled', parameters);\n                this.dispose('User prompt already handled.');\n            });\n        }\n        // keep-sorted start block=yes\n        get #session() {\n            return this.browsingContext.userContext.browser.session;\n        }\n        get closed() {\n            return this.#reason !== undefined;\n        }\n        get disposed() {\n            return this.closed;\n        }\n        get handled() {\n            return this.#result !== undefined;\n        }\n        get result() {\n            return this.#result;\n        }\n        // keep-sorted end\n        dispose(reason) {\n            this.#reason = reason;\n            this[disposeSymbol]();\n        }\n        async handle(options = {}) {\n            await this.#session.send('browsingContext.handleUserPrompt', {\n                ...options,\n                context: this.info.context,\n            });\n            // SAFETY: `handled` is triggered before the above promise resolved.\n            return this.#result;\n        }\n        [(_dispose_decorators = [inertIfDisposed], _handle_decorators = [throwIfDisposed(prompt => {\n                // SAFETY: Disposal implies this exists.\n                return prompt.#reason;\n            })], disposeSymbol)]() {\n            this.#reason ??=\n                'User prompt already closed, probably because the associated browsing context was destroyed.';\n            this.emit('closed', { reason: this.#reason });\n            this.#disposables.dispose();\n            super[disposeSymbol]();\n        }\n    };\n})();\nexport { UserPrompt };\n//# sourceMappingURL=UserPrompt.js.map", "/**\n * @license\n * Copyright 2024 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nvar __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {\n    var useValue = arguments.length > 2;\n    for (var i = 0; i < initializers.length; i++) {\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n    }\n    return useValue ? value : void 0;\n};\nvar __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n    var _, done = false;\n    for (var i = decorators.length - 1; i >= 0; i--) {\n        var context = {};\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n        if (kind === \"accessor\") {\n            if (result === void 0) continue;\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n            if (_ = accept(result.get)) descriptor.get = _;\n            if (_ = accept(result.set)) descriptor.set = _;\n            if (_ = accept(result.init)) initializers.unshift(_);\n        }\n        else if (_ = accept(result)) {\n            if (kind === \"field\") initializers.unshift(_);\n            else descriptor[key] = _;\n        }\n    }\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\n    done = true;\n};\nimport { EventEmitter } from '../../common/EventEmitter.js';\nimport { inertIfDisposed, throwIfDisposed } from '../../util/decorators.js';\nimport { DisposableStack, disposeSymbol } from '../../util/disposable.js';\nimport { Navigation } from './Navigation.js';\nimport { WindowRealm } from './Realm.js';\nimport { Request } from './Request.js';\nimport { UserPrompt } from './UserPrompt.js';\n/**\n * @internal\n */\nlet BrowsingContext = (() => {\n    var _a;\n    let _classSuper = EventEmitter;\n    let _instanceExtraInitializers = [];\n    let _dispose_decorators;\n    let _activate_decorators;\n    let _captureScreenshot_decorators;\n    let _close_decorators;\n    let _traverseHistory_decorators;\n    let _navigate_decorators;\n    let _reload_decorators;\n    let _print_decorators;\n    let _handleUserPrompt_decorators;\n    let _setViewport_decorators;\n    let _performActions_decorators;\n    let _releaseActions_decorators;\n    let _createWindowRealm_decorators;\n    let _addPreloadScript_decorators;\n    let _removePreloadScript_decorators;\n    return class BrowsingContext extends _classSuper {\n        static {\n            const _metadata = typeof Symbol === \"function\" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;\n            __esDecorate(this, null, _dispose_decorators, { kind: \"method\", name: \"dispose\", static: false, private: false, access: { has: obj => \"dispose\" in obj, get: obj => obj.dispose }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _activate_decorators, { kind: \"method\", name: \"activate\", static: false, private: false, access: { has: obj => \"activate\" in obj, get: obj => obj.activate }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _captureScreenshot_decorators, { kind: \"method\", name: \"captureScreenshot\", static: false, private: false, access: { has: obj => \"captureScreenshot\" in obj, get: obj => obj.captureScreenshot }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _close_decorators, { kind: \"method\", name: \"close\", static: false, private: false, access: { has: obj => \"close\" in obj, get: obj => obj.close }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _traverseHistory_decorators, { kind: \"method\", name: \"traverseHistory\", static: false, private: false, access: { has: obj => \"traverseHistory\" in obj, get: obj => obj.traverseHistory }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _navigate_decorators, { kind: \"method\", name: \"navigate\", static: false, private: false, access: { has: obj => \"navigate\" in obj, get: obj => obj.navigate }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _reload_decorators, { kind: \"method\", name: \"reload\", static: false, private: false, access: { has: obj => \"reload\" in obj, get: obj => obj.reload }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _print_decorators, { kind: \"method\", name: \"print\", static: false, private: false, access: { has: obj => \"print\" in obj, get: obj => obj.print }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _handleUserPrompt_decorators, { kind: \"method\", name: \"handleUserPrompt\", static: false, private: false, access: { has: obj => \"handleUserPrompt\" in obj, get: obj => obj.handleUserPrompt }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _setViewport_decorators, { kind: \"method\", name: \"setViewport\", static: false, private: false, access: { has: obj => \"setViewport\" in obj, get: obj => obj.setViewport }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _performActions_decorators, { kind: \"method\", name: \"performActions\", static: false, private: false, access: { has: obj => \"performActions\" in obj, get: obj => obj.performActions }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _releaseActions_decorators, { kind: \"method\", name: \"releaseActions\", static: false, private: false, access: { has: obj => \"releaseActions\" in obj, get: obj => obj.releaseActions }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _createWindowRealm_decorators, { kind: \"method\", name: \"createWindowRealm\", static: false, private: false, access: { has: obj => \"createWindowRealm\" in obj, get: obj => obj.createWindowRealm }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _addPreloadScript_decorators, { kind: \"method\", name: \"addPreloadScript\", static: false, private: false, access: { has: obj => \"addPreloadScript\" in obj, get: obj => obj.addPreloadScript }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _removePreloadScript_decorators, { kind: \"method\", name: \"removePreloadScript\", static: false, private: false, access: { has: obj => \"removePreloadScript\" in obj, get: obj => obj.removePreloadScript }, metadata: _metadata }, null, _instanceExtraInitializers);\n            if (_metadata) Object.defineProperty(this, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });\n        }\n        static from(userContext, parent, id, url) {\n            const browsingContext = new BrowsingContext(userContext, parent, id, url);\n            browsingContext.#initialize();\n            return browsingContext;\n        }\n        // keep-sorted start\n        #navigation = (__runInitializers(this, _instanceExtraInitializers), void 0);\n        #reason;\n        #url;\n        #children = new Map();\n        #disposables = new DisposableStack();\n        #realms = new Map();\n        #requests = new Map();\n        defaultRealm;\n        id;\n        parent;\n        userContext;\n        // keep-sorted end\n        constructor(context, parent, id, url) {\n            super();\n            // keep-sorted start\n            this.#url = url;\n            this.id = id;\n            this.parent = parent;\n            this.userContext = context;\n            // keep-sorted end\n            this.defaultRealm = WindowRealm.from(this);\n        }\n        #initialize() {\n            const userContextEmitter = this.#disposables.use(new EventEmitter(this.userContext));\n            userContextEmitter.once('closed', ({ reason }) => {\n                this.dispose(`Browsing context already closed: ${reason}`);\n            });\n            const sessionEmitter = this.#disposables.use(new EventEmitter(this.#session));\n            sessionEmitter.on('browsingContext.contextCreated', info => {\n                if (info.parent !== this.id) {\n                    return;\n                }\n                const browsingContext = BrowsingContext.from(this.userContext, this, info.context, info.url);\n                this.#children.set(info.context, browsingContext);\n                const browsingContextEmitter = this.#disposables.use(new EventEmitter(browsingContext));\n                browsingContextEmitter.once('closed', () => {\n                    browsingContextEmitter.removeAllListeners();\n                    this.#children.delete(browsingContext.id);\n                });\n                this.emit('browsingcontext', { browsingContext });\n            });\n            sessionEmitter.on('browsingContext.contextDestroyed', info => {\n                if (info.context !== this.id) {\n                    return;\n                }\n                this.dispose('Browsing context already closed.');\n            });\n            sessionEmitter.on('browsingContext.domContentLoaded', info => {\n                if (info.context !== this.id) {\n                    return;\n                }\n                this.#url = info.url;\n                this.emit('DOMContentLoaded', undefined);\n            });\n            sessionEmitter.on('browsingContext.load', info => {\n                if (info.context !== this.id) {\n                    return;\n                }\n                this.#url = info.url;\n                this.emit('load', undefined);\n            });\n            sessionEmitter.on('browsingContext.navigationStarted', info => {\n                if (info.context !== this.id) {\n                    return;\n                }\n                this.#url = info.url;\n                this.#requests.clear();\n                // Note the navigation ID is null for this event.\n                this.#navigation = Navigation.from(this);\n                const navigationEmitter = this.#disposables.use(new EventEmitter(this.#navigation));\n                for (const eventName of ['fragment', 'failed', 'aborted']) {\n                    navigationEmitter.once(eventName, ({ url }) => {\n                        navigationEmitter[disposeSymbol]();\n                        this.#url = url;\n                    });\n                }\n                this.emit('navigation', { navigation: this.#navigation });\n            });\n            sessionEmitter.on('network.beforeRequestSent', event => {\n                if (event.context !== this.id) {\n                    return;\n                }\n                if (this.#requests.has(event.request.request)) {\n                    return;\n                }\n                const request = Request.from(this, event);\n                this.#requests.set(request.id, request);\n                this.emit('request', { request });\n            });\n            sessionEmitter.on('log.entryAdded', entry => {\n                if (entry.source.context !== this.id) {\n                    return;\n                }\n                this.emit('log', { entry });\n            });\n            sessionEmitter.on('browsingContext.userPromptOpened', info => {\n                if (info.context !== this.id) {\n                    return;\n                }\n                const userPrompt = UserPrompt.from(this, info);\n                this.emit('userprompt', { userPrompt });\n            });\n        }\n        // keep-sorted start block=yes\n        get #session() {\n            return this.userContext.browser.session;\n        }\n        get children() {\n            return this.#children.values();\n        }\n        get closed() {\n            return this.#reason !== undefined;\n        }\n        get disposed() {\n            return this.closed;\n        }\n        get realms() {\n            return this.#realms.values();\n        }\n        get top() {\n            let context = this;\n            for (let { parent } = context; parent; { parent } = context) {\n                context = parent;\n            }\n            return context;\n        }\n        get url() {\n            return this.#url;\n        }\n        // keep-sorted end\n        dispose(reason) {\n            this.#reason = reason;\n            this[disposeSymbol]();\n        }\n        async activate() {\n            await this.#session.send('browsingContext.activate', {\n                context: this.id,\n            });\n        }\n        async captureScreenshot(options = {}) {\n            const { result: { data }, } = await this.#session.send('browsingContext.captureScreenshot', {\n                context: this.id,\n                ...options,\n            });\n            return data;\n        }\n        async close(promptUnload) {\n            await Promise.all([...this.#children.values()].map(async (child) => {\n                await child.close(promptUnload);\n            }));\n            await this.#session.send('browsingContext.close', {\n                context: this.id,\n                promptUnload,\n            });\n        }\n        async traverseHistory(delta) {\n            await this.#session.send('browsingContext.traverseHistory', {\n                context: this.id,\n                delta,\n            });\n        }\n        async navigate(url, wait) {\n            await this.#session.send('browsingContext.navigate', {\n                context: this.id,\n                url,\n                wait,\n            });\n            return await new Promise(resolve => {\n                this.once('navigation', ({ navigation }) => {\n                    resolve(navigation);\n                });\n            });\n        }\n        async reload(options = {}) {\n            await this.#session.send('browsingContext.reload', {\n                context: this.id,\n                ...options,\n            });\n            return await new Promise(resolve => {\n                this.once('navigation', ({ navigation }) => {\n                    resolve(navigation);\n                });\n            });\n        }\n        async print(options = {}) {\n            const { result: { data }, } = await this.#session.send('browsingContext.print', {\n                context: this.id,\n                ...options,\n            });\n            return data;\n        }\n        async handleUserPrompt(options = {}) {\n            await this.#session.send('browsingContext.handleUserPrompt', {\n                context: this.id,\n                ...options,\n            });\n        }\n        async setViewport(options = {}) {\n            await this.#session.send('browsingContext.setViewport', {\n                context: this.id,\n                ...options,\n            });\n        }\n        async performActions(actions) {\n            await this.#session.send('input.performActions', {\n                context: this.id,\n                actions,\n            });\n        }\n        async releaseActions() {\n            await this.#session.send('input.releaseActions', {\n                context: this.id,\n            });\n        }\n        createWindowRealm(sandbox) {\n            return WindowRealm.from(this, sandbox);\n        }\n        async addPreloadScript(functionDeclaration, options = {}) {\n            return await this.userContext.browser.addPreloadScript(functionDeclaration, {\n                ...options,\n                contexts: [this, ...(options.contexts ?? [])],\n            });\n        }\n        async removePreloadScript(script) {\n            await this.userContext.browser.removePreloadScript(script);\n        }\n        [(_dispose_decorators = [inertIfDisposed], _activate_decorators = [throwIfDisposed(context => {\n                // SAFETY: Disposal implies this exists.\n                return context.#reason;\n            })], _captureScreenshot_decorators = [throwIfDisposed(context => {\n                // SAFETY: Disposal implies this exists.\n                return context.#reason;\n            })], _close_decorators = [throwIfDisposed(context => {\n                // SAFETY: Disposal implies this exists.\n                return context.#reason;\n            })], _traverseHistory_decorators = [throwIfDisposed(context => {\n                // SAFETY: Disposal implies this exists.\n                return context.#reason;\n            })], _navigate_decorators = [throwIfDisposed(context => {\n                // SAFETY: Disposal implies this exists.\n                return context.#reason;\n            })], _reload_decorators = [throwIfDisposed(context => {\n                // SAFETY: Disposal implies this exists.\n                return context.#reason;\n            })], _print_decorators = [throwIfDisposed(context => {\n                // SAFETY: Disposal implies this exists.\n                return context.#reason;\n            })], _handleUserPrompt_decorators = [throwIfDisposed(context => {\n                // SAFETY: Disposal implies this exists.\n                return context.#reason;\n            })], _setViewport_decorators = [throwIfDisposed(context => {\n                // SAFETY: Disposal implies this exists.\n                return context.#reason;\n            })], _performActions_decorators = [throwIfDisposed(context => {\n                // SAFETY: Disposal implies this exists.\n                return context.#reason;\n            })], _releaseActions_decorators = [throwIfDisposed(context => {\n                // SAFETY: Disposal implies this exists.\n                return context.#reason;\n            })], _createWindowRealm_decorators = [throwIfDisposed(context => {\n                // SAFETY: Disposal implies this exists.\n                return context.#reason;\n            })], _addPreloadScript_decorators = [throwIfDisposed(context => {\n                // SAFETY: Disposal implies this exists.\n                return context.#reason;\n            })], _removePreloadScript_decorators = [throwIfDisposed(context => {\n                // SAFETY: Disposal implies this exists.\n                return context.#reason;\n            })], disposeSymbol)]() {\n            this.#reason ??=\n                'Browsing context already closed, probably because the user context closed.';\n            this.emit('closed', { reason: this.#reason });\n            this.#disposables.dispose();\n            super[disposeSymbol]();\n        }\n    };\n})();\nexport { BrowsingContext };\n//# sourceMappingURL=BrowsingContext.js.map", "/**\n * @license\n * Copyright 2024 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nvar __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {\n    var useValue = arguments.length > 2;\n    for (var i = 0; i < initializers.length; i++) {\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n    }\n    return useValue ? value : void 0;\n};\nvar __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n    var _, done = false;\n    for (var i = decorators.length - 1; i >= 0; i--) {\n        var context = {};\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n        if (kind === \"accessor\") {\n            if (result === void 0) continue;\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n            if (_ = accept(result.get)) descriptor.get = _;\n            if (_ = accept(result.set)) descriptor.set = _;\n            if (_ = accept(result.init)) initializers.unshift(_);\n        }\n        else if (_ = accept(result)) {\n            if (kind === \"field\") initializers.unshift(_);\n            else descriptor[key] = _;\n        }\n    }\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\n    done = true;\n};\nimport { EventEmitter } from '../../common/EventEmitter.js';\nimport { assert } from '../../util/assert.js';\nimport { inertIfDisposed, throwIfDisposed } from '../../util/decorators.js';\nimport { DisposableStack, disposeSymbol } from '../../util/disposable.js';\nimport { BrowsingContext } from './BrowsingContext.js';\n/**\n * @internal\n */\nlet UserContext = (() => {\n    let _classSuper = EventEmitter;\n    let _instanceExtraInitializers = [];\n    let _dispose_decorators;\n    let _createBrowsingContext_decorators;\n    let _remove_decorators;\n    return class UserContext extends _classSuper {\n        static {\n            const _metadata = typeof Symbol === \"function\" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;\n            __esDecorate(this, null, _dispose_decorators, { kind: \"method\", name: \"dispose\", static: false, private: false, access: { has: obj => \"dispose\" in obj, get: obj => obj.dispose }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _createBrowsingContext_decorators, { kind: \"method\", name: \"createBrowsingContext\", static: false, private: false, access: { has: obj => \"createBrowsingContext\" in obj, get: obj => obj.createBrowsingContext }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _remove_decorators, { kind: \"method\", name: \"remove\", static: false, private: false, access: { has: obj => \"remove\" in obj, get: obj => obj.remove }, metadata: _metadata }, null, _instanceExtraInitializers);\n            if (_metadata) Object.defineProperty(this, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });\n        }\n        static DEFAULT = 'default';\n        static create(browser, id) {\n            const context = new UserContext(browser, id);\n            context.#initialize();\n            return context;\n        }\n        // keep-sorted start\n        #reason = (__runInitializers(this, _instanceExtraInitializers), void 0);\n        // Note these are only top-level contexts.\n        #browsingContexts = new Map();\n        #disposables = new DisposableStack();\n        #id;\n        browser;\n        // keep-sorted end\n        constructor(browser, id) {\n            super();\n            // keep-sorted start\n            this.#id = id;\n            this.browser = browser;\n            // keep-sorted end\n        }\n        #initialize() {\n            const browserEmitter = this.#disposables.use(new EventEmitter(this.browser));\n            browserEmitter.once('closed', ({ reason }) => {\n                this.dispose(`User context already closed: ${reason}`);\n            });\n            const sessionEmitter = this.#disposables.use(new EventEmitter(this.#session));\n            sessionEmitter.on('browsingContext.contextCreated', info => {\n                if (info.parent) {\n                    return;\n                }\n                if (info.userContext !== this.#id) {\n                    return;\n                }\n                const browsingContext = BrowsingContext.from(this, undefined, info.context, info.url);\n                this.#browsingContexts.set(browsingContext.id, browsingContext);\n                const browsingContextEmitter = this.#disposables.use(new EventEmitter(browsingContext));\n                browsingContextEmitter.on('closed', () => {\n                    browsingContextEmitter.removeAllListeners();\n                    this.#browsingContexts.delete(browsingContext.id);\n                });\n                this.emit('browsingcontext', { browsingContext });\n            });\n        }\n        // keep-sorted start block=yes\n        get #session() {\n            return this.browser.session;\n        }\n        get browsingContexts() {\n            return this.#browsingContexts.values();\n        }\n        get closed() {\n            return this.#reason !== undefined;\n        }\n        get disposed() {\n            return this.closed;\n        }\n        get id() {\n            return this.#id;\n        }\n        // keep-sorted end\n        dispose(reason) {\n            this.#reason = reason;\n            this[disposeSymbol]();\n        }\n        async createBrowsingContext(type, options = {}) {\n            const { result: { context: contextId }, } = await this.#session.send('browsingContext.create', {\n                type,\n                ...options,\n                referenceContext: options.referenceContext?.id,\n                userContext: this.#id,\n            });\n            const browsingContext = this.#browsingContexts.get(contextId);\n            assert(browsingContext, 'The WebDriver BiDi implementation is failing to create a browsing context correctly.');\n            // We use an array to avoid the promise from being awaited.\n            return browsingContext;\n        }\n        async remove() {\n            try {\n                await this.#session.send('browser.removeUserContext', {\n                    userContext: this.#id,\n                });\n            }\n            finally {\n                this.dispose('User context already closed.');\n            }\n        }\n        [(_dispose_decorators = [inertIfDisposed], _createBrowsingContext_decorators = [throwIfDisposed(context => {\n                // SAFETY: Disposal implies this exists.\n                return context.#reason;\n            })], _remove_decorators = [throwIfDisposed(context => {\n                // SAFETY: Disposal implies this exists.\n                return context.#reason;\n            })], disposeSymbol)]() {\n            this.#reason ??=\n                'User context already closed, probably because the browser disconnected/closed.';\n            this.emit('closed', { reason: this.#reason });\n            this.#disposables.dispose();\n            super[disposeSymbol]();\n        }\n    };\n})();\nexport { UserContext };\n//# sourceMappingURL=UserContext.js.map", "/**\n * @license\n * Copyright 2022 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nimport { BrowserContext } from '../api/BrowserContext.js';\nimport { UnsupportedOperation } from '../common/Errors.js';\nimport { debugError } from '../common/util.js';\nimport { UserContext } from './core/UserContext.js';\n/**\n * @internal\n */\nexport class BidiBrowserContext extends BrowserContext {\n    #browser;\n    #connection;\n    #defaultViewport;\n    #userContext;\n    constructor(browser, userContext, options) {\n        super();\n        this.#browser = browser;\n        this.#userContext = userContext;\n        this.#connection = this.#browser.connection;\n        this.#defaultViewport = options.defaultViewport;\n    }\n    targets() {\n        return this.#browser.targets().filter(target => {\n            return target.browserContext() === this;\n        });\n    }\n    waitForTarget(predicate, options = {}) {\n        return this.#browser.waitForTarget(target => {\n            return target.browserContext() === this && predicate(target);\n        }, options);\n    }\n    get connection() {\n        return this.#connection;\n    }\n    async newPage() {\n        const { result } = await this.#connection.send('browsingContext.create', {\n            type: \"tab\" /* Bidi.BrowsingContext.CreateType.Tab */,\n            userContext: this.#userContext.id,\n        });\n        const target = this.#browser._getTargetById(result.context);\n        // TODO: once BiDi has some concept matching BrowserContext, the newly\n        // created contexts should get automatically assigned to the right\n        // BrowserContext. For now, we assume that only explicitly created pages go\n        // to the current BrowserContext. Otherwise, the contexts get assigned to\n        // the default BrowserContext by the Browser.\n        target._setBrowserContext(this);\n        const page = await target.page();\n        if (!page) {\n            throw new Error('Page is not found');\n        }\n        if (this.#defaultViewport) {\n            try {\n                await page.setViewport(this.#defaultViewport);\n            }\n            catch {\n                // No support for setViewport in Firefox.\n            }\n        }\n        return page;\n    }\n    async close() {\n        if (!this.isIncognito()) {\n            throw new Error('Default context cannot be closed!');\n        }\n        try {\n            await this.#userContext.remove();\n        }\n        catch (error) {\n            debugError(error);\n        }\n    }\n    browser() {\n        return this.#browser;\n    }\n    async pages() {\n        const results = await Promise.all([...this.targets()].map(t => {\n            return t.page();\n        }));\n        return results.filter((p) => {\n            return p !== null;\n        });\n    }\n    isIncognito() {\n        return this.#userContext.id !== UserContext.DEFAULT;\n    }\n    overridePermissions() {\n        throw new UnsupportedOperation();\n    }\n    clearPermissionOverrides() {\n        throw new UnsupportedOperation();\n    }\n    get id() {\n        if (this.#userContext.id === 'default') {\n            return undefined;\n        }\n        return this.#userContext.id;\n    }\n}\n//# sourceMappingURL=BrowserContext.js.map", "/**\n * @license\n * Copyright 2024 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nvar __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {\n    var useValue = arguments.length > 2;\n    for (var i = 0; i < initializers.length; i++) {\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n    }\n    return useValue ? value : void 0;\n};\nvar __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n    var _, done = false;\n    for (var i = decorators.length - 1; i >= 0; i--) {\n        var context = {};\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n        if (kind === \"accessor\") {\n            if (result === void 0) continue;\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n            if (_ = accept(result.get)) descriptor.get = _;\n            if (_ = accept(result.set)) descriptor.set = _;\n            if (_ = accept(result.init)) initializers.unshift(_);\n        }\n        else if (_ = accept(result)) {\n            if (kind === \"field\") initializers.unshift(_);\n            else descriptor[key] = _;\n        }\n    }\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\n    done = true;\n};\nvar __addDisposableResource = (this && this.__addDisposableResource) || function (env, value, async) {\n    if (value !== null && value !== void 0) {\n        if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n        var dispose;\n        if (async) {\n            if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n            dispose = value[Symbol.asyncDispose];\n        }\n        if (dispose === void 0) {\n            if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n            dispose = value[Symbol.dispose];\n        }\n        if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n        env.stack.push({ value: value, dispose: dispose, async: async });\n    }\n    else if (async) {\n        env.stack.push({ async: true });\n    }\n    return value;\n};\nvar __disposeResources = (this && this.__disposeResources) || (function (SuppressedError) {\n    return function (env) {\n        function fail(e) {\n            env.error = env.hasError ? new SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n            env.hasError = true;\n        }\n        function next() {\n            while (env.stack.length) {\n                var rec = env.stack.pop();\n                try {\n                    var result = rec.dispose && rec.dispose.call(rec.value);\n                    if (rec.async) return Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n                }\n                catch (e) {\n                    fail(e);\n                }\n            }\n            if (env.hasError) throw env.error;\n        }\n        return next();\n    };\n})(typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n    var e = new Error(message);\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n});\nimport { EventEmitter } from '../../common/EventEmitter.js';\nimport { inertIfDisposed, throwIfDisposed } from '../../util/decorators.js';\nimport { DisposableStack, disposeSymbol } from '../../util/disposable.js';\nimport { UserContext } from './UserContext.js';\n/**\n * @internal\n */\nlet Browser = (() => {\n    let _classSuper = EventEmitter;\n    let _instanceExtraInitializers = [];\n    let _dispose_decorators;\n    let _close_decorators;\n    let _addPreloadScript_decorators;\n    let _removePreloadScript_decorators;\n    let _createUserContext_decorators;\n    return class Browser extends _classSuper {\n        static {\n            const _metadata = typeof Symbol === \"function\" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;\n            __esDecorate(this, null, _dispose_decorators, { kind: \"method\", name: \"dispose\", static: false, private: false, access: { has: obj => \"dispose\" in obj, get: obj => obj.dispose }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _close_decorators, { kind: \"method\", name: \"close\", static: false, private: false, access: { has: obj => \"close\" in obj, get: obj => obj.close }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _addPreloadScript_decorators, { kind: \"method\", name: \"addPreloadScript\", static: false, private: false, access: { has: obj => \"addPreloadScript\" in obj, get: obj => obj.addPreloadScript }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _removePreloadScript_decorators, { kind: \"method\", name: \"removePreloadScript\", static: false, private: false, access: { has: obj => \"removePreloadScript\" in obj, get: obj => obj.removePreloadScript }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _createUserContext_decorators, { kind: \"method\", name: \"createUserContext\", static: false, private: false, access: { has: obj => \"createUserContext\" in obj, get: obj => obj.createUserContext }, metadata: _metadata }, null, _instanceExtraInitializers);\n            if (_metadata) Object.defineProperty(this, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });\n        }\n        static async from(session) {\n            const browser = new Browser(session);\n            await browser.#initialize();\n            return browser;\n        }\n        // keep-sorted start\n        #closed = (__runInitializers(this, _instanceExtraInitializers), false);\n        #reason;\n        #disposables = new DisposableStack();\n        #userContexts = new Map();\n        session;\n        // keep-sorted end\n        constructor(session) {\n            super();\n            // keep-sorted start\n            this.session = session;\n            // keep-sorted end\n            this.#userContexts.set(UserContext.DEFAULT, UserContext.create(this, UserContext.DEFAULT));\n        }\n        async #initialize() {\n            const sessionEmitter = this.#disposables.use(new EventEmitter(this.session));\n            sessionEmitter.once('ended', ({ reason }) => {\n                this.dispose(reason);\n            });\n            sessionEmitter.on('script.realmCreated', info => {\n                if (info.type === 'shared-worker') {\n                    // TODO: Create a SharedWorkerRealm.\n                }\n            });\n            await this.#syncUserContexts();\n            await this.#syncBrowsingContexts();\n        }\n        async #syncUserContexts() {\n            const { result: { userContexts }, } = await this.session.send('browser.getUserContexts', {});\n            for (const context of userContexts) {\n                if (context.userContext === UserContext.DEFAULT) {\n                    continue;\n                }\n                this.#userContexts.set(context.userContext, UserContext.create(this, context.userContext));\n            }\n        }\n        async #syncBrowsingContexts() {\n            // In case contexts are created or destroyed during `getTree`, we use this\n            // set to detect them.\n            const contextIds = new Set();\n            let contexts;\n            {\n                const env_1 = { stack: [], error: void 0, hasError: false };\n                try {\n                    const sessionEmitter = __addDisposableResource(env_1, new EventEmitter(this.session), false);\n                    sessionEmitter.on('browsingContext.contextCreated', info => {\n                        contextIds.add(info.context);\n                    });\n                    sessionEmitter.on('browsingContext.contextDestroyed', info => {\n                        contextIds.delete(info.context);\n                    });\n                    const { result } = await this.session.send('browsingContext.getTree', {});\n                    contexts = result.contexts;\n                }\n                catch (e_1) {\n                    env_1.error = e_1;\n                    env_1.hasError = true;\n                }\n                finally {\n                    __disposeResources(env_1);\n                }\n            }\n            // Simulating events so contexts are created naturally.\n            for (const info of contexts) {\n                if (contextIds.has(info.context)) {\n                    this.session.emit('browsingContext.contextCreated', info);\n                }\n                if (info.children) {\n                    contexts.push(...info.children);\n                }\n            }\n        }\n        // keep-sorted start block=yes\n        get closed() {\n            return this.#closed;\n        }\n        get defaultUserContext() {\n            // SAFETY: A UserContext is always created for the default context.\n            return this.#userContexts.get(UserContext.DEFAULT);\n        }\n        get disconnected() {\n            return this.#reason !== undefined;\n        }\n        get disposed() {\n            return this.disconnected;\n        }\n        get userContexts() {\n            return this.#userContexts.values();\n        }\n        // keep-sorted end\n        dispose(reason, closed = false) {\n            this.#closed = closed;\n            this.#reason = reason;\n            this[disposeSymbol]();\n        }\n        async close() {\n            try {\n                await this.session.send('browser.close', {});\n            }\n            finally {\n                this.dispose('Browser already closed.', true);\n            }\n        }\n        async addPreloadScript(functionDeclaration, options = {}) {\n            const { result: { script }, } = await this.session.send('script.addPreloadScript', {\n                functionDeclaration,\n                ...options,\n                contexts: options.contexts?.map(context => {\n                    return context.id;\n                }),\n            });\n            return script;\n        }\n        async removePreloadScript(script) {\n            await this.session.send('script.removePreloadScript', {\n                script,\n            });\n        }\n        async createUserContext() {\n            const { result: { userContext: context }, } = await this.session.send('browser.createUserContext', {});\n            const userContext = UserContext.create(this, context);\n            this.#userContexts.set(userContext.id, userContext);\n            const userContextEmitter = this.#disposables.use(new EventEmitter(userContext));\n            userContextEmitter.once('closed', () => {\n                userContextEmitter.removeAllListeners();\n                this.#userContexts.delete(context);\n            });\n            return userContext;\n        }\n        [(_dispose_decorators = [inertIfDisposed], _close_decorators = [throwIfDisposed(browser => {\n                // SAFETY: By definition of `disposed`, `#reason` is defined.\n                return browser.#reason;\n            })], _addPreloadScript_decorators = [throwIfDisposed(browser => {\n                // SAFETY: By definition of `disposed`, `#reason` is defined.\n                return browser.#reason;\n            })], _removePreloadScript_decorators = [throwIfDisposed(browser => {\n                // SAFETY: By definition of `disposed`, `#reason` is defined.\n                return browser.#reason;\n            })], _createUserContext_decorators = [throwIfDisposed(browser => {\n                // SAFETY: By definition of `disposed`, `#reason` is defined.\n                return browser.#reason;\n            })], disposeSymbol)]() {\n            this.#reason ??=\n                'Browser was disconnected, probably because the session ended.';\n            if (this.closed) {\n                this.emit('closed', { reason: this.#reason });\n            }\n            this.emit('disconnected', { reason: this.#reason });\n            this.#disposables.dispose();\n            super[disposeSymbol]();\n        }\n    };\n})();\nexport { Browser };\n//# sourceMappingURL=Browser.js.map", "/**\n * @license\n * Copyright 2024 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nvar __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {\n    var useValue = arguments.length > 2;\n    for (var i = 0; i < initializers.length; i++) {\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n    }\n    return useValue ? value : void 0;\n};\nvar __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n    var _, done = false;\n    for (var i = decorators.length - 1; i >= 0; i--) {\n        var context = {};\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n        if (kind === \"accessor\") {\n            if (result === void 0) continue;\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n            if (_ = accept(result.get)) descriptor.get = _;\n            if (_ = accept(result.set)) descriptor.set = _;\n            if (_ = accept(result.init)) initializers.unshift(_);\n        }\n        else if (_ = accept(result)) {\n            if (kind === \"field\") initializers.unshift(_);\n            else descriptor[key] = _;\n        }\n    }\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\n    done = true;\n};\nimport { EventEmitter } from '../../common/EventEmitter.js';\nimport { debugError } from '../../common/util.js';\nimport { inertIfDisposed, throwIfDisposed } from '../../util/decorators.js';\nimport { DisposableStack, disposeSymbol } from '../../util/disposable.js';\nimport { Browser } from './Browser.js';\n// TODO: Once Chrome supports session.status properly, uncomment this block.\n// const MAX_RETRIES = 5;\n/**\n * @internal\n */\nlet Session = (() => {\n    let _classSuper = EventEmitter;\n    let _instanceExtraInitializers = [];\n    let _dispose_decorators;\n    let _send_decorators;\n    let _subscribe_decorators;\n    let _end_decorators;\n    return class Session extends _classSuper {\n        static {\n            const _metadata = typeof Symbol === \"function\" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;\n            __esDecorate(this, null, _dispose_decorators, { kind: \"method\", name: \"dispose\", static: false, private: false, access: { has: obj => \"dispose\" in obj, get: obj => obj.dispose }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _send_decorators, { kind: \"method\", name: \"send\", static: false, private: false, access: { has: obj => \"send\" in obj, get: obj => obj.send }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _subscribe_decorators, { kind: \"method\", name: \"subscribe\", static: false, private: false, access: { has: obj => \"subscribe\" in obj, get: obj => obj.subscribe }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _end_decorators, { kind: \"method\", name: \"end\", static: false, private: false, access: { has: obj => \"end\" in obj, get: obj => obj.end }, metadata: _metadata }, null, _instanceExtraInitializers);\n            if (_metadata) Object.defineProperty(this, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });\n        }\n        static async from(connection, capabilities) {\n            // Wait until the session is ready.\n            //\n            // TODO: Once Chrome supports session.status properly, uncomment this block\n            // and remove `getBiDiConnection` in BrowserConnector.\n            // let status = {message: '', ready: false};\n            // for (let i = 0; i < MAX_RETRIES; ++i) {\n            //   status = (await connection.send('session.status', {})).result;\n            //   if (status.ready) {\n            //     break;\n            //   }\n            //   // Backoff a little bit each time.\n            //   await new Promise(resolve => {\n            //     return setTimeout(resolve, (1 << i) * 100);\n            //   });\n            // }\n            // if (!status.ready) {\n            //   throw new Error(status.message);\n            // }\n            let result;\n            try {\n                result = (await connection.send('session.new', {\n                    capabilities,\n                })).result;\n            }\n            catch (err) {\n                // Chrome does not support session.new.\n                debugError(err);\n                result = {\n                    sessionId: '',\n                    capabilities: {\n                        acceptInsecureCerts: false,\n                        browserName: '',\n                        browserVersion: '',\n                        platformName: '',\n                        setWindowRect: false,\n                        webSocketUrl: '',\n                    },\n                };\n            }\n            const session = new Session(connection, result);\n            await session.#initialize();\n            return session;\n        }\n        // keep-sorted start\n        #reason = (__runInitializers(this, _instanceExtraInitializers), void 0);\n        #disposables = new DisposableStack();\n        #info;\n        browser;\n        connection;\n        // keep-sorted end\n        constructor(connection, info) {\n            super();\n            // keep-sorted start\n            this.#info = info;\n            this.connection = connection;\n            // keep-sorted end\n        }\n        async #initialize() {\n            this.connection.pipeTo(this);\n            // SAFETY: We use `any` to allow assignment of the readonly property.\n            this.browser = await Browser.from(this);\n            const browserEmitter = this.#disposables.use(this.browser);\n            browserEmitter.once('closed', ({ reason }) => {\n                this.dispose(reason);\n            });\n        }\n        // keep-sorted start block=yes\n        get capabilities() {\n            return this.#info.capabilities;\n        }\n        get disposed() {\n            return this.ended;\n        }\n        get ended() {\n            return this.#reason !== undefined;\n        }\n        get id() {\n            return this.#info.sessionId;\n        }\n        // keep-sorted end\n        dispose(reason) {\n            this.#reason = reason;\n            this[disposeSymbol]();\n        }\n        pipeTo(emitter) {\n            this.connection.pipeTo(emitter);\n        }\n        /**\n         * Currently, there is a 1:1 relationship between the session and the\n         * session. In the future, we might support multiple sessions and in that\n         * case we always needs to make sure that the session for the right session\n         * object is used, so we implement this method here, although it's not defined\n         * in the spec.\n         */\n        async send(method, params) {\n            return await this.connection.send(method, params);\n        }\n        async subscribe(events) {\n            await this.send('session.subscribe', {\n                events,\n            });\n        }\n        async end() {\n            try {\n                await this.send('session.end', {});\n            }\n            finally {\n                this.dispose(`Session already ended.`);\n            }\n        }\n        [(_dispose_decorators = [inertIfDisposed], _send_decorators = [throwIfDisposed(session => {\n                // SAFETY: By definition of `disposed`, `#reason` is defined.\n                return session.#reason;\n            })], _subscribe_decorators = [throwIfDisposed(session => {\n                // SAFETY: By definition of `disposed`, `#reason` is defined.\n                return session.#reason;\n            })], _end_decorators = [throwIfDisposed(session => {\n                // SAFETY: By definition of `disposed`, `#reason` is defined.\n                return session.#reason;\n            })], disposeSymbol)]() {\n            this.#reason ??=\n                'Session already destroyed, probably because the connection broke.';\n            this.emit('ended', { reason: this.#reason });\n            this.#disposables.dispose();\n            super[disposeSymbol]();\n        }\n    };\n})();\nexport { Session };\n//# sourceMappingURL=Session.js.map", "/**\n * @license\n * Copyright 2017 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nimport { Dialog } from '../api/Dialog.js';\n/**\n * @internal\n */\nexport class BidiDialog extends Dialog {\n    #context;\n    /**\n     * @internal\n     */\n    constructor(context, type, message, defaultValue) {\n        super(type, message, defaultValue);\n        this.#context = context;\n    }\n    /**\n     * @internal\n     */\n    async handle(options) {\n        await this.#context.connection.send('browsingContext.handleUserPrompt', {\n            context: this.#context.id,\n            accept: options.accept,\n            userText: options.text,\n        });\n    }\n}\n//# sourceMappingURL=Dialog.js.map", "/**\n * @internal\n */\nexport class EmulationManager {\n    #browsingContext;\n    constructor(browsingContext) {\n        this.#browsingContext = browsingContext;\n    }\n    async emulateViewport(viewport) {\n        await this.#browsingContext.connection.send('browsingContext.setViewport', {\n            context: this.#browsingContext.id,\n            viewport: viewport.width && viewport.height\n                ? {\n                    width: viewport.width,\n                    height: viewport.height,\n                }\n                : null,\n            devicePixelRatio: viewport.deviceScaleFactor\n                ? viewport.deviceScaleFactor\n                : null,\n        });\n    }\n}\n//# sourceMappingURL=EmulationManager.js.map", "/**\n * @license\n * Copyright 2023 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nimport * as Bidi from 'chromium-bidi/lib/cjs/protocol/protocol.js';\nimport { debugError } from '../common/util.js';\nimport { assert } from '../util/assert.js';\nimport { Deferred } from '../util/Deferred.js';\nimport { interpolateFunction, stringifyFunction } from '../util/Function.js';\nimport { BidiDeserializer } from './Deserializer.js';\nimport { BidiSerializer } from './Serializer.js';\n/**\n * @internal\n */\nexport class ExposeableFunction {\n    #frame;\n    name;\n    #apply;\n    #channels;\n    #callerInfos = new Map();\n    #preloadScriptId;\n    constructor(frame, name, apply) {\n        this.#frame = frame;\n        this.name = name;\n        this.#apply = apply;\n        this.#channels = {\n            args: `__puppeteer__${this.#frame._id}_page_exposeFunction_${this.name}_args`,\n            resolve: `__puppeteer__${this.#frame._id}_page_exposeFunction_${this.name}_resolve`,\n            reject: `__puppeteer__${this.#frame._id}_page_exposeFunction_${this.name}_reject`,\n        };\n    }\n    async expose() {\n        const connection = this.#connection;\n        const channelArguments = this.#channelArguments;\n        // TODO(jrandolf): Implement cleanup with removePreloadScript.\n        connection.on(Bidi.ChromiumBidi.Script.EventNames.Message, this.#handleArgumentsMessage);\n        connection.on(Bidi.ChromiumBidi.Script.EventNames.Message, this.#handleResolveMessage);\n        connection.on(Bidi.ChromiumBidi.Script.EventNames.Message, this.#handleRejectMessage);\n        const functionDeclaration = stringifyFunction(interpolateFunction((sendArgs, sendResolve, sendReject) => {\n            let id = 0;\n            Object.assign(globalThis, {\n                [PLACEHOLDER('name')]: function (...args) {\n                    return new Promise((resolve, reject) => {\n                        sendArgs([id, args]);\n                        sendResolve([id, resolve]);\n                        sendReject([id, reject]);\n                        ++id;\n                    });\n                },\n            });\n        }, { name: JSON.stringify(this.name) }));\n        const { result } = await connection.send('script.addPreloadScript', {\n            functionDeclaration,\n            arguments: channelArguments,\n            contexts: [this.#frame.page().mainFrame()._id],\n        });\n        this.#preloadScriptId = result.script;\n        await Promise.all(this.#frame\n            .page()\n            .frames()\n            .map(async (frame) => {\n            return await connection.send('script.callFunction', {\n                functionDeclaration,\n                arguments: channelArguments,\n                awaitPromise: false,\n                target: frame.mainRealm().realm.target,\n            });\n        }));\n    }\n    #handleArgumentsMessage = async (params) => {\n        if (params.channel !== this.#channels.args) {\n            return;\n        }\n        const connection = this.#connection;\n        const { callbacks, remoteValue } = this.#getCallbacksAndRemoteValue(params);\n        const args = remoteValue.value?.[1];\n        assert(args);\n        try {\n            const result = await this.#apply(...BidiDeserializer.deserialize(args));\n            await connection.send('script.callFunction', {\n                functionDeclaration: stringifyFunction(([_, resolve], result) => {\n                    resolve(result);\n                }),\n                arguments: [\n                    (await callbacks.resolve.valueOrThrow()),\n                    BidiSerializer.serializeRemoteValue(result),\n                ],\n                awaitPromise: false,\n                target: {\n                    realm: params.source.realm,\n                },\n            });\n        }\n        catch (error) {\n            try {\n                if (error instanceof Error) {\n                    await connection.send('script.callFunction', {\n                        functionDeclaration: stringifyFunction(([_, reject], name, message, stack) => {\n                            const error = new Error(message);\n                            error.name = name;\n                            if (stack) {\n                                error.stack = stack;\n                            }\n                            reject(error);\n                        }),\n                        arguments: [\n                            (await callbacks.reject.valueOrThrow()),\n                            BidiSerializer.serializeRemoteValue(error.name),\n                            BidiSerializer.serializeRemoteValue(error.message),\n                            BidiSerializer.serializeRemoteValue(error.stack),\n                        ],\n                        awaitPromise: false,\n                        target: {\n                            realm: params.source.realm,\n                        },\n                    });\n                }\n                else {\n                    await connection.send('script.callFunction', {\n                        functionDeclaration: stringifyFunction(([_, reject], error) => {\n                            reject(error);\n                        }),\n                        arguments: [\n                            (await callbacks.reject.valueOrThrow()),\n                            BidiSerializer.serializeRemoteValue(error),\n                        ],\n                        awaitPromise: false,\n                        target: {\n                            realm: params.source.realm,\n                        },\n                    });\n                }\n            }\n            catch (error) {\n                debugError(error);\n            }\n        }\n    };\n    get #connection() {\n        return this.#frame.context().connection;\n    }\n    get #channelArguments() {\n        return [\n            {\n                type: 'channel',\n                value: {\n                    channel: this.#channels.args,\n                    ownership: \"root\" /* Bidi.Script.ResultOwnership.Root */,\n                },\n            },\n            {\n                type: 'channel',\n                value: {\n                    channel: this.#channels.resolve,\n                    ownership: \"root\" /* Bidi.Script.ResultOwnership.Root */,\n                },\n            },\n            {\n                type: 'channel',\n                value: {\n                    channel: this.#channels.reject,\n                    ownership: \"root\" /* Bidi.Script.ResultOwnership.Root */,\n                },\n            },\n        ];\n    }\n    #handleResolveMessage = (params) => {\n        if (params.channel !== this.#channels.resolve) {\n            return;\n        }\n        const { callbacks, remoteValue } = this.#getCallbacksAndRemoteValue(params);\n        callbacks.resolve.resolve(remoteValue);\n    };\n    #handleRejectMessage = (params) => {\n        if (params.channel !== this.#channels.reject) {\n            return;\n        }\n        const { callbacks, remoteValue } = this.#getCallbacksAndRemoteValue(params);\n        callbacks.reject.resolve(remoteValue);\n    };\n    #getCallbacksAndRemoteValue(params) {\n        const { data, source } = params;\n        assert(data.type === 'array');\n        assert(data.value);\n        const callerIdRemote = data.value[0];\n        assert(callerIdRemote);\n        assert(callerIdRemote.type === 'number');\n        assert(typeof callerIdRemote.value === 'number');\n        let bindingMap = this.#callerInfos.get(source.realm);\n        if (!bindingMap) {\n            bindingMap = new Map();\n            this.#callerInfos.set(source.realm, bindingMap);\n        }\n        const callerId = callerIdRemote.value;\n        let callbacks = bindingMap.get(callerId);\n        if (!callbacks) {\n            callbacks = {\n                resolve: new Deferred(),\n                reject: new Deferred(),\n            };\n            bindingMap.set(callerId, callbacks);\n        }\n        return { callbacks, remoteValue: data };\n    }\n    [Symbol.dispose]() {\n        void this[Symbol.asyncDispose]().catch(debugError);\n    }\n    async [Symbol.asyncDispose]() {\n        if (this.#preloadScriptId) {\n            await this.#connection.send('script.removePreloadScript', {\n                script: this.#preloadScriptId,\n            });\n        }\n    }\n}\n//# sourceMappingURL=ExposedFunction.js.map", "import { catchError } from '../../third_party/rxjs/rxjs.js';\nimport { ProtocolError, TimeoutError } from '../common/Errors.js';\n/**\n * @internal\n */\nexport function getBiDiLifeCycles(event) {\n    if (Array.isArray(event)) {\n        const pageLifeCycle = event.some(lifeCycle => {\n            return lifeCycle !== 'domcontentloaded';\n        })\n            ? 'load'\n            : 'domcontentloaded';\n        const networkLifeCycle = event.reduce((acc, lifeCycle) => {\n            if (lifeCycle === 'networkidle0') {\n                return lifeCycle;\n            }\n            else if (acc !== 'networkidle0' && lifeCycle === 'networkidle2') {\n                return lifeCycle;\n            }\n            return acc;\n        }, null);\n        return [pageLifeCycle, networkLifeCycle];\n    }\n    if (event === 'networkidle0' || event === 'networkidle2') {\n        return ['load', event];\n    }\n    return [event, null];\n}\n/**\n * @internal\n */\nexport const lifeCycleToReadinessState = new Map([\n    ['load', \"complete\" /* Bidi.BrowsingContext.ReadinessState.Complete */],\n    ['domcontentloaded', \"interactive\" /* Bidi.BrowsingContext.ReadinessState.Interactive */],\n]);\nexport function getBiDiReadinessState(event) {\n    const lifeCycles = getBiDiLifeCycles(event);\n    const readiness = lifeCycleToReadinessState.get(lifeCycles[0]);\n    return [readiness, lifeCycles[1]];\n}\n/**\n * @internal\n */\nexport const lifeCycleToSubscribedEvent = new Map([\n    ['load', 'browsingContext.load'],\n    ['domcontentloaded', 'browsingContext.domContentLoaded'],\n]);\n/**\n * @internal\n */\nexport function getBiDiLifecycleEvent(event) {\n    const lifeCycles = getBiDiLifeCycles(event);\n    const bidiEvent = lifeCycleToSubscribedEvent.get(lifeCycles[0]);\n    return [bidiEvent, lifeCycles[1]];\n}\n/**\n * @internal\n */\nexport function rewriteNavigationError(message, ms) {\n    return catchError(error => {\n        if (error instanceof ProtocolError) {\n            error.message += ` at ${message}`;\n        }\n        else if (error instanceof TimeoutError) {\n            error.message = `Navigation timeout of ${ms} ms exceeded`;\n        }\n        throw error;\n    });\n}\n//# sourceMappingURL=lifecycle.js.map", "/**\n * @license\n * Copyright 2023 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nimport { Realm } from '../api/Realm.js';\nimport { withSourcePuppeteerURLIfNone } from '../common/util.js';\nimport { BidiElementHandle } from './ElementHandle.js';\n/**\n * A unique key for {@link SandboxChart} to denote the default world.\n * Realms are automatically created in the default sandbox.\n *\n * @internal\n */\nexport const MAIN_SANDBOX = Symbol('mainSandbox');\n/**\n * A unique key for {@link SandboxChart} to denote the puppeteer sandbox.\n * This world contains all puppeteer-internal bindings/code.\n *\n * @internal\n */\nexport const PUPPETEER_SANDBOX = Symbol('puppeteerSandbox');\n/**\n * @internal\n */\nexport class Sandbox extends Realm {\n    name;\n    realm;\n    #frame;\n    constructor(name, frame, \n    // TODO: We should split the Realm and BrowsingContext\n    realm, timeoutSettings) {\n        super(timeoutSettings);\n        this.name = name;\n        this.realm = realm;\n        this.#frame = frame;\n        this.realm.setSandbox(this);\n    }\n    get environment() {\n        return this.#frame;\n    }\n    async evaluateHandle(pageFunction, ...args) {\n        pageFunction = withSourcePuppeteerURLIfNone(this.evaluateHandle.name, pageFunction);\n        return await this.realm.evaluateHandle(pageFunction, ...args);\n    }\n    async evaluate(pageFunction, ...args) {\n        pageFunction = withSourcePuppeteerURLIfNone(this.evaluate.name, pageFunction);\n        return await this.realm.evaluate(pageFunction, ...args);\n    }\n    async adoptHandle(handle) {\n        return (await this.evaluateHandle(node => {\n            return node;\n        }, handle));\n    }\n    async transferHandle(handle) {\n        if (handle.realm === this) {\n            return handle;\n        }\n        const transferredHandle = await this.evaluateHandle(node => {\n            return node;\n        }, handle);\n        await handle.dispose();\n        return transferredHandle;\n    }\n    async adoptBackendNode(backendNodeId) {\n        const { object } = await this.environment.client.send('DOM.resolveNode', {\n            backendNodeId: backendNodeId,\n        });\n        return new BidiElementHandle(this, {\n            handle: object.objectId,\n            type: 'node',\n        });\n    }\n}\n//# sourceMappingURL=Sandbox.js.map", "/**\n * @license\n * Copyright 2023 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nvar __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {\n    var useValue = arguments.length > 2;\n    for (var i = 0; i < initializers.length; i++) {\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n    }\n    return useValue ? value : void 0;\n};\nvar __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n    var _, done = false;\n    for (var i = decorators.length - 1; i >= 0; i--) {\n        var context = {};\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n        if (kind === \"accessor\") {\n            if (result === void 0) continue;\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n            if (_ = accept(result.get)) descriptor.get = _;\n            if (_ = accept(result.set)) descriptor.set = _;\n            if (_ = accept(result.init)) initializers.unshift(_);\n        }\n        else if (_ = accept(result)) {\n            if (kind === \"field\") initializers.unshift(_);\n            else descriptor[key] = _;\n        }\n    }\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\n    done = true;\n};\nimport * as Bidi from 'chromium-bidi/lib/cjs/protocol/protocol.js';\nimport { first, firstValueFrom, forkJoin, from, map, merge, raceWith, zip, } from '../../third_party/rxjs/rxjs.js';\nimport { Frame, throwIfDetached, } from '../api/Frame.js';\nimport { UnsupportedOperation } from '../common/Errors.js';\nimport { fromEmitterEvent, NETWORK_IDLE_TIME, timeout, UTILITY_WORLD_NAME, } from '../common/util.js';\nimport { Deferred } from '../util/Deferred.js';\nimport { disposeSymbol } from '../util/disposable.js';\nimport { ExposeableFunction } from './ExposedFunction.js';\nimport { getBiDiLifecycleEvent, getBiDiReadinessState, rewriteNavigationError, } from './lifecycle.js';\nimport { MAIN_SANDBOX, PUPPETEER_SANDBOX, Sandbox, } from './Sandbox.js';\n/**\n * Puppeteer's Frame class could be viewed as a BiDi BrowsingContext implementation\n * @internal\n */\nlet BidiFrame = (() => {\n    let _classSuper = Frame;\n    let _instanceExtraInitializers = [];\n    let _goto_decorators;\n    let _setContent_decorators;\n    let _waitForNavigation_decorators;\n    return class BidiFrame extends _classSuper {\n        static {\n            const _metadata = typeof Symbol === \"function\" && Symbol.metadata ? Object.create(_classSuper[Symbol.metadata] ?? null) : void 0;\n            __esDecorate(this, null, _goto_decorators, { kind: \"method\", name: \"goto\", static: false, private: false, access: { has: obj => \"goto\" in obj, get: obj => obj.goto }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _setContent_decorators, { kind: \"method\", name: \"setContent\", static: false, private: false, access: { has: obj => \"setContent\" in obj, get: obj => obj.setContent }, metadata: _metadata }, null, _instanceExtraInitializers);\n            __esDecorate(this, null, _waitForNavigation_decorators, { kind: \"method\", name: \"waitForNavigation\", static: false, private: false, access: { has: obj => \"waitForNavigation\" in obj, get: obj => obj.waitForNavigation }, metadata: _metadata }, null, _instanceExtraInitializers);\n            if (_metadata) Object.defineProperty(this, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });\n        }\n        #page = (__runInitializers(this, _instanceExtraInitializers), void 0);\n        #context;\n        #timeoutSettings;\n        #abortDeferred = Deferred.create();\n        #disposed = false;\n        sandboxes;\n        _id;\n        constructor(page, context, timeoutSettings, parentId) {\n            super();\n            this.#page = page;\n            this.#context = context;\n            this.#timeoutSettings = timeoutSettings;\n            this._id = this.#context.id;\n            this._parentId = parentId ?? undefined;\n            this.sandboxes = {\n                [MAIN_SANDBOX]: new Sandbox(undefined, this, context, timeoutSettings),\n                [PUPPETEER_SANDBOX]: new Sandbox(UTILITY_WORLD_NAME, this, context.createRealmForSandbox(), timeoutSettings),\n            };\n        }\n        get client() {\n            return this.context().cdpSession;\n        }\n        mainRealm() {\n            return this.sandboxes[MAIN_SANDBOX];\n        }\n        isolatedRealm() {\n            return this.sandboxes[PUPPETEER_SANDBOX];\n        }\n        page() {\n            return this.#page;\n        }\n        isOOPFrame() {\n            throw new UnsupportedOperation();\n        }\n        url() {\n            return this.#context.url;\n        }\n        parentFrame() {\n            return this.#page.frame(this._parentId ?? '');\n        }\n        childFrames() {\n            return this.#page.childFrames(this.#context.id);\n        }\n        async goto(url, options = {}) {\n            const { waitUntil = 'load', timeout: ms = this.#timeoutSettings.navigationTimeout(), } = options;\n            const [readiness, networkIdle] = getBiDiReadinessState(waitUntil);\n            const result$ = zip(from(this.#context.connection.send('browsingContext.navigate', {\n                context: this.#context.id,\n                url,\n                wait: readiness,\n            })), ...(networkIdle !== null\n                ? [\n                    this.#page.waitForNetworkIdle$({\n                        timeout: ms,\n                        concurrency: networkIdle === 'networkidle2' ? 2 : 0,\n                        idleTime: NETWORK_IDLE_TIME,\n                    }),\n                ]\n                : [])).pipe(map(([{ result }]) => {\n                return result;\n            }), raceWith(timeout(ms), from(this.#abortDeferred.valueOrThrow())), rewriteNavigationError(url, ms));\n            const result = await firstValueFrom(result$);\n            return this.#page.getNavigationResponse(result.navigation);\n        }\n        async setContent(html, options = {}) {\n            const { waitUntil = 'load', timeout: ms = this.#timeoutSettings.navigationTimeout(), } = options;\n            const [waitEvent, networkIdle] = getBiDiLifecycleEvent(waitUntil);\n            const result$ = zip(forkJoin([\n                fromEmitterEvent(this.#context, waitEvent).pipe(first()),\n                from(this.setFrameContent(html)),\n            ]).pipe(map(() => {\n                return null;\n            })), ...(networkIdle !== null\n                ? [\n                    this.#page.waitForNetworkIdle$({\n                        timeout: ms,\n                        concurrency: networkIdle === 'networkidle2' ? 2 : 0,\n                        idleTime: NETWORK_IDLE_TIME,\n                    }),\n                ]\n                : [])).pipe(raceWith(timeout(ms), from(this.#abortDeferred.valueOrThrow())), rewriteNavigationError('setContent', ms));\n            await firstValueFrom(result$);\n        }\n        context() {\n            return this.#context;\n        }\n        async waitForNavigation(options = {}) {\n            const { waitUntil = 'load', timeout: ms = this.#timeoutSettings.navigationTimeout(), } = options;\n            const [waitUntilEvent, networkIdle] = getBiDiLifecycleEvent(waitUntil);\n            const navigation$ = merge(forkJoin([\n                fromEmitterEvent(this.#context, Bidi.ChromiumBidi.BrowsingContext.EventNames.NavigationStarted).pipe(first()),\n                fromEmitterEvent(this.#context, waitUntilEvent).pipe(first()),\n            ]), fromEmitterEvent(this.#context, Bidi.ChromiumBidi.BrowsingContext.EventNames.FragmentNavigated)).pipe(map(result => {\n                if (Array.isArray(result)) {\n                    return { result: result[1] };\n                }\n                return { result };\n            }));\n            const result$ = zip(navigation$, ...(networkIdle !== null\n                ? [\n                    this.#page.waitForNetworkIdle$({\n                        timeout: ms,\n                        concurrency: networkIdle === 'networkidle2' ? 2 : 0,\n                        idleTime: NETWORK_IDLE_TIME,\n                    }),\n                ]\n                : [])).pipe(map(([{ result }]) => {\n                return result;\n            }), raceWith(timeout(ms), from(this.#abortDeferred.valueOrThrow())));\n            const result = await firstValueFrom(result$);\n            return this.#page.getNavigationResponse(result.navigation);\n        }\n        waitForDevicePrompt() {\n            throw new UnsupportedOperation();\n        }\n        get detached() {\n            return this.#disposed;\n        }\n        [(_goto_decorators = [throwIfDetached], _setContent_decorators = [throwIfDetached], _waitForNavigation_decorators = [throwIfDetached], disposeSymbol)]() {\n            if (this.#disposed) {\n                return;\n            }\n            this.#disposed = true;\n            this.#abortDeferred.reject(new Error('Frame detached'));\n            this.#context.dispose();\n            this.sandboxes[MAIN_SANDBOX][disposeSymbol]();\n            this.sandboxes[PUPPETEER_SANDBOX][disposeSymbol]();\n        }\n        #exposedFunctions = new Map();\n        async exposeFunction(name, apply) {\n            if (this.#exposedFunctions.has(name)) {\n                throw new Error(`Failed to add page binding with name ${name}: globalThis['${name}'] already exists!`);\n            }\n            const exposeable = new ExposeableFunction(this, name, apply);\n            this.#exposedFunctions.set(name, exposeable);\n            try {\n                await exposeable.expose();\n            }\n            catch (error) {\n                this.#exposedFunctions.delete(name);\n                throw error;\n            }\n        }\n        waitForSelector(selector, options) {\n            if (selector.startsWith('aria')) {\n                throw new UnsupportedOperation('ARIA selector is not supported for BiDi!');\n            }\n            return super.waitForSelector(selector, options);\n        }\n    };\n})();\nexport { BidiFrame };\n//# sourceMappingURL=Frame.js.map", "/**\n * @license\n * Copyright 2017 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nimport { <PERSON><PERSON>, Mouse, MouseButton, Touchscreen, } from '../api/Input.js';\nimport { UnsupportedOperation } from '../common/Errors.js';\nvar SourceActionsType;\n(function (SourceActionsType) {\n    SourceActionsType[\"None\"] = \"none\";\n    SourceActionsType[\"Key\"] = \"key\";\n    SourceActionsType[\"Pointer\"] = \"pointer\";\n    SourceActionsType[\"Wheel\"] = \"wheel\";\n})(SourceActionsType || (SourceActionsType = {}));\nvar ActionType;\n(function (ActionType) {\n    ActionType[\"Pause\"] = \"pause\";\n    ActionType[\"KeyDown\"] = \"keyDown\";\n    ActionType[\"KeyUp\"] = \"keyUp\";\n    ActionType[\"PointerUp\"] = \"pointerUp\";\n    ActionType[\"PointerDown\"] = \"pointerDown\";\n    ActionType[\"PointerMove\"] = \"pointerMove\";\n    ActionType[\"Scroll\"] = \"scroll\";\n})(ActionType || (ActionType = {}));\nconst getBidiKeyValue = (key) => {\n    switch (key) {\n        case '\\r':\n        case '\\n':\n            key = 'Enter';\n            break;\n    }\n    // Measures the number of code points rather than UTF-16 code units.\n    if ([...key].length === 1) {\n        return key;\n    }\n    switch (key) {\n        case 'Cancel':\n            return '\\uE001';\n        case 'Help':\n            return '\\uE002';\n        case 'Backspace':\n            return '\\uE003';\n        case 'Tab':\n            return '\\uE004';\n        case 'Clear':\n            return '\\uE005';\n        case 'Enter':\n            return '\\uE007';\n        case 'Shift':\n        case 'ShiftLeft':\n            return '\\uE008';\n        case 'Control':\n        case 'ControlLeft':\n            return '\\uE009';\n        case 'Alt':\n        case 'AltLeft':\n            return '\\uE00A';\n        case 'Pause':\n            return '\\uE00B';\n        case 'Escape':\n            return '\\uE00C';\n        case 'PageUp':\n            return '\\uE00E';\n        case 'PageDown':\n            return '\\uE00F';\n        case 'End':\n            return '\\uE010';\n        case 'Home':\n            return '\\uE011';\n        case 'ArrowLeft':\n            return '\\uE012';\n        case 'ArrowUp':\n            return '\\uE013';\n        case 'ArrowRight':\n            return '\\uE014';\n        case 'ArrowDown':\n            return '\\uE015';\n        case 'Insert':\n            return '\\uE016';\n        case 'Delete':\n            return '\\uE017';\n        case 'NumpadEqual':\n            return '\\uE019';\n        case 'Numpad0':\n            return '\\uE01A';\n        case 'Numpad1':\n            return '\\uE01B';\n        case 'Numpad2':\n            return '\\uE01C';\n        case 'Numpad3':\n            return '\\uE01D';\n        case 'Numpad4':\n            return '\\uE01E';\n        case 'Numpad5':\n            return '\\uE01F';\n        case 'Numpad6':\n            return '\\uE020';\n        case 'Numpad7':\n            return '\\uE021';\n        case 'Numpad8':\n            return '\\uE022';\n        case 'Numpad9':\n            return '\\uE023';\n        case 'NumpadMultiply':\n            return '\\uE024';\n        case 'NumpadAdd':\n            return '\\uE025';\n        case 'NumpadSubtract':\n            return '\\uE027';\n        case 'NumpadDecimal':\n            return '\\uE028';\n        case 'NumpadDivide':\n            return '\\uE029';\n        case 'F1':\n            return '\\uE031';\n        case 'F2':\n            return '\\uE032';\n        case 'F3':\n            return '\\uE033';\n        case 'F4':\n            return '\\uE034';\n        case 'F5':\n            return '\\uE035';\n        case 'F6':\n            return '\\uE036';\n        case 'F7':\n            return '\\uE037';\n        case 'F8':\n            return '\\uE038';\n        case 'F9':\n            return '\\uE039';\n        case 'F10':\n            return '\\uE03A';\n        case 'F11':\n            return '\\uE03B';\n        case 'F12':\n            return '\\uE03C';\n        case 'Meta':\n        case 'MetaLeft':\n            return '\\uE03D';\n        case 'ShiftRight':\n            return '\\uE050';\n        case 'ControlRight':\n            return '\\uE051';\n        case 'AltRight':\n            return '\\uE052';\n        case 'MetaRight':\n            return '\\uE053';\n        case 'Digit0':\n            return '0';\n        case 'Digit1':\n            return '1';\n        case 'Digit2':\n            return '2';\n        case 'Digit3':\n            return '3';\n        case 'Digit4':\n            return '4';\n        case 'Digit5':\n            return '5';\n        case 'Digit6':\n            return '6';\n        case 'Digit7':\n            return '7';\n        case 'Digit8':\n            return '8';\n        case 'Digit9':\n            return '9';\n        case 'KeyA':\n            return 'a';\n        case 'KeyB':\n            return 'b';\n        case 'KeyC':\n            return 'c';\n        case 'KeyD':\n            return 'd';\n        case 'KeyE':\n            return 'e';\n        case 'KeyF':\n            return 'f';\n        case 'KeyG':\n            return 'g';\n        case 'KeyH':\n            return 'h';\n        case 'KeyI':\n            return 'i';\n        case 'KeyJ':\n            return 'j';\n        case 'KeyK':\n            return 'k';\n        case 'KeyL':\n            return 'l';\n        case 'KeyM':\n            return 'm';\n        case 'KeyN':\n            return 'n';\n        case 'KeyO':\n            return 'o';\n        case 'KeyP':\n            return 'p';\n        case 'KeyQ':\n            return 'q';\n        case 'KeyR':\n            return 'r';\n        case 'KeyS':\n            return 's';\n        case 'KeyT':\n            return 't';\n        case 'KeyU':\n            return 'u';\n        case 'KeyV':\n            return 'v';\n        case 'KeyW':\n            return 'w';\n        case 'KeyX':\n            return 'x';\n        case 'KeyY':\n            return 'y';\n        case 'KeyZ':\n            return 'z';\n        case 'Semicolon':\n            return ';';\n        case 'Equal':\n            return '=';\n        case 'Comma':\n            return ',';\n        case 'Minus':\n            return '-';\n        case 'Period':\n            return '.';\n        case 'Slash':\n            return '/';\n        case 'Backquote':\n            return '`';\n        case 'BracketLeft':\n            return '[';\n        case 'Backslash':\n            return '\\\\';\n        case 'BracketRight':\n            return ']';\n        case 'Quote':\n            return '\"';\n        default:\n            throw new Error(`Unknown key: \"${key}\"`);\n    }\n};\n/**\n * @internal\n */\nexport class BidiKeyboard extends Keyboard {\n    #page;\n    constructor(page) {\n        super();\n        this.#page = page;\n    }\n    async down(key, _options) {\n        await this.#page.connection.send('input.performActions', {\n            context: this.#page.mainFrame()._id,\n            actions: [\n                {\n                    type: SourceActionsType.Key,\n                    id: \"__puppeteer_keyboard\" /* InputId.Keyboard */,\n                    actions: [\n                        {\n                            type: ActionType.KeyDown,\n                            value: getBidiKeyValue(key),\n                        },\n                    ],\n                },\n            ],\n        });\n    }\n    async up(key) {\n        await this.#page.connection.send('input.performActions', {\n            context: this.#page.mainFrame()._id,\n            actions: [\n                {\n                    type: SourceActionsType.Key,\n                    id: \"__puppeteer_keyboard\" /* InputId.Keyboard */,\n                    actions: [\n                        {\n                            type: ActionType.KeyUp,\n                            value: getBidiKeyValue(key),\n                        },\n                    ],\n                },\n            ],\n        });\n    }\n    async press(key, options = {}) {\n        const { delay = 0 } = options;\n        const actions = [\n            {\n                type: ActionType.KeyDown,\n                value: getBidiKeyValue(key),\n            },\n        ];\n        if (delay > 0) {\n            actions.push({\n                type: ActionType.Pause,\n                duration: delay,\n            });\n        }\n        actions.push({\n            type: ActionType.KeyUp,\n            value: getBidiKeyValue(key),\n        });\n        await this.#page.connection.send('input.performActions', {\n            context: this.#page.mainFrame()._id,\n            actions: [\n                {\n                    type: SourceActionsType.Key,\n                    id: \"__puppeteer_keyboard\" /* InputId.Keyboard */,\n                    actions,\n                },\n            ],\n        });\n    }\n    async type(text, options = {}) {\n        const { delay = 0 } = options;\n        // This spread separates the characters into code points rather than UTF-16\n        // code units.\n        const values = [...text].map(getBidiKeyValue);\n        const actions = [];\n        if (delay <= 0) {\n            for (const value of values) {\n                actions.push({\n                    type: ActionType.KeyDown,\n                    value,\n                }, {\n                    type: ActionType.KeyUp,\n                    value,\n                });\n            }\n        }\n        else {\n            for (const value of values) {\n                actions.push({\n                    type: ActionType.KeyDown,\n                    value,\n                }, {\n                    type: ActionType.Pause,\n                    duration: delay,\n                }, {\n                    type: ActionType.KeyUp,\n                    value,\n                });\n            }\n        }\n        await this.#page.connection.send('input.performActions', {\n            context: this.#page.mainFrame()._id,\n            actions: [\n                {\n                    type: SourceActionsType.Key,\n                    id: \"__puppeteer_keyboard\" /* InputId.Keyboard */,\n                    actions,\n                },\n            ],\n        });\n    }\n    async sendCharacter(char) {\n        // Measures the number of code points rather than UTF-16 code units.\n        if ([...char].length > 1) {\n            throw new Error('Cannot send more than 1 character.');\n        }\n        const frame = await this.#page.focusedFrame();\n        await frame.isolatedRealm().evaluate(async (char) => {\n            document.execCommand('insertText', false, char);\n        }, char);\n    }\n}\nconst getBidiButton = (button) => {\n    switch (button) {\n        case MouseButton.Left:\n            return 0;\n        case MouseButton.Middle:\n            return 1;\n        case MouseButton.Right:\n            return 2;\n        case MouseButton.Back:\n            return 3;\n        case MouseButton.Forward:\n            return 4;\n    }\n};\n/**\n * @internal\n */\nexport class BidiMouse extends Mouse {\n    #context;\n    #lastMovePoint = { x: 0, y: 0 };\n    constructor(context) {\n        super();\n        this.#context = context;\n    }\n    async reset() {\n        this.#lastMovePoint = { x: 0, y: 0 };\n        await this.#context.connection.send('input.releaseActions', {\n            context: this.#context.id,\n        });\n    }\n    async move(x, y, options = {}) {\n        const from = this.#lastMovePoint;\n        const to = {\n            x: Math.round(x),\n            y: Math.round(y),\n        };\n        const actions = [];\n        const steps = options.steps ?? 0;\n        for (let i = 0; i < steps; ++i) {\n            actions.push({\n                type: ActionType.PointerMove,\n                x: from.x + (to.x - from.x) * (i / steps),\n                y: from.y + (to.y - from.y) * (i / steps),\n                origin: options.origin,\n            });\n        }\n        actions.push({\n            type: ActionType.PointerMove,\n            ...to,\n            origin: options.origin,\n        });\n        // https://w3c.github.io/webdriver-bidi/#command-input-performActions:~:text=input.PointerMoveAction%20%3D%20%7B%0A%20%20type%3A%20%22pointerMove%22%2C%0A%20%20x%3A%20js%2Dint%2C\n        this.#lastMovePoint = to;\n        await this.#context.connection.send('input.performActions', {\n            context: this.#context.id,\n            actions: [\n                {\n                    type: SourceActionsType.Pointer,\n                    id: \"__puppeteer_mouse\" /* InputId.Mouse */,\n                    actions,\n                },\n            ],\n        });\n    }\n    async down(options = {}) {\n        await this.#context.connection.send('input.performActions', {\n            context: this.#context.id,\n            actions: [\n                {\n                    type: SourceActionsType.Pointer,\n                    id: \"__puppeteer_mouse\" /* InputId.Mouse */,\n                    actions: [\n                        {\n                            type: ActionType.PointerDown,\n                            button: getBidiButton(options.button ?? MouseButton.Left),\n                        },\n                    ],\n                },\n            ],\n        });\n    }\n    async up(options = {}) {\n        await this.#context.connection.send('input.performActions', {\n            context: this.#context.id,\n            actions: [\n                {\n                    type: SourceActionsType.Pointer,\n                    id: \"__puppeteer_mouse\" /* InputId.Mouse */,\n                    actions: [\n                        {\n                            type: ActionType.PointerUp,\n                            button: getBidiButton(options.button ?? MouseButton.Left),\n                        },\n                    ],\n                },\n            ],\n        });\n    }\n    async click(x, y, options = {}) {\n        const actions = [\n            {\n                type: ActionType.PointerMove,\n                x: Math.round(x),\n                y: Math.round(y),\n                origin: options.origin,\n            },\n        ];\n        const pointerDownAction = {\n            type: ActionType.PointerDown,\n            button: getBidiButton(options.button ?? MouseButton.Left),\n        };\n        const pointerUpAction = {\n            type: ActionType.PointerUp,\n            button: pointerDownAction.button,\n        };\n        for (let i = 1; i < (options.count ?? 1); ++i) {\n            actions.push(pointerDownAction, pointerUpAction);\n        }\n        actions.push(pointerDownAction);\n        if (options.delay) {\n            actions.push({\n                type: ActionType.Pause,\n                duration: options.delay,\n            });\n        }\n        actions.push(pointerUpAction);\n        await this.#context.connection.send('input.performActions', {\n            context: this.#context.id,\n            actions: [\n                {\n                    type: SourceActionsType.Pointer,\n                    id: \"__puppeteer_mouse\" /* InputId.Mouse */,\n                    actions,\n                },\n            ],\n        });\n    }\n    async wheel(options = {}) {\n        await this.#context.connection.send('input.performActions', {\n            context: this.#context.id,\n            actions: [\n                {\n                    type: SourceActionsType.Wheel,\n                    id: \"__puppeteer_wheel\" /* InputId.Wheel */,\n                    actions: [\n                        {\n                            type: ActionType.Scroll,\n                            ...(this.#lastMovePoint ?? {\n                                x: 0,\n                                y: 0,\n                            }),\n                            deltaX: options.deltaX ?? 0,\n                            deltaY: options.deltaY ?? 0,\n                        },\n                    ],\n                },\n            ],\n        });\n    }\n    drag() {\n        throw new UnsupportedOperation();\n    }\n    dragOver() {\n        throw new UnsupportedOperation();\n    }\n    dragEnter() {\n        throw new UnsupportedOperation();\n    }\n    drop() {\n        throw new UnsupportedOperation();\n    }\n    dragAndDrop() {\n        throw new UnsupportedOperation();\n    }\n}\n/**\n * @internal\n */\nexport class BidiTouchscreen extends Touchscreen {\n    #context;\n    constructor(context) {\n        super();\n        this.#context = context;\n    }\n    async touchStart(x, y, options = {}) {\n        await this.#context.connection.send('input.performActions', {\n            context: this.#context.id,\n            actions: [\n                {\n                    type: SourceActionsType.Pointer,\n                    id: \"__puppeteer_finger\" /* InputId.Finger */,\n                    parameters: {\n                        pointerType: \"touch\" /* Bidi.Input.PointerType.Touch */,\n                    },\n                    actions: [\n                        {\n                            type: ActionType.PointerMove,\n                            x: Math.round(x),\n                            y: Math.round(y),\n                            origin: options.origin,\n                        },\n                        {\n                            type: ActionType.PointerDown,\n                            button: 0,\n                        },\n                    ],\n                },\n            ],\n        });\n    }\n    async touchMove(x, y, options = {}) {\n        await this.#context.connection.send('input.performActions', {\n            context: this.#context.id,\n            actions: [\n                {\n                    type: SourceActionsType.Pointer,\n                    id: \"__puppeteer_finger\" /* InputId.Finger */,\n                    parameters: {\n                        pointerType: \"touch\" /* Bidi.Input.PointerType.Touch */,\n                    },\n                    actions: [\n                        {\n                            type: ActionType.PointerMove,\n                            x: Math.round(x),\n                            y: Math.round(y),\n                            origin: options.origin,\n                        },\n                    ],\n                },\n            ],\n        });\n    }\n    async touchEnd() {\n        await this.#context.connection.send('input.performActions', {\n            context: this.#context.id,\n            actions: [\n                {\n                    type: SourceActionsType.Pointer,\n                    id: \"__puppeteer_finger\" /* InputId.Finger */,\n                    parameters: {\n                        pointerType: \"touch\" /* Bidi.Input.PointerType.Touch */,\n                    },\n                    actions: [\n                        {\n                            type: ActionType.PointerUp,\n                            button: 0,\n                        },\n                    ],\n                },\n            ],\n        });\n    }\n}\n//# sourceMappingURL=Input.js.map", "import { HTTPRequest } from '../api/HTTPRequest.js';\nimport { UnsupportedOperation } from '../common/Errors.js';\n/**\n * @internal\n */\nexport class BidiHTTPRequest extends HTTPRequest {\n    _response = null;\n    _redirectChain;\n    _navigationId;\n    #url;\n    #resourceType;\n    #method;\n    #postData;\n    #headers = {};\n    #initiator;\n    #frame;\n    constructor(event, frame, redirectChain = []) {\n        super();\n        this.#url = event.request.url;\n        this.#resourceType = event.initiator.type.toLowerCase();\n        this.#method = event.request.method;\n        this.#postData = undefined;\n        this.#initiator = event.initiator;\n        this.#frame = frame;\n        this._requestId = event.request.request;\n        this._redirectChain = redirectChain;\n        this._navigationId = event.navigation;\n        for (const header of event.request.headers) {\n            // TODO: How to handle Binary Headers\n            // https://w3c.github.io/webdriver-bidi/#type-network-Header\n            if (header.value.type === 'string') {\n                this.#headers[header.name.toLowerCase()] = header.value.value;\n            }\n        }\n    }\n    get client() {\n        throw new UnsupportedOperation();\n    }\n    url() {\n        return this.#url;\n    }\n    resourceType() {\n        return this.#resourceType;\n    }\n    method() {\n        return this.#method;\n    }\n    postData() {\n        return this.#postData;\n    }\n    hasPostData() {\n        return this.#postData !== undefined;\n    }\n    async fetchPostData() {\n        return this.#postData;\n    }\n    headers() {\n        return this.#headers;\n    }\n    response() {\n        return this._response;\n    }\n    isNavigationRequest() {\n        return Boolean(this._navigationId);\n    }\n    initiator() {\n        return this.#initiator;\n    }\n    redirectChain() {\n        return this._redirectChain.slice();\n    }\n    enqueueInterceptAction(pendingHandler) {\n        // Execute the handler when interception is not supported\n        void pendingHandler();\n    }\n    frame() {\n        return this.#frame;\n    }\n    continueRequestOverrides() {\n        throw new UnsupportedOperation();\n    }\n    continue(_overrides = {}) {\n        throw new UnsupportedOperation();\n    }\n    responseForRequest() {\n        throw new UnsupportedOperation();\n    }\n    abortErrorReason() {\n        throw new UnsupportedOperation();\n    }\n    interceptResolutionState() {\n        throw new UnsupportedOperation();\n    }\n    isInterceptResolutionHandled() {\n        throw new UnsupportedOperation();\n    }\n    finalizeInterceptions() {\n        throw new UnsupportedOperation();\n    }\n    abort() {\n        throw new UnsupportedOperation();\n    }\n    respond(_response, _priority) {\n        throw new UnsupportedOperation();\n    }\n    failure() {\n        throw new UnsupportedOperation();\n    }\n}\n//# sourceMappingURL=HTTPRequest.js.map", "import { HTTPResponse as HTTPResponse, } from '../api/HTTPResponse.js';\nimport { UnsupportedOperation } from '../common/Errors.js';\n/**\n * @internal\n */\nexport class BidiHTTPResponse extends HTTPResponse {\n    #request;\n    #remoteAddress;\n    #status;\n    #statusText;\n    #url;\n    #fromCache;\n    #headers = {};\n    #timings;\n    constructor(request, { response }) {\n        super();\n        this.#request = request;\n        this.#remoteAddress = {\n            ip: '',\n            port: -1,\n        };\n        this.#url = response.url;\n        this.#fromCache = response.fromCache;\n        this.#status = response.status;\n        this.#statusText = response.statusText;\n        // TODO: File and issue with BiDi spec\n        this.#timings = null;\n        // TODO: Removed once the Firefox implementation is compliant with https://w3c.github.io/webdriver-bidi/#get-the-response-data.\n        for (const header of response.headers || []) {\n            // TODO: How to handle Binary Headers\n            // https://w3c.github.io/webdriver-bidi/#type-network-Header\n            if (header.value.type === 'string') {\n                this.#headers[header.name.toLowerCase()] = header.value.value;\n            }\n        }\n    }\n    remoteAddress() {\n        return this.#remoteAddress;\n    }\n    url() {\n        return this.#url;\n    }\n    status() {\n        return this.#status;\n    }\n    statusText() {\n        return this.#statusText;\n    }\n    headers() {\n        return this.#headers;\n    }\n    request() {\n        return this.#request;\n    }\n    fromCache() {\n        return this.#fromCache;\n    }\n    timing() {\n        return this.#timings;\n    }\n    frame() {\n        return this.#request.frame();\n    }\n    fromServiceWorker() {\n        return false;\n    }\n    securityDetails() {\n        throw new UnsupportedOperation();\n    }\n    buffer() {\n        throw new UnsupportedOperation();\n    }\n}\n//# sourceMappingURL=HTTPResponse.js.map", "/**\n * @license\n * Copyright 2023 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nimport { EventEmitter, EventSubscription } from '../common/EventEmitter.js';\nimport { NetworkManagerEvent, } from '../common/NetworkManagerEvents.js';\nimport { DisposableStack } from '../util/disposable.js';\nimport { BidiHTTPRequest } from './HTTPRequest.js';\nimport { BidiHTTPResponse } from './HTTPResponse.js';\n/**\n * @internal\n */\nexport class BidiNetworkManager extends EventEmitter {\n    #connection;\n    #page;\n    #subscriptions = new DisposableStack();\n    #requestMap = new Map();\n    #navigationMap = new Map();\n    constructor(connection, page) {\n        super();\n        this.#connection = connection;\n        this.#page = page;\n        // TODO: Subscribe to the Frame individually\n        this.#subscriptions.use(new EventSubscription(this.#connection, 'network.beforeRequestSent', this.#onBeforeRequestSent.bind(this)));\n        this.#subscriptions.use(new EventSubscription(this.#connection, 'network.responseStarted', this.#onResponseStarted.bind(this)));\n        this.#subscriptions.use(new EventSubscription(this.#connection, 'network.responseCompleted', this.#onResponseCompleted.bind(this)));\n        this.#subscriptions.use(new EventSubscription(this.#connection, 'network.fetchError', this.#onFetchError.bind(this)));\n    }\n    #onBeforeRequestSent(event) {\n        const frame = this.#page.frame(event.context ?? '');\n        if (!frame) {\n            return;\n        }\n        const request = this.#requestMap.get(event.request.request);\n        let upsertRequest;\n        if (request) {\n            request._redirectChain.push(request);\n            upsertRequest = new BidiHTTPRequest(event, frame, request._redirectChain);\n        }\n        else {\n            upsertRequest = new BidiHTTPRequest(event, frame, []);\n        }\n        this.#requestMap.set(event.request.request, upsertRequest);\n        this.emit(NetworkManagerEvent.Request, upsertRequest);\n    }\n    #onResponseStarted(_event) { }\n    #onResponseCompleted(event) {\n        const request = this.#requestMap.get(event.request.request);\n        if (!request) {\n            return;\n        }\n        const response = new BidiHTTPResponse(request, event);\n        request._response = response;\n        if (event.navigation) {\n            this.#navigationMap.set(event.navigation, response);\n        }\n        if (response.fromCache()) {\n            this.emit(NetworkManagerEvent.RequestServedFromCache, request);\n        }\n        this.emit(NetworkManagerEvent.Response, response);\n        this.emit(NetworkManagerEvent.RequestFinished, request);\n    }\n    #onFetchError(event) {\n        const request = this.#requestMap.get(event.request.request);\n        if (!request) {\n            return;\n        }\n        request._failureText = event.errorText;\n        this.emit(NetworkManagerEvent.RequestFailed, request);\n        this.#requestMap.delete(event.request.request);\n    }\n    getNavigationResponse(navigationId) {\n        if (!navigationId) {\n            return null;\n        }\n        const response = this.#navigationMap.get(navigationId);\n        return response ?? null;\n    }\n    inFlightRequestsCount() {\n        let inFlightRequestCounter = 0;\n        for (const request of this.#requestMap.values()) {\n            if (!request.response() || request._failureText) {\n                inFlightRequestCounter++;\n            }\n        }\n        return inFlightRequestCounter;\n    }\n    clearMapAfterFrameDispose(frame) {\n        for (const [id, request] of this.#requestMap.entries()) {\n            if (request.frame() === frame) {\n                this.#requestMap.delete(id);\n            }\n        }\n        for (const [id, response] of this.#navigationMap.entries()) {\n            if (response.frame() === frame) {\n                this.#navigationMap.delete(id);\n            }\n        }\n    }\n    dispose() {\n        this.removeAllListeners();\n        this.#requestMap.clear();\n        this.#navigationMap.clear();\n        this.#subscriptions.dispose();\n    }\n}\n//# sourceMappingURL=NetworkManager.js.map", "/**\n * @license\n * Copyright 2022 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nvar __addDisposableResource = (this && this.__addDisposableResource) || function (env, value, async) {\n    if (value !== null && value !== void 0) {\n        if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n        var dispose;\n        if (async) {\n            if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n            dispose = value[Symbol.asyncDispose];\n        }\n        if (dispose === void 0) {\n            if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n            dispose = value[Symbol.dispose];\n        }\n        if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n        env.stack.push({ value: value, dispose: dispose, async: async });\n    }\n    else if (async) {\n        env.stack.push({ async: true });\n    }\n    return value;\n};\nvar __disposeResources = (this && this.__disposeResources) || (function (SuppressedError) {\n    return function (env) {\n        function fail(e) {\n            env.error = env.hasError ? new SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n            env.hasError = true;\n        }\n        function next() {\n            while (env.stack.length) {\n                var rec = env.stack.pop();\n                try {\n                    var result = rec.dispose && rec.dispose.call(rec.value);\n                    if (rec.async) return Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n                }\n                catch (e) {\n                    fail(e);\n                }\n            }\n            if (env.hasError) throw env.error;\n        }\n        return next();\n    };\n})(typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n    var e = new Error(message);\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n});\nimport { firstValueFrom, from, map, raceWith, zip, } from '../../third_party/rxjs/rxjs.js';\nimport { Page, } from '../api/Page.js';\nimport { Accessibility } from '../cdp/Accessibility.js';\nimport { Coverage } from '../cdp/Coverage.js';\nimport { EmulationManager as CdpEmulationManager } from '../cdp/EmulationManager.js';\nimport { FrameTree } from '../cdp/FrameTree.js';\nimport { Tracing } from '../cdp/Tracing.js';\nimport { ConsoleMessage, } from '../common/ConsoleMessage.js';\nimport { TargetCloseError, UnsupportedOperation } from '../common/Errors.js';\nimport { NetworkManagerEvent } from '../common/NetworkManagerEvents.js';\nimport { debugError, evaluationString, NETWORK_IDLE_TIME, parsePDFOptions, timeout, validateDialogType, } from '../common/util.js';\nimport { assert } from '../util/assert.js';\nimport { Deferred } from '../util/Deferred.js';\nimport { disposeSymbol } from '../util/disposable.js';\nimport { isErrorLike } from '../util/ErrorLike.js';\nimport { BrowsingContextEvent, CdpSessionWrapper, } from './BrowsingContext.js';\nimport { BidiDeserializer } from './Deserializer.js';\nimport { BidiDialog } from './Dialog.js';\nimport { BidiElementHandle } from './ElementHandle.js';\nimport { EmulationManager } from './EmulationManager.js';\nimport { BidiFrame } from './Frame.js';\nimport { BidiKeyboard, BidiMouse, BidiTouchscreen } from './Input.js';\nimport { getBiDiReadinessState, rewriteNavigationError } from './lifecycle.js';\nimport { BidiNetworkManager } from './NetworkManager.js';\nimport { createBidiHandle } from './Realm.js';\n/**\n * @internal\n */\nexport class BidiPage extends Page {\n    #accessibility;\n    #connection;\n    #frameTree = new FrameTree();\n    #networkManager;\n    #viewport = null;\n    #closedDeferred = Deferred.create();\n    #subscribedEvents = new Map([\n        ['log.entryAdded', this.#onLogEntryAdded.bind(this)],\n        ['browsingContext.load', this.#onFrameLoaded.bind(this)],\n        [\n            'browsingContext.fragmentNavigated',\n            this.#onFrameFragmentNavigated.bind(this),\n        ],\n        [\n            'browsingContext.domContentLoaded',\n            this.#onFrameDOMContentLoaded.bind(this),\n        ],\n        ['browsingContext.userPromptOpened', this.#onDialog.bind(this)],\n    ]);\n    #networkManagerEvents = [\n        [\n            NetworkManagerEvent.Request,\n            (request) => {\n                this.emit(\"request\" /* PageEvent.Request */, request);\n            },\n        ],\n        [\n            NetworkManagerEvent.RequestServedFromCache,\n            (request) => {\n                this.emit(\"requestservedfromcache\" /* PageEvent.RequestServedFromCache */, request);\n            },\n        ],\n        [\n            NetworkManagerEvent.RequestFailed,\n            (request) => {\n                this.emit(\"requestfailed\" /* PageEvent.RequestFailed */, request);\n            },\n        ],\n        [\n            NetworkManagerEvent.RequestFinished,\n            (request) => {\n                this.emit(\"requestfinished\" /* PageEvent.RequestFinished */, request);\n            },\n        ],\n        [\n            NetworkManagerEvent.Response,\n            (response) => {\n                this.emit(\"response\" /* PageEvent.Response */, response);\n            },\n        ],\n    ];\n    #browsingContextEvents = new Map([\n        [BrowsingContextEvent.Created, this.#onContextCreated.bind(this)],\n        [BrowsingContextEvent.Destroyed, this.#onContextDestroyed.bind(this)],\n    ]);\n    #tracing;\n    #coverage;\n    #cdpEmulationManager;\n    #emulationManager;\n    #mouse;\n    #touchscreen;\n    #keyboard;\n    #browsingContext;\n    #browserContext;\n    #target;\n    _client() {\n        return this.mainFrame().context().cdpSession;\n    }\n    constructor(browsingContext, browserContext, target) {\n        super();\n        this.#browsingContext = browsingContext;\n        this.#browserContext = browserContext;\n        this.#target = target;\n        this.#connection = browsingContext.connection;\n        for (const [event, subscriber] of this.#browsingContextEvents) {\n            this.#browsingContext.on(event, subscriber);\n        }\n        this.#networkManager = new BidiNetworkManager(this.#connection, this);\n        for (const [event, subscriber] of this.#subscribedEvents) {\n            this.#connection.on(event, subscriber);\n        }\n        for (const [event, subscriber] of this.#networkManagerEvents) {\n            // TODO: remove any\n            this.#networkManager.on(event, subscriber);\n        }\n        const frame = new BidiFrame(this, this.#browsingContext, this._timeoutSettings, this.#browsingContext.parent);\n        this.#frameTree.addFrame(frame);\n        this.emit(\"frameattached\" /* PageEvent.FrameAttached */, frame);\n        // TODO: https://github.com/w3c/webdriver-bidi/issues/443\n        this.#accessibility = new Accessibility(this.mainFrame().context().cdpSession);\n        this.#tracing = new Tracing(this.mainFrame().context().cdpSession);\n        this.#coverage = new Coverage(this.mainFrame().context().cdpSession);\n        this.#cdpEmulationManager = new CdpEmulationManager(this.mainFrame().context().cdpSession);\n        this.#emulationManager = new EmulationManager(browsingContext);\n        this.#mouse = new BidiMouse(this.mainFrame().context());\n        this.#touchscreen = new BidiTouchscreen(this.mainFrame().context());\n        this.#keyboard = new BidiKeyboard(this);\n    }\n    /**\n     * @internal\n     */\n    get connection() {\n        return this.#connection;\n    }\n    async setUserAgent(userAgent, userAgentMetadata) {\n        // TODO: handle CDP-specific cases such as mprach.\n        await this._client().send('Network.setUserAgentOverride', {\n            userAgent: userAgent,\n            userAgentMetadata: userAgentMetadata,\n        });\n    }\n    async setBypassCSP(enabled) {\n        // TODO: handle CDP-specific cases such as mprach.\n        await this._client().send('Page.setBypassCSP', { enabled });\n    }\n    async queryObjects(prototypeHandle) {\n        assert(!prototypeHandle.disposed, 'Prototype JSHandle is disposed!');\n        assert(prototypeHandle.id, 'Prototype JSHandle must not be referencing primitive value');\n        const response = await this.mainFrame().client.send('Runtime.queryObjects', {\n            prototypeObjectId: prototypeHandle.id,\n        });\n        return createBidiHandle(this.mainFrame().mainRealm(), {\n            type: 'array',\n            handle: response.objects.objectId,\n        });\n    }\n    _setBrowserContext(browserContext) {\n        this.#browserContext = browserContext;\n    }\n    get accessibility() {\n        return this.#accessibility;\n    }\n    get tracing() {\n        return this.#tracing;\n    }\n    get coverage() {\n        return this.#coverage;\n    }\n    get mouse() {\n        return this.#mouse;\n    }\n    get touchscreen() {\n        return this.#touchscreen;\n    }\n    get keyboard() {\n        return this.#keyboard;\n    }\n    browser() {\n        return this.browserContext().browser();\n    }\n    browserContext() {\n        return this.#browserContext;\n    }\n    mainFrame() {\n        const mainFrame = this.#frameTree.getMainFrame();\n        assert(mainFrame, 'Requesting main frame too early!');\n        return mainFrame;\n    }\n    /**\n     * @internal\n     */\n    async focusedFrame() {\n        const env_1 = { stack: [], error: void 0, hasError: false };\n        try {\n            const frame = __addDisposableResource(env_1, await this.mainFrame()\n                .isolatedRealm()\n                .evaluateHandle(() => {\n                let frame;\n                let win = window;\n                while (win?.document.activeElement instanceof HTMLIFrameElement) {\n                    frame = win.document.activeElement;\n                    win = frame.contentWindow;\n                }\n                return frame;\n            }), false);\n            if (!(frame instanceof BidiElementHandle)) {\n                return this.mainFrame();\n            }\n            return await frame.contentFrame();\n        }\n        catch (e_1) {\n            env_1.error = e_1;\n            env_1.hasError = true;\n        }\n        finally {\n            __disposeResources(env_1);\n        }\n    }\n    frames() {\n        return Array.from(this.#frameTree.frames());\n    }\n    frame(frameId) {\n        return this.#frameTree.getById(frameId ?? '') || null;\n    }\n    childFrames(frameId) {\n        return this.#frameTree.childFrames(frameId);\n    }\n    #onFrameLoaded(info) {\n        const frame = this.frame(info.context);\n        if (frame && this.mainFrame() === frame) {\n            this.emit(\"load\" /* PageEvent.Load */, undefined);\n        }\n    }\n    #onFrameFragmentNavigated(info) {\n        const frame = this.frame(info.context);\n        if (frame) {\n            this.emit(\"framenavigated\" /* PageEvent.FrameNavigated */, frame);\n        }\n    }\n    #onFrameDOMContentLoaded(info) {\n        const frame = this.frame(info.context);\n        if (frame) {\n            frame._hasStartedLoading = true;\n            if (this.mainFrame() === frame) {\n                this.emit(\"domcontentloaded\" /* PageEvent.DOMContentLoaded */, undefined);\n            }\n            this.emit(\"framenavigated\" /* PageEvent.FrameNavigated */, frame);\n        }\n    }\n    #onContextCreated(context) {\n        if (!this.frame(context.id) &&\n            (this.frame(context.parent ?? '') || !this.#frameTree.getMainFrame())) {\n            const frame = new BidiFrame(this, context, this._timeoutSettings, context.parent);\n            this.#frameTree.addFrame(frame);\n            if (frame !== this.mainFrame()) {\n                this.emit(\"frameattached\" /* PageEvent.FrameAttached */, frame);\n            }\n        }\n    }\n    #onContextDestroyed(context) {\n        const frame = this.frame(context.id);\n        if (frame) {\n            if (frame === this.mainFrame()) {\n                this.emit(\"close\" /* PageEvent.Close */, undefined);\n            }\n            this.#removeFramesRecursively(frame);\n        }\n    }\n    #removeFramesRecursively(frame) {\n        for (const child of frame.childFrames()) {\n            this.#removeFramesRecursively(child);\n        }\n        frame[disposeSymbol]();\n        this.#networkManager.clearMapAfterFrameDispose(frame);\n        this.#frameTree.removeFrame(frame);\n        this.emit(\"framedetached\" /* PageEvent.FrameDetached */, frame);\n    }\n    #onLogEntryAdded(event) {\n        const frame = this.frame(event.source.context);\n        if (!frame) {\n            return;\n        }\n        if (isConsoleLogEntry(event)) {\n            const args = event.args.map(arg => {\n                return createBidiHandle(frame.mainRealm(), arg);\n            });\n            const text = args\n                .reduce((value, arg) => {\n                const parsedValue = arg.isPrimitiveValue\n                    ? BidiDeserializer.deserialize(arg.remoteValue())\n                    : arg.toString();\n                return `${value} ${parsedValue}`;\n            }, '')\n                .slice(1);\n            this.emit(\"console\" /* PageEvent.Console */, new ConsoleMessage(event.method, text, args, getStackTraceLocations(event.stackTrace)));\n        }\n        else if (isJavaScriptLogEntry(event)) {\n            const error = new Error(event.text ?? '');\n            const messageHeight = error.message.split('\\n').length;\n            const messageLines = error.stack.split('\\n').splice(0, messageHeight);\n            const stackLines = [];\n            if (event.stackTrace) {\n                for (const frame of event.stackTrace.callFrames) {\n                    // Note we need to add `1` because the values are 0-indexed.\n                    stackLines.push(`    at ${frame.functionName || '<anonymous>'} (${frame.url}:${frame.lineNumber + 1}:${frame.columnNumber + 1})`);\n                    if (stackLines.length >= Error.stackTraceLimit) {\n                        break;\n                    }\n                }\n            }\n            error.stack = [...messageLines, ...stackLines].join('\\n');\n            this.emit(\"pageerror\" /* PageEvent.PageError */, error);\n        }\n        else {\n            debugError(`Unhandled LogEntry with type \"${event.type}\", text \"${event.text}\" and level \"${event.level}\"`);\n        }\n    }\n    #onDialog(event) {\n        const frame = this.frame(event.context);\n        if (!frame) {\n            return;\n        }\n        const type = validateDialogType(event.type);\n        const dialog = new BidiDialog(frame.context(), type, event.message, event.defaultValue);\n        this.emit(\"dialog\" /* PageEvent.Dialog */, dialog);\n    }\n    getNavigationResponse(id) {\n        return this.#networkManager.getNavigationResponse(id);\n    }\n    isClosed() {\n        return this.#closedDeferred.finished();\n    }\n    async close(options) {\n        if (this.#closedDeferred.finished()) {\n            return;\n        }\n        this.#closedDeferred.reject(new TargetCloseError('Page closed!'));\n        this.#networkManager.dispose();\n        await this.#connection.send('browsingContext.close', {\n            context: this.mainFrame()._id,\n            promptUnload: options?.runBeforeUnload ?? false,\n        });\n        this.emit(\"close\" /* PageEvent.Close */, undefined);\n        this.removeAllListeners();\n    }\n    async reload(options = {}) {\n        const { waitUntil = 'load', timeout: ms = this._timeoutSettings.navigationTimeout(), } = options;\n        const [readiness, networkIdle] = getBiDiReadinessState(waitUntil);\n        const result$ = zip(from(this.#connection.send('browsingContext.reload', {\n            context: this.mainFrame()._id,\n            wait: readiness,\n        })), ...(networkIdle !== null\n            ? [\n                this.waitForNetworkIdle$({\n                    timeout: ms,\n                    concurrency: networkIdle === 'networkidle2' ? 2 : 0,\n                    idleTime: NETWORK_IDLE_TIME,\n                }),\n            ]\n            : [])).pipe(map(([{ result }]) => {\n            return result;\n        }), raceWith(timeout(ms), from(this.#closedDeferred.valueOrThrow())), rewriteNavigationError(this.url(), ms));\n        const result = await firstValueFrom(result$);\n        return this.getNavigationResponse(result.navigation);\n    }\n    setDefaultNavigationTimeout(timeout) {\n        this._timeoutSettings.setDefaultNavigationTimeout(timeout);\n    }\n    setDefaultTimeout(timeout) {\n        this._timeoutSettings.setDefaultTimeout(timeout);\n    }\n    getDefaultTimeout() {\n        return this._timeoutSettings.timeout();\n    }\n    isJavaScriptEnabled() {\n        return this.#cdpEmulationManager.javascriptEnabled;\n    }\n    async setGeolocation(options) {\n        return await this.#cdpEmulationManager.setGeolocation(options);\n    }\n    async setJavaScriptEnabled(enabled) {\n        return await this.#cdpEmulationManager.setJavaScriptEnabled(enabled);\n    }\n    async emulateMediaType(type) {\n        return await this.#cdpEmulationManager.emulateMediaType(type);\n    }\n    async emulateCPUThrottling(factor) {\n        return await this.#cdpEmulationManager.emulateCPUThrottling(factor);\n    }\n    async emulateMediaFeatures(features) {\n        return await this.#cdpEmulationManager.emulateMediaFeatures(features);\n    }\n    async emulateTimezone(timezoneId) {\n        return await this.#cdpEmulationManager.emulateTimezone(timezoneId);\n    }\n    async emulateIdleState(overrides) {\n        return await this.#cdpEmulationManager.emulateIdleState(overrides);\n    }\n    async emulateVisionDeficiency(type) {\n        return await this.#cdpEmulationManager.emulateVisionDeficiency(type);\n    }\n    async setViewport(viewport) {\n        if (!this.#browsingContext.supportsCdp()) {\n            await this.#emulationManager.emulateViewport(viewport);\n            this.#viewport = viewport;\n            return;\n        }\n        const needsReload = await this.#cdpEmulationManager.emulateViewport(viewport);\n        this.#viewport = viewport;\n        if (needsReload) {\n            await this.reload();\n        }\n    }\n    viewport() {\n        return this.#viewport;\n    }\n    async pdf(options = {}) {\n        const { timeout: ms = this._timeoutSettings.timeout(), path = undefined } = options;\n        const { printBackground: background, margin, landscape, width, height, pageRanges: ranges, scale, preferCSSPageSize, } = parsePDFOptions(options, 'cm');\n        const pageRanges = ranges ? ranges.split(', ') : [];\n        const { result } = await firstValueFrom(from(this.#connection.send('browsingContext.print', {\n            context: this.mainFrame()._id,\n            background,\n            margin,\n            orientation: landscape ? 'landscape' : 'portrait',\n            page: {\n                width,\n                height,\n            },\n            pageRanges,\n            scale,\n            shrinkToFit: !preferCSSPageSize,\n        })).pipe(raceWith(timeout(ms))));\n        const buffer = Buffer.from(result.data, 'base64');\n        await this._maybeWriteBufferToFile(path, buffer);\n        return buffer;\n    }\n    async createPDFStream(options) {\n        const buffer = await this.pdf(options);\n        try {\n            const { Readable } = await import('stream');\n            return Readable.from(buffer);\n        }\n        catch (error) {\n            if (error instanceof TypeError) {\n                throw new Error('Can only pass a file path in a Node-like environment.');\n            }\n            throw error;\n        }\n    }\n    async _screenshot(options) {\n        const { clip, type, captureBeyondViewport, quality } = options;\n        if (options.omitBackground !== undefined && options.omitBackground) {\n            throw new UnsupportedOperation(`BiDi does not support 'omitBackground'.`);\n        }\n        if (options.optimizeForSpeed !== undefined && options.optimizeForSpeed) {\n            throw new UnsupportedOperation(`BiDi does not support 'optimizeForSpeed'.`);\n        }\n        if (options.fromSurface !== undefined && !options.fromSurface) {\n            throw new UnsupportedOperation(`BiDi does not support 'fromSurface'.`);\n        }\n        if (clip !== undefined && clip.scale !== undefined && clip.scale !== 1) {\n            throw new UnsupportedOperation(`BiDi does not support 'scale' in 'clip'.`);\n        }\n        let box;\n        if (clip) {\n            if (captureBeyondViewport) {\n                box = clip;\n            }\n            else {\n                // The clip is always with respect to the document coordinates, so we\n                // need to convert this to viewport coordinates when we aren't capturing\n                // beyond the viewport.\n                const [pageLeft, pageTop] = await this.evaluate(() => {\n                    if (!window.visualViewport) {\n                        throw new Error('window.visualViewport is not supported.');\n                    }\n                    return [\n                        window.visualViewport.pageLeft,\n                        window.visualViewport.pageTop,\n                    ];\n                });\n                box = {\n                    ...clip,\n                    x: clip.x - pageLeft,\n                    y: clip.y - pageTop,\n                };\n            }\n        }\n        const { result: { data }, } = await this.#connection.send('browsingContext.captureScreenshot', {\n            context: this.mainFrame()._id,\n            origin: captureBeyondViewport ? 'document' : 'viewport',\n            format: {\n                type: `image/${type}`,\n                ...(quality !== undefined ? { quality: quality / 100 } : {}),\n            },\n            ...(box ? { clip: { type: 'box', ...box } } : {}),\n        });\n        return data;\n    }\n    async createCDPSession() {\n        const { sessionId } = await this.mainFrame()\n            .context()\n            .cdpSession.send('Target.attachToTarget', {\n            targetId: this.mainFrame()._id,\n            flatten: true,\n        });\n        return new CdpSessionWrapper(this.mainFrame().context(), sessionId);\n    }\n    async bringToFront() {\n        await this.#connection.send('browsingContext.activate', {\n            context: this.mainFrame()._id,\n        });\n    }\n    async evaluateOnNewDocument(pageFunction, ...args) {\n        const expression = evaluationExpression(pageFunction, ...args);\n        const { result } = await this.#connection.send('script.addPreloadScript', {\n            functionDeclaration: expression,\n            contexts: [this.mainFrame()._id],\n        });\n        return { identifier: result.script };\n    }\n    async removeScriptToEvaluateOnNewDocument(id) {\n        await this.#connection.send('script.removePreloadScript', {\n            script: id,\n        });\n    }\n    async exposeFunction(name, pptrFunction) {\n        return await this.mainFrame().exposeFunction(name, 'default' in pptrFunction ? pptrFunction.default : pptrFunction);\n    }\n    isDragInterceptionEnabled() {\n        return false;\n    }\n    async setCacheEnabled(enabled) {\n        // TODO: handle CDP-specific cases such as mprach.\n        await this._client().send('Network.setCacheDisabled', {\n            cacheDisabled: !enabled,\n        });\n    }\n    isServiceWorkerBypassed() {\n        throw new UnsupportedOperation();\n    }\n    target() {\n        return this.#target;\n    }\n    waitForFileChooser() {\n        throw new UnsupportedOperation();\n    }\n    workers() {\n        throw new UnsupportedOperation();\n    }\n    setRequestInterception() {\n        throw new UnsupportedOperation();\n    }\n    setDragInterception() {\n        throw new UnsupportedOperation();\n    }\n    setBypassServiceWorker() {\n        throw new UnsupportedOperation();\n    }\n    setOfflineMode() {\n        throw new UnsupportedOperation();\n    }\n    emulateNetworkConditions() {\n        throw new UnsupportedOperation();\n    }\n    cookies() {\n        throw new UnsupportedOperation();\n    }\n    setCookie() {\n        throw new UnsupportedOperation();\n    }\n    deleteCookie() {\n        throw new UnsupportedOperation();\n    }\n    removeExposedFunction() {\n        // TODO: Quick win?\n        throw new UnsupportedOperation();\n    }\n    authenticate() {\n        throw new UnsupportedOperation();\n    }\n    setExtraHTTPHeaders() {\n        throw new UnsupportedOperation();\n    }\n    metrics() {\n        throw new UnsupportedOperation();\n    }\n    async goBack(options = {}) {\n        return await this.#go(-1, options);\n    }\n    async goForward(options = {}) {\n        return await this.#go(+1, options);\n    }\n    async #go(delta, options) {\n        try {\n            const result = await Promise.all([\n                this.waitForNavigation(options),\n                this.#connection.send('browsingContext.traverseHistory', {\n                    delta,\n                    context: this.mainFrame()._id,\n                }),\n            ]);\n            return result[0];\n        }\n        catch (err) {\n            // TODO: waitForNavigation should be cancelled if an error happens.\n            if (isErrorLike(err)) {\n                if (err.message.includes('no such history entry')) {\n                    return null;\n                }\n            }\n            throw err;\n        }\n    }\n    waitForDevicePrompt() {\n        throw new UnsupportedOperation();\n    }\n}\nfunction isConsoleLogEntry(event) {\n    return event.type === 'console';\n}\nfunction isJavaScriptLogEntry(event) {\n    return event.type === 'javascript';\n}\nfunction getStackTraceLocations(stackTrace) {\n    const stackTraceLocations = [];\n    if (stackTrace) {\n        for (const callFrame of stackTrace.callFrames) {\n            stackTraceLocations.push({\n                url: callFrame.url,\n                lineNumber: callFrame.lineNumber,\n                columnNumber: callFrame.columnNumber,\n            });\n        }\n    }\n    return stackTraceLocations;\n}\nfunction evaluationExpression(fun, ...args) {\n    return `() => {${evaluationString(fun, ...args)}}`;\n}\n//# sourceMappingURL=Page.js.map", "/**\n * @license\n * Copyright 2023 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nimport { Target, TargetType } from '../api/Target.js';\nimport { UnsupportedOperation } from '../common/Errors.js';\nimport { CdpSessionWrapper } from './BrowsingContext.js';\nimport { BidiPage } from './Page.js';\n/**\n * @internal\n */\nexport class BidiTarget extends Target {\n    _browserContext;\n    constructor(browserContext) {\n        super();\n        this._browserContext = browserContext;\n    }\n    _setBrowserContext(browserContext) {\n        this._browserContext = browserContext;\n    }\n    asPage() {\n        throw new UnsupportedOperation();\n    }\n    browser() {\n        return this._browserContext.browser();\n    }\n    browserContext() {\n        return this._browserContext;\n    }\n    opener() {\n        throw new UnsupportedOperation();\n    }\n    createCDPSession() {\n        throw new UnsupportedOperation();\n    }\n}\n/**\n * @internal\n */\nexport class BiDi<PERSON>rowserTarget extends Target {\n    #browser;\n    constructor(browser) {\n        super();\n        this.#browser = browser;\n    }\n    url() {\n        return '';\n    }\n    type() {\n        return TargetType.BROWSER;\n    }\n    asPage() {\n        throw new UnsupportedOperation();\n    }\n    browser() {\n        return this.#browser;\n    }\n    browserContext() {\n        return this.#browser.defaultBrowserContext();\n    }\n    opener() {\n        throw new UnsupportedOperation();\n    }\n    createCDPSession() {\n        throw new UnsupportedOperation();\n    }\n}\n/**\n * @internal\n */\nexport class BiDiBrowsingContextTarget extends BidiTarget {\n    _browsingContext;\n    constructor(browserContext, browsingContext) {\n        super(browserContext);\n        this._browsingContext = browsingContext;\n    }\n    url() {\n        return this._browsingContext.url;\n    }\n    async createCDPSession() {\n        const { sessionId } = await this._browsingContext.cdpSession.send('Target.attachToTarget', {\n            targetId: this._browsingContext.id,\n            flatten: true,\n        });\n        return new CdpSessionWrapper(this._browsingContext, sessionId);\n    }\n    type() {\n        return TargetType.PAGE;\n    }\n}\n/**\n * @internal\n */\nexport class BiDiPageTarget extends BiDiBrowsingContextTarget {\n    #page;\n    constructor(browserContext, browsingContext) {\n        super(browserContext, browsingContext);\n        this.#page = new BidiPage(browsingContext, browserContext, this);\n    }\n    async page() {\n        return this.#page;\n    }\n    _setBrowserContext(browserContext) {\n        super._setBrowserContext(browserContext);\n        this.#page._setBrowserContext(browserContext);\n    }\n}\n//# sourceMappingURL=Target.js.map", "/**\n * @license\n * Copyright 2022 Google Inc.\n * SPDX-License-Identifier: Apache-2.0\n */\nimport { Browser, } from '../api/Browser.js';\nimport { UnsupportedOperation } from '../common/Errors.js';\nimport { debugError } from '../common/util.js';\nimport { BidiBrowserContext } from './BrowserContext.js';\nimport { BrowsingContext, BrowsingContextEvent } from './BrowsingContext.js';\nimport { Session } from './core/Session.js';\nimport { BiDiBrowserTarget, BiDiBrowsingContextTarget, BiDiPageTarget, } from './Target.js';\n/**\n * @internal\n */\nexport class BidiBrowser extends Browser {\n    protocol = 'webDriverBiDi';\n    // TODO: Update generator to include fully module\n    static subscribeModules = [\n        'browsingContext',\n        'network',\n        'log',\n        'script',\n    ];\n    static subscribeCdpEvents = [\n        // Coverage\n        'cdp.Debugger.scriptParsed',\n        'cdp.CSS.styleSheetAdded',\n        'cdp.Runtime.executionContextsCleared',\n        // Tracing\n        'cdp.Tracing.tracingComplete',\n        // TODO: subscribe to all CDP events in the future.\n        'cdp.Network.requestWillBeSent',\n        'cdp.Debugger.scriptParsed',\n        'cdp.Page.screencastFrame',\n    ];\n    static async create(opts) {\n        const session = await Session.from(opts.connection, {\n            alwaysMatch: {\n                acceptInsecureCerts: opts.ignoreHTTPSErrors,\n                webSocketUrl: true,\n            },\n        });\n        await session.subscribe(session.capabilities.browserName.toLocaleLowerCase().includes('firefox')\n            ? BidiBrowser.subscribeModules\n            : [...BidiBrowser.subscribeModules, ...BidiBrowser.subscribeCdpEvents]);\n        const browser = new BidiBrowser(session.browser, opts);\n        browser.#initialize();\n        await browser.#getTree();\n        return browser;\n    }\n    #process;\n    #closeCallback;\n    #browserCore;\n    #defaultViewport;\n    #targets = new Map();\n    #browserContexts = new WeakMap();\n    #browserTarget;\n    #connectionEventHandlers = new Map([\n        ['browsingContext.contextCreated', this.#onContextCreated.bind(this)],\n        ['browsingContext.contextDestroyed', this.#onContextDestroyed.bind(this)],\n        ['browsingContext.domContentLoaded', this.#onContextDomLoaded.bind(this)],\n        ['browsingContext.fragmentNavigated', this.#onContextNavigation.bind(this)],\n        ['browsingContext.navigationStarted', this.#onContextNavigation.bind(this)],\n    ]);\n    constructor(browserCore, opts) {\n        super();\n        this.#process = opts.process;\n        this.#closeCallback = opts.closeCallback;\n        this.#browserCore = browserCore;\n        this.#defaultViewport = opts.defaultViewport;\n        this.#browserTarget = new BiDiBrowserTarget(this);\n        for (const context of this.#browserCore.userContexts) {\n            this.#createBrowserContext(context);\n        }\n    }\n    #initialize() {\n        this.#browserCore.once('disconnected', () => {\n            this.emit(\"disconnected\" /* BrowserEvent.Disconnected */, undefined);\n        });\n        this.#process?.once('close', () => {\n            this.#browserCore.dispose('Browser process exited.', true);\n            this.connection.dispose();\n        });\n        for (const [eventName, handler] of this.#connectionEventHandlers) {\n            this.connection.on(eventName, handler);\n        }\n    }\n    get #browserName() {\n        return this.#browserCore.session.capabilities.browserName;\n    }\n    get #browserVersion() {\n        return this.#browserCore.session.capabilities.browserVersion;\n    }\n    userAgent() {\n        throw new UnsupportedOperation();\n    }\n    #createBrowserContext(userContext) {\n        const browserContext = new BidiBrowserContext(this, userContext, {\n            defaultViewport: this.#defaultViewport,\n        });\n        this.#browserContexts.set(userContext, browserContext);\n        return browserContext;\n    }\n    #onContextDomLoaded(event) {\n        const target = this.#targets.get(event.context);\n        if (target) {\n            this.emit(\"targetchanged\" /* BrowserEvent.TargetChanged */, target);\n            target.browserContext().emit(\"targetchanged\" /* BrowserContextEvent.TargetChanged */, target);\n        }\n    }\n    #onContextNavigation(event) {\n        const target = this.#targets.get(event.context);\n        if (target) {\n            this.emit(\"targetchanged\" /* BrowserEvent.TargetChanged */, target);\n            target.browserContext().emit(\"targetchanged\" /* BrowserContextEvent.TargetChanged */, target);\n        }\n    }\n    #onContextCreated(event) {\n        const context = new BrowsingContext(this.connection, event, this.#browserName);\n        this.connection.registerBrowsingContexts(context);\n        const browserContext = event.userContext === 'default'\n            ? this.defaultBrowserContext()\n            : this.browserContexts().find(browserContext => {\n                return browserContext.id === event.userContext;\n            });\n        if (!browserContext) {\n            throw new Error('Missing browser contexts');\n        }\n        const target = !context.parent\n            ? new BiDiPageTarget(browserContext, context)\n            : new BiDiBrowsingContextTarget(browserContext, context);\n        this.#targets.set(event.context, target);\n        this.emit(\"targetcreated\" /* BrowserEvent.TargetCreated */, target);\n        target.browserContext().emit(\"targetcreated\" /* BrowserContextEvent.TargetCreated */, target);\n        if (context.parent) {\n            const topLevel = this.connection.getTopLevelContext(context.parent);\n            topLevel.emit(BrowsingContextEvent.Created, context);\n        }\n    }\n    async #getTree() {\n        const { result } = await this.connection.send('browsingContext.getTree', {});\n        for (const context of result.contexts) {\n            this.#onContextCreated(context);\n        }\n    }\n    async #onContextDestroyed(event) {\n        const context = this.connection.getBrowsingContext(event.context);\n        const topLevelContext = this.connection.getTopLevelContext(event.context);\n        topLevelContext.emit(BrowsingContextEvent.Destroyed, context);\n        const target = this.#targets.get(event.context);\n        const page = await target?.page();\n        await page?.close().catch(debugError);\n        this.#targets.delete(event.context);\n        if (target) {\n            this.emit(\"targetdestroyed\" /* BrowserEvent.TargetDestroyed */, target);\n            target.browserContext().emit(\"targetdestroyed\" /* BrowserContextEvent.TargetDestroyed */, target);\n        }\n    }\n    get connection() {\n        // SAFETY: We only have one implementation.\n        return this.#browserCore.session.connection;\n    }\n    wsEndpoint() {\n        return this.connection.url;\n    }\n    async close() {\n        for (const [eventName, handler] of this.#connectionEventHandlers) {\n            this.connection.off(eventName, handler);\n        }\n        if (this.connection.closed) {\n            return;\n        }\n        try {\n            await this.#browserCore.close();\n            await this.#closeCallback?.call(null);\n        }\n        catch (error) {\n            // Fail silently.\n            debugError(error);\n        }\n        finally {\n            this.connection.dispose();\n        }\n    }\n    get connected() {\n        return !this.#browserCore.disposed;\n    }\n    process() {\n        return this.#process ?? null;\n    }\n    async createIncognitoBrowserContext(_options) {\n        const userContext = await this.#browserCore.createUserContext();\n        return this.#createBrowserContext(userContext);\n    }\n    async version() {\n        return `${this.#browserName}/${this.#browserVersion}`;\n    }\n    browserContexts() {\n        return [...this.#browserCore.userContexts].map(context => {\n            return this.#browserContexts.get(context);\n        });\n    }\n    defaultBrowserContext() {\n        return this.#browserContexts.get(this.#browserCore.defaultUserContext);\n    }\n    newPage() {\n        return this.defaultBrowserContext().newPage();\n    }\n    targets() {\n        return [this.#browserTarget, ...Array.from(this.#targets.values())];\n    }\n    _getTargetById(id) {\n        const target = this.#targets.get(id);\n        if (!target) {\n            throw new Error('Target not found');\n        }\n        return target;\n    }\n    target() {\n        return this.#browserTarget;\n    }\n    async disconnect() {\n        try {\n            await this.#browserCore.session.end();\n        }\n        catch (error) {\n            // Fail silently.\n            debugError(error);\n        }\n        finally {\n            this.connection.dispose();\n        }\n    }\n    get debugInfo() {\n        return {\n            pendingProtocolErrors: this.connection.getPendingProtocolErrors(),\n        };\n    }\n}\n//# sourceMappingURL=Browser.js.map"], "names": ["_a", "__runInitializers", "__esDecorate", "__addDisposableResource", "__disposeResources", "SuppressedError", "_sandbox", "Bidi.ChromiumBidi", "BrowsingContextEvent", "_id", "timeout", "_url", "BidiMapper.BidiServer", "BidiMapper.EventEmitter", "_closed", "_request", "_browsingContext", "initialize_fn", "_workers", "_BrowsingContext_instances", "_browsingContexts", "_context", "result", "error", "catchError", "Realm", "_frame", "_page", "_disposed", "zip", "from", "map", "raceWith", "firstValueFrom", "fork<PERSON><PERSON>n", "first", "merge", "SourceActionsType", "ActionType", "char", "_headers", "_connection", "CdpEmulationManager", "frame", "_browser", "Browser", "_defaultViewport", "onContextCreated_fn", "onContextDestroyed_fn", "BrowsingContext", "browserContext"], "mappings": ";;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AASO,MAAM,iBAAiB;AAAA,EAC1B,OAAO,kBAAkB,OAAO;AAC5B,YAAQ,OAAK;AAAA,MACT,KAAK;AACD,eAAO;AAAA,MACX,KAAK;AACD,eAAO;AAAA,MACX,KAAK;AACD,eAAO;AAAA,MACX,KAAK;AACD,eAAO;AAAA,MACX;AACI,eAAO;AAAA,IACvB;AAAA,EACA;AAAA,EACI,OAAO,sBAAsB,QAAQ;;AACjC,YAAQ,OAAO,MAAI;AAAA,MACf,KAAK;AACD,gBAAOA,MAAA,OAAO,UAAP,gBAAAA,IAAc,IAAI,WAAS;AAC9B,iBAAO,iBAAiB,sBAAsB,KAAK;AAAA,QACvE;AAAA,MACY,KAAK;AACD,gBAAO,YAAO,UAAP,mBAAc,OAAO,CAAC,KAAK,UAAU;AACxC,iBAAO,IAAI,IAAI,iBAAiB,sBAAsB,KAAK,CAAC;AAAA,QAChF,GAAmB,oBAAI,IAAG;AAAA,MACd,KAAK;AACD,gBAAO,YAAO,UAAP,mBAAc,OAAO,CAAC,KAAK,UAAU;AACxC,gBAAM,EAAE,KAAK,MAAK,IAAK,iBAAiB,iBAAiB,KAAK;AAC9D,cAAI,GAAG,IAAI;AACX,iBAAO;AAAA,QACV,GAAE;MACP,KAAK;AACD,gBAAO,YAAO,UAAP,mBAAc,OAAO,CAAC,KAAK,UAAU;AACxC,gBAAM,EAAE,KAAK,MAAK,IAAK,iBAAiB,iBAAiB,KAAK;AAC9D,iBAAO,IAAI,IAAI,KAAK,KAAK;AAAA,QAC7C,GAAmB,oBAAI,IAAG;AAAA,MACd,KAAK;AACD,eAAO,CAAE;AAAA,MACb,KAAK;AACD,eAAO,IAAI,OAAO,OAAO,MAAM,SAAS,OAAO,MAAM,KAAK;AAAA,MAC9D,KAAK;AACD,eAAO,IAAI,KAAK,OAAO,KAAK;AAAA,MAChC,KAAK;AACD,eAAO;AAAA,MACX,KAAK;AACD,eAAO;AAAA,MACX,KAAK;AACD,eAAO,iBAAiB,kBAAkB,OAAO,KAAK;AAAA,MAC1D,KAAK;AACD,eAAO,OAAO,OAAO,KAAK;AAAA,MAC9B,KAAK;AACD,eAAO,QAAQ,OAAO,KAAK;AAAA,MAC/B,KAAK;AACD,eAAO,OAAO;AAAA,IAC9B;AACQ,eAAW,2BAA2B,OAAO,IAAI,iBAAiB;AAClE,WAAO;AAAA,EACf;AAAA,EACI,OAAO,iBAAiB,CAAC,eAAe,eAAe,GAAG;AACtD,UAAM,MAAM,OAAO,kBAAkB,WAC/B,gBACA,iBAAiB,sBAAsB,aAAa;AAC1D,UAAM,QAAQ,iBAAiB,sBAAsB,eAAe;AACpE,WAAO,EAAE,KAAK,MAAO;AAAA,EAC7B;AAAA,EACI,OAAO,YAAY,QAAQ;AACvB,QAAI,CAAC,QAAQ;AACT,iBAAW,mCAAmC;AAC9C,aAAO;AAAA,IACnB;AACQ,WAAO,iBAAiB,sBAAsB,MAAM;AAAA,EAC5D;AACA;ACjFA;AAAA;AAAA;AAAA;AAAA;AAUO,eAAe,iBAAiB,QAAQ,iBAAiB;AAC5D,MAAI,CAAC,gBAAgB,QAAQ;AACzB;AAAA,EACR;AACI,QAAM,OAAO,WACR,KAAK,iBAAiB;AAAA,IACvB,QAAQ,OAAO;AAAA,IACf,SAAS,CAAC,gBAAgB,MAAM;AAAA,EACnC,CAAA,EACI,MAAM,WAAS;AAGhB,eAAW,KAAK;AAAA,EACxB,CAAK;AACL;AAIO,SAAS,sBAAsB,SAAS;AAC3C,MAAI,QAAQ,UAAU,SAAS,SAAS;AACpC,WAAO,iBAAiB,YAAY,QAAQ,SAAS;AAAA,EAC7D;AACI,QAAM,CAAC,OAAO,IAAI,GAAG,KAAK,IAAI,QAAQ,KAAK,MAAM,IAAI;AACrD,QAAM,UAAU,MAAM,KAAK,IAAI;AAC/B,QAAM,QAAQ,IAAI,MAAM,OAAO;AAC/B,QAAM,OAAO;AAEb,QAAM,aAAa,CAAE;AACrB,MAAI,QAAQ,cAAc,WAAW,SAAS,MAAM,iBAAiB;AACjE,eAAW,SAAS,QAAQ,WAAW,WAAW,QAAO,GAAI;AACzD,UAAI,aAAa,eAAe,MAAM,GAAG,KACrC,MAAM,QAAQ,aAAa,cAAc;AACzC,cAAM,MAAM,aAAa,MAAM,MAAM,GAAG;AACxC,mBAAW,QAAQ,UAAU,MAAM,gBAAgB,IAAI,YAAY,KAAK,IAAI,YAAY,OAAO,IAAI,UAAU,iBAAiB,MAAM,UAAU,IAAI,MAAM,YAAY,GAAG;AAAA,MACvL,OACiB;AACD,mBAAW,KAAK,UAAU,MAAM,gBAAgB,aAAa,KAAK,MAAM,GAAG,IAAI,MAAM,UAAU,IAAI,MAAM,YAAY,GAAG;AAAA,MACxI;AACY,UAAI,WAAW,UAAU,MAAM,iBAAiB;AAC5C;AAAA,MAChB;AAAA,IACA;AAAA,EACA;AACI,QAAM,QAAQ,CAAC,QAAQ,MAAM,GAAG,UAAU,EAAE,KAAK,IAAI;AACrD,SAAO;AACX;ACvDA;AAAA;AAAA;AAAA;AAAA;AAYO,MAAM,qBAAqB,SAAS;AAAA,EAIvC,YAAY,SAAS,aAAa;AAC9B,UAAO;AAJX,kCAAY;AACZ;AACA;AAGI,uBAAK,UAAW;AAChB,uBAAK,cAAe;AAAA,EAC5B;AAAA,EACI,UAAU;AACN,WAAO,KAAK,MAAM,YAAY,QAAS;AAAA,EAC/C;AAAA,EACI,IAAI,QAAQ;AACR,WAAO,mBAAK;AAAA,EACpB;AAAA,EACI,IAAI,WAAW;AACX,WAAO,mBAAK;AAAA,EACpB;AAAA,EACI,MAAM,YAAY;AACd,WAAO,MAAM,KAAK,SAAS,WAAS;AAChC,aAAO;AAAA,IACnB,CAAS;AAAA,EACT;AAAA,EACI,YAAY;AACR,WAAO;AAAA,EACf;AAAA,EACI,MAAM,UAAU;AACZ,QAAI,mBAAK,YAAW;AAChB;AAAA,IACZ;AACQ,uBAAK,WAAY;AACjB,QAAI,YAAY,mBAAK,eAAc;AAC/B,YAAM,iBAAiB,KAAK,QAAO,GAAI,mBAAK,aAAY;AAAA,IACpE;AAAA,EACA;AAAA,EACI,IAAI,mBAAmB;AACnB,YAAQ,mBAAK,cAAa,MAAI;AAAA,MAC1B,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,eAAO;AAAA,MACX;AACI,eAAO;AAAA,IACvB;AAAA,EACA;AAAA,EACI,WAAW;AACP,QAAI,KAAK,kBAAkB;AACvB,aAAO,cAAc,iBAAiB,YAAY,mBAAK,aAAY;AAAA,IAC/E;AACQ,WAAO,cAAc,mBAAK,cAAa;AAAA,EAC/C;AAAA,EACI,IAAI,KAAK;AACL,WAAO,YAAY,mBAAK,gBAAe,mBAAK,cAAa,SAAS;AAAA,EAC1E;AAAA,EACI,cAAc;AACV,WAAO,mBAAK;AAAA,EACpB;AAAA,EACI,eAAe;AACX,UAAM,IAAI,qBAAqB,iCAAiC;AAAA,EACxE;AACA;AA9DI;AACA;AACA;ACfJ;AAAA;AAAA;AAAA;AAAA;AAKA,IAAIC,sBAAwD,SAAU,SAAS,cAAc,OAAO;AAChG,MAAI,WAAW,UAAU,SAAS;AAClC,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC1C,YAAQ,WAAW,aAAa,CAAC,EAAE,KAAK,SAAS,KAAK,IAAI,aAAa,CAAC,EAAE,KAAK,OAAO;AAAA,EAC9F;AACI,SAAO,WAAW,QAAQ;AAC9B;AACA,IAAIC,iBAA8C,SAAU,MAAM,cAAc,YAAY,WAAW,cAAc,mBAAmB;AACpI,WAAS,OAAO,GAAG;AAAE,QAAI,MAAM,UAAU,OAAO,MAAM,WAAY,OAAM,IAAI,UAAU,mBAAmB;AAAG,WAAO;AAAA,EAAE;AACrH,MAAI,OAAO,UAAU,MAAM,MAAM,SAAS,WAAW,QAAQ,SAAS,WAAW,QAAQ;AACzF,MAAI,SAAS,CAAC,gBAAgB,OAAO,UAAU,QAAQ,IAAI,OAAO,KAAK,YAAY;AACnF,MAAI,aAAa,iBAAiB,SAAS,OAAO,yBAAyB,QAAQ,UAAU,IAAI,IAAI;AACrG,MAAI,GAAG,OAAO;AACd,WAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,QAAI,UAAU,CAAE;AAChB,aAAS,KAAK,UAAW,SAAQ,CAAC,IAAI,MAAM,WAAW,CAAA,IAAK,UAAU,CAAC;AACvE,aAAS,KAAK,UAAU,OAAQ,SAAQ,OAAO,CAAC,IAAI,UAAU,OAAO,CAAC;AACtE,YAAQ,iBAAiB,SAAU,GAAG;AAAE,UAAI,KAAM,OAAM,IAAI,UAAU,wDAAwD;AAAG,wBAAkB,KAAK,OAAO,KAAK,IAAI,CAAC;AAAA,IAAI;AAC7K,QAAI,UAAS,GAAI,WAAW,CAAC,GAAG,SAAS,aAAa,EAAE,KAAK,WAAW,KAAK,KAAK,WAAW,IAAK,IAAG,WAAW,GAAG,GAAG,OAAO;AAC7H,QAAI,SAAS,YAAY;AACrB,UAAI,WAAW,OAAQ;AACvB,UAAI,WAAW,QAAQ,OAAO,WAAW,SAAU,OAAM,IAAI,UAAU,iBAAiB;AACxF,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,IAAI,EAAG,cAAa,QAAQ,CAAC;AAAA,IAC/D,WACiB,IAAI,OAAO,MAAM,GAAG;AACzB,UAAI,SAAS,QAAS,cAAa,QAAQ,CAAC;AAAA,UACvC,YAAW,GAAG,IAAI;AAAA,IACnC;AAAA,EACA;AACI,MAAI,OAAQ,QAAO,eAAe,QAAQ,UAAU,MAAM,UAAU;AACpE,SAAO;AACX;AACA,IAAIC,4BAAoE,SAAU,KAAK,OAAO,OAAO;AACjG,MAAI,UAAU,QAAQ,UAAU,QAAQ;AACpC,QAAI,OAAO,UAAU,YAAY,OAAO,UAAU,WAAY,OAAM,IAAI,UAAU,kBAAkB;AACpG,QAAI;AACJ,QAAI,OAAO;AACP,UAAI,CAAC,OAAO,aAAc,OAAM,IAAI,UAAU,qCAAqC;AACnF,gBAAU,MAAM,OAAO,YAAY;AAAA,IAC/C;AACQ,QAAI,YAAY,QAAQ;AACpB,UAAI,CAAC,OAAO,QAAS,OAAM,IAAI,UAAU,gCAAgC;AACzE,gBAAU,MAAM,OAAO,OAAO;AAAA,IAC1C;AACQ,QAAI,OAAO,YAAY,WAAY,OAAM,IAAI,UAAU,wBAAwB;AAC/E,QAAI,MAAM,KAAK,EAAE,OAAc,SAAkB,OAAc;AAAA,EACvE,WACa,OAAO;AACZ,QAAI,MAAM,KAAK,EAAE,OAAO,KAAI,CAAE;AAAA,EACtC;AACI,SAAO;AACX;AACA,IAAIC,uBAA2D,yBAAUC,kBAAiB;AACtF,SAAO,SAAU,KAAK;AAClB,aAAS,KAAK,GAAG;AACb,UAAI,QAAQ,IAAI,WAAW,IAAIA,iBAAgB,GAAG,IAAI,OAAO,0CAA0C,IAAI;AAC3G,UAAI,WAAW;AAAA,IAC3B;AACQ,aAAS,OAAO;AACZ,aAAO,IAAI,MAAM,QAAQ;AACrB,YAAI,MAAM,IAAI,MAAM,IAAK;AACzB,YAAI;AACA,cAAI,SAAS,IAAI,WAAW,IAAI,QAAQ,KAAK,IAAI,KAAK;AACtD,cAAI,IAAI,MAAO,QAAO,QAAQ,QAAQ,MAAM,EAAE,KAAK,MAAM,SAAS,GAAG;AAAE,iBAAK,CAAC;AAAG,mBAAO,KAAM;AAAA,WAAG;AAAA,QACpH,SACuB,GAAG;AACN,eAAK,CAAC;AAAA,QAC1B;AAAA,MACA;AACY,UAAI,IAAI,SAAU,OAAM,IAAI;AAAA,IACxC;AACQ,WAAO,KAAM;AAAA,EAChB;AACL,EAAG,OAAO,oBAAoB,aAAa,kBAAkB,SAAU,OAAO,YAAY,SAAS;AAC/F,MAAI,IAAI,IAAI,MAAM,OAAO;AACzB,SAAO,EAAE,OAAO,mBAAmB,EAAE,QAAQ,OAAO,EAAE,aAAa,YAAY;AACnF,CAAC;AAQE,IAAC,qBAAqB,MAAM;;AAC3B,MAAIL;AACJ,MAAI,cAAc;AAClB,MAAI,6BAA6B,CAAE;AACnC,MAAI;AACJ,MAAI;AACJ,SAAOA,MAAA,cAAgC,YAAY;AAAA,IAS/C,YAAY,SAAS,aAAa;AAC9B,YAAM,IAAI,aAAa,SAAS,WAAW,CAAC;AAC5CC,0BAAkB,MAAM,0BAA0B;AAAA,IAC9D;AAAA,IACQ,IAAI,QAAQ;AACR,aAAO,KAAK,OAAO;AAAA,IAC/B;AAAA,IACQ,IAAI,QAAQ;AACR,aAAO,KAAK,MAAM;AAAA,IAC9B;AAAA,IACQ,UAAU;AACN,aAAO,KAAK,OAAO,QAAS;AAAA,IACxC;AAAA,IACQ,IAAI,mBAAmB;AACnB,aAAO,KAAK,OAAO;AAAA,IAC/B;AAAA,IACQ,cAAc;AACV,aAAO,KAAK,OAAO,YAAa;AAAA,IAC5C;AAAA,IACQ,MAAM,SAAS,MAAM;AACjB,YAAM,SAAS,KAAK,MAAM;AAC1B,YAAM,WAAW,MAAM,OAAO,KAAK,oBAAoB;AAAA,QACnD,UAAU,KAAK,OAAO;AAAA,MACtC,CAAa;AACD,YAAM,UAAU,SAAS,KAAK;AAC9B,YAAM,UAAU,KAAK,MAAM;AAC3B,YAAM,OAAO,KAAK,oBAAoB;AAAA,QAClC;AAAA,QACA;AAAA,QACA,MAAM,KAAK;AAAA,MAC3B,CAAa;AAAA,IACb;AAAA,IACQ,MAAM,eAAe;AACjB,YAAM,QAAQ,EAAE,OAAO,CAAA,GAAI,OAAO,QAAQ,UAAU,MAAO;AAC3D,UAAI;AACA,cAAM,SAASE,0BAAwB,OAAQ,MAAM,KAAK,eAAe,aAAW;AAChF,cAAI,mBAAmB,mBAAmB;AACtC,mBAAO,QAAQ;AAAA,UACvC;AACoB;AAAA,QACH,CAAA,GAAI,KAAK;AACV,cAAM,QAAQ,OAAO,YAAa;AAClC,YAAI,MAAM,SAAS,UAAU;AACzB,iBAAO,KAAK,MAAM,KAAI,EAAG,MAAM,MAAM,MAAM,OAAO;AAAA,QACtE;AACgB,eAAO;AAAA,MACvB,SACmB,KAAK;AACR,cAAM,QAAQ;AACd,cAAM,WAAW;AAAA,MACjC,UACoB;AACJC,6BAAmB,KAAK;AAAA,MACxC;AAAA,IACA;AAAA,IACQ,aAAa;AACT,YAAM,IAAI,qBAAsB;AAAA,IAC5C;AAAA,EACK,IAlEG,MAAA;AACI,UAAM,YAAY,OAAO,WAAW,cAAc,OAAO,WAAW,OAAO,OAAO,YAAY,OAAO,QAAQ,KAAK,IAAI,IAAI;AAC1H,2BAAuB,CAAC,iBAAiB;AACzC,+BAA2B,CAAC,gBAAe,IAAKJ,MAAK,eAAe,mBAAmB,KAAKA,GAAE,CAAC;AAC/FE,mBAAaF,KAAM,MAAM,sBAAsB,EAAE,MAAM,UAAU,MAAM,YAAY,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,cAAc,KAAK,KAAK,SAAO,IAAI,SAAU,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC9OE,mBAAaF,KAAM,MAAM,0BAA0B,EAAE,MAAM,UAAU,MAAM,gBAAgB,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,kBAAkB,KAAK,KAAK,SAAO,IAAI,aAAc,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC9P,QAAI,UAAW,QAAO,eAAeA,KAAM,OAAO,UAAU,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,OAAO,WAAW;AAAA,EAClJ,MARWA;AAoEX,GAAC;ACrKD;AAAA;AAAA;AAAA;AAAA;AAYA,MAAM,4BAA4B,MAAM;AACxC;AAIO,MAAM,eAAe;AAAA,EACxB,OAAO,gBAAgB,KAAK;AACxB,QAAI;AACJ,QAAI,OAAO,GAAG,KAAK,EAAE,GAAG;AACpB,cAAQ;AAAA,IACpB,WACiB,OAAO,GAAG,KAAK,QAAQ,GAAG;AAC/B,cAAQ;AAAA,IACpB,WACiB,OAAO,GAAG,KAAK,SAAS,GAAG;AAChC,cAAQ;AAAA,IACpB,WACiB,OAAO,GAAG,KAAK,GAAG,GAAG;AAC1B,cAAQ;AAAA,IACpB,OACa;AACD,cAAQ;AAAA,IACpB;AACQ,WAAO;AAAA,MACH,MAAM;AAAA,MACN;AAAA,IACH;AAAA,EACT;AAAA,EACI,OAAO,gBAAgB,KAAK;AACxB,QAAI,QAAQ,MAAM;AACd,aAAO;AAAA,QACH,MAAM;AAAA,MACT;AAAA,IACb,WACiB,MAAM,QAAQ,GAAG,GAAG;AACzB,YAAM,cAAc,IAAI,IAAI,YAAU;AAClC,eAAO,eAAe,qBAAqB,MAAM;AAAA,MACjE,CAAa;AACD,aAAO;AAAA,QACH,MAAM;AAAA,QACN,OAAO;AAAA,MACV;AAAA,IACb,WACiB,cAAc,GAAG,GAAG;AACzB,UAAI;AACA,aAAK,UAAU,GAAG;AAAA,MAClC,SACmB,OAAO;AACV,YAAI,iBAAiB,aACjB,MAAM,QAAQ,WAAW,uCAAuC,GAAG;AACnE,gBAAM,WAAW;AAAA,QACrC;AACgB,cAAM;AAAA,MACtB;AACY,YAAM,eAAe,CAAE;AACvB,iBAAW,OAAO,KAAK;AACnB,qBAAa,KAAK;AAAA,UACd,eAAe,qBAAqB,GAAG;AAAA,UACvC,eAAe,qBAAqB,IAAI,GAAG,CAAC;AAAA,QAChE,CAAiB;AAAA,MACjB;AACY,aAAO;AAAA,QACH,MAAM;AAAA,QACN,OAAO;AAAA,MACV;AAAA,IACb,WACiB,SAAS,GAAG,GAAG;AACpB,aAAO;AAAA,QACH,MAAM;AAAA,QACN,OAAO;AAAA,UACH,SAAS,IAAI;AAAA,UACb,OAAO,IAAI;AAAA,QACd;AAAA,MACJ;AAAA,IACb,WACiB,OAAO,GAAG,GAAG;AAClB,aAAO;AAAA,QACH,MAAM;AAAA,QACN,OAAO,IAAI,YAAa;AAAA,MAC3B;AAAA,IACb;AACQ,UAAM,IAAI,oBAAoB,sEAAsE;AAAA,EAC5G;AAAA,EACI,OAAO,qBAAqB,KAAK;AAC7B,YAAQ,OAAO,KAAG;AAAA,MACd,KAAK;AAAA,MACL,KAAK;AACD,cAAM,IAAI,oBAAoB,0BAA0B,OAAO,GAAG,EAAE;AAAA,MACxE,KAAK;AACD,eAAO,eAAe,gBAAgB,GAAG;AAAA,MAC7C,KAAK;AACD,eAAO;AAAA,UACH,MAAM;AAAA,QACT;AAAA,MACL,KAAK;AACD,eAAO,eAAe,gBAAgB,GAAG;AAAA,MAC7C,KAAK;AACD,eAAO;AAAA,UACH,MAAM;AAAA,UACN,OAAO,IAAI,SAAU;AAAA,QACxB;AAAA,MACL,KAAK;AACD,eAAO;AAAA,UACH,MAAM;AAAA,UACN,OAAO;AAAA,QACV;AAAA,MACL,KAAK;AACD,eAAO;AAAA,UACH,MAAM;AAAA,UACN,OAAO;AAAA,QACV;AAAA,IACjB;AAAA,EACA;AAAA,EACI,aAAa,UAAU,SAAS,KAAK;AACjC,QAAI,eAAe,SAAS;AACxB,YAAM,MAAM,IAAI,IAAI,QAAQ,KAAK;AAAA,IAC7C;AAEQ,UAAM,eAAe,QAAQ,eAAe,gBAAgB,eAAe,qBACrE,MACA;AACN,QAAI,cAAc;AACd,UAAI,aAAa,MAAM,YAAY,QAAS,MACxC,QAAQ,YAAY,WAAW;AAC/B,cAAM,IAAI,MAAM,mEAAmE;AAAA,MACnG;AACY,UAAI,aAAa,UAAU;AACvB,cAAM,IAAI,MAAM,uBAAuB;AAAA,MACvD;AACY,aAAO,aAAa,YAAa;AAAA,IAC7C;AACQ,WAAO,eAAe,qBAAqB,GAAG;AAAA,EACtD;AACA;ACnIO,MAAM,kBAAkB,aAAa;AAAA,EAIxC,YAAY,YAAY;AACpB,UAAO;AALR;AACH;AACA;AACA,uBAAAM;AAWA,gDAAuB,OAAO,WAAW;AACrC,UAAI,OAAO,UAAU,mBAAK,MAAK;AAG3B,aAAK,wBAAwB;AAC7B,2BAAKA,WAAS,YAAY,oBAAqB;AAAA,MAC3D;AAAA,IACK;AACD,8CAAqB,CAAC,WAAW;AAC7B,UAAI,OAAO,SAAS,YAChB,OAAO,YAAY,mBAAKA,WAAS,YAAY,OAC7C,OAAO,YAAY,mBAAKA,WAAS,MAAM;AACvC,2BAAK,KAAM,OAAO;AAClB,aAAK,mBAAKA,WAAS,YAAY,SAAU;AAAA,MACrD;AAAA,IACK;AAMD;AA7BI,SAAK,aAAa;AAAA,EAC1B;AAAA,EACI,IAAI,SAAS;AACT,WAAO;AAAA,MACH,SAAS,mBAAKA,WAAS,YAAY;AAAA,MACnC,SAAS,mBAAKA,WAAS;AAAA,IAC1B;AAAA,EACT;AAAA,EAiBI,WAAW,SAAS;AAChB,uBAAKA,WAAW;AAChB,SAAK,WAAW,GAAGC,SAAiB,aAAC,OAAO,WAAW,cAAc,KAAK,kBAAkB;AAC5F,SAAK,WAAW,GAAGA,SAAiB,aAAC,OAAO,WAAW,gBAAgB,KAAK,oBAAoB;AAAA,EACxG;AAAA,EAEI,IAAI,gBAAgB;AAChB,UAAM,UAAU,QAAQ,QAAS;AACjC,mBAAe,OAAO,YAAU;AAC5B,UAAI,KAAK,uBAAuB;AAC5B,aAAK,KAAK,sBAAsB,KAAK,YAAU;AAC3C,eAAK,OAAO,QAAS;AAAA,QACzC,CAAiB;AAAA,MACjB;AACY,WAAK,wBAAwB,QAAQ,KAAK,MAAM;AAC5C,eAAO,KAAK,eAAe,MAAM;AAAA,MACjD,CAAa;AAAA,IACb,GAAW,CAAC,KAAK,qBAAqB;AAC9B,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,MAAM,eAAe,iBAAiB,MAAM;AACxC,WAAO,MAAM,sBAAK,mCAAL,WAAe,OAAO,cAAc,GAAG;AAAA,EAC5D;AAAA,EACI,MAAM,SAAS,iBAAiB,MAAM;AAClC,WAAO,MAAM,sBAAK,mCAAL,WAAe,MAAM,cAAc,GAAG;AAAA,EAC3D;AAAA,EAuDI,CAAC,aAAa,IAAI;AACd,SAAK,WAAW,IAAIA,SAAiB,aAAC,OAAO,WAAW,cAAc,KAAK,kBAAkB;AAC7F,SAAK,WAAW,IAAIA,SAAiB,aAAC,OAAO,WAAW,gBAAgB,KAAK,oBAAoB;AAAA,EACzG;AACA;AAhHI;AACAD,YAAA;AAHG;AAwDG,cAAS,eAAC,eAAe,iBAAiB,MAAM;;AAClD,QAAM,mBAAmB,sBAAoBN,MAAA,iCAAiC,YAAY,MAA7C,gBAAAA,IAAgD,eACzF,aAAa,YAAY;AAC7B,QAAM,UAAU,mBAAKM;AACrB,MAAI;AACJ,QAAM,kBAAkB,gBAClB,SACA;AACN,QAAM,uBAAuB,gBACvB,CAAA,IACA;AAAA,IACE,gBAAgB;AAAA,IAChB,aAAa;AAAA,EAChB;AACL,MAAI,SAAS,YAAY,GAAG;AACxB,UAAM,aAAa,iBAAiB,KAAK,YAAY,IAC/C,eACA,GAAG,YAAY;AAAA,EAAK,gBAAgB;AAAA;AAC1C,sBAAkB,KAAK,WAAW,KAAK,mBAAmB;AAAA,MACtD;AAAA,MACA,QAAQ,KAAK;AAAA,MACb;AAAA,MACA,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB;AAAA,IAChB,CAAa;AAAA,EACb,OACa;AACD,QAAI,sBAAsB,kBAAkB,YAAY;AACxD,0BAAsB,iBAAiB,KAAK,mBAAmB,IACzD,sBACA,GAAG,mBAAmB;AAAA,EAAK,gBAAgB;AAAA;AACjD,sBAAkB,KAAK,WAAW,KAAK,uBAAuB;AAAA,MAC1D;AAAA,MACA,WAAW,KAAK,SACV,MAAM,QAAQ,IAAI,KAAK,IAAI,SAAO;AAChC,eAAO,eAAe,UAAU,SAAS,GAAG;AAAA,MACpE,CAAqB,CAAC,IACA,CAAE;AAAA,MACR,QAAQ,KAAK;AAAA,MACb;AAAA,MACA,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB;AAAA,IAChB,CAAa;AAAA,EACb;AACQ,QAAM,EAAE,OAAQ,IAAG,MAAM;AACzB,MAAI,UAAU,UAAU,OAAO,SAAS,aAAa;AACjD,UAAM,sBAAsB,OAAO,gBAAgB;AAAA,EAC/D;AACQ,SAAO,gBACD,iBAAiB,YAAY,OAAO,MAAM,IAC1C,iBAAiB,SAAS,OAAO,MAAM;AACrD;AASO,SAAS,iBAAiB,SAAS,QAAQ;AAC9C,MAAI,OAAO,SAAS,UAAU,OAAO,SAAS,UAAU;AACpD,WAAO,IAAI,kBAAkB,SAAS,MAAM;AAAA,EACpD;AACI,SAAO,IAAI,aAAa,SAAS,MAAM;AAC3C;ACjIY,MAAC,cAAc,oBAAI,IAAG;AAI3B,MAAM,0BAA0B,WAAW;AAAA,EAI9C,YAAY,SAAS,WAAW;AAC5B,UAAO;AAJX;AACA,mCAAa,SAAS,OAAQ;AAC9B,kCAAY;AAGR,uBAAK,UAAW;AAChB,QAAI,CAAC,mBAAK,UAAS,eAAe;AAC9B;AAAA,IACZ;AACQ,QAAI,WAAW;AACX,yBAAK,YAAW,QAAQ,SAAS;AACjC,kBAAY,IAAI,WAAW,IAAI;AAAA,IAC3C,OACa;AACD,cAAQ,WACH,KAAK,kBAAkB;AAAA,QACxB,SAAS,QAAQ;AAAA,MACpB,CAAA,EACI,KAAK,aAAW;AACjB,2BAAK,YAAW,QAAQ,QAAQ,OAAO,OAAO;AAC9C,oBAAY,IAAI,QAAQ,OAAO,SAAS,IAAI;AAAA,MAC/C,CAAA,EACI,MAAM,SAAO;AACd,2BAAK,YAAW,OAAO,GAAG;AAAA,MAC1C,CAAa;AAAA,IACb;AAAA,EACA;AAAA,EACI,aAAa;AACT,WAAO;AAAA,EACf;AAAA,EACI,MAAM,KAAK,WAAW,WAAW;AAC7B,QAAI,CAAC,mBAAK,UAAS,eAAe;AAC9B,YAAM,IAAI,qBAAqB,qFAAqF;AAAA,IAChI;AACQ,QAAI,mBAAK,YAAW;AAChB,YAAM,IAAI,iBAAiB,mBAAmB,MAAM,0DAA0D;AAAA,IAC1H;AACQ,UAAM,UAAU,MAAM,mBAAK,YAAW,aAAc;AACpD,UAAM,EAAE,OAAM,IAAK,MAAM,mBAAK,UAAS,WAAW,KAAK,mBAAmB;AAAA,MACtE;AAAA,MACA,QAAQ,UAAU,CAAC;AAAA,MACnB;AAAA,IACZ,CAAS;AACD,WAAO,OAAO;AAAA,EACtB;AAAA,EACI,MAAM,SAAS;AACX,gBAAY,OAAO,KAAK,IAAI;AAC5B,QAAI,CAAC,mBAAK,cAAa,mBAAK,UAAS,YAAW,GAAI;AAChD,YAAM,mBAAK,UAAS,WAAW,KAAK,2BAA2B;AAAA,QAC3D,WAAW,KAAK,GAAI;AAAA,MACpC,CAAa;AAAA,IACb;AACQ,uBAAK,WAAY;AAAA,EACzB;AAAA,EACI,KAAK;AACD,UAAM,MAAM,mBAAK,YAAW,MAAO;AACnC,WAAO,eAAe,SAAS,QAAQ,SAAY,KAAK;AAAA,EAChE;AACA;AA1DI;AACA;AACA;AA+DM,IAAC;AAAA,CACV,SAAUE,uBAAsB;AAI7B,EAAAA,sBAAqB,UAAU,OAAO,yBAAyB;AAK/D,EAAAA,sBAAqB,YAAY,OAAO,2BAA2B;AACvE,GAAG,yBAAyB,uBAAuB,CAAA,EAAG;yBAI/C,mBAA8B,UAAU;AAAA,EAM3C,YAAY,YAAY,MAAM,aAAa;AACvC,UAAM,UAAU;AAPjB;AACH,uBAAAC;AACA;AACA;AACA;AACA,qCAAe;AAGX,uBAAKA,MAAM,KAAK;AAChB,uBAAK,MAAO,KAAK;AACjB,uBAAK,SAAU,KAAK;AACpB,uBAAK,cAAe;AACpB,uBAAK,aAAc,IAAI,kBAAkB,MAAM,MAAS;AACxD,SAAK,GAAG,oCAAoC,sBAAK,0CAAW,KAAK,IAAI,CAAC;AACtE,SAAK,GAAG,qCAAqC,sBAAK,0CAAW,KAAK,IAAI,CAAC;AACvE,SAAK,GAAG,wBAAwB,sBAAK,0CAAW,KAAK,IAAI,CAAC;AAAA,EAClE;AAAA,EACI,cAAc;AACV,WAAO,CAAC,mBAAK,cAAa,YAAW,EAAG,SAAS,SAAS;AAAA,EAClE;AAAA,EAII,wBAAwB;AACpB,WAAO,IAAI,UAAU,KAAK,UAAU;AAAA,EAC5C;AAAA,EACI,IAAI,MAAM;AACN,WAAO,mBAAK;AAAA,EACpB;AAAA,EACI,IAAI,KAAK;AACL,WAAO,mBAAKA;AAAA,EACpB;AAAA,EACI,IAAI,SAAS;AACT,WAAO,mBAAK;AAAA,EACpB;AAAA,EACI,IAAI,aAAa;AACb,WAAO,mBAAK;AAAA,EACpB;AAAA,EACI,MAAM,eAAe,WAAW,WAAW;AACvC,WAAO,MAAM,mBAAK,aAAY,KAAK,QAAQ,GAAG,SAAS;AAAA,EAC/D;AAAA,EACI,UAAU;AACN,SAAK,mBAAoB;AACzB,SAAK,WAAW,2BAA2B,mBAAKA,KAAG;AACnD,SAAK,mBAAK,aAAY,OAAM,EAAG,MAAM,UAAU;AAAA,EACvD;AACA,GA7CIA,OAAA,eACA,sBACA,6BACA,yBACA,8BALG,4CAoBH,eAAU,SAAC,MAAM;AACb,qBAAK,MAAO,KAAK;AACzB,GAtBO;AC7FP;AAAA;AAAA;AAAA;AAAA;AAWA,MAAM,oBAAoB,MAAM,gCAAgC;AAChE,MAAM,uBAAuB,MAAM,gCAAgC;AAI5D,MAAM,uBAAuB,aAAa;AAAA,EAS7C,YAAY,KAAK,WAAW,QAAQ,GAAGC,UAAS;AAC5C,UAAO;AAVR;AACH,uBAAAC;AACA;AACA;AACA,iCAAW;AACX,gCAAU;AACV,mCAAa,IAAI,iBAAkB;AACnC,0CAAoB,oBAAI,IAAK;AAC7B,kCAAY,CAAE;AAGV,uBAAKA,OAAO;AACZ,uBAAK,QAAS;AACd,uBAAK,UAAWD,YAAW;AAC3B,uBAAK,YAAa;AAClB,uBAAK,YAAW,YAAY,KAAK,UAAU,KAAK,IAAI;AACpD,uBAAK,YAAW,UAAU,KAAK,OAAO,KAAK,IAAI;AAAA,EACvD;AAAA,EACI,IAAI,SAAS;AACT,WAAO,mBAAK;AAAA,EACpB;AAAA,EACI,IAAI,MAAM;AACN,WAAO,mBAAKC;AAAA,EACpB;AAAA,EACI,OAAO,SAAS;AACZ,uBAAK,WAAU,KAAK,OAAO;AAAA,EACnC;AAAA,EACI,KAAK,MAAM,OAAO;AACd,eAAW,WAAW,mBAAK,YAAW;AAClC,cAAQ,KAAK,MAAM,KAAK;AAAA,IACpC;AACQ,WAAO,MAAM,KAAK,MAAM,KAAK;AAAA,EACrC;AAAA,EACI,KAAK,QAAQ,QAAQ;AACjB,WAAO,CAAC,mBAAK,UAAS,oCAAoC;AAC1D,WAAO,mBAAK,YAAW,OAAO,QAAQ,mBAAK,WAAU,QAAM;AACvD,YAAM,qBAAqB,KAAK,UAAU;AAAA,QACtC;AAAA,QACA;AAAA,QACA;AAAA,MAChB,CAAa;AACD,wBAAkB,kBAAkB;AACpC,yBAAK,YAAW,KAAK,kBAAkB;AAAA,IACnD,CAAS;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAII,MAAM,UAAU,SAAS;;AACrB,QAAI,mBAAK,SAAQ;AACb,YAAM,IAAI,QAAQ,OAAK;AACnB,eAAO,WAAW,GAAG,mBAAK,OAAM;AAAA,MAChD,CAAa;AAAA,IACb;AACQ,yBAAqB,OAAO;AAC5B,UAAM,SAAS,KAAK,MAAM,OAAO;AACjC,QAAI,UAAU,QAAQ;AAClB,cAAQ,OAAO,MAAI;AAAA,QACf,KAAK;AACD,6BAAK,YAAW,QAAQ,OAAO,IAAI,MAAM;AACzC;AAAA,QACJ,KAAK;AACD,cAAI,OAAO,OAAO,MAAM;AACpB;AAAA,UACxB;AACoB,6BAAK,YAAW,OAAO,OAAO,IAAI,oBAAoB,MAAM,GAAG,OAAO,OAAO;AAC7E;AAAA,QACJ,KAAK;AACD,cAAI,WAAW,MAAM,GAAG;AACpB,aAAAX,MAAA,YACK,IAAI,OAAO,OAAO,OAAO,MAD9B,gBAAAA,IAEM,KAAK,OAAO,OAAO,OAAO,OAAO,OAAO;AAC9C;AAAA,UACxB;AACoB,gCAAK,kDAAL,WAAyB;AAEzB,eAAK,KAAK,OAAO,QAAQ,OAAO,MAAM;AACtC;AAAA,MACpB;AAAA,IACA;AAGQ,QAAI,QAAQ,QAAQ;AAChB,yBAAK,YAAW,OAAO,OAAO,IAAI,4DAA4D,OAAO,KAAK,OAAO,OAAO;AAAA,IACpI;AACQ,eAAW,MAAM;AAAA,EACzB;AAAA,EAcI,yBAAyB,SAAS;AAC9B,uBAAK,mBAAkB,IAAI,QAAQ,IAAI,OAAO;AAAA,EACtD;AAAA,EACI,mBAAmB,WAAW;AAC1B,UAAM,iBAAiB,mBAAK,mBAAkB,IAAI,SAAS;AAC3D,QAAI,CAAC,gBAAgB;AACjB,YAAM,IAAI,MAAM,mBAAmB,SAAS,kBAAkB;AAAA,IAC1E;AACQ,WAAO;AAAA,EACf;AAAA,EACI,mBAAmB,WAAW;AAC1B,QAAI,iBAAiB,mBAAK,mBAAkB,IAAI,SAAS;AACzD,QAAI,CAAC,gBAAgB;AACjB,YAAM,IAAI,MAAM,mBAAmB,SAAS,kBAAkB;AAAA,IAC1E;AACQ,WAAO,eAAe,QAAQ;AAC1B,kBAAY,eAAe;AAC3B,uBAAiB,mBAAK,mBAAkB,IAAI,SAAS;AACrD,UAAI,CAAC,gBAAgB;AACjB,cAAM,IAAI,MAAM,mBAAmB,SAAS,kBAAkB;AAAA,MAC9E;AAAA,IACA;AACQ,WAAO;AAAA,EACf;AAAA,EACI,2BAA2B,IAAI;AAC3B,uBAAK,mBAAkB,OAAO,EAAE;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMI,SAAS;AACL,QAAI,mBAAK,UAAS;AACd;AAAA,IACZ;AACQ,uBAAK,SAAU;AAEf,uBAAK,YAAW,YAAY,MAAM;AAAA,IAAG;AACrC,uBAAK,YAAW,UAAU,MAAM;AAAA,IAAG;AACnC,uBAAK,mBAAkB,MAAO;AAC9B,uBAAK,YAAW,MAAO;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAII,UAAU;AACN,SAAK,OAAQ;AACb,uBAAK,YAAW,MAAO;AAAA,EAC/B;AAAA,EACI,2BAA2B;AACvB,WAAO,mBAAK,YAAW,yBAA0B;AAAA,EACzD;AACA;AAxJIW,QAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARG;AAuFH,wBAAmB,SAAC,OAAO;AACvB,MAAI;AAEJ,MAAI,aAAa,MAAM,UAAU,MAAM,OAAO,YAAY,MAAM;AAC5D,cAAU,mBAAK,mBAAkB,IAAI,MAAM,OAAO,OAAO;AAAA,EAErE,WACiB,YAAY,MAAM,UACvB,MAAM,OAAO,OAAO,YAAY,QAAW;AAC3C,cAAU,mBAAK,mBAAkB,IAAI,MAAM,OAAO,OAAO,OAAO;AAAA,EAC5E;AACQ,qCAAS,KAAK,MAAM,QAAQ,MAAM;AAC1C;AA0DA,SAAS,oBAAoB,QAAQ;AACjC,MAAI,UAAU,GAAG,OAAO,KAAK,IAAI,OAAO,OAAO;AAC/C,MAAI,OAAO,YAAY;AACnB,eAAW,IAAI,OAAO,UAAU;AAAA,EACxC;AACI,SAAO;AACX;AACA,SAAS,WAAW,OAAO;AACvB,SAAO,MAAM,OAAO,WAAW,MAAM;AACzC;ACtLA;AAAA;AAAA;AAAA;AAAA;AASA,MAAM,mBAAmB,CAAC,WAAW,SAAS;AAC1C,QAAM,QAAQ,MAAM,EAAE,EAAE,IAAI;AAChC;AAIO,eAAe,mBAAmB,KAGzC,SAAS;AACL,QAAM,gBAAgB,IAAI,cAAe;AACzC,QAAM,uBAAuB,IAAI,qBAAqB,GAAG;AACzD,QAAM,gBAAgB;AAAA,IAClB,KAAK,SAAS;AAEV,oBAAc,YAAY,KAAK,MAAM,OAAO,CAAC;AAAA,IAChD;AAAA,IACD,QAAQ;AACJ,iBAAW,MAAO;AAClB,2BAAqB,MAAO;AAC5B,UAAI,QAAS;AAAA,IAChB;AAAA,IACD,UAAU,UAAU;AAAA,IAEnB;AAAA,EACJ;AACD,gBAAc,GAAG,gBAAgB,CAAC,YAAY;AAE1C,kBAAc,UAAU,KAAK,UAAU,OAAO,CAAC;AAAA,EACvD,CAAK;AACD,QAAM,qBAAqB,IAAI,eAAe,IAAI,IAAG,GAAI,aAAa;AACtE,QAAM,aAAa,MAAMC,WAAAA,WAAsB;AAAA,IAAe;AAAA,IAAe;AAAA;AAAA,IAE7E,qBAAqB,cAAa;AAAA,IAAI;AAAA,IAAI;AAAA,IAAS;AAAA,IAAW;AAAA,EAAgB;AAC9E,SAAO;AACX;AAKA,MAAM,qBAAqB;AAAA,EAIvB,YAAY,KAAK;AAHjB;AACA,kCAAY,oBAAI,IAAK;AACrB;AAEI,uBAAK,MAAO;AACZ,uBAAK,uBAAwB,IAAI,iBAAiB,GAAG;AAAA,EAC7D;AAAA,EACI,gBAAgB;AACZ,WAAO,mBAAK;AAAA,EACpB;AAAA,EACI,aAAa,IAAI;AACb,UAAM,UAAU,mBAAK,MAAK,QAAQ,EAAE;AACpC,QAAI,CAAC,SAAS;AACV,YAAM,IAAI,MAAM,+BAA+B,EAAE,EAAE;AAAA,IAC/D;AACQ,QAAI,CAAC,mBAAK,WAAU,IAAI,OAAO,GAAG;AAC9B,YAAM,UAAU,IAAI,iBAAiB,SAAS,IAAI,mBAAK,sBAAqB;AAC5E,yBAAK,WAAU,IAAI,SAAS,OAAO;AACnC,aAAO;AAAA,IACnB;AACQ,WAAO,mBAAK,WAAU,IAAI,OAAO;AAAA,EACzC;AAAA,EACI,QAAQ;AACJ,uBAAK,uBAAsB,MAAO;AAClC,eAAW,WAAW,mBAAK,WAAU,OAAM,GAAI;AAC3C,cAAQ,MAAO;AAAA,IAC3B;AAAA,EACA;AACA;AA5BI;AACA;AACA;AAiCJ,MAAM,yBAAyBC,WAAAA,aAAwB;AAAA,EAKnD,YAAY,QAAQ,WAAW,eAAe;AAC1C,UAAO;AALX,uBAAAC,UAAU;AACV;AACA;AACA;AAWA,wCAAkB,CAAC,QAAQ,UAAU;AACjC,WAAK,KAAK,QAAQ,KAAK;AAAA,IAC1B;AAVG,uBAAK,SAAU;AACf,SAAK,YAAY;AACjB,uBAAK,gBAAiB;AACtB,uBAAK,SAAQ,GAAG,KAAK,mBAAK,gBAAe;AAAA,EACjD;AAAA,EACI,gBAAgB;AACZ,WAAO,mBAAK;AAAA,EACpB;AAAA,EAII,MAAM,YAAY,WAAW,QAAQ;AACjC,QAAI,mBAAKA,WAAS;AACd;AAAA,IACZ;AACQ,QAAI;AACA,aAAO,MAAM,mBAAK,SAAQ,KAAK,QAAQ,GAAG,MAAM;AAAA,IAC5D,SACe,KAAK;AACR,UAAI,mBAAKA,WAAS;AACd;AAAA,MAChB;AACY,YAAM;AAAA,IAClB;AAAA,EACA;AAAA,EACI,QAAQ;AACJ,uBAAK,SAAQ,IAAI,KAAK,mBAAK,gBAAe;AAC1C,uBAAKA,UAAU;AAAA,EACvB;AAAA,EACI,aAAa,OAAO;AAChB,WAAO,iBAAiB;AAAA,EAChC;AACA;AAtCIA,WAAA;AACA;AAEA;AAWA;AA8BJ,MAAM,sBAAsBD,WAAAA,aAAwB;AAAA,EAApD;AAAA;AACI,mCAAa,OAAO,OAAO;AACvB;AAAA,IACH;AAAA;AAAA,EACD,YAAY,SAAS;AACjB,SAAK,mBAAK,YAAL,WAAgB;AAAA,EAC7B;AAAA,EACI,aAAa,WAAW;AACpB,uBAAK,YAAa;AAAA,EAC1B;AAAA,EACI,MAAM,YAAY,SAAS;AACvB,SAAK,KAAK,gBAAgB,OAAO;AAAA,EACzC;AAAA,EACI,QAAQ;AACJ,uBAAK,YAAa,OAAO,OAAO;AAC5B;AAAA,IACH;AAAA,EACT;AACA;AAjBI;ACnIJ;AAAA;AAAA;AAAA;AAAA;AAKA,IAAIZ,sBAAwD,SAAU,SAAS,cAAc,OAAO;AAChG,MAAI,WAAW,UAAU,SAAS;AAClC,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC1C,YAAQ,WAAW,aAAa,CAAC,EAAE,KAAK,SAAS,KAAK,IAAI,aAAa,CAAC,EAAE,KAAK,OAAO;AAAA,EAC9F;AACI,SAAO,WAAW,QAAQ;AAC9B;AACA,IAAIC,iBAA8C,SAAU,MAAM,cAAc,YAAY,WAAW,cAAc,mBAAmB;AACpI,WAAS,OAAO,GAAG;AAAE,QAAI,MAAM,UAAU,OAAO,MAAM,WAAY,OAAM,IAAI,UAAU,mBAAmB;AAAG,WAAO;AAAA,EAAE;AACrH,MAAI,OAAO,UAAU,MAAM,MAAM,SAAS,WAAW,QAAQ,SAAS,WAAW,QAAQ;AACzF,MAAI,SAAS,CAAC,gBAAgB,OAAO,UAAU,QAAQ,IAAI,OAAO,KAAK,YAAY;AACnF,MAAI,aAAa,iBAAiB,SAAS,OAAO,yBAAyB,QAAQ,UAAU,IAAI,IAAI;AACrG,MAAI,GAAG,OAAO;AACd,WAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,QAAI,UAAU,CAAE;AAChB,aAAS,KAAK,UAAW,SAAQ,CAAC,IAAI,MAAM,WAAW,CAAA,IAAK,UAAU,CAAC;AACvE,aAAS,KAAK,UAAU,OAAQ,SAAQ,OAAO,CAAC,IAAI,UAAU,OAAO,CAAC;AACtE,YAAQ,iBAAiB,SAAU,GAAG;AAAE,UAAI,KAAM,OAAM,IAAI,UAAU,wDAAwD;AAAG,wBAAkB,KAAK,OAAO,KAAK,IAAI,CAAC;AAAA,IAAI;AAC7K,QAAI,UAAS,GAAI,WAAW,CAAC,GAAG,SAAS,aAAa,EAAE,KAAK,WAAW,KAAK,KAAK,WAAW,IAAK,IAAG,WAAW,GAAG,GAAG,OAAO;AAC7H,QAAI,SAAS,YAAY;AACrB,UAAI,WAAW,OAAQ;AACvB,UAAI,WAAW,QAAQ,OAAO,WAAW,SAAU,OAAM,IAAI,UAAU,iBAAiB;AACxF,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,IAAI,EAAG,cAAa,QAAQ,CAAC;AAAA,IAC/D,WACiB,IAAI,OAAO,MAAM,GAAG;AACzB,UAAI,SAAS,QAAS,cAAa,QAAQ,CAAC;AAAA,UACvC,YAAW,GAAG,IAAI;AAAA,IACnC;AAAA,EACA;AACI,MAAI,OAAQ,QAAO,eAAe,QAAQ,UAAU,MAAM,UAAU;AACpE,SAAO;AACX;AAQA,IAAI,cAAc,MAAM;;AACpB,MAAI,cAAc;AAClB,MAAI,6BAA6B,CAAE;AACnC,MAAI;AACJ,SAAOF,MAAA,cAAyB,YAAY;AAAA;AAAA,IAiBxC,YAAY,SAAS;AACjB,YAAO;AAlBR;AAYH;AAAA,yBAAAe,YAAYd,oBAAkB,MAAM,0BAA0B,GAAG;AACjE,yBAAAe;AACA,uCAAe,IAAI,gBAAiB;AACpC,yBAAAP,MAAM,IAAI,SAAU;AAKhB,yBAAKO,mBAAmB;AAAA,IAEpC;AAAA,IAhBQ,OAAO,KAAK,SAAS;;AACjB,YAAM,aAAa,IAAIhB,IAAW,OAAO;AACzC,sBAAAA,MAAA,YAAW,uBAAAiB,gBAAX,KAAAjB;AACA,aAAO;AAAA,IACnB;AAAA,IA4EQ,IAAI,WAAW;AACX,aAAO,mBAAK,cAAa;AAAA,IACrC;AAAA,IACQ,IAAI,UAAU;AACV,aAAO,mBAAKe;AAAA,IACxB;AAAA;AAAA,IAEQ,UAAU;AACN,WAAK,aAAa,EAAG;AAAA,IACjC;AAAA,IACQ,EAAE,sBAAsB,CAAC,eAAe,GAAG,cAAa,IAAK;AACzD,yBAAK,cAAa,QAAS;AAC3B,YAAM,aAAa,EAAG;AAAA,IAClC;AAAA,EACK,GAxFGA,YAAA,eACAC,oBAAA,eACA,8BACAP,OAAA,eAfG,uCAuBHQ,iBAAW,WAAG;AACV,UAAM,yBAAyB,mBAAK,cAAa,IAAI,IAAI,aAAa,mBAAKD,kBAAgB,CAAC;AAC5F,2BAAuB,KAAK,UAAU,MAAM;AACxC,WAAK,KAAK,UAAU;AAAA,QAChB,KAAK,mBAAKA,mBAAiB;AAAA,QAC3B,WAAW,oBAAI,KAAM;AAAA,MACzC,CAAiB;AACD,WAAK,QAAS;AAAA,IAC9B,CAAa;AACD,uBAAKA,mBAAiB,GAAG,WAAW,CAAC,EAAE,QAAO,MAAO;AACjD,UAAI,QAAQ,eAAe,mBAAKP,MAAI,MAAK,GAAI;AACzC,2BAAKM,WAAW;AAChB,aAAK,KAAK,WAAW,OAAO;AAAA,MAChD;AAAA,IACA,CAAa;AACD,UAAM,iBAAiB,mBAAK,cAAa,IAAI,IAAI,aAAa,mBAAK,mCAAQ,CAAC;AAE5E,eAAW,aAAa;AAAA,MACpB;AAAA,MACA;AAAA,IAChB,GAAe;AACC,qBAAe,GAAG,WAAW,UAAQ;AACjC,YAAI,KAAK,YAAY,mBAAKC,mBAAiB,IAAI;AAC3C;AAAA,QACxB;AACoB,YAAI,CAAC,KAAK,YAAY;AAClB;AAAA,QACxB;AACoB,YAAI,CAAC,mBAAKP,MAAI,YAAY;AACtB,6BAAKA,MAAI,QAAQ,KAAK,UAAU;AAAA,QACxD;AAAA,MACA,CAAiB;AAAA,IACjB;AACY,eAAW,CAAC,WAAW,KAAK,KAAK;AAAA,MAC7B,CAAC,qCAAqC,UAAU;AAAA,MAChD,CAAC,oCAAoC,QAAQ;AAAA,MAC7C,CAAC,qCAAqC,SAAS;AAAA,IAC/D,GAAe;AACC,qBAAe,GAAG,WAAW,UAAQ;AACjC,YAAI,KAAK,YAAY,mBAAKO,mBAAiB,IAAI;AAC3C;AAAA,QACxB;AACoB,YAAI,CAAC,KAAK,YAAY;AAClB;AAAA,QACxB;AACoB,YAAI,CAAC,mBAAKP,MAAI,YAAY;AACtB,6BAAKA,MAAI,QAAQ,KAAK,UAAU;AAAA,QACxD;AACoB,YAAI,mBAAKA,MAAI,MAAK,MAAO,KAAK,YAAY;AACtC;AAAA,QACxB;AACoB,aAAK,KAAK,OAAO;AAAA,UACb,KAAK,KAAK;AAAA,UACV,WAAW,IAAI,KAAK,KAAK,SAAS;AAAA,QAC1D,CAAqB;AACD,aAAK,QAAS;AAAA,MAClC,CAAiB;AAAA,IACjB;AAAA,EACA,GAEY,cAAQ,WAAG;AACX,WAAO,mBAAKO,mBAAiB,YAAY,QAAQ;AAAA,EAC7D,IApFQ,MAAA;AACI,UAAM,YAAY,OAAO,WAAW,cAAc,OAAO,WAAW,OAAO,OAAO,YAAY,OAAO,QAAQ,KAAK,IAAI,IAAI;AAC1Hd,mBAAaF,KAAM,MAAM,qBAAqB,EAAE,MAAM,UAAU,MAAM,WAAW,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,aAAa,KAAK,KAAK,SAAO,IAAI,QAAS,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC1O,QAAI,UAAW,QAAO,eAAeA,KAAM,OAAO,UAAU,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,OAAO,WAAW;AAAA,EAClJ,MALWA;AAqGX,GAAI;ACvJJ;AAAA;AAAA;AAAA;AAAA;AAKA,IAAIC,sBAAwD,SAAU,SAAS,cAAc,OAAO;AAChG,MAAI,WAAW,UAAU,SAAS;AAClC,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC1C,YAAQ,WAAW,aAAa,CAAC,EAAE,KAAK,SAAS,KAAK,IAAI,aAAa,CAAC,EAAE,KAAK,OAAO;AAAA,EAC9F;AACI,SAAO,WAAW,QAAQ;AAC9B;AACA,IAAIC,iBAA8C,SAAU,MAAM,cAAc,YAAY,WAAW,cAAc,mBAAmB;AACpI,WAAS,OAAO,GAAG;AAAE,QAAI,MAAM,UAAU,OAAO,MAAM,WAAY,OAAM,IAAI,UAAU,mBAAmB;AAAG,WAAO;AAAA,EAAE;AACrH,MAAI,OAAO,UAAU,MAAM,MAAM,SAAS,WAAW,QAAQ,SAAS,WAAW,QAAQ;AACzF,MAAI,SAAS,CAAC,gBAAgB,OAAO,UAAU,QAAQ,IAAI,OAAO,KAAK,YAAY;AACnF,MAAI,aAAa,iBAAiB,SAAS,OAAO,yBAAyB,QAAQ,UAAU,IAAI,IAAI;AACrG,MAAI,GAAG,OAAO;AACd,WAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,QAAI,UAAU,CAAE;AAChB,aAAS,KAAK,UAAW,SAAQ,CAAC,IAAI,MAAM,WAAW,CAAA,IAAK,UAAU,CAAC;AACvE,aAAS,KAAK,UAAU,OAAQ,SAAQ,OAAO,CAAC,IAAI,UAAU,OAAO,CAAC;AACtE,YAAQ,iBAAiB,SAAU,GAAG;AAAE,UAAI,KAAM,OAAM,IAAI,UAAU,wDAAwD;AAAG,wBAAkB,KAAK,OAAO,KAAK,IAAI,CAAC;AAAA,IAAI;AAC7K,QAAI,UAAS,GAAI,WAAW,CAAC,GAAG,SAAS,aAAa,EAAE,KAAK,WAAW,KAAK,KAAK,WAAW,IAAK,IAAG,WAAW,GAAG,GAAG,OAAO;AAC7H,QAAI,SAAS,YAAY;AACrB,UAAI,WAAW,OAAQ;AACvB,UAAI,WAAW,QAAQ,OAAO,WAAW,SAAU,OAAM,IAAI,UAAU,iBAAiB;AACxF,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,IAAI,EAAG,cAAa,QAAQ,CAAC;AAAA,IAC/D,WACiB,IAAI,OAAO,MAAM,GAAG;AACzB,UAAI,SAAS,QAAS,cAAa,QAAQ,CAAC;AAAA,UACvC,YAAW,GAAG,IAAI;AAAA,IACnC;AAAA,EACA;AACI,MAAI,OAAQ,QAAO,eAAe,QAAQ,UAAU,MAAM,UAAU;AACpE,SAAO;AACX;AAOA,IAAI,SAAS,MAAM;;AACf,MAAI,cAAc;AAClB,MAAI,6BAA6B,CAAE;AACnC,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,SAAOF,MAAA,cAAoB,YAAY;AAAA;AAAA,IAenC,YAAY,IAAI,QAAQ;AACpB,YAAO;AANX;AAAA,mCAAWC,oBAAkB,MAAM,0BAA0B,GAAG;AAChE,yCAAc,IAAI,gBAAiB;AACnC;AACA;AAKI,WAAK,KAAK;AACV,WAAK,SAAS;AAAA,IAE1B;AAAA,IACQ,aAAa;AACT,YAAM,iBAAiB,KAAK,YAAY,IAAI,IAAI,aAAa,KAAK,OAAO,CAAC;AAC1E,qBAAe,GAAG,yBAAyB,UAAQ;AAC/C,YAAI,KAAK,UAAU,KAAK,IAAI;AACxB;AAAA,QACpB;AACgB,aAAK,QAAQ,0BAA0B;AAAA,MACvD,CAAa;AAAA,IACb;AAAA;AAAA,IAEQ,IAAI,WAAW;AACX,aAAO,mBAAK,aAAY;AAAA,IACpC;AAAA,IACQ,IAAI,SAAS;AACT,aAAO,EAAE,OAAO,KAAK,GAAI;AAAA,IACrC;AAAA;AAAA,IAEQ,QAAQ,QAAQ;AACZ,yBAAK,SAAU;AACf,WAAK,aAAa,EAAG;AAAA,IACjC;AAAA,IACQ,MAAM,OAAO,SAAS;AAClB,YAAM,KAAK,QAAQ,KAAK,iBAAiB;AAAA,QACrC,QAAQ,KAAK;AAAA,QACb;AAAA,MAChB,CAAa;AAAA,IACb;AAAA,IACQ,MAAM,aAAa,qBAAqB,cAAc,UAAU,CAAA,GAAI;AAChE,YAAM,EAAE,OAAM,IAAK,MAAM,KAAK,QAAQ,KAAK,uBAAuB;AAAA,QAC9D;AAAA,QACA;AAAA,QACA,QAAQ,KAAK;AAAA,QACb,GAAG;AAAA,MACnB,CAAa;AACD,aAAO;AAAA,IACnB;AAAA,IACQ,MAAM,SAAS,YAAY,cAAc,UAAU,CAAA,GAAI;AACnD,YAAM,EAAE,OAAM,IAAK,MAAM,KAAK,QAAQ,KAAK,mBAAmB;AAAA,QAC1D;AAAA,QACA;AAAA,QACA,QAAQ,KAAK;AAAA,QACb,GAAG;AAAA,MACnB,CAAa;AACD,aAAO;AAAA,IACnB;AAAA,IACQ,EAAE,sBAAsB,CAAC,eAAe,GAAG,qBAAqB,CAAC,gBAAgB,WAAS;AAElF,aAAO,oBAAM;AAAA,IAChB,CAAA,CAAC,GAAG,2BAA2B,CAAC,gBAAgB,WAAS;AAEtD,aAAO,oBAAM;AAAA,IAChB,CAAA,CAAC,GAAG,uBAAuB,CAAC,gBAAgB,WAAS;AAElD,aAAO,oBAAM;AAAA,IAC7B,CAAa,CAAC,GAAG,kBAAkB;AACvB,yBAAK,YAAL,mBAAK,SACD;AACJ,WAAK,KAAK,aAAa,EAAE,QAAQ,mBAAK,UAAS;AAC/C,WAAK,YAAY,QAAS;AAC1B,YAAM,aAAa,EAAG;AAAA,IAClC;AAAA,EACK,GAzEG,0BATA,MAAA;AACI,UAAM,YAAY,OAAO,WAAW,cAAc,OAAO,WAAW,OAAO,OAAO,YAAY,OAAO,QAAQ,KAAK,IAAI,IAAI;AAC1HC,mBAAaF,KAAM,MAAM,qBAAqB,EAAE,MAAM,UAAU,MAAM,WAAW,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,aAAa,KAAK,KAAK,SAAO,IAAI,QAAS,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC1OE,mBAAaF,KAAM,MAAM,oBAAoB,EAAE,MAAM,UAAU,MAAM,UAAU,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,YAAY,KAAK,KAAK,SAAO,IAAI,OAAQ,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AACtOE,mBAAaF,KAAM,MAAM,0BAA0B,EAAE,MAAM,UAAU,MAAM,gBAAgB,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,kBAAkB,KAAK,KAAK,SAAO,IAAI,aAAc,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC9PE,mBAAaF,KAAM,MAAM,sBAAsB,EAAE,MAAM,UAAU,MAAM,YAAY,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,cAAc,KAAK,KAAK,SAAO,IAAI,SAAU,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC9O,QAAI,UAAW,QAAO,eAAeA,KAAM,OAAO,UAAU,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,OAAO,WAAW;AAAA,EAClJ,MARWA;AAoFX,GAAI;AAKG,MAAM,eAAN,MAAM,qBAAoB,MAAM;AAAA,EAcnC,YAAY,SAAS,SAAS;AAC1B,UAAM,IAAI,EAAE;AARhB;AAAA;AACA;AAEA;AAAA,iCAAW;AAAA,MACP,WAAW,oBAAI,IAAK;AAAA,MACpB,QAAQ,oBAAI,IAAK;AAAA,IACpB;AAIG,SAAK,kBAAkB;AACvB,SAAK,UAAU;AAAA,EAEvB;AAAA,EAnBI,OAAO,KAAK,SAAS,SAAS;AAC1B,UAAM,QAAQ,IAAI,aAAY,SAAS,OAAO;AAC9C,UAAM,WAAY;AAClB,WAAO;AAAA,EACf;AAAA,EAgBI,aAAa;AACT,UAAM,WAAY;AAClB,UAAM,iBAAiB,KAAK,YAAY,IAAI,IAAI,aAAa,KAAK,OAAO,CAAC;AAC1E,mBAAe,GAAG,uBAAuB,UAAQ;AAC7C,UAAI,KAAK,SAAS,UAAU;AACxB;AAAA,MAChB;AACY,WAAK,KAAK,KAAK;AACf,WAAK,SAAS,KAAK;AAAA,IAC/B,CAAS;AACD,mBAAe,GAAG,uBAAuB,UAAQ;AAC7C,UAAI,KAAK,SAAS,oBAAoB;AAClC;AAAA,MAChB;AACY,UAAI,CAAC,KAAK,OAAO,SAAS,KAAK,EAAE,GAAG;AAChC;AAAA,MAChB;AACY,YAAM,QAAQ,qBAAqB,KAAK,MAAM,KAAK,OAAO,KAAK,MAAM;AACrE,yBAAK,UAAS,UAAU,IAAI,MAAM,IAAI,KAAK;AAC3C,YAAM,eAAe,KAAK,YAAY,IAAI,IAAI,aAAa,KAAK,CAAC;AACjE,mBAAa,KAAK,aAAa,MAAM;AACjC,qBAAa,mBAAoB;AACjC,2BAAK,UAAS,UAAU,OAAO,MAAM,EAAE;AAAA,MACvD,CAAa;AACD,WAAK,KAAK,UAAU,KAAK;AAAA,IACrC,CAAS;AACD,SAAK,gBAAgB,YAAY,QAAQ,GAAG,gBAAgB,CAAC,EAAE,YAAY;AACvE,UAAI,CAAC,MAAM,OAAO,IAAI,IAAI,GAAG;AACzB;AAAA,MAChB;AACY,yBAAK,UAAS,OAAO,IAAI,MAAM,IAAI,KAAK;AACxC,YAAM,eAAe,KAAK,YAAY,IAAI,IAAI,aAAa,KAAK,CAAC;AACjE,mBAAa,KAAK,aAAa,MAAM;AACjC,qBAAa,mBAAoB;AACjC,2BAAK,UAAS,OAAO,OAAO,MAAM,EAAE;AAAA,MACpD,CAAa;AACD,WAAK,KAAK,gBAAgB,KAAK;AAAA,IAC3C,CAAS;AAAA,EACT;AAAA,EACI,IAAI,UAAU;AACV,WAAO,KAAK,gBAAgB,YAAY,QAAQ;AAAA,EACxD;AAAA,EACI,IAAI,SAAS;AACT,WAAO,EAAE,SAAS,KAAK,gBAAgB,IAAI,SAAS,KAAK,QAAS;AAAA,EAC1E;AACA;AAxDI;AAVG,IAAM,cAAN;AAsEA,MAAM,wBAAN,MAAM,8BAA6B,MAAM;AAAA;AAAA,EAU5C,YAAY,OAAO,IAAI,QAAQ;AAC3B,UAAM,IAAI,MAAM;AAJpB;AAAA,uBAAAkB,WAAW,oBAAI,IAAK;AACpB;AAII,SAAK,SAAS,oBAAI,IAAI,CAAC,KAAK,CAAC;AAAA,EACrC;AAAA,EAZI,OAAO,KAAK,OAAO,IAAI,QAAQ;AAC3B,UAAM,QAAQ,IAAI,sBAAqB,OAAO,IAAI,MAAM;AACxD,UAAM,WAAY;AAClB,WAAO;AAAA,EACf;AAAA,EASI,aAAa;AACT,UAAM,WAAY;AAClB,UAAM,iBAAiB,KAAK,YAAY,IAAI,IAAI,aAAa,KAAK,OAAO,CAAC;AAC1E,mBAAe,GAAG,uBAAuB,UAAQ;AAC7C,UAAI,KAAK,SAAS,oBAAoB;AAClC;AAAA,MAChB;AACY,UAAI,CAAC,KAAK,OAAO,SAAS,KAAK,EAAE,GAAG;AAChC;AAAA,MAChB;AACY,YAAM,QAAQ,sBAAqB,KAAK,MAAM,KAAK,OAAO,KAAK,MAAM;AACrE,yBAAKA,WAAS,IAAI,MAAM,IAAI,KAAK;AACjC,YAAM,eAAe,KAAK,YAAY,IAAI,IAAI,aAAa,KAAK,CAAC;AACjE,mBAAa,KAAK,aAAa,MAAM;AACjC,2BAAKA,WAAS,OAAO,MAAM,EAAE;AAAA,MAC7C,CAAa;AACD,WAAK,KAAK,UAAU,KAAK;AAAA,IACrC,CAAS;AAAA,EACT;AAAA,EACI,IAAI,UAAU;AAEV,WAAO,KAAK,OAAO,OAAQ,EAAC,KAAI,EAAG,MAAM;AAAA,EACjD;AACA;AA9BIA,YAAA;AAPG,IAAM,uBAAN;ACnNP;AAAA;AAAA;AAAA;AAAA;AAKA,IAAIjB,sBAAwD,SAAU,SAAS,cAAc,OAAO;AAChG,MAAI,WAAW,UAAU,SAAS;AAClC,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC1C,YAAQ,WAAW,aAAa,CAAC,EAAE,KAAK,SAAS,KAAK,IAAI,aAAa,CAAC,EAAE,KAAK,OAAO;AAAA,EAC9F;AACI,SAAO,WAAW,QAAQ;AAC9B;AACA,IAAIC,iBAA8C,SAAU,MAAM,cAAc,YAAY,WAAW,cAAc,mBAAmB;AACpI,WAAS,OAAO,GAAG;AAAE,QAAI,MAAM,UAAU,OAAO,MAAM,WAAY,OAAM,IAAI,UAAU,mBAAmB;AAAG,WAAO;AAAA,EAAE;AACrH,MAAI,OAAO,UAAU,MAAM,MAAM,SAAS,WAAW,QAAQ,SAAS,WAAW,QAAQ;AACzF,MAAI,SAAS,CAAC,gBAAgB,OAAO,UAAU,QAAQ,IAAI,OAAO,KAAK,YAAY;AACnF,MAAI,aAAa,iBAAiB,SAAS,OAAO,yBAAyB,QAAQ,UAAU,IAAI,IAAI;AACrG,MAAI,GAAG,OAAO;AACd,WAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,QAAI,UAAU,CAAE;AAChB,aAAS,KAAK,UAAW,SAAQ,CAAC,IAAI,MAAM,WAAW,CAAA,IAAK,UAAU,CAAC;AACvE,aAAS,KAAK,UAAU,OAAQ,SAAQ,OAAO,CAAC,IAAI,UAAU,OAAO,CAAC;AACtE,YAAQ,iBAAiB,SAAU,GAAG;AAAE,UAAI,KAAM,OAAM,IAAI,UAAU,wDAAwD;AAAG,wBAAkB,KAAK,OAAO,KAAK,IAAI,CAAC;AAAA,IAAI;AAC7K,QAAI,UAAS,GAAI,WAAW,CAAC,GAAG,SAAS,aAAa,EAAE,KAAK,WAAW,KAAK,KAAK,WAAW,IAAK,IAAG,WAAW,GAAG,GAAG,OAAO;AAC7H,QAAI,SAAS,YAAY;AACrB,UAAI,WAAW,OAAQ;AACvB,UAAI,WAAW,QAAQ,OAAO,WAAW,SAAU,OAAM,IAAI,UAAU,iBAAiB;AACxF,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,IAAI,EAAG,cAAa,QAAQ,CAAC;AAAA,IAC/D,WACiB,IAAI,OAAO,MAAM,GAAG;AACzB,UAAI,SAAS,QAAS,cAAa,QAAQ,CAAC;AAAA,UACvC,YAAW,GAAG,IAAI;AAAA,IACnC;AAAA,EACA;AACI,MAAI,OAAQ,QAAO,eAAe,QAAQ,UAAU,MAAM,UAAU;AACpE,SAAO;AACX;AAOA,IAAI,WAAW,MAAM;;AAEjB,MAAI,cAAc;AAClB,MAAI,6BAA6B,CAAE;AACnC,MAAI;AACJ,SAAOF,MAAA,cAAsB,YAAY;AAAA;AAAA,IAmBrC,YAAY,iBAAiB,OAAO;AAChC,YAAO;AApBR;AAYH;AAAA,kCAAUC,oBAAkB,MAAM,0BAA0B,GAAG;AAC/D;AACA;AACA,yBAAAe;AACA,uCAAe,IAAI,gBAAiB;AACpC;AAKI,yBAAKA,mBAAmB;AACxB,yBAAK,QAAS;AAAA,IAE1B;AAAA,IAnBQ,OAAO,KAAK,iBAAiB,OAAO;;AAChC,YAAM,UAAU,IAAIhB,IAAQ,iBAAiB,KAAK;AAClD,sBAAAA,MAAA,SAAQ,oBAAAiB,gBAAR,KAAAjB;AACA,aAAO;AAAA,IACnB;AAAA,IA8DQ,IAAI,WAAW;AACX,aAAO,mBAAK,cAAa;AAAA,IACrC;AAAA,IACQ,IAAI,QAAQ;AACR,aAAO,mBAAK;AAAA,IACxB;AAAA,IACQ,IAAI,UAAU;AACV,aAAO,mBAAK,QAAO,QAAQ;AAAA,IACvC;AAAA,IACQ,IAAI,KAAK;AACL,aAAO,mBAAK,QAAO,QAAQ;AAAA,IACvC;AAAA,IACQ,IAAI,YAAY;AACZ,aAAO,mBAAK,QAAO;AAAA,IAC/B;AAAA,IACQ,IAAI,SAAS;AACT,aAAO,mBAAK,QAAO,QAAQ;AAAA,IACvC;AAAA,IACQ,IAAI,aAAa;AACb,aAAO,mBAAK,QAAO,cAAc;AAAA,IAC7C;AAAA,IACQ,IAAI,WAAW;AACX,aAAO,KAAK;AAAA,IACxB;AAAA,IACQ,IAAI,WAAW;AACX,aAAO,mBAAK;AAAA,IACxB;AAAA,IACQ,IAAI,MAAM;AACN,aAAO,mBAAK,QAAO,QAAQ;AAAA,IACvC;AAAA;AAAA,IAEQ,UAAU;AACN,WAAK,aAAa,EAAG;AAAA,IACjC;AAAA,IACQ,EAAE,sBAAsB,CAAC,eAAe,GAAG,cAAa,IAAK;AACzD,yBAAK,cAAa,QAAS;AAC3B,YAAM,aAAa,EAAG;AAAA,IAClC;AAAA,EACK,GAlGG,wBACA,2BACA,2BACAgB,oBAAA,eACA,8BACA,wBAjBG,oCA0BHC,iBAAW,WAAG;AACV,UAAM,yBAAyB,mBAAK,cAAa,IAAI,IAAI,aAAa,mBAAKD,kBAAgB,CAAC;AAC5F,2BAAuB,KAAK,UAAU,CAAC,EAAE,OAAM,MAAO;AAClD,yBAAK,QAAS;AACd,WAAK,KAAK,SAAS,mBAAK,OAAM;AAC9B,WAAK,QAAS;AAAA,IAC9B,CAAa;AACD,UAAM,iBAAiB,mBAAK,cAAa,IAAI,IAAI,aAAa,mBAAK,gCAAQ,CAAC;AAC5E,mBAAe,GAAG,6BAA6B,WAAS;AACpD,UAAI,MAAM,YAAY,mBAAKA,mBAAiB,IAAI;AAC5C;AAAA,MACpB;AACgB,UAAI,MAAM,QAAQ,YAAY,KAAK,IAAI;AACnC;AAAA,MACpB;AACgB,yBAAK,WAAYhB,IAAQ,KAAK,mBAAKgB,oBAAkB,KAAK;AAC1D,WAAK,KAAK,YAAY,mBAAK,UAAS;AACpC,WAAK,QAAS;AAAA,IAC9B,CAAa;AACD,mBAAe,GAAG,sBAAsB,WAAS;AAC7C,UAAI,MAAM,YAAY,mBAAKA,mBAAiB,IAAI;AAC5C;AAAA,MACpB;AACgB,UAAI,MAAM,QAAQ,YAAY,KAAK,IAAI;AACnC;AAAA,MACpB;AACgB,yBAAK,QAAS,MAAM;AACpB,WAAK,KAAK,SAAS,mBAAK,OAAM;AAC9B,WAAK,QAAS;AAAA,IAC9B,CAAa;AACD,mBAAe,GAAG,6BAA6B,WAAS;AACpD,UAAI,MAAM,YAAY,mBAAKA,mBAAiB,IAAI;AAC5C;AAAA,MACpB;AACgB,UAAI,MAAM,QAAQ,YAAY,KAAK,IAAI;AACnC;AAAA,MACpB;AACgB,yBAAK,WAAY,MAAM;AACvB,WAAK,KAAK,WAAW,mBAAK,UAAS;AACnC,WAAK,QAAS;AAAA,IAC9B,CAAa;AAAA,EACb,GAEY,cAAQ,WAAG;AACX,WAAO,mBAAKA,mBAAiB,YAAY,QAAQ;AAAA,EAC7D,IAtEQ,MAAA;AACI,UAAM,YAAY,OAAO,WAAW,cAAc,OAAO,WAAW,OAAO,OAAO,YAAY,OAAO,QAAQ,KAAK,IAAI,IAAI;AAC1Hd,mBAAaF,KAAM,MAAM,qBAAqB,EAAE,MAAM,UAAU,MAAM,WAAW,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,aAAa,KAAK,KAAK,SAAO,IAAI,QAAS,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC1O,QAAI,UAAW,QAAO,eAAeA,KAAM,OAAO,UAAU,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,OAAO,WAAW;AAAA,EAClJ,MALWA;AA+GX,GAAI;ACjKJ;AAAA;AAAA;AAAA;AAAA;AAKA,IAAIC,sBAAwD,SAAU,SAAS,cAAc,OAAO;AAChG,MAAI,WAAW,UAAU,SAAS;AAClC,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC1C,YAAQ,WAAW,aAAa,CAAC,EAAE,KAAK,SAAS,KAAK,IAAI,aAAa,CAAC,EAAE,KAAK,OAAO;AAAA,EAC9F;AACI,SAAO,WAAW,QAAQ;AAC9B;AACA,IAAIC,iBAA8C,SAAU,MAAM,cAAc,YAAY,WAAW,cAAc,mBAAmB;AACpI,WAAS,OAAO,GAAG;AAAE,QAAI,MAAM,UAAU,OAAO,MAAM,WAAY,OAAM,IAAI,UAAU,mBAAmB;AAAG,WAAO;AAAA,EAAE;AACrH,MAAI,OAAO,UAAU,MAAM,MAAM,SAAS,WAAW,QAAQ,SAAS,WAAW,QAAQ;AACzF,MAAI,SAAS,CAAC,gBAAgB,OAAO,UAAU,QAAQ,IAAI,OAAO,KAAK,YAAY;AACnF,MAAI,aAAa,iBAAiB,SAAS,OAAO,yBAAyB,QAAQ,UAAU,IAAI,IAAI;AACrG,MAAI,GAAG,OAAO;AACd,WAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,QAAI,UAAU,CAAE;AAChB,aAAS,KAAK,UAAW,SAAQ,CAAC,IAAI,MAAM,WAAW,CAAA,IAAK,UAAU,CAAC;AACvE,aAAS,KAAK,UAAU,OAAQ,SAAQ,OAAO,CAAC,IAAI,UAAU,OAAO,CAAC;AACtE,YAAQ,iBAAiB,SAAU,GAAG;AAAE,UAAI,KAAM,OAAM,IAAI,UAAU,wDAAwD;AAAG,wBAAkB,KAAK,OAAO,KAAK,IAAI,CAAC;AAAA,IAAI;AAC7K,QAAI,UAAS,GAAI,WAAW,CAAC,GAAG,SAAS,aAAa,EAAE,KAAK,WAAW,KAAK,KAAK,WAAW,IAAK,IAAG,WAAW,GAAG,GAAG,OAAO;AAC7H,QAAI,SAAS,YAAY;AACrB,UAAI,WAAW,OAAQ;AACvB,UAAI,WAAW,QAAQ,OAAO,WAAW,SAAU,OAAM,IAAI,UAAU,iBAAiB;AACxF,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,IAAI,EAAG,cAAa,QAAQ,CAAC;AAAA,IAC/D,WACiB,IAAI,OAAO,MAAM,GAAG;AACzB,UAAI,SAAS,QAAS,cAAa,QAAQ,CAAC;AAAA,UACvC,YAAW,GAAG,IAAI;AAAA,IACnC;AAAA,EACA;AACI,MAAI,OAAQ,QAAO,eAAe,QAAQ,UAAU,MAAM,UAAU;AACpE,SAAO;AACX;AAOA,IAAI,cAAc,MAAM;;AACpB,MAAI,cAAc;AAClB,MAAI,6BAA6B,CAAE;AACnC,MAAI;AACJ,MAAI;AACJ,SAAOF,MAAA,cAAyB,YAAY;AAAA;AAAA,IAmBxC,YAAY,SAAS,MAAM;AACvB,YAAO;AApBR;AAaH;AAAA,mCAAWC,oBAAkB,MAAM,0BAA0B,GAAG;AAChE;AACA,uCAAe,IAAI,gBAAiB;AACpC;AACA;AAKI,WAAK,kBAAkB;AACvB,WAAK,OAAO;AAAA,IAExB;AAAA,IAlBQ,OAAO,KAAK,iBAAiB,MAAM;;AAC/B,YAAM,aAAa,IAAID,IAAW,iBAAiB,IAAI;AACvD,sBAAAA,MAAA,YAAW,uBAAAiB,gBAAX,KAAAjB;AACA,aAAO;AAAA,IACnB;AAAA,IAkCQ,IAAI,SAAS;AACT,aAAO,mBAAK,aAAY;AAAA,IACpC;AAAA,IACQ,IAAI,WAAW;AACX,aAAO,KAAK;AAAA,IACxB;AAAA,IACQ,IAAI,UAAU;AACV,aAAO,mBAAK,aAAY;AAAA,IACpC;AAAA,IACQ,IAAI,SAAS;AACT,aAAO,mBAAK;AAAA,IACxB;AAAA;AAAA,IAEQ,QAAQ,QAAQ;AACZ,yBAAK,SAAU;AACf,WAAK,aAAa,EAAG;AAAA,IACjC;AAAA,IACQ,MAAM,OAAO,UAAU,IAAI;AACvB,YAAM,mBAAK,oCAAS,KAAK,oCAAoC;AAAA,QACzD,GAAG;AAAA,QACH,SAAS,KAAK,KAAK;AAAA,MACnC,CAAa;AAED,aAAO,mBAAK;AAAA,IACxB;AAAA,IACQ,EAAE,sBAAsB,CAAC,eAAe,GAAG,qBAAqB,CAAC,gBAAgB,YAAU;AAEnF,aAAO,qBAAO;AAAA,IAC9B,CAAa,CAAC,GAAG,kBAAkB;AACvB,yBAAK,YAAL,mBAAK,SACD;AACJ,WAAK,KAAK,UAAU,EAAE,QAAQ,mBAAK,UAAS;AAC5C,yBAAK,cAAa,QAAS;AAC3B,YAAM,aAAa,EAAG;AAAA,IAClC;AAAA,EACK,GAnEG,yBACA,yBACA,8BAfG,uCA0BHiB,iBAAW,WAAG;AACV,UAAM,wBAAwB,mBAAK,cAAa,IAAI,IAAI,aAAa,KAAK,eAAe,CAAC;AAC1F,0BAAsB,KAAK,UAAU,CAAC,EAAE,OAAM,MAAO;AACjD,WAAK,QAAQ,+BAA+B,MAAM,EAAE;AAAA,IACpE,CAAa;AACD,UAAM,iBAAiB,mBAAK,cAAa,IAAI,IAAI,aAAa,mBAAK,mCAAQ,CAAC;AAC5E,mBAAe,GAAG,oCAAoC,gBAAc;AAChE,UAAI,WAAW,YAAY,KAAK,gBAAgB,IAAI;AAChD;AAAA,MACpB;AACgB,yBAAK,SAAU;AACf,WAAK,KAAK,WAAW,UAAU;AAC/B,WAAK,QAAQ,8BAA8B;AAAA,IAC3D,CAAa;AAAA,EACb,GAEY,cAAQ,WAAG;AACX,WAAO,KAAK,gBAAgB,YAAY,QAAQ;AAAA,EAC5D,IA3CQ,MAAA;AACI,UAAM,YAAY,OAAO,WAAW,cAAc,OAAO,WAAW,OAAO,OAAO,YAAY,OAAO,QAAQ,KAAK,IAAI,IAAI;AAC1Hf,mBAAaF,KAAM,MAAM,qBAAqB,EAAE,MAAM,UAAU,MAAM,WAAW,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,aAAa,KAAK,KAAK,SAAO,IAAI,QAAS,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC1OE,mBAAaF,KAAM,MAAM,oBAAoB,EAAE,MAAM,UAAU,MAAM,UAAU,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,YAAY,KAAK,KAAK,SAAO,IAAI,OAAQ,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AACtO,QAAI,UAAW,QAAO,eAAeA,KAAM,OAAO,UAAU,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,OAAO,WAAW;AAAA,EAClJ,MANWA;AAiFX,GAAI;ACnIJ;AAAA;AAAA;AAAA;AAAA;AAKA,IAAIC,sBAAwD,SAAU,SAAS,cAAc,OAAO;AAChG,MAAI,WAAW,UAAU,SAAS;AAClC,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC1C,YAAQ,WAAW,aAAa,CAAC,EAAE,KAAK,SAAS,KAAK,IAAI,aAAa,CAAC,EAAE,KAAK,OAAO;AAAA,EAC9F;AACI,SAAO,WAAW,QAAQ;AAC9B;AACA,IAAIC,iBAA8C,SAAU,MAAM,cAAc,YAAY,WAAW,cAAc,mBAAmB;AACpI,WAAS,OAAO,GAAG;AAAE,QAAI,MAAM,UAAU,OAAO,MAAM,WAAY,OAAM,IAAI,UAAU,mBAAmB;AAAG,WAAO;AAAA,EAAE;AACrH,MAAI,OAAO,UAAU,MAAM,MAAM,SAAS,WAAW,QAAQ,SAAS,WAAW,QAAQ;AACzF,MAAI,SAAS,CAAC,gBAAgB,OAAO,UAAU,QAAQ,IAAI,OAAO,KAAK,YAAY;AACnF,MAAI,aAAa,iBAAiB,SAAS,OAAO,yBAAyB,QAAQ,UAAU,IAAI,IAAI;AACrG,MAAI,GAAG,OAAO;AACd,WAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,QAAI,UAAU,CAAE;AAChB,aAAS,KAAK,UAAW,SAAQ,CAAC,IAAI,MAAM,WAAW,CAAA,IAAK,UAAU,CAAC;AACvE,aAAS,KAAK,UAAU,OAAQ,SAAQ,OAAO,CAAC,IAAI,UAAU,OAAO,CAAC;AACtE,YAAQ,iBAAiB,SAAU,GAAG;AAAE,UAAI,KAAM,OAAM,IAAI,UAAU,wDAAwD;AAAG,wBAAkB,KAAK,OAAO,KAAK,IAAI,CAAC;AAAA,IAAI;AAC7K,QAAI,UAAS,GAAI,WAAW,CAAC,GAAG,SAAS,aAAa,EAAE,KAAK,WAAW,KAAK,KAAK,WAAW,IAAK,IAAG,WAAW,GAAG,GAAG,OAAO;AAC7H,QAAI,SAAS,YAAY;AACrB,UAAI,WAAW,OAAQ;AACvB,UAAI,WAAW,QAAQ,OAAO,WAAW,SAAU,OAAM,IAAI,UAAU,iBAAiB;AACxF,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,IAAI,EAAG,cAAa,QAAQ,CAAC;AAAA,IAC/D,WACiB,IAAI,OAAO,MAAM,GAAG;AACzB,UAAI,SAAS,QAAS,cAAa,QAAQ,CAAC;AAAA,UACvC,YAAW,GAAG,IAAI;AAAA,IACnC;AAAA,EACA;AACI,MAAI,OAAQ,QAAO,eAAe,QAAQ,UAAU,MAAM,UAAU;AACpE,SAAO;AACX;AAWA,IAAI,mBAAmB,MAAM;;AAEzB,MAAI,cAAc;AAClB,MAAI,6BAA6B,CAAE;AACnC,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,SAAOF,MAAA,cAA8B,YAAY;AAAA;AAAA,IAsC7C,YAAY,SAAS,QAAQ,IAAI,KAAK;AAClC,YAAO;AAvCR,yBAAAmB;AA0BH;AAAA,uCAAelB,oBAAkB,MAAM,0BAA0B,GAAG;AACpE;AACA,yBAAAU;AACA,oCAAY,oBAAI,IAAK;AACrB,uCAAe,IAAI,gBAAiB;AACpC,kCAAU,oBAAI,IAAK;AACnB,oCAAY,oBAAI,IAAK;AACrB;AACA;AACA;AACA;AAKI,yBAAKA,OAAO;AACZ,WAAK,KAAK;AACV,WAAK,SAAS;AACd,WAAK,cAAc;AAEnB,WAAK,eAAe,YAAY,KAAK,IAAI;AAAA,IACrD;AAAA,IA3BQ,OAAO,KAAK,aAAa,QAAQ,IAAI,KAAK;;AACtC,YAAM,kBAAkB,IAAIX,IAAgB,aAAa,QAAQ,IAAI,GAAG;AACxE,sBAAAA,MAAA,iBAAgBmB,6BAAAF,gBAAhB,KAAAjB;AACA,aAAO;AAAA,IACnB;AAAA,IA6GQ,IAAI,WAAW;AACX,aAAO,mBAAK,WAAU,OAAQ;AAAA,IAC1C;AAAA,IACQ,IAAI,SAAS;AACT,aAAO,mBAAK,aAAY;AAAA,IACpC;AAAA,IACQ,IAAI,WAAW;AACX,aAAO,KAAK;AAAA,IACxB;AAAA,IACQ,IAAI,SAAS;AACT,aAAO,mBAAK,SAAQ,OAAQ;AAAA,IACxC;AAAA,IACQ,IAAI,MAAM;AACN,UAAI,UAAU;AACd,eAAS,EAAE,WAAW,SAAS,QAAQ,EAAE,OAAQ,IAAG,SAAS;AACzD,kBAAU;AAAA,MAC1B;AACY,aAAO;AAAA,IACnB;AAAA,IACQ,IAAI,MAAM;AACN,aAAO,mBAAKW;AAAA,IACxB;AAAA;AAAA,IAEQ,QAAQ,QAAQ;AACZ,yBAAK,SAAU;AACf,WAAK,aAAa,EAAG;AAAA,IACjC;AAAA,IACQ,MAAM,WAAW;AACb,YAAM,mBAAKQ,6BAAA,aAAS,KAAK,4BAA4B;AAAA,QACjD,SAAS,KAAK;AAAA,MAC9B,CAAa;AAAA,IACb;AAAA,IACQ,MAAM,kBAAkB,UAAU,IAAI;AAClC,YAAM,EAAE,QAAQ,EAAE,KAAM,EAAA,IAAM,MAAM,mBAAKA,6BAAA,aAAS,KAAK,qCAAqC;AAAA,QACxF,SAAS,KAAK;AAAA,QACd,GAAG;AAAA,MACnB,CAAa;AACD,aAAO;AAAA,IACnB;AAAA,IACQ,MAAM,MAAM,cAAc;AACtB,YAAM,QAAQ,IAAI,CAAC,GAAG,mBAAK,WAAU,QAAQ,EAAE,IAAI,OAAO,UAAU;AAChE,cAAM,MAAM,MAAM,YAAY;AAAA,MAC9C,CAAa,CAAC;AACF,YAAM,mBAAKA,6BAAA,aAAS,KAAK,yBAAyB;AAAA,QAC9C,SAAS,KAAK;AAAA,QACd;AAAA,MAChB,CAAa;AAAA,IACb;AAAA,IACQ,MAAM,gBAAgB,OAAO;AACzB,YAAM,mBAAKA,6BAAA,aAAS,KAAK,mCAAmC;AAAA,QACxD,SAAS,KAAK;AAAA,QACd;AAAA,MAChB,CAAa;AAAA,IACb;AAAA,IACQ,MAAM,SAAS,KAAK,MAAM;AACtB,YAAM,mBAAKA,6BAAA,aAAS,KAAK,4BAA4B;AAAA,QACjD,SAAS,KAAK;AAAA,QACd;AAAA,QACA;AAAA,MAChB,CAAa;AACD,aAAO,MAAM,IAAI,QAAQ,aAAW;AAChC,aAAK,KAAK,cAAc,CAAC,EAAE,WAAU,MAAO;AACxC,kBAAQ,UAAU;AAAA,QACtC,CAAiB;AAAA,MACjB,CAAa;AAAA,IACb;AAAA,IACQ,MAAM,OAAO,UAAU,IAAI;AACvB,YAAM,mBAAKA,6BAAA,aAAS,KAAK,0BAA0B;AAAA,QAC/C,SAAS,KAAK;AAAA,QACd,GAAG;AAAA,MACnB,CAAa;AACD,aAAO,MAAM,IAAI,QAAQ,aAAW;AAChC,aAAK,KAAK,cAAc,CAAC,EAAE,WAAU,MAAO;AACxC,kBAAQ,UAAU;AAAA,QACtC,CAAiB;AAAA,MACjB,CAAa;AAAA,IACb;AAAA,IACQ,MAAM,MAAM,UAAU,IAAI;AACtB,YAAM,EAAE,QAAQ,EAAE,KAAM,EAAA,IAAM,MAAM,mBAAKA,6BAAA,aAAS,KAAK,yBAAyB;AAAA,QAC5E,SAAS,KAAK;AAAA,QACd,GAAG;AAAA,MACnB,CAAa;AACD,aAAO;AAAA,IACnB;AAAA,IACQ,MAAM,iBAAiB,UAAU,IAAI;AACjC,YAAM,mBAAKA,6BAAA,aAAS,KAAK,oCAAoC;AAAA,QACzD,SAAS,KAAK;AAAA,QACd,GAAG;AAAA,MACnB,CAAa;AAAA,IACb;AAAA,IACQ,MAAM,YAAY,UAAU,IAAI;AAC5B,YAAM,mBAAKA,6BAAA,aAAS,KAAK,+BAA+B;AAAA,QACpD,SAAS,KAAK;AAAA,QACd,GAAG;AAAA,MACnB,CAAa;AAAA,IACb;AAAA,IACQ,MAAM,eAAe,SAAS;AAC1B,YAAM,mBAAKA,6BAAA,aAAS,KAAK,wBAAwB;AAAA,QAC7C,SAAS,KAAK;AAAA,QACd;AAAA,MAChB,CAAa;AAAA,IACb;AAAA,IACQ,MAAM,iBAAiB;AACnB,YAAM,mBAAKA,6BAAA,aAAS,KAAK,wBAAwB;AAAA,QAC7C,SAAS,KAAK;AAAA,MAC9B,CAAa;AAAA,IACb;AAAA,IACQ,kBAAkB,SAAS;AACvB,aAAO,YAAY,KAAK,MAAM,OAAO;AAAA,IACjD;AAAA,IACQ,MAAM,iBAAiB,qBAAqB,UAAU,IAAI;AACtD,aAAO,MAAM,KAAK,YAAY,QAAQ,iBAAiB,qBAAqB;AAAA,QACxE,GAAG;AAAA,QACH,UAAU,CAAC,MAAM,GAAI,QAAQ,YAAY,CAAE,CAAC;AAAA,MAC5D,CAAa;AAAA,IACb;AAAA,IACQ,MAAM,oBAAoB,QAAQ;AAC9B,YAAM,KAAK,YAAY,QAAQ,oBAAoB,MAAM;AAAA,IACrE;AAAA,IACQ,EAAE,sBAAsB,CAAC,eAAe,GAAG,uBAAuB,CAAC,gBAAgB,aAAW;AAEtF,aAAO,sBAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,gCAAgC,CAAC,gBAAgB,aAAW;AAE7D,aAAO,sBAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,oBAAoB,CAAC,gBAAgB,aAAW;AAEjD,aAAO,sBAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,8BAA8B,CAAC,gBAAgB,aAAW;AAE3D,aAAO,sBAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,uBAAuB,CAAC,gBAAgB,aAAW;AAEpD,aAAO,sBAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,qBAAqB,CAAC,gBAAgB,aAAW;AAElD,aAAO,sBAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,oBAAoB,CAAC,gBAAgB,aAAW;AAEjD,aAAO,sBAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,+BAA+B,CAAC,gBAAgB,aAAW;AAE5D,aAAO,sBAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,0BAA0B,CAAC,gBAAgB,aAAW;AAEvD,aAAO,sBAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,6BAA6B,CAAC,gBAAgB,aAAW;AAE1D,aAAO,sBAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,6BAA6B,CAAC,gBAAgB,aAAW;AAE1D,aAAO,sBAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,gCAAgC,CAAC,gBAAgB,aAAW;AAE7D,aAAO,sBAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,+BAA+B,CAAC,gBAAgB,aAAW;AAE5D,aAAO,sBAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,kCAAkC,CAAC,gBAAgB,aAAW;AAE/D,aAAO,sBAAQ;AAAA,IAC/B,CAAa,CAAC,GAAG,kBAAkB;AACvB,yBAAK,YAAL,mBAAK,SACD;AACJ,WAAK,KAAK,UAAU,EAAE,QAAQ,mBAAK,UAAS;AAC5C,yBAAK,cAAa,QAAS;AAC3B,YAAM,aAAa,EAAG;AAAA,IAClC;AAAA,EACK,GAnRG,6BACA,yBACAR,QAAA,eACA,2BACA,8BACA,yBACA,2BAhCGQ,8BAAA,eAgDHF,iBAAW,WAAG;AACV,UAAM,qBAAqB,mBAAK,cAAa,IAAI,IAAI,aAAa,KAAK,WAAW,CAAC;AACnF,uBAAmB,KAAK,UAAU,CAAC,EAAE,OAAM,MAAO;AAC9C,WAAK,QAAQ,oCAAoC,MAAM,EAAE;AAAA,IACzE,CAAa;AACD,UAAM,iBAAiB,mBAAK,cAAa,IAAI,IAAI,aAAa,mBAAKE,6BAAA,YAAQ,CAAC;AAC5E,mBAAe,GAAG,kCAAkC,UAAQ;AACxD,UAAI,KAAK,WAAW,KAAK,IAAI;AACzB;AAAA,MACpB;AACgB,YAAM,kBAAkBnB,IAAgB,KAAK,KAAK,aAAa,MAAM,KAAK,SAAS,KAAK,GAAG;AAC3F,yBAAK,WAAU,IAAI,KAAK,SAAS,eAAe;AAChD,YAAM,yBAAyB,mBAAK,cAAa,IAAI,IAAI,aAAa,eAAe,CAAC;AACtF,6BAAuB,KAAK,UAAU,MAAM;AACxC,+BAAuB,mBAAoB;AAC3C,2BAAK,WAAU,OAAO,gBAAgB,EAAE;AAAA,MAC5D,CAAiB;AACD,WAAK,KAAK,mBAAmB,EAAE,gBAAe,CAAE;AAAA,IAChE,CAAa;AACD,mBAAe,GAAG,oCAAoC,UAAQ;AAC1D,UAAI,KAAK,YAAY,KAAK,IAAI;AAC1B;AAAA,MACpB;AACgB,WAAK,QAAQ,kCAAkC;AAAA,IAC/D,CAAa;AACD,mBAAe,GAAG,oCAAoC,UAAQ;AAC1D,UAAI,KAAK,YAAY,KAAK,IAAI;AAC1B;AAAA,MACpB;AACgB,yBAAKW,OAAO,KAAK;AACjB,WAAK,KAAK,oBAAoB,MAAS;AAAA,IACvD,CAAa;AACD,mBAAe,GAAG,wBAAwB,UAAQ;AAC9C,UAAI,KAAK,YAAY,KAAK,IAAI;AAC1B;AAAA,MACpB;AACgB,yBAAKA,OAAO,KAAK;AACjB,WAAK,KAAK,QAAQ,MAAS;AAAA,IAC3C,CAAa;AACD,mBAAe,GAAG,qCAAqC,UAAQ;AAC3D,UAAI,KAAK,YAAY,KAAK,IAAI;AAC1B;AAAA,MACpB;AACgB,yBAAKA,OAAO,KAAK;AACjB,yBAAK,WAAU,MAAO;AAEtB,yBAAK,aAAc,WAAW,KAAK,IAAI;AACvC,YAAM,oBAAoB,mBAAK,cAAa,IAAI,IAAI,aAAa,mBAAK,YAAW,CAAC;AAClF,iBAAW,aAAa,CAAC,YAAY,UAAU,SAAS,GAAG;AACvD,0BAAkB,KAAK,WAAW,CAAC,EAAE,IAAG,MAAO;AAC3C,4BAAkB,aAAa,EAAG;AAClC,6BAAKA,OAAO;AAAA,QACpC,CAAqB;AAAA,MACrB;AACgB,WAAK,KAAK,cAAc,EAAE,YAAY,mBAAK,cAAa;AAAA,IACxE,CAAa;AACD,mBAAe,GAAG,6BAA6B,WAAS;AACpD,UAAI,MAAM,YAAY,KAAK,IAAI;AAC3B;AAAA,MACpB;AACgB,UAAI,mBAAK,WAAU,IAAI,MAAM,QAAQ,OAAO,GAAG;AAC3C;AAAA,MACpB;AACgB,YAAM,UAAU,QAAQ,KAAK,MAAM,KAAK;AACxC,yBAAK,WAAU,IAAI,QAAQ,IAAI,OAAO;AACtC,WAAK,KAAK,WAAW,EAAE,QAAO,CAAE;AAAA,IAChD,CAAa;AACD,mBAAe,GAAG,kBAAkB,WAAS;AACzC,UAAI,MAAM,OAAO,YAAY,KAAK,IAAI;AAClC;AAAA,MACpB;AACgB,WAAK,KAAK,OAAO,EAAE,MAAK,CAAE;AAAA,IAC1C,CAAa;AACD,mBAAe,GAAG,oCAAoC,UAAQ;AAC1D,UAAI,KAAK,YAAY,KAAK,IAAI;AAC1B;AAAA,MACpB;AACgB,YAAM,aAAa,WAAW,KAAK,MAAM,IAAI;AAC7C,WAAK,KAAK,cAAc,EAAE,WAAU,CAAE;AAAA,IACtD,CAAa;AAAA,EACb,GAEY,cAAQ,WAAG;AACX,WAAO,KAAK,YAAY,QAAQ;AAAA,EAC5C,IAnIQ,MAAA;AACI,UAAM,YAAY,OAAO,WAAW,cAAc,OAAO,WAAW,OAAO,OAAO,YAAY,OAAO,QAAQ,KAAK,IAAI,IAAI;AAC1HT,mBAAaF,KAAM,MAAM,qBAAqB,EAAE,MAAM,UAAU,MAAM,WAAW,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,aAAa,KAAK,KAAK,SAAO,IAAI,QAAS,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC1OE,mBAAaF,KAAM,MAAM,sBAAsB,EAAE,MAAM,UAAU,MAAM,YAAY,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,cAAc,KAAK,KAAK,SAAO,IAAI,SAAU,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC9OE,mBAAaF,KAAM,MAAM,+BAA+B,EAAE,MAAM,UAAU,MAAM,qBAAqB,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,uBAAuB,KAAK,KAAK,SAAO,IAAI,kBAAmB,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAClRE,mBAAaF,KAAM,MAAM,mBAAmB,EAAE,MAAM,UAAU,MAAM,SAAS,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,WAAW,KAAK,KAAK,SAAO,IAAI,MAAO,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAClOE,mBAAaF,KAAM,MAAM,6BAA6B,EAAE,MAAM,UAAU,MAAM,mBAAmB,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,qBAAqB,KAAK,KAAK,SAAO,IAAI,gBAAiB,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC1QE,mBAAaF,KAAM,MAAM,sBAAsB,EAAE,MAAM,UAAU,MAAM,YAAY,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,cAAc,KAAK,KAAK,SAAO,IAAI,SAAU,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC9OE,mBAAaF,KAAM,MAAM,oBAAoB,EAAE,MAAM,UAAU,MAAM,UAAU,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,YAAY,KAAK,KAAK,SAAO,IAAI,OAAQ,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AACtOE,mBAAaF,KAAM,MAAM,mBAAmB,EAAE,MAAM,UAAU,MAAM,SAAS,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,WAAW,KAAK,KAAK,SAAO,IAAI,MAAO,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAClOE,mBAAaF,KAAM,MAAM,8BAA8B,EAAE,MAAM,UAAU,MAAM,oBAAoB,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,sBAAsB,KAAK,KAAK,SAAO,IAAI,iBAAkB,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC9QE,mBAAaF,KAAM,MAAM,yBAAyB,EAAE,MAAM,UAAU,MAAM,eAAe,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,iBAAiB,KAAK,KAAK,SAAO,IAAI,YAAa,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC1PE,mBAAaF,KAAM,MAAM,4BAA4B,EAAE,MAAM,UAAU,MAAM,kBAAkB,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,oBAAoB,KAAK,KAAK,SAAO,IAAI,eAAgB,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AACtQE,mBAAaF,KAAM,MAAM,4BAA4B,EAAE,MAAM,UAAU,MAAM,kBAAkB,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,oBAAoB,KAAK,KAAK,SAAO,IAAI,eAAgB,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AACtQE,mBAAaF,KAAM,MAAM,+BAA+B,EAAE,MAAM,UAAU,MAAM,qBAAqB,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,uBAAuB,KAAK,KAAK,SAAO,IAAI,kBAAmB,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAClRE,mBAAaF,KAAM,MAAM,8BAA8B,EAAE,MAAM,UAAU,MAAM,oBAAoB,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,sBAAsB,KAAK,KAAK,SAAO,IAAI,iBAAkB,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC9QE,mBAAaF,KAAM,MAAM,iCAAiC,EAAE,MAAM,UAAU,MAAM,uBAAuB,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,yBAAyB,KAAK,KAAK,SAAO,IAAI,oBAAqB,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC1R,QAAI,UAAW,QAAO,eAAeA,KAAM,OAAO,UAAU,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,OAAO,WAAW;AAAA,EAClJ,MAnBWA;AA8SX,GAAI;AClXJ;AAAA;AAAA;AAAA;AAAA;AAKA,IAAIC,sBAAwD,SAAU,SAAS,cAAc,OAAO;AAChG,MAAI,WAAW,UAAU,SAAS;AAClC,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC1C,YAAQ,WAAW,aAAa,CAAC,EAAE,KAAK,SAAS,KAAK,IAAI,aAAa,CAAC,EAAE,KAAK,OAAO;AAAA,EAC9F;AACI,SAAO,WAAW,QAAQ;AAC9B;AACA,IAAIC,iBAA8C,SAAU,MAAM,cAAc,YAAY,WAAW,cAAc,mBAAmB;AACpI,WAAS,OAAO,GAAG;AAAE,QAAI,MAAM,UAAU,OAAO,MAAM,WAAY,OAAM,IAAI,UAAU,mBAAmB;AAAG,WAAO;AAAA,EAAE;AACrH,MAAI,OAAO,UAAU,MAAM,MAAM,SAAS,WAAW,QAAQ,SAAS,WAAW,QAAQ;AACzF,MAAI,SAAS,CAAC,gBAAgB,OAAO,UAAU,QAAQ,IAAI,OAAO,KAAK,YAAY;AACnF,MAAI,aAAa,iBAAiB,SAAS,OAAO,yBAAyB,QAAQ,UAAU,IAAI,IAAI;AACrG,MAAI,GAAG,OAAO;AACd,WAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,QAAI,UAAU,CAAE;AAChB,aAAS,KAAK,UAAW,SAAQ,CAAC,IAAI,MAAM,WAAW,CAAA,IAAK,UAAU,CAAC;AACvE,aAAS,KAAK,UAAU,OAAQ,SAAQ,OAAO,CAAC,IAAI,UAAU,OAAO,CAAC;AACtE,YAAQ,iBAAiB,SAAU,GAAG;AAAE,UAAI,KAAM,OAAM,IAAI,UAAU,wDAAwD;AAAG,wBAAkB,KAAK,OAAO,KAAK,IAAI,CAAC;AAAA,IAAI;AAC7K,QAAI,UAAS,GAAI,WAAW,CAAC,GAAG,SAAS,aAAa,EAAE,KAAK,WAAW,KAAK,KAAK,WAAW,IAAK,IAAG,WAAW,GAAG,GAAG,OAAO;AAC7H,QAAI,SAAS,YAAY;AACrB,UAAI,WAAW,OAAQ;AACvB,UAAI,WAAW,QAAQ,OAAO,WAAW,SAAU,OAAM,IAAI,UAAU,iBAAiB;AACxF,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,IAAI,EAAG,cAAa,QAAQ,CAAC;AAAA,IAC/D,WACiB,IAAI,OAAO,MAAM,GAAG;AACzB,UAAI,SAAS,QAAS,cAAa,QAAQ,CAAC;AAAA,UACvC,YAAW,GAAG,IAAI;AAAA,IACnC;AAAA,EACA;AACI,MAAI,OAAQ,QAAO,eAAe,QAAQ,UAAU,MAAM,UAAU;AACpE,SAAO;AACX;AASA,IAAI,eAAe,MAAM;;AACrB,MAAI,cAAc;AAClB,MAAI,6BAA6B,CAAE;AACnC,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,SAAOF,MAAA,cAA0B,YAAY;AAAA;AAAA,IAsBzC,YAAY,SAAS,IAAI;AACrB,YAAO;AAvBR;AAeH;AAAA,mCAAWC,oBAAkB,MAAM,0BAA0B,GAAG;AAEhE;AAAA,yBAAAmB,oBAAoB,oBAAI,IAAK;AAC7B,uCAAe,IAAI,gBAAiB;AACpC,yBAAAX;AACA;AAKI,yBAAKA,MAAM;AACX,WAAK,UAAU;AAAA,IAE3B;AAAA,IAnBQ,OAAO,OAAO,SAAS,IAAI;;AACvB,YAAM,UAAU,IAAIT,IAAY,SAAS,EAAE;AAC3C,sBAAAA,MAAA,SAAQ,wBAAAiB,gBAAR,KAAAjB;AACA,aAAO;AAAA,IACnB;AAAA,IA2CQ,IAAI,mBAAmB;AACnB,aAAO,mBAAKoB,oBAAkB,OAAQ;AAAA,IAClD;AAAA,IACQ,IAAI,SAAS;AACT,aAAO,mBAAK,aAAY;AAAA,IACpC;AAAA,IACQ,IAAI,WAAW;AACX,aAAO,KAAK;AAAA,IACxB;AAAA,IACQ,IAAI,KAAK;AACL,aAAO,mBAAKX;AAAA,IACxB;AAAA;AAAA,IAEQ,QAAQ,QAAQ;AACZ,yBAAK,SAAU;AACf,WAAK,aAAa,EAAG;AAAA,IACjC;AAAA,IACQ,MAAM,sBAAsB,MAAM,UAAU,IAAI;;AAC5C,YAAM,EAAE,QAAQ,EAAE,SAAS,UAAS,MAAQ,MAAM,mBAAK,qCAAS,KAAK,0BAA0B;AAAA,QAC3F;AAAA,QACA,GAAG;AAAA,QACH,mBAAkBT,MAAA,QAAQ,qBAAR,gBAAAA,IAA0B;AAAA,QAC5C,aAAa,mBAAKS;AAAA,MAClC,CAAa;AACD,YAAM,kBAAkB,mBAAKW,oBAAkB,IAAI,SAAS;AAC5D,aAAO,iBAAiB,sFAAsF;AAE9G,aAAO;AAAA,IACnB;AAAA,IACQ,MAAM,SAAS;AACX,UAAI;AACA,cAAM,mBAAK,qCAAS,KAAK,6BAA6B;AAAA,UAClD,aAAa,mBAAKX;AAAA,QACtC,CAAiB;AAAA,MACjB,UACoB;AACJ,aAAK,QAAQ,8BAA8B;AAAA,MAC3D;AAAA,IACA;AAAA,IACQ,EAAE,sBAAsB,CAAC,eAAe,GAAG,oCAAoC,CAAC,gBAAgB,aAAW;AAEnG,aAAO,sBAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,qBAAqB,CAAC,gBAAgB,aAAW;AAElD,aAAO,sBAAQ;AAAA,IAC/B,CAAa,CAAC,GAAG,kBAAkB;AACvB,yBAAK,YAAL,mBAAK,SACD;AACJ,WAAK,KAAK,UAAU,EAAE,QAAQ,mBAAK,UAAS;AAC5C,yBAAK,cAAa,QAAS;AAC3B,YAAM,aAAa,EAAG;AAAA,IAClC;AAAA,EACK,GA7FG,yBAEAW,qBAAA,eACA,8BACAX,OAAA,eAnBG,wCA6BHQ,iBAAW,WAAG;AACV,UAAM,iBAAiB,mBAAK,cAAa,IAAI,IAAI,aAAa,KAAK,OAAO,CAAC;AAC3E,mBAAe,KAAK,UAAU,CAAC,EAAE,OAAM,MAAO;AAC1C,WAAK,QAAQ,gCAAgC,MAAM,EAAE;AAAA,IACrE,CAAa;AACD,UAAM,iBAAiB,mBAAK,cAAa,IAAI,IAAI,aAAa,mBAAK,oCAAQ,CAAC;AAC5E,mBAAe,GAAG,kCAAkC,UAAQ;AACxD,UAAI,KAAK,QAAQ;AACb;AAAA,MACpB;AACgB,UAAI,KAAK,gBAAgB,mBAAKR,OAAK;AAC/B;AAAA,MACpB;AACgB,YAAM,kBAAkB,gBAAgB,KAAK,MAAM,QAAW,KAAK,SAAS,KAAK,GAAG;AACpF,yBAAKW,oBAAkB,IAAI,gBAAgB,IAAI,eAAe;AAC9D,YAAM,yBAAyB,mBAAK,cAAa,IAAI,IAAI,aAAa,eAAe,CAAC;AACtF,6BAAuB,GAAG,UAAU,MAAM;AACtC,+BAAuB,mBAAoB;AAC3C,2BAAKA,oBAAkB,OAAO,gBAAgB,EAAE;AAAA,MACpE,CAAiB;AACD,WAAK,KAAK,mBAAmB,EAAE,gBAAe,CAAE;AAAA,IAChE,CAAa;AAAA,EACb,GAEY,cAAQ,WAAG;AACX,WAAO,KAAK,QAAQ;AAAA,EAChC,IAtDQ,MAAA;AACI,UAAM,YAAY,OAAO,WAAW,cAAc,OAAO,WAAW,OAAO,OAAO,YAAY,OAAO,QAAQ,KAAK,IAAI,IAAI;AAC1HlB,mBAAaF,KAAM,MAAM,qBAAqB,EAAE,MAAM,UAAU,MAAM,WAAW,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,aAAa,KAAK,KAAK,SAAO,IAAI,QAAS,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC1OE,mBAAaF,KAAM,MAAM,mCAAmC,EAAE,MAAM,UAAU,MAAM,yBAAyB,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,2BAA2B,KAAK,KAAK,SAAO,IAAI,sBAAuB,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAClSE,mBAAaF,KAAM,MAAM,oBAAoB,EAAE,MAAM,UAAU,MAAM,UAAU,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,YAAY,KAAK,KAAK,SAAO,IAAI,OAAQ,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AACtO,QAAI,UAAW,QAAO,eAAeA,KAAM,OAAO,UAAU,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,OAAO,WAAW;AAAA,EAClJ,MACQ,cARGA,KAQI,WAAU,YARdA;AA6GX,GAAI;AClKJ;AAAA;AAAA;AAAA;AAAA;AAYO,MAAM,2BAA2B,eAAe;AAAA,EAKnD,YAAY,SAAS,aAAa,SAAS;AACvC,UAAO;AALX;AACA;AACA;AACA;AAGI,uBAAK,UAAW;AAChB,uBAAK,cAAe;AACpB,uBAAK,aAAc,mBAAK,UAAS;AACjC,uBAAK,kBAAmB,QAAQ;AAAA,EACxC;AAAA,EACI,UAAU;AACN,WAAO,mBAAK,UAAS,QAAO,EAAG,OAAO,YAAU;AAC5C,aAAO,OAAO,eAAc,MAAO;AAAA,IAC/C,CAAS;AAAA,EACT;AAAA,EACI,cAAc,WAAW,UAAU,IAAI;AACnC,WAAO,mBAAK,UAAS,cAAc,YAAU;AACzC,aAAO,OAAO,eAAc,MAAO,QAAQ,UAAU,MAAM;AAAA,IAC9D,GAAE,OAAO;AAAA,EAClB;AAAA,EACI,IAAI,aAAa;AACb,WAAO,mBAAK;AAAA,EACpB;AAAA,EACI,MAAM,UAAU;AACZ,UAAM,EAAE,OAAM,IAAK,MAAM,mBAAK,aAAY,KAAK,0BAA0B;AAAA,MACrE,MAAM;AAAA,MACN,aAAa,mBAAK,cAAa;AAAA,IAC3C,CAAS;AACD,UAAM,SAAS,mBAAK,UAAS,eAAe,OAAO,OAAO;AAM1D,WAAO,mBAAmB,IAAI;AAC9B,UAAM,OAAO,MAAM,OAAO,KAAM;AAChC,QAAI,CAAC,MAAM;AACP,YAAM,IAAI,MAAM,mBAAmB;AAAA,IAC/C;AACQ,QAAI,mBAAK,mBAAkB;AACvB,UAAI;AACA,cAAM,KAAK,YAAY,mBAAK,iBAAgB;AAAA,MAC5D,QACkB;AAAA,MAElB;AAAA,IACA;AACQ,WAAO;AAAA,EACf;AAAA,EACI,MAAM,QAAQ;AACV,QAAI,CAAC,KAAK,eAAe;AACrB,YAAM,IAAI,MAAM,mCAAmC;AAAA,IAC/D;AACQ,QAAI;AACA,YAAM,mBAAK,cAAa,OAAQ;AAAA,IAC5C,SACe,OAAO;AACV,iBAAW,KAAK;AAAA,IAC5B;AAAA,EACA;AAAA,EACI,UAAU;AACN,WAAO,mBAAK;AAAA,EACpB;AAAA,EACI,MAAM,QAAQ;AACV,UAAM,UAAU,MAAM,QAAQ,IAAI,CAAC,GAAG,KAAK,QAAS,CAAA,EAAE,IAAI,OAAK;AAC3D,aAAO,EAAE,KAAM;AAAA,IAC3B,CAAS,CAAC;AACF,WAAO,QAAQ,OAAO,CAAC,MAAM;AACzB,aAAO,MAAM;AAAA,IACzB,CAAS;AAAA,EACT;AAAA,EACI,cAAc;AACV,WAAO,mBAAK,cAAa,OAAO,YAAY;AAAA,EACpD;AAAA,EACI,sBAAsB;AAClB,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,2BAA2B;AACvB,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,IAAI,KAAK;AACL,QAAI,mBAAK,cAAa,OAAO,WAAW;AACpC,aAAO;AAAA,IACnB;AACQ,WAAO,mBAAK,cAAa;AAAA,EACjC;AACA;AAvFI;AACA;AACA;AACA;AChBJ;AAAA;AAAA;AAAA;AAAA;AAKA,IAAIC,sBAAwD,SAAU,SAAS,cAAc,OAAO;AAChG,MAAI,WAAW,UAAU,SAAS;AAClC,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC1C,YAAQ,WAAW,aAAa,CAAC,EAAE,KAAK,SAAS,KAAK,IAAI,aAAa,CAAC,EAAE,KAAK,OAAO;AAAA,EAC9F;AACI,SAAO,WAAW,QAAQ;AAC9B;AACA,IAAIC,iBAA8C,SAAU,MAAM,cAAc,YAAY,WAAW,cAAc,mBAAmB;AACpI,WAAS,OAAO,GAAG;AAAE,QAAI,MAAM,UAAU,OAAO,MAAM,WAAY,OAAM,IAAI,UAAU,mBAAmB;AAAG,WAAO;AAAA,EAAE;AACrH,MAAI,OAAO,UAAU,MAAM,MAAM,SAAS,WAAW,QAAQ,SAAS,WAAW,QAAQ;AACzF,MAAI,SAAS,CAAC,gBAAgB,OAAO,UAAU,QAAQ,IAAI,OAAO,KAAK,YAAY;AACnF,MAAI,aAAa,iBAAiB,SAAS,OAAO,yBAAyB,QAAQ,UAAU,IAAI,IAAI;AACrG,MAAI,GAAG,OAAO;AACd,WAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,QAAI,UAAU,CAAE;AAChB,aAAS,KAAK,UAAW,SAAQ,CAAC,IAAI,MAAM,WAAW,CAAA,IAAK,UAAU,CAAC;AACvE,aAAS,KAAK,UAAU,OAAQ,SAAQ,OAAO,CAAC,IAAI,UAAU,OAAO,CAAC;AACtE,YAAQ,iBAAiB,SAAU,GAAG;AAAE,UAAI,KAAM,OAAM,IAAI,UAAU,wDAAwD;AAAG,wBAAkB,KAAK,OAAO,KAAK,IAAI,CAAC;AAAA,IAAI;AAC7K,QAAI,UAAS,GAAI,WAAW,CAAC,GAAG,SAAS,aAAa,EAAE,KAAK,WAAW,KAAK,KAAK,WAAW,IAAK,IAAG,WAAW,GAAG,GAAG,OAAO;AAC7H,QAAI,SAAS,YAAY;AACrB,UAAI,WAAW,OAAQ;AACvB,UAAI,WAAW,QAAQ,OAAO,WAAW,SAAU,OAAM,IAAI,UAAU,iBAAiB;AACxF,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,IAAI,EAAG,cAAa,QAAQ,CAAC;AAAA,IAC/D,WACiB,IAAI,OAAO,MAAM,GAAG;AACzB,UAAI,SAAS,QAAS,cAAa,QAAQ,CAAC;AAAA,UACvC,YAAW,GAAG,IAAI;AAAA,IACnC;AAAA,EACA;AACI,MAAI,OAAQ,QAAO,eAAe,QAAQ,UAAU,MAAM,UAAU;AACpE,SAAO;AACX;AACA,IAAIC,4BAAoE,SAAU,KAAK,OAAO,OAAO;AACjG,MAAI,UAAU,QAAQ,UAAU,QAAQ;AACpC,QAAI,OAAO,UAAU,YAAY,OAAO,UAAU,WAAY,OAAM,IAAI,UAAU,kBAAkB;AACpG,QAAI;AACJ,QAAI,OAAO;AACP,UAAI,CAAC,OAAO,aAAc,OAAM,IAAI,UAAU,qCAAqC;AACnF,gBAAU,MAAM,OAAO,YAAY;AAAA,IAC/C;AACQ,QAAI,YAAY,QAAQ;AACpB,UAAI,CAAC,OAAO,QAAS,OAAM,IAAI,UAAU,gCAAgC;AACzE,gBAAU,MAAM,OAAO,OAAO;AAAA,IAC1C;AACQ,QAAI,OAAO,YAAY,WAAY,OAAM,IAAI,UAAU,wBAAwB;AAC/E,QAAI,MAAM,KAAK,EAAE,OAAc,SAAkB,OAAc;AAAA,EACvE,WACa,OAAO;AACZ,QAAI,MAAM,KAAK,EAAE,OAAO,KAAI,CAAE;AAAA,EACtC;AACI,SAAO;AACX;AACA,IAAIC,uBAA2D,yBAAUC,kBAAiB;AACtF,SAAO,SAAU,KAAK;AAClB,aAAS,KAAK,GAAG;AACb,UAAI,QAAQ,IAAI,WAAW,IAAIA,iBAAgB,GAAG,IAAI,OAAO,0CAA0C,IAAI;AAC3G,UAAI,WAAW;AAAA,IAC3B;AACQ,aAAS,OAAO;AACZ,aAAO,IAAI,MAAM,QAAQ;AACrB,YAAI,MAAM,IAAI,MAAM,IAAK;AACzB,YAAI;AACA,cAAI,SAAS,IAAI,WAAW,IAAI,QAAQ,KAAK,IAAI,KAAK;AACtD,cAAI,IAAI,MAAO,QAAO,QAAQ,QAAQ,MAAM,EAAE,KAAK,MAAM,SAAS,GAAG;AAAE,iBAAK,CAAC;AAAG,mBAAO,KAAM;AAAA,WAAG;AAAA,QACpH,SACuB,GAAG;AACN,eAAK,CAAC;AAAA,QAC1B;AAAA,MACA;AACY,UAAI,IAAI,SAAU,OAAM,IAAI;AAAA,IACxC;AACQ,WAAO,KAAM;AAAA,EAChB;AACL,EAAG,OAAO,oBAAoB,aAAa,kBAAkB,SAAU,OAAO,YAAY,SAAS;AAC/F,MAAI,IAAI,IAAI,MAAM,OAAO;AACzB,SAAO,EAAE,OAAO,mBAAmB,EAAE,QAAQ,OAAO,EAAE,aAAa,YAAY;AACnF,CAAC;AAQD,IAAI,WAAW,MAAM;;AACjB,MAAI,cAAc;AAClB,MAAI,6BAA6B,CAAE;AACnC,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,SAAOL,MAAA,cAAsB,YAAY;AAAA;AAAA,IAsBrC,YAAY,SAAS;AACjB,YAAO;AAvBR;AAgBH;AAAA,yBAAAc,WAAWb,oBAAkB,MAAM,0BAA0B,GAAG;AAChE;AACA,uCAAe,IAAI,gBAAiB;AACpC,wCAAgB,oBAAI,IAAK;AACzB;AAKI,WAAK,UAAU;AAEf,yBAAK,eAAc,IAAI,YAAY,SAAS,YAAY,OAAO,MAAM,YAAY,OAAO,CAAC;AAAA,IACrG;AAAA,IAlBQ,aAAa,KAAK,SAAS;;AACvB,YAAM,UAAU,IAAID,IAAQ,OAAO;AACnC,YAAM,gBAAAA,MAAA,SAAQ,oBAAAiB,gBAAR,KAAAjB;AACN,aAAO;AAAA,IACnB;AAAA;AAAA,IA0EQ,IAAI,SAAS;AACT,aAAO,mBAAKc;AAAA,IACxB;AAAA,IACQ,IAAI,qBAAqB;AAErB,aAAO,mBAAK,eAAc,IAAI,YAAY,OAAO;AAAA,IAC7D;AAAA,IACQ,IAAI,eAAe;AACf,aAAO,mBAAK,aAAY;AAAA,IACpC;AAAA,IACQ,IAAI,WAAW;AACX,aAAO,KAAK;AAAA,IACxB;AAAA,IACQ,IAAI,eAAe;AACf,aAAO,mBAAK,eAAc,OAAQ;AAAA,IAC9C;AAAA;AAAA,IAEQ,QAAQ,QAAQ,SAAS,OAAO;AAC5B,yBAAKA,UAAU;AACf,yBAAK,SAAU;AACf,WAAK,aAAa,EAAG;AAAA,IACjC;AAAA,IACQ,MAAM,QAAQ;AACV,UAAI;AACA,cAAM,KAAK,QAAQ,KAAK,iBAAiB,CAAA,CAAE;AAAA,MAC3D,UACoB;AACJ,aAAK,QAAQ,2BAA2B,IAAI;AAAA,MAC5D;AAAA,IACA;AAAA,IACQ,MAAM,iBAAiB,qBAAqB,UAAU,IAAI;;AACtD,YAAM,EAAE,QAAQ,EAAE,OAAQ,EAAA,IAAM,MAAM,KAAK,QAAQ,KAAK,2BAA2B;AAAA,QAC/E;AAAA,QACA,GAAG;AAAA,QACH,WAAUd,MAAA,QAAQ,aAAR,gBAAAA,IAAkB,IAAI,aAAW;AACvC,iBAAO,QAAQ;AAAA,QACnC;AAAA,MACA,CAAa;AACD,aAAO;AAAA,IACnB;AAAA,IACQ,MAAM,oBAAoB,QAAQ;AAC9B,YAAM,KAAK,QAAQ,KAAK,8BAA8B;AAAA,QAClD;AAAA,MAChB,CAAa;AAAA,IACb;AAAA,IACQ,MAAM,oBAAoB;AACtB,YAAM,EAAE,QAAQ,EAAE,aAAa,QAAS,EAAA,IAAM,MAAM,KAAK,QAAQ,KAAK,6BAA6B,CAAA,CAAE;AACrG,YAAM,cAAc,YAAY,OAAO,MAAM,OAAO;AACpD,yBAAK,eAAc,IAAI,YAAY,IAAI,WAAW;AAClD,YAAM,qBAAqB,mBAAK,cAAa,IAAI,IAAI,aAAa,WAAW,CAAC;AAC9E,yBAAmB,KAAK,UAAU,MAAM;AACpC,2BAAmB,mBAAoB;AACvC,2BAAK,eAAc,OAAO,OAAO;AAAA,MACjD,CAAa;AACD,aAAO;AAAA,IACnB;AAAA,IACQ,EAAE,sBAAsB,CAAC,eAAe,GAAG,oBAAoB,CAAC,gBAAgB,aAAW;AAEnF,aAAO,sBAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,+BAA+B,CAAC,gBAAgB,aAAW;AAE5D,aAAO,sBAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,kCAAkC,CAAC,gBAAgB,aAAW;AAE/D,aAAO,sBAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,gCAAgC,CAAC,gBAAgB,aAAW;AAE7D,aAAO,sBAAQ;AAAA,IAC/B,CAAa,CAAC,GAAG,kBAAkB;AACvB,yBAAK,YAAL,mBAAK,SACD;AACJ,UAAI,KAAK,QAAQ;AACb,aAAK,KAAK,UAAU,EAAE,QAAQ,mBAAK,UAAS;AAAA,MAC5D;AACY,WAAK,KAAK,gBAAgB,EAAE,QAAQ,mBAAK,UAAS;AAClD,yBAAK,cAAa,QAAS;AAC3B,YAAM,aAAa,EAAG;AAAA,IAClC;AAAA,EACK,GAtJGc,WAAA,eACA,yBACA,8BACA,+BAnBG,oCA6BGG,iBAAW,iBAAG;AAChB,UAAM,iBAAiB,mBAAK,cAAa,IAAI,IAAI,aAAa,KAAK,OAAO,CAAC;AAC3E,mBAAe,KAAK,SAAS,CAAC,EAAE,OAAM,MAAO;AACzC,WAAK,QAAQ,MAAM;AAAA,IACnC,CAAa;AACD,mBAAe,GAAG,uBAAuB,UAAQ;AAC7C,UAAI,KAAK,SAAS,gBAAiB;AAAA,IAGnD,CAAa;AACD,UAAM,sBAAK,yCAAL;AACN,UAAM,sBAAK,6CAAL;AAAA,EAClB,GACc,sBAAiB,iBAAG;AACtB,UAAM,EAAE,QAAQ,EAAE,eAAiB,IAAG,MAAM,KAAK,QAAQ,KAAK,2BAA2B,EAAE;AAC3F,eAAW,WAAW,cAAc;AAChC,UAAI,QAAQ,gBAAgB,YAAY,SAAS;AAC7C;AAAA,MACpB;AACgB,yBAAK,eAAc,IAAI,QAAQ,aAAa,YAAY,OAAO,MAAM,QAAQ,WAAW,CAAC;AAAA,IACzG;AAAA,EACA,GACc,0BAAqB,iBAAG;AAG1B,UAAM,aAAa,oBAAI,IAAK;AAC5B,QAAI;AACJ;AACI,YAAM,QAAQ,EAAE,OAAO,CAAA,GAAI,OAAO,QAAQ,UAAU,MAAO;AAC3D,UAAI;AACA,cAAM,iBAAiBd,0BAAwB,OAAO,IAAI,aAAa,KAAK,OAAO,GAAG,KAAK;AAC3F,uBAAe,GAAG,kCAAkC,UAAQ;AACxD,qBAAW,IAAI,KAAK,OAAO;AAAA,QACnD,CAAqB;AACD,uBAAe,GAAG,oCAAoC,UAAQ;AAC1D,qBAAW,OAAO,KAAK,OAAO;AAAA,QACtD,CAAqB;AACD,cAAM,EAAE,OAAM,IAAK,MAAM,KAAK,QAAQ,KAAK,2BAA2B,EAAE;AACxE,mBAAW,OAAO;AAAA,MACtC,SACuB,KAAK;AACR,cAAM,QAAQ;AACd,cAAM,WAAW;AAAA,MACrC,UACwB;AACJC,6BAAmB,KAAK;AAAA,MAC5C;AAAA,IACA;AAEY,eAAW,QAAQ,UAAU;AACzB,UAAI,WAAW,IAAI,KAAK,OAAO,GAAG;AAC9B,aAAK,QAAQ,KAAK,kCAAkC,IAAI;AAAA,MAC5E;AACgB,UAAI,KAAK,UAAU;AACf,iBAAS,KAAK,GAAG,KAAK,QAAQ;AAAA,MAClD;AAAA,IACA;AAAA,EACA,IArFQ,MAAA;AACI,UAAM,YAAY,OAAO,WAAW,cAAc,OAAO,WAAW,OAAO,OAAO,YAAY,OAAO,QAAQ,KAAK,IAAI,IAAI;AAC1HF,mBAAaF,KAAM,MAAM,qBAAqB,EAAE,MAAM,UAAU,MAAM,WAAW,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,aAAa,KAAK,KAAK,SAAO,IAAI,QAAS,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC1OE,mBAAaF,KAAM,MAAM,mBAAmB,EAAE,MAAM,UAAU,MAAM,SAAS,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,WAAW,KAAK,KAAK,SAAO,IAAI,MAAO,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAClOE,mBAAaF,KAAM,MAAM,8BAA8B,EAAE,MAAM,UAAU,MAAM,oBAAoB,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,sBAAsB,KAAK,KAAK,SAAO,IAAI,iBAAkB,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC9QE,mBAAaF,KAAM,MAAM,iCAAiC,EAAE,MAAM,UAAU,MAAM,uBAAuB,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,yBAAyB,KAAK,KAAK,SAAO,IAAI,oBAAqB,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC1RE,mBAAaF,KAAM,MAAM,+BAA+B,EAAE,MAAM,UAAU,MAAM,qBAAqB,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,uBAAuB,KAAK,KAAK,SAAO,IAAI,kBAAmB,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAClR,QAAI,UAAW,QAAO,eAAeA,KAAM,OAAO,UAAU,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,OAAO,WAAW;AAAA,EAClJ,MATWA;AAuKX,GAAI;AC1QJ;AAAA;AAAA;AAAA;AAAA;AAKA,IAAIC,sBAAwD,SAAU,SAAS,cAAc,OAAO;AAChG,MAAI,WAAW,UAAU,SAAS;AAClC,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC1C,YAAQ,WAAW,aAAa,CAAC,EAAE,KAAK,SAAS,KAAK,IAAI,aAAa,CAAC,EAAE,KAAK,OAAO;AAAA,EAC9F;AACI,SAAO,WAAW,QAAQ;AAC9B;AACA,IAAIC,iBAA8C,SAAU,MAAM,cAAc,YAAY,WAAW,cAAc,mBAAmB;AACpI,WAAS,OAAO,GAAG;AAAE,QAAI,MAAM,UAAU,OAAO,MAAM,WAAY,OAAM,IAAI,UAAU,mBAAmB;AAAG,WAAO;AAAA,EAAE;AACrH,MAAI,OAAO,UAAU,MAAM,MAAM,SAAS,WAAW,QAAQ,SAAS,WAAW,QAAQ;AACzF,MAAI,SAAS,CAAC,gBAAgB,OAAO,UAAU,QAAQ,IAAI,OAAO,KAAK,YAAY;AACnF,MAAI,aAAa,iBAAiB,SAAS,OAAO,yBAAyB,QAAQ,UAAU,IAAI,IAAI;AACrG,MAAI,GAAG,OAAO;AACd,WAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,QAAI,UAAU,CAAE;AAChB,aAAS,KAAK,UAAW,SAAQ,CAAC,IAAI,MAAM,WAAW,CAAA,IAAK,UAAU,CAAC;AACvE,aAAS,KAAK,UAAU,OAAQ,SAAQ,OAAO,CAAC,IAAI,UAAU,OAAO,CAAC;AACtE,YAAQ,iBAAiB,SAAU,GAAG;AAAE,UAAI,KAAM,OAAM,IAAI,UAAU,wDAAwD;AAAG,wBAAkB,KAAK,OAAO,KAAK,IAAI,CAAC;AAAA,IAAI;AAC7K,QAAI,UAAS,GAAI,WAAW,CAAC,GAAG,SAAS,aAAa,EAAE,KAAK,WAAW,KAAK,KAAK,WAAW,IAAK,IAAG,WAAW,GAAG,GAAG,OAAO;AAC7H,QAAI,SAAS,YAAY;AACrB,UAAI,WAAW,OAAQ;AACvB,UAAI,WAAW,QAAQ,OAAO,WAAW,SAAU,OAAM,IAAI,UAAU,iBAAiB;AACxF,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,IAAI,EAAG,cAAa,QAAQ,CAAC;AAAA,IAC/D,WACiB,IAAI,OAAO,MAAM,GAAG;AACzB,UAAI,SAAS,QAAS,cAAa,QAAQ,CAAC;AAAA,UACvC,YAAW,GAAG,IAAI;AAAA,IACnC;AAAA,EACA;AACI,MAAI,OAAQ,QAAO,eAAe,QAAQ,UAAU,MAAM,UAAU;AACpE,SAAO;AACX;AAWA,IAAI,WAAW,MAAM;;AACjB,MAAI,cAAc;AAClB,MAAI,6BAA6B,CAAE;AACnC,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,SAAOF,MAAA,cAAsB,YAAY;AAAA;AAAA,IA4DrC,YAAY,YAAY,MAAM;AAC1B,YAAO;AA7DR;AAsDH;AAAA,mCAAWC,oBAAkB,MAAM,0BAA0B,GAAG;AAChE,uCAAe,IAAI,gBAAiB;AACpC;AACA;AACA;AAKI,yBAAK,OAAQ;AACb,WAAK,aAAa;AAAA,IAE9B;AAAA,IAzDQ,aAAa,KAAK,YAAY,cAAc;;AAmBxC,UAAI;AACJ,UAAI;AACA,kBAAU,MAAM,WAAW,KAAK,eAAe;AAAA,UAC3C;AAAA,QACH,CAAA,GAAG;AAAA,MACpB,SACmB,KAAK;AAER,mBAAW,GAAG;AACd,iBAAS;AAAA,UACL,WAAW;AAAA,UACX,cAAc;AAAA,YACV,qBAAqB;AAAA,YACrB,aAAa;AAAA,YACb,gBAAgB;AAAA,YAChB,cAAc;AAAA,YACd,eAAe;AAAA,YACf,cAAc;AAAA,UACjB;AAAA,QACJ;AAAA,MACjB;AACY,YAAM,UAAU,IAAID,IAAQ,YAAY,MAAM;AAC9C,YAAM,gBAAAA,MAAA,SAAQ,oBAAAiB,gBAAR,KAAAjB;AACN,aAAO;AAAA,IACnB;AAAA;AAAA,IAyBQ,IAAI,eAAe;AACf,aAAO,mBAAK,OAAM;AAAA,IAC9B;AAAA,IACQ,IAAI,WAAW;AACX,aAAO,KAAK;AAAA,IACxB;AAAA,IACQ,IAAI,QAAQ;AACR,aAAO,mBAAK,aAAY;AAAA,IACpC;AAAA,IACQ,IAAI,KAAK;AACL,aAAO,mBAAK,OAAM;AAAA,IAC9B;AAAA;AAAA,IAEQ,QAAQ,QAAQ;AACZ,yBAAK,SAAU;AACf,WAAK,aAAa,EAAG;AAAA,IACjC;AAAA,IACQ,OAAO,SAAS;AACZ,WAAK,WAAW,OAAO,OAAO;AAAA,IAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAQQ,MAAM,KAAK,QAAQ,QAAQ;AACvB,aAAO,MAAM,KAAK,WAAW,KAAK,QAAQ,MAAM;AAAA,IAC5D;AAAA,IACQ,MAAM,UAAU,QAAQ;AACpB,YAAM,KAAK,KAAK,qBAAqB;AAAA,QACjC;AAAA,MAChB,CAAa;AAAA,IACb;AAAA,IACQ,MAAM,MAAM;AACR,UAAI;AACA,cAAM,KAAK,KAAK,eAAe,EAAE;AAAA,MACjD,UACoB;AACJ,aAAK,QAAQ,wBAAwB;AAAA,MACrD;AAAA,IACA;AAAA,IACQ,EAAE,sBAAsB,CAAC,eAAe,GAAG,mBAAmB,CAAC,gBAAgB,aAAW;AAElF,aAAO,sBAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,wBAAwB,CAAC,gBAAgB,aAAW;AAErD,aAAO,sBAAQ;AAAA,IAClB,CAAA,CAAC,GAAG,kBAAkB,CAAC,gBAAgB,aAAW;AAE/C,aAAO,sBAAQ;AAAA,IAC/B,CAAa,CAAC,GAAG,kBAAkB;AACvB,yBAAK,YAAL,mBAAK,SACD;AACJ,WAAK,KAAK,SAAS,EAAE,QAAQ,mBAAK,UAAS;AAC3C,yBAAK,cAAa,QAAS;AAC3B,YAAM,aAAa,EAAG;AAAA,IAClC;AAAA,EACK,GAlFG,yBACA,8BACA,uBAxDG,oCAmEGiB,iBAAW,iBAAG;AAChB,SAAK,WAAW,OAAO,IAAI;AAE3B,SAAK,UAAU,MAAM,QAAQ,KAAK,IAAI;AACtC,UAAM,iBAAiB,mBAAK,cAAa,IAAI,KAAK,OAAO;AACzD,mBAAe,KAAK,UAAU,CAAC,EAAE,OAAM,MAAO;AAC1C,WAAK,QAAQ,MAAM;AAAA,IACnC,CAAa;AAAA,EACb,IA1EQ,MAAA;AACI,UAAM,YAAY,OAAO,WAAW,cAAc,OAAO,WAAW,OAAO,OAAO,YAAY,OAAO,QAAQ,KAAK,IAAI,IAAI;AAC1Hf,mBAAaF,KAAM,MAAM,qBAAqB,EAAE,MAAM,UAAU,MAAM,WAAW,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,aAAa,KAAK,KAAK,SAAO,IAAI,QAAS,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC1OE,mBAAaF,KAAM,MAAM,kBAAkB,EAAE,MAAM,UAAU,MAAM,QAAQ,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,UAAU,KAAK,KAAK,SAAO,IAAI,KAAM,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC9NE,mBAAaF,KAAM,MAAM,uBAAuB,EAAE,MAAM,UAAU,MAAM,aAAa,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,eAAe,KAAK,KAAK,SAAO,IAAI,UAAW,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAClPE,mBAAaF,KAAM,MAAM,iBAAiB,EAAE,MAAM,UAAU,MAAM,OAAO,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,SAAS,KAAK,KAAK,SAAO,IAAI,IAAK,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC1N,QAAI,UAAW,QAAO,eAAeA,KAAM,OAAO,UAAU,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,OAAO,WAAW;AAAA,EAClJ,MARWA;AAyIX,GAAI;ACjMJ;AAAA;AAAA;AAAA;AAAA;AASO,MAAM,mBAAmB,OAAO;AAAA;AAAA;AAAA;AAAA,EAKnC,YAAY,SAAS,MAAM,SAAS,cAAc;AAC9C,UAAM,MAAM,SAAS,YAAY;AALrC,uBAAAqB;AAMI,uBAAKA,WAAW;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAII,MAAM,OAAO,SAAS;AAClB,UAAM,mBAAKA,WAAS,WAAW,KAAK,oCAAoC;AAAA,MACpE,SAAS,mBAAKA,WAAS;AAAA,MACvB,QAAQ,QAAQ;AAAA,MAChB,UAAU,QAAQ;AAAA,IAC9B,CAAS;AAAA,EACT;AACA;AAlBIA,YAAA;ACPG,MAAM,iBAAiB;AAAA,EAE1B,YAAY,iBAAiB;AAD7B;AAEI,uBAAK,kBAAmB;AAAA,EAChC;AAAA,EACI,MAAM,gBAAgB,UAAU;AAC5B,UAAM,mBAAK,kBAAiB,WAAW,KAAK,+BAA+B;AAAA,MACvE,SAAS,mBAAK,kBAAiB;AAAA,MAC/B,UAAU,SAAS,SAAS,SAAS,SAC/B;AAAA,QACE,OAAO,SAAS;AAAA,QAChB,QAAQ,SAAS;AAAA,MACrC,IACkB;AAAA,MACN,kBAAkB,SAAS,oBACrB,SAAS,oBACT;AAAA,IAClB,CAAS;AAAA,EACT;AACA;AAlBI;ACJJ;AAAA;AAAA;AAAA;AAAA;AAeO,MAAM,mBAAmB;AAAA,EAO5B,YAAY,OAAO,MAAM,OAAO;AAP7B;AACH;AACA;AACA;AACA;AACA,qCAAe,oBAAI,IAAK;AACxB;AAiDA,gDAA0B,OAAO,WAAW;;AACxC,UAAI,OAAO,YAAY,mBAAK,WAAU,MAAM;AACxC;AAAA,MACZ;AACQ,YAAM,aAAa,mBAAK;AACxB,YAAM,EAAE,WAAW,YAAW,IAAK,sBAAK,8DAAL,WAAiC;AACpE,YAAM,QAAOrB,MAAA,YAAY,UAAZ,gBAAAA,IAAoB;AACjC,aAAO,IAAI;AACX,UAAI;AACA,cAAM,SAAS,MAAM,mBAAK,QAAL,WAAY,GAAG,iBAAiB,YAAY,IAAI;AACrE,cAAM,WAAW,KAAK,uBAAuB;AAAA,UACzC,qBAAqB,kBAAkB,CAAC,CAAC,GAAG,OAAO,GAAGsB,YAAW;AAC7D,oBAAQA,OAAM;AAAA,UAClC,CAAiB;AAAA,UACD,WAAW;AAAA,YACN,MAAM,UAAU,QAAQ,aAAc;AAAA,YACvC,eAAe,qBAAqB,MAAM;AAAA,UAC7C;AAAA,UACD,cAAc;AAAA,UACd,QAAQ;AAAA,YACJ,OAAO,OAAO,OAAO;AAAA,UACxB;AAAA,QACjB,CAAa;AAAA,MACb,SACe,OAAO;AACV,YAAI;AACA,cAAI,iBAAiB,OAAO;AACxB,kBAAM,WAAW,KAAK,uBAAuB;AAAA,cACzC,qBAAqB,kBAAkB,CAAC,CAAC,GAAG,MAAM,GAAG,MAAM,SAAS,UAAU;AAC1E,sBAAMC,SAAQ,IAAI,MAAM,OAAO;AAC/B,gBAAAA,OAAM,OAAO;AACb,oBAAI,OAAO;AACP,kBAAAA,OAAM,QAAQ;AAAA,gBAC9C;AAC4B,uBAAOA,MAAK;AAAA,cACxC,CAAyB;AAAA,cACD,WAAW;AAAA,gBACN,MAAM,UAAU,OAAO,aAAc;AAAA,gBACtC,eAAe,qBAAqB,MAAM,IAAI;AAAA,gBAC9C,eAAe,qBAAqB,MAAM,OAAO;AAAA,gBACjD,eAAe,qBAAqB,MAAM,KAAK;AAAA,cAClD;AAAA,cACD,cAAc;AAAA,cACd,QAAQ;AAAA,gBACJ,OAAO,OAAO,OAAO;AAAA,cACxB;AAAA,YACzB,CAAqB;AAAA,UACrB,OACqB;AACD,kBAAM,WAAW,KAAK,uBAAuB;AAAA,cACzC,qBAAqB,kBAAkB,CAAC,CAAC,GAAG,MAAM,GAAGA,WAAU;AAC3D,uBAAOA,MAAK;AAAA,cACxC,CAAyB;AAAA,cACD,WAAW;AAAA,gBACN,MAAM,UAAU,OAAO,aAAc;AAAA,gBACtC,eAAe,qBAAqB,KAAK;AAAA,cAC5C;AAAA,cACD,cAAc;AAAA,cACd,QAAQ;AAAA,gBACJ,OAAO,OAAO,OAAO;AAAA,cACxB;AAAA,YACzB,CAAqB;AAAA,UACrB;AAAA,QACA,SACmBA,QAAO;AACV,qBAAWA,MAAK;AAAA,QAChC;AAAA,MACA;AAAA,IACK;AA6BD,8CAAwB,CAAC,WAAW;AAChC,UAAI,OAAO,YAAY,mBAAK,WAAU,SAAS;AAC3C;AAAA,MACZ;AACQ,YAAM,EAAE,WAAW,YAAW,IAAK,sBAAK,8DAAL,WAAiC;AACpE,gBAAU,QAAQ,QAAQ,WAAW;AAAA,IACxC;AACD,6CAAuB,CAAC,WAAW;AAC/B,UAAI,OAAO,YAAY,mBAAK,WAAU,QAAQ;AAC1C;AAAA,MACZ;AACQ,YAAM,EAAE,WAAW,YAAW,IAAK,sBAAK,8DAAL,WAAiC;AACpE,gBAAU,OAAO,QAAQ,WAAW;AAAA,IACvC;AA7JG,uBAAK,QAAS;AACd,SAAK,OAAO;AACZ,uBAAK,QAAS;AACd,uBAAK,WAAY;AAAA,MACb,MAAM,gBAAgB,mBAAK,QAAO,GAAG,wBAAwB,KAAK,IAAI;AAAA,MACtE,SAAS,gBAAgB,mBAAK,QAAO,GAAG,wBAAwB,KAAK,IAAI;AAAA,MACzE,QAAQ,gBAAgB,mBAAK,QAAO,GAAG,wBAAwB,KAAK,IAAI;AAAA,IAC3E;AAAA,EACT;AAAA,EACI,MAAM,SAAS;AACX,UAAM,aAAa,mBAAK;AACxB,UAAM,mBAAmB,mBAAK;AAE9B,eAAW,GAAGhB,sBAAkB,OAAO,WAAW,SAAS,mBAAK,wBAAuB;AACvF,eAAW,GAAGA,sBAAkB,OAAO,WAAW,SAAS,mBAAK,sBAAqB;AACrF,eAAW,GAAGA,sBAAkB,OAAO,WAAW,SAAS,mBAAK,qBAAoB;AACpF,UAAM,sBAAsB,kBAAkB,oBAAoB,CAAC,UAAU,aAAa,eAAe;AACrG,UAAI,KAAK;AACT,aAAO,OAAO,YAAY;AAAA,QACtB,CAAC,YAAY,MAAM,CAAC,GAAG,YAAa,MAAM;AACtC,iBAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,qBAAS,CAAC,IAAI,IAAI,CAAC;AACnB,wBAAY,CAAC,IAAI,OAAO,CAAC;AACzB,uBAAW,CAAC,IAAI,MAAM,CAAC;AACvB,cAAE;AAAA,UAC1B,CAAqB;AAAA,QACJ;AAAA,MACjB,CAAa;AAAA,IACb,GAAW,EAAE,MAAM,KAAK,UAAU,KAAK,IAAI,EAAC,CAAE,CAAC;AACvC,UAAM,EAAE,OAAQ,IAAG,MAAM,WAAW,KAAK,2BAA2B;AAAA,MAChE;AAAA,MACA,WAAW;AAAA,MACX,UAAU,CAAC,mBAAK,QAAO,KAAI,EAAG,UAAW,EAAC,GAAG;AAAA,IACzD,CAAS;AACD,uBAAK,kBAAmB,OAAO;AAC/B,UAAM,QAAQ,IAAI,mBAAK,QAClB,KAAI,EACJ,OAAM,EACN,IAAI,OAAO,UAAU;AACtB,aAAO,MAAM,WAAW,KAAK,uBAAuB;AAAA,QAChD;AAAA,QACA,WAAW;AAAA,QACX,cAAc;AAAA,QACd,QAAQ,MAAM,UAAW,EAAC,MAAM;AAAA,MAChD,CAAa;AAAA,IACb,CAAS,CAAC;AAAA,EACV;AAAA,EAwII,CAAC,OAAO,OAAO,IAAI;AACf,SAAK,KAAK,OAAO,YAAY,EAAC,EAAG,MAAM,UAAU;AAAA,EACzD;AAAA,EACI,OAAO,OAAO,YAAY,IAAI;AAC1B,QAAI,mBAAK,mBAAkB;AACvB,YAAM,mBAAK,+CAAY,KAAK,8BAA8B;AAAA,QACtD,QAAQ,mBAAK;AAAA,MAC7B,CAAa;AAAA,IACb;AAAA,EACA;AACA;AAvMI;AAEA;AACA;AACA;AACA;AAiDA;AAvDG;AA4HC,iBAAW,WAAG;AACd,SAAO,mBAAK,QAAO,QAAO,EAAG;AACrC;AACQ,uBAAiB,WAAG;AACpB,SAAO;AAAA,IACH;AAAA,MACI,MAAM;AAAA,MACN,OAAO;AAAA,QACH,SAAS,mBAAK,WAAU;AAAA,QACxB,WAAW;AAAA,MACd;AAAA,IACJ;AAAA,IACD;AAAA,MACI,MAAM;AAAA,MACN,OAAO;AAAA,QACH,SAAS,mBAAK,WAAU;AAAA,QACxB,WAAW;AAAA,MACd;AAAA,IACJ;AAAA,IACD;AAAA,MACI,MAAM;AAAA,MACN,OAAO;AAAA,QACH,SAAS,mBAAK,WAAU;AAAA,QACxB,WAAW;AAAA,MACd;AAAA,IACJ;AAAA,EACJ;AACT;AACI;AAOA;AAOA,gCAA2B,SAAC,QAAQ;AAChC,QAAM,EAAE,MAAM,OAAM,IAAK;AACzB,SAAO,KAAK,SAAS,OAAO;AAC5B,SAAO,KAAK,KAAK;AACjB,QAAM,iBAAiB,KAAK,MAAM,CAAC;AACnC,SAAO,cAAc;AACrB,SAAO,eAAe,SAAS,QAAQ;AACvC,SAAO,OAAO,eAAe,UAAU,QAAQ;AAC/C,MAAI,aAAa,mBAAK,cAAa,IAAI,OAAO,KAAK;AACnD,MAAI,CAAC,YAAY;AACb,iBAAa,oBAAI,IAAK;AACtB,uBAAK,cAAa,IAAI,OAAO,OAAO,UAAU;AAAA,EAC1D;AACQ,QAAM,WAAW,eAAe;AAChC,MAAI,YAAY,WAAW,IAAI,QAAQ;AACvC,MAAI,CAAC,WAAW;AACZ,gBAAY;AAAA,MACR,SAAS,IAAI,SAAU;AAAA,MACvB,QAAQ,IAAI,SAAU;AAAA,IACzB;AACD,eAAW,IAAI,UAAU,SAAS;AAAA,EAC9C;AACQ,SAAO,EAAE,WAAW,aAAa,KAAM;AAC/C;ACvMO,SAAS,kBAAkB,OAAO;AACrC,MAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,UAAM,gBAAgB,MAAM,KAAK,eAAa;AAC1C,aAAO,cAAc;AAAA,IACxB,CAAA,IACK,SACA;AACN,UAAM,mBAAmB,MAAM,OAAO,CAAC,KAAK,cAAc;AACtD,UAAI,cAAc,gBAAgB;AAC9B,eAAO;AAAA,MACvB,WACqB,QAAQ,kBAAkB,cAAc,gBAAgB;AAC7D,eAAO;AAAA,MACvB;AACY,aAAO;AAAA,IACV,GAAE,IAAI;AACP,WAAO,CAAC,eAAe,gBAAgB;AAAA,EAC/C;AACI,MAAI,UAAU,kBAAkB,UAAU,gBAAgB;AACtD,WAAO,CAAC,QAAQ,KAAK;AAAA,EAC7B;AACI,SAAO,CAAC,OAAO,IAAI;AACvB;AAIO,MAAM,4BAA4B,oBAAI,IAAI;AAAA,EAC7C;AAAA,IAAC;AAAA,IAAQ;AAAA;AAAA,EAA8D;AAAA,EACvE;AAAA,IAAC;AAAA,IAAoB;AAAA;AAAA,EAAoE;AAC7F,CAAC;AACM,SAAS,sBAAsB,OAAO;AACzC,QAAM,aAAa,kBAAkB,KAAK;AAC1C,QAAM,YAAY,0BAA0B,IAAI,WAAW,CAAC,CAAC;AAC7D,SAAO,CAAC,WAAW,WAAW,CAAC,CAAC;AACpC;AAIO,MAAM,6BAA6B,oBAAI,IAAI;AAAA,EAC9C,CAAC,QAAQ,sBAAsB;AAAA,EAC/B,CAAC,oBAAoB,kCAAkC;AAC3D,CAAC;AAIM,SAAS,sBAAsB,OAAO;AACzC,QAAM,aAAa,kBAAkB,KAAK;AAC1C,QAAM,YAAY,2BAA2B,IAAI,WAAW,CAAC,CAAC;AAC9D,SAAO,CAAC,WAAW,WAAW,CAAC,CAAC;AACpC;AAIO,SAAS,uBAAuB,SAAS,IAAI;AAChD,SAAOiB,GAAW,WAAS;AACvB,QAAI,iBAAiB,eAAe;AAChC,YAAM,WAAW,OAAO,OAAO;AAAA,IAC3C,WACiB,iBAAiB,cAAc;AACpC,YAAM,UAAU,yBAAyB,EAAE;AAAA,IACvD;AACQ,UAAM;AAAA,EACd,CAAK;AACL;ACpEA;AAAA;AAAA;AAAA;AAAA;AAcY,MAAC,eAAe,OAAO,aAAa;AAOpC,MAAC,oBAAoB,OAAO,kBAAkB;AAInD,MAAM,gBAAgBC,QAAM;AAAA,EAI/B,YAAY,MAAM,OAElB,OAAO,iBAAiB;AACpB,UAAM,eAAe;AANzB;AACA;AACA,uBAAAC;AAKI,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,uBAAKA,SAAS;AACd,SAAK,MAAM,WAAW,IAAI;AAAA,EAClC;AAAA,EACI,IAAI,cAAc;AACd,WAAO,mBAAKA;AAAA,EACpB;AAAA,EACI,MAAM,eAAe,iBAAiB,MAAM;AACxC,mBAAe,6BAA6B,KAAK,eAAe,MAAM,YAAY;AAClF,WAAO,MAAM,KAAK,MAAM,eAAe,cAAc,GAAG,IAAI;AAAA,EACpE;AAAA,EACI,MAAM,SAAS,iBAAiB,MAAM;AAClC,mBAAe,6BAA6B,KAAK,SAAS,MAAM,YAAY;AAC5E,WAAO,MAAM,KAAK,MAAM,SAAS,cAAc,GAAG,IAAI;AAAA,EAC9D;AAAA,EACI,MAAM,YAAY,QAAQ;AACtB,WAAQ,MAAM,KAAK,eAAe,UAAQ;AACtC,aAAO;AAAA,IACV,GAAE,MAAM;AAAA,EACjB;AAAA,EACI,MAAM,eAAe,QAAQ;AACzB,QAAI,OAAO,UAAU,MAAM;AACvB,aAAO;AAAA,IACnB;AACQ,UAAM,oBAAoB,MAAM,KAAK,eAAe,UAAQ;AACxD,aAAO;AAAA,IACV,GAAE,MAAM;AACT,UAAM,OAAO,QAAS;AACtB,WAAO;AAAA,EACf;AAAA,EACI,MAAM,iBAAiB,eAAe;AAClC,UAAM,EAAE,OAAM,IAAK,MAAM,KAAK,YAAY,OAAO,KAAK,mBAAmB;AAAA,MACrE;AAAA,IACZ,CAAS;AACD,WAAO,IAAI,kBAAkB,MAAM;AAAA,MAC/B,QAAQ,OAAO;AAAA,MACf,MAAM;AAAA,IAClB,CAAS;AAAA,EACT;AACA;AA7CIA,UAAA;AC5BJ;AAAA;AAAA;AAAA;AAAA;AAKA,IAAI,oBAAwD,SAAU,SAAS,cAAc,OAAO;AAChG,MAAI,WAAW,UAAU,SAAS;AAClC,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC1C,YAAQ,WAAW,aAAa,CAAC,EAAE,KAAK,SAAS,KAAK,IAAI,aAAa,CAAC,EAAE,KAAK,OAAO;AAAA,EAC9F;AACI,SAAO,WAAW,QAAQ;AAC9B;AACA,IAAI,eAA8C,SAAU,MAAM,cAAc,YAAY,WAAW,cAAc,mBAAmB;AACpI,WAAS,OAAO,GAAG;AAAE,QAAI,MAAM,UAAU,OAAO,MAAM,WAAY,OAAM,IAAI,UAAU,mBAAmB;AAAG,WAAO;AAAA,EAAE;AACrH,MAAI,OAAO,UAAU,MAAM,MAAM,SAAS,WAAW,QAAQ,SAAS,WAAW,QAAQ;AACzF,MAAI,SAAS,CAAC,gBAAgB,OAAO,UAAU,QAAQ,IAAI,OAAO,KAAK,YAAY;AACnF,MAAI,aAAa,iBAAiB,SAAS,OAAO,yBAAyB,QAAQ,UAAU,IAAI,IAAI;AACrG,MAAI,GAAG,OAAO;AACd,WAAS,IAAI,WAAW,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,QAAI,UAAU,CAAE;AAChB,aAAS,KAAK,UAAW,SAAQ,CAAC,IAAI,MAAM,WAAW,CAAA,IAAK,UAAU,CAAC;AACvE,aAAS,KAAK,UAAU,OAAQ,SAAQ,OAAO,CAAC,IAAI,UAAU,OAAO,CAAC;AACtE,YAAQ,iBAAiB,SAAU,GAAG;AAAE,UAAI,KAAM,OAAM,IAAI,UAAU,wDAAwD;AAAG,wBAAkB,KAAK,OAAO,KAAK,IAAI,CAAC;AAAA,IAAI;AAC7K,QAAI,UAAS,GAAI,WAAW,CAAC,GAAG,SAAS,aAAa,EAAE,KAAK,WAAW,KAAK,KAAK,WAAW,IAAK,IAAG,WAAW,GAAG,GAAG,OAAO;AAC7H,QAAI,SAAS,YAAY;AACrB,UAAI,WAAW,OAAQ;AACvB,UAAI,WAAW,QAAQ,OAAO,WAAW,SAAU,OAAM,IAAI,UAAU,iBAAiB;AACxF,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,GAAG,EAAG,YAAW,MAAM;AAC7C,UAAI,IAAI,OAAO,OAAO,IAAI,EAAG,cAAa,QAAQ,CAAC;AAAA,IAC/D,WACiB,IAAI,OAAO,MAAM,GAAG;AACzB,UAAI,SAAS,QAAS,cAAa,QAAQ,CAAC;AAAA,UACvC,YAAW,GAAG,IAAI;AAAA,IACnC;AAAA,EACA;AACI,MAAI,OAAQ,QAAO,eAAe,QAAQ,UAAU,MAAM,UAAU;AACpE,SAAO;AACX;AAeG,IAAC,aAAa,MAAM;;AACnB,MAAI,cAAc;AAClB,MAAI,6BAA6B,CAAE;AACnC,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,SAAO1B,MAAA,cAAwB,YAAY;AAAA,IAevC,YAAY,MAAM,SAAS,iBAAiB,UAAU;AAClD,YAAO;AARX,yBAAA2B,SAAS,kBAAkB,MAAM,0BAA0B,GAAG;AAC9D,yBAAAN;AACA;AACA,yCAAiB,SAAS,OAAQ;AAClC,yBAAAO,YAAY;AACZ;AACA;AA0HA,4CAAoB,oBAAI,IAAK;AAvHzB,yBAAKD,QAAQ;AACb,yBAAKN,WAAW;AAChB,yBAAK,kBAAmB;AACxB,WAAK,MAAM,mBAAKA,WAAS;AACzB,WAAK,YAAY,YAAY;AAC7B,WAAK,YAAY;AAAA,QACb,CAAC,YAAY,GAAG,IAAI,QAAQ,QAAW,MAAM,SAAS,eAAe;AAAA,QACrE,CAAC,iBAAiB,GAAG,IAAI,QAAQ,oBAAoB,MAAM,QAAQ,sBAAuB,GAAE,eAAe;AAAA,MAC9G;AAAA,IACb;AAAA,IACQ,IAAI,SAAS;AACT,aAAO,KAAK,QAAO,EAAG;AAAA,IAClC;AAAA,IACQ,YAAY;AACR,aAAO,KAAK,UAAU,YAAY;AAAA,IAC9C;AAAA,IACQ,gBAAgB;AACZ,aAAO,KAAK,UAAU,iBAAiB;AAAA,IACnD;AAAA,IACQ,OAAO;AACH,aAAO,mBAAKM;AAAA,IACxB;AAAA,IACQ,aAAa;AACT,YAAM,IAAI,qBAAsB;AAAA,IAC5C;AAAA,IACQ,MAAM;AACF,aAAO,mBAAKN,WAAS;AAAA,IACjC;AAAA,IACQ,cAAc;AACV,aAAO,mBAAKM,QAAM,MAAM,KAAK,aAAa,EAAE;AAAA,IACxD;AAAA,IACQ,cAAc;AACV,aAAO,mBAAKA,QAAM,YAAY,mBAAKN,WAAS,EAAE;AAAA,IAC1D;AAAA,IACQ,MAAM,KAAK,KAAK,UAAU,IAAI;AAC1B,YAAM,EAAE,YAAY,QAAQ,SAAS,KAAK,mBAAK,kBAAiB,kBAAmB,EAAA,IAAM;AACzF,YAAM,CAAC,WAAW,WAAW,IAAI,sBAAsB,SAAS;AAChE,YAAM,UAAUQ,GAAIC,EAAK,mBAAKT,WAAS,WAAW,KAAK,4BAA4B;AAAA,QAC/E,SAAS,mBAAKA,WAAS;AAAA,QACvB;AAAA,QACA,MAAM;AAAA,MACtB,CAAa,CAAC,GAAG,GAAI,gBAAgB,OACnB;AAAA,QACE,mBAAKM,QAAM,oBAAoB;AAAA,UAC3B,SAAS;AAAA,UACT,aAAa,gBAAgB,iBAAiB,IAAI;AAAA,UAClD,UAAU;AAAA,QAClC,CAAqB;AAAA,MACrB,IACkB,CAAE,CAAC,EAAE,KAAKI,EAAI,CAAC,CAAC,EAAE,QAAAT,QAAM,CAAE,MAAM;AAClC,eAAOA;AAAA,MACV,CAAA,GAAGU,GAAS,QAAQ,EAAE,GAAGF,EAAK,mBAAK,gBAAe,aAAc,CAAA,CAAC,GAAG,uBAAuB,KAAK,EAAE,CAAC;AACpG,YAAM,SAAS,MAAMG,GAAe,OAAO;AAC3C,aAAO,mBAAKN,QAAM,sBAAsB,OAAO,UAAU;AAAA,IACrE;AAAA,IACQ,MAAM,WAAW,MAAM,UAAU,IAAI;AACjC,YAAM,EAAE,YAAY,QAAQ,SAAS,KAAK,mBAAK,kBAAiB,kBAAmB,EAAA,IAAM;AACzF,YAAM,CAAC,WAAW,WAAW,IAAI,sBAAsB,SAAS;AAChE,YAAM,UAAUE,GAAIK,GAAS;AAAA,QACzB,iBAAiB,mBAAKb,YAAU,SAAS,EAAE,KAAKc,IAAO;AAAA,QACvDL,EAAK,KAAK,gBAAgB,IAAI,CAAC;AAAA,MAC/C,CAAa,EAAE,KAAKC,EAAI,MAAM;AACd,eAAO;AAAA,MACvB,CAAa,CAAC,GAAG,GAAI,gBAAgB,OACnB;AAAA,QACE,mBAAKJ,QAAM,oBAAoB;AAAA,UAC3B,SAAS;AAAA,UACT,aAAa,gBAAgB,iBAAiB,IAAI;AAAA,UAClD,UAAU;AAAA,QAClC,CAAqB;AAAA,MACrB,IACkB,CAAE,CAAC,EAAE,KAAKK,GAAS,QAAQ,EAAE,GAAGF,EAAK,mBAAK,gBAAe,aAAY,CAAE,CAAC,GAAG,uBAAuB,cAAc,EAAE,CAAC;AACzH,YAAMG,GAAe,OAAO;AAAA,IACxC;AAAA,IACQ,UAAU;AACN,aAAO,mBAAKZ;AAAA,IACxB;AAAA,IACQ,MAAM,kBAAkB,UAAU,IAAI;AAClC,YAAM,EAAE,YAAY,QAAQ,SAAS,KAAK,mBAAK,kBAAiB,kBAAmB,EAAA,IAAM;AACzF,YAAM,CAAC,gBAAgB,WAAW,IAAI,sBAAsB,SAAS;AACrE,YAAM,cAAce,GAAMF,GAAS;AAAA,QAC/B,iBAAiB,mBAAKb,YAAUd,sBAAkB,gBAAgB,WAAW,iBAAiB,EAAE,KAAK4B,IAAO;AAAA,QAC5G,iBAAiB,mBAAKd,YAAU,cAAc,EAAE,KAAKc,IAAO;AAAA,MAC/D,CAAA,GAAG,iBAAiB,mBAAKd,YAAUd,SAAAA,aAAkB,gBAAgB,WAAW,iBAAiB,CAAC,EAAE,KAAKwB,EAAI,CAAAT,YAAU;AACpH,YAAI,MAAM,QAAQA,OAAM,GAAG;AACvB,iBAAO,EAAE,QAAQA,QAAO,CAAC,EAAG;AAAA,QAChD;AACgB,eAAO,EAAE,QAAAA,QAAQ;AAAA,MACjC,CAAa,CAAC;AACF,YAAM,UAAUO,GAAI,aAAa,GAAI,gBAAgB,OAC/C;AAAA,QACE,mBAAKF,QAAM,oBAAoB;AAAA,UAC3B,SAAS;AAAA,UACT,aAAa,gBAAgB,iBAAiB,IAAI;AAAA,UAClD,UAAU;AAAA,QAClC,CAAqB;AAAA,MACrB,IACkB,CAAE,CAAC,EAAE,KAAKI,EAAI,CAAC,CAAC,EAAE,QAAAT,QAAM,CAAE,MAAM;AAClC,eAAOA;AAAA,MACvB,CAAa,GAAGU,GAAS,QAAQ,EAAE,GAAGF,EAAK,mBAAK,gBAAe,aAAc,CAAA,CAAC,CAAC;AACnE,YAAM,SAAS,MAAMG,GAAe,OAAO;AAC3C,aAAO,mBAAKN,QAAM,sBAAsB,OAAO,UAAU;AAAA,IACrE;AAAA,IACQ,sBAAsB;AAClB,YAAM,IAAI,qBAAsB;AAAA,IAC5C;AAAA,IACQ,IAAI,WAAW;AACX,aAAO,mBAAKC;AAAA,IACxB;AAAA,IACQ,EAAE,mBAAmB,CAAC,eAAe,GAAG,yBAAyB,CAAC,eAAe,GAAG,gCAAgC,CAAC,eAAe,GAAG,cAAa,IAAK;AACrJ,UAAI,mBAAKA,aAAW;AAChB;AAAA,MAChB;AACY,yBAAKA,YAAY;AACjB,yBAAK,gBAAe,OAAO,IAAI,MAAM,gBAAgB,CAAC;AACtD,yBAAKP,WAAS,QAAS;AACvB,WAAK,UAAU,YAAY,EAAE,aAAa,EAAG;AAC7C,WAAK,UAAU,iBAAiB,EAAE,aAAa,EAAG;AAAA,IAC9D;AAAA,IAEQ,MAAM,eAAe,MAAM,OAAO;AAC9B,UAAI,mBAAK,mBAAkB,IAAI,IAAI,GAAG;AAClC,cAAM,IAAI,MAAM,wCAAwC,IAAI,iBAAiB,IAAI,oBAAoB;AAAA,MACrH;AACY,YAAM,aAAa,IAAI,mBAAmB,MAAM,MAAM,KAAK;AAC3D,yBAAK,mBAAkB,IAAI,MAAM,UAAU;AAC3C,UAAI;AACA,cAAM,WAAW,OAAQ;AAAA,MACzC,SACmB,OAAO;AACV,2BAAK,mBAAkB,OAAO,IAAI;AAClC,cAAM;AAAA,MACtB;AAAA,IACA;AAAA,IACQ,gBAAgB,UAAU,SAAS;AAC/B,UAAI,SAAS,WAAW,MAAM,GAAG;AAC7B,cAAM,IAAI,qBAAqB,0CAA0C;AAAA,MACzF;AACY,aAAO,MAAM,gBAAgB,UAAU,OAAO;AAAA,IAC1D;AAAA,EACK,GArJGM,SAAA,eACAN,YAAA,eACA,kCACA,gCACAO,aAAA,eA4HA,oCAvIA,MAAA;AACI,UAAM,YAAY,OAAO,WAAW,cAAc,OAAO,WAAW,OAAO,OAAO,YAAY,OAAO,QAAQ,KAAK,IAAI,IAAI;AAC1H,iBAAa5B,KAAM,MAAM,kBAAkB,EAAE,MAAM,UAAU,MAAM,QAAQ,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,UAAU,KAAK,KAAK,SAAO,IAAI,KAAM,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAC9N,iBAAaA,KAAM,MAAM,wBAAwB,EAAE,MAAM,UAAU,MAAM,cAAc,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,gBAAgB,KAAK,KAAK,SAAO,IAAI,WAAY,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AACtP,iBAAaA,KAAM,MAAM,+BAA+B,EAAE,MAAM,UAAU,MAAM,qBAAqB,QAAQ,OAAO,SAAS,OAAO,QAAQ,EAAE,KAAK,SAAO,uBAAuB,KAAK,KAAK,SAAO,IAAI,kBAAmB,GAAE,UAAU,UAAS,GAAI,MAAM,0BAA0B;AAClR,QAAI,UAAW,QAAO,eAAeA,KAAM,OAAO,UAAU,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,OAAO,WAAW;AAAA,EAClJ,MAPWA;AA8JX,GAAC;ACzND;AAAA;AAAA;AAAA;AAAA;AAOA,IAAI;AAAA,CACH,SAAUqC,oBAAmB;AAC1B,EAAAA,mBAAkB,MAAM,IAAI;AAC5B,EAAAA,mBAAkB,KAAK,IAAI;AAC3B,EAAAA,mBAAkB,SAAS,IAAI;AAC/B,EAAAA,mBAAkB,OAAO,IAAI;AACjC,GAAG,sBAAsB,oBAAoB,CAAA,EAAG;AAChD,IAAI;AAAA,CACH,SAAUC,aAAY;AACnB,EAAAA,YAAW,OAAO,IAAI;AACtB,EAAAA,YAAW,SAAS,IAAI;AACxB,EAAAA,YAAW,OAAO,IAAI;AACtB,EAAAA,YAAW,WAAW,IAAI;AAC1B,EAAAA,YAAW,aAAa,IAAI;AAC5B,EAAAA,YAAW,aAAa,IAAI;AAC5B,EAAAA,YAAW,QAAQ,IAAI;AAC3B,GAAG,eAAe,aAAa,CAAA,EAAG;AAClC,MAAM,kBAAkB,CAAC,QAAQ;AAC7B,UAAQ,KAAG;AAAA,IACP,KAAK;AAAA,IACL,KAAK;AACD,YAAM;AACN;AAAA,EACZ;AAEI,MAAI,CAAC,GAAG,GAAG,EAAE,WAAW,GAAG;AACvB,WAAO;AAAA,EACf;AACI,UAAQ,KAAG;AAAA,IACP,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX;AACI,YAAM,IAAI,MAAM,iBAAiB,GAAG,GAAG;AAAA,EACnD;AACA;AAIO,MAAM,qBAAqB,SAAS;AAAA,EAEvC,YAAY,MAAM;AACd,UAAO;AAFX;AAGI,uBAAK,OAAQ;AAAA,EACrB;AAAA,EACI,MAAM,KAAK,KAAK,UAAU;AACtB,UAAM,mBAAK,OAAM,WAAW,KAAK,wBAAwB;AAAA,MACrD,SAAS,mBAAK,OAAM,UAAW,EAAC;AAAA,MAChC,SAAS;AAAA,QACL;AAAA,UACI,MAAM,kBAAkB;AAAA,UACxB,IAAI;AAAA,UACJ,SAAS;AAAA,YACL;AAAA,cACI,MAAM,WAAW;AAAA,cACjB,OAAO,gBAAgB,GAAG;AAAA,YAC7B;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACb,CAAS;AAAA,EACT;AAAA,EACI,MAAM,GAAG,KAAK;AACV,UAAM,mBAAK,OAAM,WAAW,KAAK,wBAAwB;AAAA,MACrD,SAAS,mBAAK,OAAM,UAAW,EAAC;AAAA,MAChC,SAAS;AAAA,QACL;AAAA,UACI,MAAM,kBAAkB;AAAA,UACxB,IAAI;AAAA,UACJ,SAAS;AAAA,YACL;AAAA,cACI,MAAM,WAAW;AAAA,cACjB,OAAO,gBAAgB,GAAG;AAAA,YAC7B;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACb,CAAS;AAAA,EACT;AAAA,EACI,MAAM,MAAM,KAAK,UAAU,IAAI;AAC3B,UAAM,EAAE,QAAQ,EAAC,IAAK;AACtB,UAAM,UAAU;AAAA,MACZ;AAAA,QACI,MAAM,WAAW;AAAA,QACjB,OAAO,gBAAgB,GAAG;AAAA,MAC7B;AAAA,IACJ;AACD,QAAI,QAAQ,GAAG;AACX,cAAQ,KAAK;AAAA,QACT,MAAM,WAAW;AAAA,QACjB,UAAU;AAAA,MAC1B,CAAa;AAAA,IACb;AACQ,YAAQ,KAAK;AAAA,MACT,MAAM,WAAW;AAAA,MACjB,OAAO,gBAAgB,GAAG;AAAA,IACtC,CAAS;AACD,UAAM,mBAAK,OAAM,WAAW,KAAK,wBAAwB;AAAA,MACrD,SAAS,mBAAK,OAAM,UAAW,EAAC;AAAA,MAChC,SAAS;AAAA,QACL;AAAA,UACI,MAAM,kBAAkB;AAAA,UACxB,IAAI;AAAA,UACJ;AAAA,QACH;AAAA,MACJ;AAAA,IACb,CAAS;AAAA,EACT;AAAA,EACI,MAAM,KAAK,MAAM,UAAU,IAAI;AAC3B,UAAM,EAAE,QAAQ,EAAC,IAAK;AAGtB,UAAM,SAAS,CAAC,GAAG,IAAI,EAAE,IAAI,eAAe;AAC5C,UAAM,UAAU,CAAE;AAClB,QAAI,SAAS,GAAG;AACZ,iBAAW,SAAS,QAAQ;AACxB,gBAAQ,KAAK;AAAA,UACT,MAAM,WAAW;AAAA,UACjB;AAAA,QACpB,GAAmB;AAAA,UACC,MAAM,WAAW;AAAA,UACjB;AAAA,QACpB,CAAiB;AAAA,MACjB;AAAA,IACA,OACa;AACD,iBAAW,SAAS,QAAQ;AACxB,gBAAQ,KAAK;AAAA,UACT,MAAM,WAAW;AAAA,UACjB;AAAA,QACpB,GAAmB;AAAA,UACC,MAAM,WAAW;AAAA,UACjB,UAAU;AAAA,QAC9B,GAAmB;AAAA,UACC,MAAM,WAAW;AAAA,UACjB;AAAA,QACpB,CAAiB;AAAA,MACjB;AAAA,IACA;AACQ,UAAM,mBAAK,OAAM,WAAW,KAAK,wBAAwB;AAAA,MACrD,SAAS,mBAAK,OAAM,UAAW,EAAC;AAAA,MAChC,SAAS;AAAA,QACL;AAAA,UACI,MAAM,kBAAkB;AAAA,UACxB,IAAI;AAAA,UACJ;AAAA,QACH;AAAA,MACJ;AAAA,IACb,CAAS;AAAA,EACT;AAAA,EACI,MAAM,cAAc,MAAM;AAEtB,QAAI,CAAC,GAAG,IAAI,EAAE,SAAS,GAAG;AACtB,YAAM,IAAI,MAAM,oCAAoC;AAAA,IAChE;AACQ,UAAM,QAAQ,MAAM,mBAAK,OAAM,aAAc;AAC7C,UAAM,MAAM,cAAa,EAAG,SAAS,OAAOC,UAAS;AACjD,eAAS,YAAY,cAAc,OAAOA,KAAI;AAAA,IACjD,GAAE,IAAI;AAAA,EACf;AACA;AAxHI;AAyHJ,MAAM,gBAAgB,CAAC,WAAW;AAC9B,UAAQ,QAAM;AAAA,IACV,KAAK,YAAY;AACb,aAAO;AAAA,IACX,KAAK,YAAY;AACb,aAAO;AAAA,IACX,KAAK,YAAY;AACb,aAAO;AAAA,IACX,KAAK,YAAY;AACb,aAAO;AAAA,IACX,KAAK,YAAY;AACb,aAAO;AAAA,EACnB;AACA;AAIO,MAAM,kBAAkB,MAAM;AAAA,EAGjC,YAAY,SAAS;AACjB,UAAO;AAHX,uBAAAlB;AACA,uCAAiB,EAAE,GAAG,GAAG,GAAG,EAAG;AAG3B,uBAAKA,WAAW;AAAA,EACxB;AAAA,EACI,MAAM,QAAQ;AACV,uBAAK,gBAAiB,EAAE,GAAG,GAAG,GAAG,EAAG;AACpC,UAAM,mBAAKA,WAAS,WAAW,KAAK,wBAAwB;AAAA,MACxD,SAAS,mBAAKA,WAAS;AAAA,IACnC,CAAS;AAAA,EACT;AAAA,EACI,MAAM,KAAK,GAAG,GAAG,UAAU,CAAA,GAAI;AAC3B,UAAM,OAAO,mBAAK;AAClB,UAAM,KAAK;AAAA,MACP,GAAG,KAAK,MAAM,CAAC;AAAA,MACf,GAAG,KAAK,MAAM,CAAC;AAAA,IAClB;AACD,UAAM,UAAU,CAAE;AAClB,UAAM,QAAQ,QAAQ,SAAS;AAC/B,aAAS,IAAI,GAAG,IAAI,OAAO,EAAE,GAAG;AAC5B,cAAQ,KAAK;AAAA,QACT,MAAM,WAAW;AAAA,QACjB,GAAG,KAAK,KAAK,GAAG,IAAI,KAAK,MAAM,IAAI;AAAA,QACnC,GAAG,KAAK,KAAK,GAAG,IAAI,KAAK,MAAM,IAAI;AAAA,QACnC,QAAQ,QAAQ;AAAA,MAChC,CAAa;AAAA,IACb;AACQ,YAAQ,KAAK;AAAA,MACT,MAAM,WAAW;AAAA,MACjB,GAAG;AAAA,MACH,QAAQ,QAAQ;AAAA,IAC5B,CAAS;AAED,uBAAK,gBAAiB;AACtB,UAAM,mBAAKA,WAAS,WAAW,KAAK,wBAAwB;AAAA,MACxD,SAAS,mBAAKA,WAAS;AAAA,MACvB,SAAS;AAAA,QACL;AAAA,UACI,MAAM,kBAAkB;AAAA,UACxB,IAAI;AAAA,UACJ;AAAA,QACH;AAAA,MACJ;AAAA,IACb,CAAS;AAAA,EACT;AAAA,EACI,MAAM,KAAK,UAAU,IAAI;AACrB,UAAM,mBAAKA,WAAS,WAAW,KAAK,wBAAwB;AAAA,MACxD,SAAS,mBAAKA,WAAS;AAAA,MACvB,SAAS;AAAA,QACL;AAAA,UACI,MAAM,kBAAkB;AAAA,UACxB,IAAI;AAAA,UACJ,SAAS;AAAA,YACL;AAAA,cACI,MAAM,WAAW;AAAA,cACjB,QAAQ,cAAc,QAAQ,UAAU,YAAY,IAAI;AAAA,YAC3D;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACb,CAAS;AAAA,EACT;AAAA,EACI,MAAM,GAAG,UAAU,IAAI;AACnB,UAAM,mBAAKA,WAAS,WAAW,KAAK,wBAAwB;AAAA,MACxD,SAAS,mBAAKA,WAAS;AAAA,MACvB,SAAS;AAAA,QACL;AAAA,UACI,MAAM,kBAAkB;AAAA,UACxB,IAAI;AAAA,UACJ,SAAS;AAAA,YACL;AAAA,cACI,MAAM,WAAW;AAAA,cACjB,QAAQ,cAAc,QAAQ,UAAU,YAAY,IAAI;AAAA,YAC3D;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACb,CAAS;AAAA,EACT;AAAA,EACI,MAAM,MAAM,GAAG,GAAG,UAAU,CAAA,GAAI;AAC5B,UAAM,UAAU;AAAA,MACZ;AAAA,QACI,MAAM,WAAW;AAAA,QACjB,GAAG,KAAK,MAAM,CAAC;AAAA,QACf,GAAG,KAAK,MAAM,CAAC;AAAA,QACf,QAAQ,QAAQ;AAAA,MACnB;AAAA,IACJ;AACD,UAAM,oBAAoB;AAAA,MACtB,MAAM,WAAW;AAAA,MACjB,QAAQ,cAAc,QAAQ,UAAU,YAAY,IAAI;AAAA,IAC3D;AACD,UAAM,kBAAkB;AAAA,MACpB,MAAM,WAAW;AAAA,MACjB,QAAQ,kBAAkB;AAAA,IAC7B;AACD,aAAS,IAAI,GAAG,KAAK,QAAQ,SAAS,IAAI,EAAE,GAAG;AAC3C,cAAQ,KAAK,mBAAmB,eAAe;AAAA,IAC3D;AACQ,YAAQ,KAAK,iBAAiB;AAC9B,QAAI,QAAQ,OAAO;AACf,cAAQ,KAAK;AAAA,QACT,MAAM,WAAW;AAAA,QACjB,UAAU,QAAQ;AAAA,MAClC,CAAa;AAAA,IACb;AACQ,YAAQ,KAAK,eAAe;AAC5B,UAAM,mBAAKA,WAAS,WAAW,KAAK,wBAAwB;AAAA,MACxD,SAAS,mBAAKA,WAAS;AAAA,MACvB,SAAS;AAAA,QACL;AAAA,UACI,MAAM,kBAAkB;AAAA,UACxB,IAAI;AAAA,UACJ;AAAA,QACH;AAAA,MACJ;AAAA,IACb,CAAS;AAAA,EACT;AAAA,EACI,MAAM,MAAM,UAAU,IAAI;AACtB,UAAM,mBAAKA,WAAS,WAAW,KAAK,wBAAwB;AAAA,MACxD,SAAS,mBAAKA,WAAS;AAAA,MACvB,SAAS;AAAA,QACL;AAAA,UACI,MAAM,kBAAkB;AAAA,UACxB,IAAI;AAAA,UACJ,SAAS;AAAA,YACL;AAAA,cACI,MAAM,WAAW;AAAA,cACjB,GAAI,mBAAK,mBAAkB;AAAA,gBACvB,GAAG;AAAA,gBACH,GAAG;AAAA,cACnC;AAAA,cAC4B,QAAQ,QAAQ,UAAU;AAAA,cAC1B,QAAQ,QAAQ,UAAU;AAAA,YAC7B;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACb,CAAS;AAAA,EACT;AAAA,EACI,OAAO;AACH,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,WAAW;AACP,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,YAAY;AACR,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,OAAO;AACH,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,cAAc;AACV,UAAM,IAAI,qBAAsB;AAAA,EACxC;AACA;AA5JIA,YAAA;AACA;AA+JG,MAAM,wBAAwB,YAAY;AAAA,EAE7C,YAAY,SAAS;AACjB,UAAO;AAFX,uBAAAA;AAGI,uBAAKA,WAAW;AAAA,EACxB;AAAA,EACI,MAAM,WAAW,GAAG,GAAG,UAAU,CAAA,GAAI;AACjC,UAAM,mBAAKA,WAAS,WAAW,KAAK,wBAAwB;AAAA,MACxD,SAAS,mBAAKA,WAAS;AAAA,MACvB,SAAS;AAAA,QACL;AAAA,UACI,MAAM,kBAAkB;AAAA,UACxB,IAAI;AAAA,UACJ,YAAY;AAAA,YACR,aAAa;AAAA,UAChB;AAAA,UACD,SAAS;AAAA,YACL;AAAA,cACI,MAAM,WAAW;AAAA,cACjB,GAAG,KAAK,MAAM,CAAC;AAAA,cACf,GAAG,KAAK,MAAM,CAAC;AAAA,cACf,QAAQ,QAAQ;AAAA,YACnB;AAAA,YACD;AAAA,cACI,MAAM,WAAW;AAAA,cACjB,QAAQ;AAAA,YACX;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACb,CAAS;AAAA,EACT;AAAA,EACI,MAAM,UAAU,GAAG,GAAG,UAAU,CAAA,GAAI;AAChC,UAAM,mBAAKA,WAAS,WAAW,KAAK,wBAAwB;AAAA,MACxD,SAAS,mBAAKA,WAAS;AAAA,MACvB,SAAS;AAAA,QACL;AAAA,UACI,MAAM,kBAAkB;AAAA,UACxB,IAAI;AAAA,UACJ,YAAY;AAAA,YACR,aAAa;AAAA,UAChB;AAAA,UACD,SAAS;AAAA,YACL;AAAA,cACI,MAAM,WAAW;AAAA,cACjB,GAAG,KAAK,MAAM,CAAC;AAAA,cACf,GAAG,KAAK,MAAM,CAAC;AAAA,cACf,QAAQ,QAAQ;AAAA,YACnB;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACb,CAAS;AAAA,EACT;AAAA,EACI,MAAM,WAAW;AACb,UAAM,mBAAKA,WAAS,WAAW,KAAK,wBAAwB;AAAA,MACxD,SAAS,mBAAKA,WAAS;AAAA,MACvB,SAAS;AAAA,QACL;AAAA,UACI,MAAM,kBAAkB;AAAA,UACxB,IAAI;AAAA,UACJ,YAAY;AAAA,YACR,aAAa;AAAA,UAChB;AAAA,UACD,SAAS;AAAA,YACL;AAAA,cACI,MAAM,WAAW;AAAA,cACjB,QAAQ;AAAA,YACX;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAAA,IACb,CAAS;AAAA,EACT;AACA;AAzEIA,YAAA;ACjiBG,MAAM,wBAAwB,YAAY;AAAA,EAW7C,YAAY,OAAO,OAAO,gBAAgB,CAAA,GAAI;AAC1C,UAAO;AAXX,qCAAY;AACZ;AACA;AACA,uBAAAV;AACA;AACA;AACA;AACA,iCAAW,CAAE;AACb;AACA,uBAAAe;AAGI,uBAAKf,OAAO,MAAM,QAAQ;AAC1B,uBAAK,eAAgB,MAAM,UAAU,KAAK,YAAa;AACvD,uBAAK,SAAU,MAAM,QAAQ;AAC7B,uBAAK,WAAY;AACjB,uBAAK,YAAa,MAAM;AACxB,uBAAKe,SAAS;AACd,SAAK,aAAa,MAAM,QAAQ;AAChC,SAAK,iBAAiB;AACtB,SAAK,gBAAgB,MAAM;AAC3B,eAAW,UAAU,MAAM,QAAQ,SAAS;AAGxC,UAAI,OAAO,MAAM,SAAS,UAAU;AAChC,2BAAK,UAAS,OAAO,KAAK,YAAW,CAAE,IAAI,OAAO,MAAM;AAAA,MACxE;AAAA,IACA;AAAA,EACA;AAAA,EACI,IAAI,SAAS;AACT,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,MAAM;AACF,WAAO,mBAAKf;AAAA,EACpB;AAAA,EACI,eAAe;AACX,WAAO,mBAAK;AAAA,EACpB;AAAA,EACI,SAAS;AACL,WAAO,mBAAK;AAAA,EACpB;AAAA,EACI,WAAW;AACP,WAAO,mBAAK;AAAA,EACpB;AAAA,EACI,cAAc;AACV,WAAO,mBAAK,eAAc;AAAA,EAClC;AAAA,EACI,MAAM,gBAAgB;AAClB,WAAO,mBAAK;AAAA,EACpB;AAAA,EACI,UAAU;AACN,WAAO,mBAAK;AAAA,EACpB;AAAA,EACI,WAAW;AACP,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,sBAAsB;AAClB,WAAO,QAAQ,KAAK,aAAa;AAAA,EACzC;AAAA,EACI,YAAY;AACR,WAAO,mBAAK;AAAA,EACpB;AAAA,EACI,gBAAgB;AACZ,WAAO,KAAK,eAAe,MAAO;AAAA,EAC1C;AAAA,EACI,uBAAuB,gBAAgB;AAEnC,SAAK,eAAgB;AAAA,EAC7B;AAAA,EACI,QAAQ;AACJ,WAAO,mBAAKe;AAAA,EACpB;AAAA,EACI,2BAA2B;AACvB,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,SAAS,aAAa,IAAI;AACtB,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,qBAAqB;AACjB,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,mBAAmB;AACf,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,2BAA2B;AACvB,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,+BAA+B;AAC3B,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,wBAAwB;AACpB,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,QAAQ;AACJ,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,QAAQ,WAAW,WAAW;AAC1B,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,UAAU;AACN,UAAM,IAAI,qBAAsB;AAAA,EACxC;AACA;AAnGIf,QAAA;AACA;AACA;AACA;AACA;AACA;AACAe,UAAA;ACVG,MAAM,yBAAyB,aAAa;AAAA,EAS/C,YAAY,SAAS,EAAE,YAAY;AAC/B,UAAO;AATX;AACA;AACA;AACA;AACA,uBAAAf;AACA;AACA,uBAAA6B,WAAW,CAAE;AACb;AAGI,uBAAK,UAAW;AAChB,uBAAK,gBAAiB;AAAA,MAClB,IAAI;AAAA,MACJ,MAAM;AAAA,IACT;AACD,uBAAK7B,OAAO,SAAS;AACrB,uBAAK,YAAa,SAAS;AAC3B,uBAAK,SAAU,SAAS;AACxB,uBAAK,aAAc,SAAS;AAE5B,uBAAK,UAAW;AAEhB,eAAW,UAAU,SAAS,WAAW,CAAA,GAAI;AAGzC,UAAI,OAAO,MAAM,SAAS,UAAU;AAChC,2BAAK6B,WAAS,OAAO,KAAK,YAAW,CAAE,IAAI,OAAO,MAAM;AAAA,MACxE;AAAA,IACA;AAAA,EACA;AAAA,EACI,gBAAgB;AACZ,WAAO,mBAAK;AAAA,EACpB;AAAA,EACI,MAAM;AACF,WAAO,mBAAK7B;AAAA,EACpB;AAAA,EACI,SAAS;AACL,WAAO,mBAAK;AAAA,EACpB;AAAA,EACI,aAAa;AACT,WAAO,mBAAK;AAAA,EACpB;AAAA,EACI,UAAU;AACN,WAAO,mBAAK6B;AAAA,EACpB;AAAA,EACI,UAAU;AACN,WAAO,mBAAK;AAAA,EACpB;AAAA,EACI,YAAY;AACR,WAAO,mBAAK;AAAA,EACpB;AAAA,EACI,SAAS;AACL,WAAO,mBAAK;AAAA,EACpB;AAAA,EACI,QAAQ;AACJ,WAAO,mBAAK,UAAS,MAAO;AAAA,EACpC;AAAA,EACI,oBAAoB;AAChB,WAAO;AAAA,EACf;AAAA,EACI,kBAAkB;AACd,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,SAAS;AACL,UAAM,IAAI,qBAAsB;AAAA,EACxC;AACA;AAlEI;AACA;AACA;AACA;AACA7B,QAAA;AACA;AACA6B,YAAA;AACA;ACbJ;AAAA;AAAA;AAAA;AAAA;AAaO,MAAM,2BAA2B,aAAa;AAAA,EAMjD,YAAY,YAAY,MAAM;AAC1B,UAAO;AAPR;AACH,uBAAAC;AACA,uBAAAd;AACA,uCAAiB,IAAI,gBAAiB;AACtC,oCAAc,oBAAI,IAAK;AACvB,uCAAiB,oBAAI,IAAK;AAGtB,uBAAKc,cAAc;AACnB,uBAAKd,QAAQ;AAEb,uBAAK,gBAAe,IAAI,IAAI,kBAAkB,mBAAKc,eAAa,6BAA6B,sBAAK,uDAAqB,KAAK,IAAI,CAAC,CAAC;AAClI,uBAAK,gBAAe,IAAI,IAAI,kBAAkB,mBAAKA,eAAa,2BAA2B,sBAAK,qDAAmB,KAAK,IAAI,CAAC,CAAC;AAC9H,uBAAK,gBAAe,IAAI,IAAI,kBAAkB,mBAAKA,eAAa,6BAA6B,sBAAK,uDAAqB,KAAK,IAAI,CAAC,CAAC;AAClI,uBAAK,gBAAe,IAAI,IAAI,kBAAkB,mBAAKA,eAAa,sBAAsB,sBAAK,gDAAc,KAAK,IAAI,CAAC,CAAC;AAAA,EAC5H;AAAA,EA4CI,sBAAsB,cAAc;AAChC,QAAI,CAAC,cAAc;AACf,aAAO;AAAA,IACnB;AACQ,UAAM,WAAW,mBAAK,gBAAe,IAAI,YAAY;AACrD,WAAO,YAAY;AAAA,EAC3B;AAAA,EACI,wBAAwB;AACpB,QAAI,yBAAyB;AAC7B,eAAW,WAAW,mBAAK,aAAY,OAAM,GAAI;AAC7C,UAAI,CAAC,QAAQ,cAAc,QAAQ,cAAc;AAC7C;AAAA,MAChB;AAAA,IACA;AACQ,WAAO;AAAA,EACf;AAAA,EACI,0BAA0B,OAAO;AAC7B,eAAW,CAAC,IAAI,OAAO,KAAK,mBAAK,aAAY,WAAW;AACpD,UAAI,QAAQ,MAAO,MAAK,OAAO;AAC3B,2BAAK,aAAY,OAAO,EAAE;AAAA,MAC1C;AAAA,IACA;AACQ,eAAW,CAAC,IAAI,QAAQ,KAAK,mBAAK,gBAAe,WAAW;AACxD,UAAI,SAAS,MAAO,MAAK,OAAO;AAC5B,2BAAK,gBAAe,OAAO,EAAE;AAAA,MAC7C;AAAA,IACA;AAAA,EACA;AAAA,EACI,UAAU;AACN,SAAK,mBAAoB;AACzB,uBAAK,aAAY,MAAO;AACxB,uBAAK,gBAAe,MAAO;AAC3B,uBAAK,gBAAe,QAAS;AAAA,EACrC;AACA;AA5FIA,eAAA;AACAd,SAAA;AACA;AACA;AACA;AALG;AAgBH,yBAAoB,SAAC,OAAO;AACxB,QAAM,QAAQ,mBAAKA,QAAM,MAAM,MAAM,WAAW,EAAE;AAClD,MAAI,CAAC,OAAO;AACR;AAAA,EACZ;AACQ,QAAM,UAAU,mBAAK,aAAY,IAAI,MAAM,QAAQ,OAAO;AAC1D,MAAI;AACJ,MAAI,SAAS;AACT,YAAQ,eAAe,KAAK,OAAO;AACnC,oBAAgB,IAAI,gBAAgB,OAAO,OAAO,QAAQ,cAAc;AAAA,EACpF,OACa;AACD,oBAAgB,IAAI,gBAAgB,OAAO,OAAO,CAAA,CAAE;AAAA,EAChE;AACQ,qBAAK,aAAY,IAAI,MAAM,QAAQ,SAAS,aAAa;AACzD,OAAK,KAAK,oBAAoB,SAAS,aAAa;AAC5D;AACI,uBAAkB,SAAC,QAAQ;AAAA;AAC3B,yBAAoB,SAAC,OAAO;AACxB,QAAM,UAAU,mBAAK,aAAY,IAAI,MAAM,QAAQ,OAAO;AAC1D,MAAI,CAAC,SAAS;AACV;AAAA,EACZ;AACQ,QAAM,WAAW,IAAI,iBAAiB,SAAS,KAAK;AACpD,UAAQ,YAAY;AACpB,MAAI,MAAM,YAAY;AAClB,uBAAK,gBAAe,IAAI,MAAM,YAAY,QAAQ;AAAA,EAC9D;AACQ,MAAI,SAAS,aAAa;AACtB,SAAK,KAAK,oBAAoB,wBAAwB,OAAO;AAAA,EACzE;AACQ,OAAK,KAAK,oBAAoB,UAAU,QAAQ;AAChD,OAAK,KAAK,oBAAoB,iBAAiB,OAAO;AAC9D;AACI,kBAAa,SAAC,OAAO;AACjB,QAAM,UAAU,mBAAK,aAAY,IAAI,MAAM,QAAQ,OAAO;AAC1D,MAAI,CAAC,SAAS;AACV;AAAA,EACZ;AACQ,UAAQ,eAAe,MAAM;AAC7B,OAAK,KAAK,oBAAoB,eAAe,OAAO;AACpD,qBAAK,aAAY,OAAO,MAAM,QAAQ,OAAO;AACrD;ACvEA;AAAA;AAAA;AAAA;AAAA;AAKA,IAAI,0BAAoE,SAAU,KAAK,OAAO,OAAO;AACjG,MAAI,UAAU,QAAQ,UAAU,QAAQ;AACpC,QAAI,OAAO,UAAU,YAAY,OAAO,UAAU,WAAY,OAAM,IAAI,UAAU,kBAAkB;AACpG,QAAI;AACJ,QAAI,OAAO;AACP,UAAI,CAAC,OAAO,aAAc,OAAM,IAAI,UAAU,qCAAqC;AACnF,gBAAU,MAAM,OAAO,YAAY;AAAA,IAC/C;AACQ,QAAI,YAAY,QAAQ;AACpB,UAAI,CAAC,OAAO,QAAS,OAAM,IAAI,UAAU,gCAAgC;AACzE,gBAAU,MAAM,OAAO,OAAO;AAAA,IAC1C;AACQ,QAAI,OAAO,YAAY,WAAY,OAAM,IAAI,UAAU,wBAAwB;AAC/E,QAAI,MAAM,KAAK,EAAE,OAAc,SAAkB,OAAc;AAAA,EACvE,WACa,OAAO;AACZ,QAAI,MAAM,KAAK,EAAE,OAAO,KAAI,CAAE;AAAA,EACtC;AACI,SAAO;AACX;AACA,IAAI,qBAA2D,yBAAUtB,kBAAiB;AACtF,SAAO,SAAU,KAAK;AAClB,aAAS,KAAK,GAAG;AACb,UAAI,QAAQ,IAAI,WAAW,IAAIA,iBAAgB,GAAG,IAAI,OAAO,0CAA0C,IAAI;AAC3G,UAAI,WAAW;AAAA,IAC3B;AACQ,aAAS,OAAO;AACZ,aAAO,IAAI,MAAM,QAAQ;AACrB,YAAI,MAAM,IAAI,MAAM,IAAK;AACzB,YAAI;AACA,cAAI,SAAS,IAAI,WAAW,IAAI,QAAQ,KAAK,IAAI,KAAK;AACtD,cAAI,IAAI,MAAO,QAAO,QAAQ,QAAQ,MAAM,EAAE,KAAK,MAAM,SAAS,GAAG;AAAE,iBAAK,CAAC;AAAG,mBAAO,KAAM;AAAA,WAAG;AAAA,QACpH,SACuB,GAAG;AACN,eAAK,CAAC;AAAA,QAC1B;AAAA,MACA;AACY,UAAI,IAAI,SAAU,OAAM,IAAI;AAAA,IACxC;AACQ,WAAO,KAAM;AAAA,EAChB;AACL,EAAG,OAAO,oBAAoB,aAAa,kBAAkB,SAAU,OAAO,YAAY,SAAS;AAC/F,MAAI,IAAI,IAAI,MAAM,OAAO;AACzB,SAAO,EAAE,OAAO,mBAAmB,EAAE,QAAQ,OAAO,EAAE,aAAa,YAAY;AACnF,CAAC;AA6BM,MAAM,iBAAiB,KAAK;AAAA,EAqE/B,YAAY,iBAAiB,gBAAgB,QAAQ;AACjD,UAAO;AAtER;AACH;AACA,uBAAAoC;AACA,mCAAa,IAAI,UAAW;AAC5B;AACA,kCAAY;AACZ,wCAAkB,SAAS,OAAQ;AACnC,0CAAoB,oBAAI,IAAI;AAAA,MACxB,CAAC,kBAAkB,sBAAK,yCAAiB,KAAK,IAAI,CAAC;AAAA,MACnD,CAAC,wBAAwB,sBAAK,uCAAe,KAAK,IAAI,CAAC;AAAA,MACvD;AAAA,QACI;AAAA,QACA,sBAAK,kDAA0B,KAAK,IAAI;AAAA,MAC3C;AAAA,MACD;AAAA,QACI;AAAA,QACA,sBAAK,iDAAyB,KAAK,IAAI;AAAA,MAC1C;AAAA,MACD,CAAC,oCAAoC,sBAAK,kCAAU,KAAK,IAAI,CAAC;AAAA,IACtE,CAAK;AACD,8CAAwB;AAAA,MACpB;AAAA,QACI,oBAAoB;AAAA,QACpB,CAAC,YAAY;AACT,eAAK,KAAK,WAAmC,OAAO;AAAA,QACvD;AAAA,MACJ;AAAA,MACD;AAAA,QACI,oBAAoB;AAAA,QACpB,CAAC,YAAY;AACT,eAAK,KAAK,0BAAiE,OAAO;AAAA,QACrF;AAAA,MACJ;AAAA,MACD;AAAA,QACI,oBAAoB;AAAA,QACpB,CAAC,YAAY;AACT,eAAK,KAAK,iBAA+C,OAAO;AAAA,QACnE;AAAA,MACJ;AAAA,MACD;AAAA,QACI,oBAAoB;AAAA,QACpB,CAAC,YAAY;AACT,eAAK,KAAK,mBAAmD,OAAO;AAAA,QACvE;AAAA,MACJ;AAAA,MACD;AAAA,QACI,oBAAoB;AAAA,QACpB,CAAC,aAAa;AACV,eAAK,KAAK,YAAqC,QAAQ;AAAA,QAC1D;AAAA,MACJ;AAAA,IACJ;AACD,+CAAyB,oBAAI,IAAI;AAAA,MAC7B,CAAC,qBAAqB,SAAS,sBAAK,0CAAkB,KAAK,IAAI,CAAC;AAAA,MAChE,CAAC,qBAAqB,WAAW,sBAAK,4CAAoB,KAAK,IAAI,CAAC;AAAA,IAC5E,CAAK;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAAzB;AACA;AACA;AAMI,uBAAKA,mBAAmB;AACxB,uBAAK,iBAAkB;AACvB,uBAAK,SAAU;AACf,uBAAKyB,cAAc,gBAAgB;AACnC,eAAW,CAAC,OAAO,UAAU,KAAK,mBAAK,yBAAwB;AAC3D,yBAAKzB,mBAAiB,GAAG,OAAO,UAAU;AAAA,IACtD;AACQ,uBAAK,iBAAkB,IAAI,mBAAmB,mBAAKyB,eAAa,IAAI;AACpE,eAAW,CAAC,OAAO,UAAU,KAAK,mBAAK,oBAAmB;AACtD,yBAAKA,cAAY,GAAG,OAAO,UAAU;AAAA,IACjD;AACQ,eAAW,CAAC,OAAO,UAAU,KAAK,mBAAK,wBAAuB;AAE1D,yBAAK,iBAAgB,GAAG,OAAO,UAAU;AAAA,IACrD;AACQ,UAAM,QAAQ,IAAI,UAAU,MAAM,mBAAKzB,oBAAkB,KAAK,kBAAkB,mBAAKA,mBAAiB,MAAM;AAC5G,uBAAK,YAAW,SAAS,KAAK;AAC9B,SAAK,KAAK,iBAA+C,KAAK;AAE9D,uBAAK,gBAAiB,IAAI,cAAc,KAAK,YAAY,QAAS,EAAC,UAAU;AAC7E,uBAAK,UAAW,IAAI,QAAQ,KAAK,YAAY,QAAS,EAAC,UAAU;AACjE,uBAAK,WAAY,IAAI,SAAS,KAAK,YAAY,QAAS,EAAC,UAAU;AACnE,uBAAK,sBAAuB,IAAI0B,mBAAoB,KAAK,YAAY,QAAS,EAAC,UAAU;AACzF,uBAAK,mBAAoB,IAAI,iBAAiB,eAAe;AAC7D,uBAAK,QAAS,IAAI,UAAU,KAAK,UAAS,EAAG,SAAS;AACtD,uBAAK,cAAe,IAAI,gBAAgB,KAAK,UAAS,EAAG,SAAS;AAClE,uBAAK,WAAY,IAAI,aAAa,IAAI;AAAA,EAC9C;AAAA,EAhCI,UAAU;AACN,WAAO,KAAK,YAAY,QAAS,EAAC;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAkCI,IAAI,aAAa;AACb,WAAO,mBAAKD;AAAA,EACpB;AAAA,EACI,MAAM,aAAa,WAAW,mBAAmB;AAE7C,UAAM,KAAK,UAAU,KAAK,gCAAgC;AAAA,MACtD;AAAA,MACA;AAAA,IACZ,CAAS;AAAA,EACT;AAAA,EACI,MAAM,aAAa,SAAS;AAExB,UAAM,KAAK,QAAS,EAAC,KAAK,qBAAqB,EAAE,SAAS;AAAA,EAClE;AAAA,EACI,MAAM,aAAa,iBAAiB;AAChC,WAAO,CAAC,gBAAgB,UAAU,iCAAiC;AACnE,WAAO,gBAAgB,IAAI,4DAA4D;AACvF,UAAM,WAAW,MAAM,KAAK,UAAW,EAAC,OAAO,KAAK,wBAAwB;AAAA,MACxE,mBAAmB,gBAAgB;AAAA,IAC/C,CAAS;AACD,WAAO,iBAAiB,KAAK,UAAW,EAAC,UAAS,GAAI;AAAA,MAClD,MAAM;AAAA,MACN,QAAQ,SAAS,QAAQ;AAAA,IACrC,CAAS;AAAA,EACT;AAAA,EACI,mBAAmB,gBAAgB;AAC/B,uBAAK,iBAAkB;AAAA,EAC/B;AAAA,EACI,IAAI,gBAAgB;AAChB,WAAO,mBAAK;AAAA,EACpB;AAAA,EACI,IAAI,UAAU;AACV,WAAO,mBAAK;AAAA,EACpB;AAAA,EACI,IAAI,WAAW;AACX,WAAO,mBAAK;AAAA,EACpB;AAAA,EACI,IAAI,QAAQ;AACR,WAAO,mBAAK;AAAA,EACpB;AAAA,EACI,IAAI,cAAc;AACd,WAAO,mBAAK;AAAA,EACpB;AAAA,EACI,IAAI,WAAW;AACX,WAAO,mBAAK;AAAA,EACpB;AAAA,EACI,UAAU;AACN,WAAO,KAAK,eAAgB,EAAC,QAAS;AAAA,EAC9C;AAAA,EACI,iBAAiB;AACb,WAAO,mBAAK;AAAA,EACpB;AAAA,EACI,YAAY;AACR,UAAM,YAAY,mBAAK,YAAW,aAAc;AAChD,WAAO,WAAW,kCAAkC;AACpD,WAAO;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,EAII,MAAM,eAAe;AACjB,UAAM,QAAQ,EAAE,OAAO,CAAA,GAAI,OAAO,QAAQ,UAAU,MAAO;AAC3D,QAAI;AACA,YAAM,QAAQ,wBAAwB,OAAO,MAAM,KAAK,UAAS,EAC5D,cAAa,EACb,eAAe,MAAM;AACtB,YAAIE;AACJ,YAAI,MAAM;AACV,gBAAO,2BAAK,SAAS,0BAAyB,mBAAmB;AAC7D,UAAAA,SAAQ,IAAI,SAAS;AACrB,gBAAMA,OAAM;AAAA,QAChC;AACgB,eAAOA;AAAA,MACV,CAAA,GAAG,KAAK;AACT,UAAI,EAAE,iBAAiB,oBAAoB;AACvC,eAAO,KAAK,UAAW;AAAA,MACvC;AACY,aAAO,MAAM,MAAM,aAAc;AAAA,IAC7C,SACe,KAAK;AACR,YAAM,QAAQ;AACd,YAAM,WAAW;AAAA,IAC7B,UACgB;AACJ,yBAAmB,KAAK;AAAA,IACpC;AAAA,EACA;AAAA,EACI,SAAS;AACL,WAAO,MAAM,KAAK,mBAAK,YAAW,OAAM,CAAE;AAAA,EAClD;AAAA,EACI,MAAM,SAAS;AACX,WAAO,mBAAK,YAAW,QAAQ,WAAW,EAAE,KAAK;AAAA,EACzD;AAAA,EACI,YAAY,SAAS;AACjB,WAAO,mBAAK,YAAW,YAAY,OAAO;AAAA,EAClD;AAAA,EAoGI,sBAAsB,IAAI;AACtB,WAAO,mBAAK,iBAAgB,sBAAsB,EAAE;AAAA,EAC5D;AAAA,EACI,WAAW;AACP,WAAO,mBAAK,iBAAgB,SAAU;AAAA,EAC9C;AAAA,EACI,MAAM,MAAM,SAAS;AACjB,QAAI,mBAAK,iBAAgB,YAAY;AACjC;AAAA,IACZ;AACQ,uBAAK,iBAAgB,OAAO,IAAI,iBAAiB,cAAc,CAAC;AAChE,uBAAK,iBAAgB,QAAS;AAC9B,UAAM,mBAAKF,cAAY,KAAK,yBAAyB;AAAA,MACjD,SAAS,KAAK,UAAS,EAAG;AAAA,MAC1B,eAAc,mCAAS,oBAAmB;AAAA,IACtD,CAAS;AACD,SAAK,KAAK,SAA+B,MAAS;AAClD,SAAK,mBAAoB;AAAA,EACjC;AAAA,EACI,MAAM,OAAO,UAAU,IAAI;AACvB,UAAM,EAAE,YAAY,QAAQ,SAAS,KAAK,KAAK,iBAAiB,kBAAmB,EAAA,IAAM;AACzF,UAAM,CAAC,WAAW,WAAW,IAAI,sBAAsB,SAAS;AAChE,UAAM,UAAUZ,GAAIC,EAAK,mBAAKW,cAAY,KAAK,0BAA0B;AAAA,MACrE,SAAS,KAAK,UAAS,EAAG;AAAA,MAC1B,MAAM;AAAA,IAClB,CAAS,CAAC,GAAG,GAAI,gBAAgB,OACnB;AAAA,MACE,KAAK,oBAAoB;AAAA,QACrB,SAAS;AAAA,QACT,aAAa,gBAAgB,iBAAiB,IAAI;AAAA,QAClD,UAAU;AAAA,MAC9B,CAAiB;AAAA,IACjB,IACc,CAAE,CAAC,EAAE,KAAKV,EAAI,CAAC,CAAC,EAAE,QAAAT,QAAM,CAAE,MAAM;AAClC,aAAOA;AAAA,IACnB,CAAS,GAAGU,GAAS,QAAQ,EAAE,GAAGF,EAAK,mBAAK,iBAAgB,aAAc,CAAA,CAAC,GAAG,uBAAuB,KAAK,IAAG,GAAI,EAAE,CAAC;AAC5G,UAAM,SAAS,MAAMG,GAAe,OAAO;AAC3C,WAAO,KAAK,sBAAsB,OAAO,UAAU;AAAA,EAC3D;AAAA,EACI,4BAA4BvB,UAAS;AACjC,SAAK,iBAAiB,4BAA4BA,QAAO;AAAA,EACjE;AAAA,EACI,kBAAkBA,UAAS;AACvB,SAAK,iBAAiB,kBAAkBA,QAAO;AAAA,EACvD;AAAA,EACI,oBAAoB;AAChB,WAAO,KAAK,iBAAiB,QAAS;AAAA,EAC9C;AAAA,EACI,sBAAsB;AAClB,WAAO,mBAAK,sBAAqB;AAAA,EACzC;AAAA,EACI,MAAM,eAAe,SAAS;AAC1B,WAAO,MAAM,mBAAK,sBAAqB,eAAe,OAAO;AAAA,EACrE;AAAA,EACI,MAAM,qBAAqB,SAAS;AAChC,WAAO,MAAM,mBAAK,sBAAqB,qBAAqB,OAAO;AAAA,EAC3E;AAAA,EACI,MAAM,iBAAiB,MAAM;AACzB,WAAO,MAAM,mBAAK,sBAAqB,iBAAiB,IAAI;AAAA,EACpE;AAAA,EACI,MAAM,qBAAqB,QAAQ;AAC/B,WAAO,MAAM,mBAAK,sBAAqB,qBAAqB,MAAM;AAAA,EAC1E;AAAA,EACI,MAAM,qBAAqB,UAAU;AACjC,WAAO,MAAM,mBAAK,sBAAqB,qBAAqB,QAAQ;AAAA,EAC5E;AAAA,EACI,MAAM,gBAAgB,YAAY;AAC9B,WAAO,MAAM,mBAAK,sBAAqB,gBAAgB,UAAU;AAAA,EACzE;AAAA,EACI,MAAM,iBAAiB,WAAW;AAC9B,WAAO,MAAM,mBAAK,sBAAqB,iBAAiB,SAAS;AAAA,EACzE;AAAA,EACI,MAAM,wBAAwB,MAAM;AAChC,WAAO,MAAM,mBAAK,sBAAqB,wBAAwB,IAAI;AAAA,EAC3E;AAAA,EACI,MAAM,YAAY,UAAU;AACxB,QAAI,CAAC,mBAAKM,mBAAiB,eAAe;AACtC,YAAM,mBAAK,mBAAkB,gBAAgB,QAAQ;AACrD,yBAAK,WAAY;AACjB;AAAA,IACZ;AACQ,UAAM,cAAc,MAAM,mBAAK,sBAAqB,gBAAgB,QAAQ;AAC5E,uBAAK,WAAY;AACjB,QAAI,aAAa;AACb,YAAM,KAAK,OAAQ;AAAA,IAC/B;AAAA,EACA;AAAA,EACI,WAAW;AACP,WAAO,mBAAK;AAAA,EACpB;AAAA,EACI,MAAM,IAAI,UAAU,IAAI;AACpB,UAAM,EAAE,SAAS,KAAK,KAAK,iBAAiB,WAAW,OAAO,OAAS,IAAK;AAC5E,UAAM,EAAE,iBAAiB,YAAY,QAAQ,WAAW,OAAO,QAAQ,YAAY,QAAQ,OAAO,kBAAiB,IAAM,gBAAgB,SAAS,IAAI;AACtJ,UAAM,aAAa,SAAS,OAAO,MAAM,IAAI,IAAI,CAAE;AACnD,UAAM,EAAE,OAAQ,IAAG,MAAMiB,GAAeH,EAAK,mBAAKW,cAAY,KAAK,yBAAyB;AAAA,MACxF,SAAS,KAAK,UAAS,EAAG;AAAA,MAC1B;AAAA,MACA;AAAA,MACA,aAAa,YAAY,cAAc;AAAA,MACvC,MAAM;AAAA,QACF;AAAA,QACA;AAAA,MACH;AAAA,MACD;AAAA,MACA;AAAA,MACA,aAAa,CAAC;AAAA,IAC1B,CAAS,CAAC,EAAE,KAAKT,GAAS,QAAQ,EAAE,CAAC,CAAC,CAAC;AAC/B,UAAM,SAAS,OAAO,KAAK,OAAO,MAAM,QAAQ;AAChD,UAAM,KAAK,wBAAwB,MAAM,MAAM;AAC/C,WAAO;AAAA,EACf;AAAA,EACI,MAAM,gBAAgB,SAAS;AAC3B,UAAM,SAAS,MAAM,KAAK,IAAI,OAAO;AACrC,QAAI;AACA,YAAM,EAAE,SAAQ,IAAK,MAAM,OAAO,QAAQ;AAC1C,aAAO,SAAS,KAAK,MAAM;AAAA,IACvC,SACe,OAAO;AACV,UAAI,iBAAiB,WAAW;AAC5B,cAAM,IAAI,MAAM,uDAAuD;AAAA,MACvF;AACY,YAAM;AAAA,IAClB;AAAA,EACA;AAAA,EACI,MAAM,YAAY,SAAS;AACvB,UAAM,EAAE,MAAM,MAAM,uBAAuB,QAAS,IAAG;AACvD,QAAI,QAAQ,mBAAmB,UAAa,QAAQ,gBAAgB;AAChE,YAAM,IAAI,qBAAqB,yCAAyC;AAAA,IACpF;AACQ,QAAI,QAAQ,qBAAqB,UAAa,QAAQ,kBAAkB;AACpE,YAAM,IAAI,qBAAqB,2CAA2C;AAAA,IACtF;AACQ,QAAI,QAAQ,gBAAgB,UAAa,CAAC,QAAQ,aAAa;AAC3D,YAAM,IAAI,qBAAqB,sCAAsC;AAAA,IACjF;AACQ,QAAI,SAAS,UAAa,KAAK,UAAU,UAAa,KAAK,UAAU,GAAG;AACpE,YAAM,IAAI,qBAAqB,0CAA0C;AAAA,IACrF;AACQ,QAAI;AACJ,QAAI,MAAM;AACN,UAAI,uBAAuB;AACvB,cAAM;AAAA,MACtB,OACiB;AAID,cAAM,CAAC,UAAU,OAAO,IAAI,MAAM,KAAK,SAAS,MAAM;AAClD,cAAI,CAAC,OAAO,gBAAgB;AACxB,kBAAM,IAAI,MAAM,yCAAyC;AAAA,UACjF;AACoB,iBAAO;AAAA,YACH,OAAO,eAAe;AAAA,YACtB,OAAO,eAAe;AAAA,UACzB;AAAA,QACrB,CAAiB;AACD,cAAM;AAAA,UACF,GAAG;AAAA,UACH,GAAG,KAAK,IAAI;AAAA,UACZ,GAAG,KAAK,IAAI;AAAA,QACf;AAAA,MACjB;AAAA,IACA;AACQ,UAAM,EAAE,QAAQ,EAAE,KAAM,EAAA,IAAM,MAAM,mBAAKS,cAAY,KAAK,qCAAqC;AAAA,MAC3F,SAAS,KAAK,UAAS,EAAG;AAAA,MAC1B,QAAQ,wBAAwB,aAAa;AAAA,MAC7C,QAAQ;AAAA,QACJ,MAAM,SAAS,IAAI;AAAA,QACnB,GAAI,YAAY,SAAY,EAAE,SAAS,UAAU,IAAK,IAAG;MAC5D;AAAA,MACD,GAAI,MAAM,EAAE,MAAM,EAAE,MAAM,OAAO,GAAG,MAAO,IAAG;IAC1D,CAAS;AACD,WAAO;AAAA,EACf;AAAA,EACI,MAAM,mBAAmB;AACrB,UAAM,EAAE,UAAS,IAAK,MAAM,KAAK,UAAS,EACrC,QAAO,EACP,WAAW,KAAK,yBAAyB;AAAA,MAC1C,UAAU,KAAK,UAAS,EAAG;AAAA,MAC3B,SAAS;AAAA,IACrB,CAAS;AACD,WAAO,IAAI,kBAAkB,KAAK,UAAS,EAAG,QAAS,GAAE,SAAS;AAAA,EAC1E;AAAA,EACI,MAAM,eAAe;AACjB,UAAM,mBAAKA,cAAY,KAAK,4BAA4B;AAAA,MACpD,SAAS,KAAK,UAAS,EAAG;AAAA,IACtC,CAAS;AAAA,EACT;AAAA,EACI,MAAM,sBAAsB,iBAAiB,MAAM;AAC/C,UAAM,aAAa,qBAAqB,cAAc,GAAG,IAAI;AAC7D,UAAM,EAAE,OAAM,IAAK,MAAM,mBAAKA,cAAY,KAAK,2BAA2B;AAAA,MACtE,qBAAqB;AAAA,MACrB,UAAU,CAAC,KAAK,UAAS,EAAG,GAAG;AAAA,IAC3C,CAAS;AACD,WAAO,EAAE,YAAY,OAAO,OAAQ;AAAA,EAC5C;AAAA,EACI,MAAM,oCAAoC,IAAI;AAC1C,UAAM,mBAAKA,cAAY,KAAK,8BAA8B;AAAA,MACtD,QAAQ;AAAA,IACpB,CAAS;AAAA,EACT;AAAA,EACI,MAAM,eAAe,MAAM,cAAc;AACrC,WAAO,MAAM,KAAK,UAAW,EAAC,eAAe,MAAM,aAAa,eAAe,aAAa,UAAU,YAAY;AAAA,EAC1H;AAAA,EACI,4BAA4B;AACxB,WAAO;AAAA,EACf;AAAA,EACI,MAAM,gBAAgB,SAAS;AAE3B,UAAM,KAAK,UAAU,KAAK,4BAA4B;AAAA,MAClD,eAAe,CAAC;AAAA,IAC5B,CAAS;AAAA,EACT;AAAA,EACI,0BAA0B;AACtB,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,SAAS;AACL,WAAO,mBAAK;AAAA,EACpB;AAAA,EACI,qBAAqB;AACjB,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,UAAU;AACN,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,yBAAyB;AACrB,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,sBAAsB;AAClB,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,yBAAyB;AACrB,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,iBAAiB;AACb,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,2BAA2B;AACvB,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,UAAU;AACN,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,YAAY;AACR,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,eAAe;AACX,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,wBAAwB;AAEpB,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,eAAe;AACX,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,sBAAsB;AAClB,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,UAAU;AACN,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,MAAM,OAAO,UAAU,IAAI;AACvB,WAAO,MAAM,sBAAK,4BAAL,WAAS,IAAI;AAAA,EAClC;AAAA,EACI,MAAM,UAAU,UAAU,IAAI;AAC1B,WAAO,MAAM,sBAAK,4BAAL,WAAS,GAAI;AAAA,EAClC;AAAA,EAsBI,sBAAsB;AAClB,UAAM,IAAI,qBAAsB;AAAA,EACxC;AACA;AA5kBI;AACAA,eAAA;AACA;AACA;AACA;AACA;AACA;AAaA;AAgCA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACAzB,oBAAA;AACA;AACA;AAjEG;AAsMH,mBAAc,SAAC,MAAM;AACjB,QAAM,QAAQ,KAAK,MAAM,KAAK,OAAO;AACrC,MAAI,SAAS,KAAK,UAAS,MAAO,OAAO;AACrC,SAAK,KAAK,QAA6B,MAAS;AAAA,EAC5D;AACA;AACI,8BAAyB,SAAC,MAAM;AAC5B,QAAM,QAAQ,KAAK,MAAM,KAAK,OAAO;AACrC,MAAI,OAAO;AACP,SAAK,KAAK,kBAAiD,KAAK;AAAA,EAC5E;AACA;AACI,6BAAwB,SAAC,MAAM;AAC3B,QAAM,QAAQ,KAAK,MAAM,KAAK,OAAO;AACrC,MAAI,OAAO;AACP,UAAM,qBAAqB;AAC3B,QAAI,KAAK,UAAW,MAAK,OAAO;AAC5B,WAAK,KAAK,oBAAqD,MAAS;AAAA,IACxF;AACY,SAAK,KAAK,kBAAiD,KAAK;AAAA,EAC5E;AACA;AACI,sBAAiB,SAAC,SAAS;AACvB,MAAI,CAAC,KAAK,MAAM,QAAQ,EAAE,MACrB,KAAK,MAAM,QAAQ,UAAU,EAAE,KAAK,CAAC,mBAAK,YAAW,aAAY,IAAK;AACvE,UAAM,QAAQ,IAAI,UAAU,MAAM,SAAS,KAAK,kBAAkB,QAAQ,MAAM;AAChF,uBAAK,YAAW,SAAS,KAAK;AAC9B,QAAI,UAAU,KAAK,aAAa;AAC5B,WAAK,KAAK,iBAA+C,KAAK;AAAA,IAC9E;AAAA,EACA;AACA;AACI,wBAAmB,SAAC,SAAS;AACzB,QAAM,QAAQ,KAAK,MAAM,QAAQ,EAAE;AACnC,MAAI,OAAO;AACP,QAAI,UAAU,KAAK,aAAa;AAC5B,WAAK,KAAK,SAA+B,MAAS;AAAA,IAClE;AACY,0BAAK,iDAAL,WAA8B;AAAA,EAC1C;AACA;AACI,6BAAwB,SAAC,OAAO;AAC5B,aAAW,SAAS,MAAM,eAAe;AACrC,0BAAK,iDAAL,WAA8B;AAAA,EAC1C;AACQ,QAAM,aAAa,EAAG;AACtB,qBAAK,iBAAgB,0BAA0B,KAAK;AACpD,qBAAK,YAAW,YAAY,KAAK;AACjC,OAAK,KAAK,iBAA+C,KAAK;AACtE;AACI,qBAAgB,SAAC,OAAO;AACpB,QAAM,QAAQ,KAAK,MAAM,MAAM,OAAO,OAAO;AAC7C,MAAI,CAAC,OAAO;AACR;AAAA,EACZ;AACQ,MAAI,kBAAkB,KAAK,GAAG;AAC1B,UAAM,OAAO,MAAM,KAAK,IAAI,SAAO;AAC/B,aAAO,iBAAiB,MAAM,UAAS,GAAI,GAAG;AAAA,IAC9D,CAAa;AACD,UAAM,OAAO,KACR,OAAO,CAAC,OAAO,QAAQ;AACxB,YAAM,cAAc,IAAI,mBAClB,iBAAiB,YAAY,IAAI,YAAa,CAAA,IAC9C,IAAI,SAAU;AACpB,aAAO,GAAG,KAAK,IAAI,WAAW;AAAA,IAC9C,GAAe,EAAE,EACA,MAAM,CAAC;AACZ,SAAK,KAAK,WAAmC,IAAI,eAAe,MAAM,QAAQ,MAAM,MAAM,uBAAuB,MAAM,UAAU,CAAC,CAAC;AAAA,EAC/I,WACiB,qBAAqB,KAAK,GAAG;AAClC,UAAM,QAAQ,IAAI,MAAM,MAAM,QAAQ,EAAE;AACxC,UAAM,gBAAgB,MAAM,QAAQ,MAAM,IAAI,EAAE;AAChD,UAAM,eAAe,MAAM,MAAM,MAAM,IAAI,EAAE,OAAO,GAAG,aAAa;AACpE,UAAM,aAAa,CAAE;AACrB,QAAI,MAAM,YAAY;AAClB,iBAAW2B,UAAS,MAAM,WAAW,YAAY;AAE7C,mBAAW,KAAK,UAAUA,OAAM,gBAAgB,aAAa,KAAKA,OAAM,GAAG,IAAIA,OAAM,aAAa,CAAC,IAAIA,OAAM,eAAe,CAAC,GAAG;AAChI,YAAI,WAAW,UAAU,MAAM,iBAAiB;AAC5C;AAAA,QACxB;AAAA,MACA;AAAA,IACA;AACY,UAAM,QAAQ,CAAC,GAAG,cAAc,GAAG,UAAU,EAAE,KAAK,IAAI;AACxD,SAAK,KAAK,aAAuC,KAAK;AAAA,EAClE,OACa;AACD,eAAW,iCAAiC,MAAM,IAAI,YAAY,MAAM,IAAI,gBAAgB,MAAM,KAAK,GAAG;AAAA,EACtH;AACA;AACI,cAAS,SAAC,OAAO;AACb,QAAM,QAAQ,KAAK,MAAM,MAAM,OAAO;AACtC,MAAI,CAAC,OAAO;AACR;AAAA,EACZ;AACQ,QAAM,OAAO,mBAAmB,MAAM,IAAI;AAC1C,QAAM,SAAS,IAAI,WAAW,MAAM,QAAO,GAAI,MAAM,MAAM,SAAS,MAAM,YAAY;AACtF,OAAK,KAAK,UAAiC,MAAM;AACzD;AA6QU,QAAG,eAAC,OAAO,SAAS;AACtB,MAAI;AACA,UAAM,SAAS,MAAM,QAAQ,IAAI;AAAA,MAC7B,KAAK,kBAAkB,OAAO;AAAA,MAC9B,mBAAKF,cAAY,KAAK,mCAAmC;AAAA,QACrD;AAAA,QACA,SAAS,KAAK,UAAS,EAAG;AAAA,MAC9C,CAAiB;AAAA,IACjB,CAAa;AACD,WAAO,OAAO,CAAC;AAAA,EAC3B,SACe,KAAK;AAER,QAAI,YAAY,GAAG,GAAG;AAClB,UAAI,IAAI,QAAQ,SAAS,uBAAuB,GAAG;AAC/C,eAAO;AAAA,MAC3B;AAAA,IACA;AACY,UAAM;AAAA,EAClB;AACA;AAKA,SAAS,kBAAkB,OAAO;AAC9B,SAAO,MAAM,SAAS;AAC1B;AACA,SAAS,qBAAqB,OAAO;AACjC,SAAO,MAAM,SAAS;AAC1B;AACA,SAAS,uBAAuB,YAAY;AACxC,QAAM,sBAAsB,CAAE;AAC9B,MAAI,YAAY;AACZ,eAAW,aAAa,WAAW,YAAY;AAC3C,0BAAoB,KAAK;AAAA,QACrB,KAAK,UAAU;AAAA,QACf,YAAY,UAAU;AAAA,QACtB,cAAc,UAAU;AAAA,MACxC,CAAa;AAAA,IACb;AAAA,EACA;AACI,SAAO;AACX;AACA,SAAS,qBAAqB,QAAQ,MAAM;AACxC,SAAO,UAAU,iBAAiB,KAAK,GAAG,IAAI,CAAC;AACnD;ACjrBA;AAAA;AAAA;AAAA;AAAA;AAYO,MAAM,mBAAmB,OAAO;AAAA,EAEnC,YAAY,gBAAgB;AACxB,UAAO;AAFX;AAGI,SAAK,kBAAkB;AAAA,EAC/B;AAAA,EACI,mBAAmB,gBAAgB;AAC/B,SAAK,kBAAkB;AAAA,EAC/B;AAAA,EACI,SAAS;AACL,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,UAAU;AACN,WAAO,KAAK,gBAAgB,QAAS;AAAA,EAC7C;AAAA,EACI,iBAAiB;AACb,WAAO,KAAK;AAAA,EACpB;AAAA,EACI,SAAS;AACL,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,mBAAmB;AACf,UAAM,IAAI,qBAAsB;AAAA,EACxC;AACA;AAIO,MAAM,0BAA0B,OAAO;AAAA,EAE1C,YAAY,SAAS;AACjB,UAAO;AAFX,uBAAAG;AAGI,uBAAKA,WAAW;AAAA,EACxB;AAAA,EACI,MAAM;AACF,WAAO;AAAA,EACf;AAAA,EACI,OAAO;AACH,WAAO,WAAW;AAAA,EAC1B;AAAA,EACI,SAAS;AACL,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,UAAU;AACN,WAAO,mBAAKA;AAAA,EACpB;AAAA,EACI,iBAAiB;AACb,WAAO,mBAAKA,WAAS,sBAAuB;AAAA,EACpD;AAAA,EACI,SAAS;AACL,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EACI,mBAAmB;AACf,UAAM,IAAI,qBAAsB;AAAA,EACxC;AACA;AA1BIA,YAAA;AA8BG,MAAM,kCAAkC,WAAW;AAAA,EAEtD,YAAY,gBAAgB,iBAAiB;AACzC,UAAM,cAAc;AAFxB;AAGI,SAAK,mBAAmB;AAAA,EAChC;AAAA,EACI,MAAM;AACF,WAAO,KAAK,iBAAiB;AAAA,EACrC;AAAA,EACI,MAAM,mBAAmB;AACrB,UAAM,EAAE,UAAS,IAAK,MAAM,KAAK,iBAAiB,WAAW,KAAK,yBAAyB;AAAA,MACvF,UAAU,KAAK,iBAAiB;AAAA,MAChC,SAAS;AAAA,IACrB,CAAS;AACD,WAAO,IAAI,kBAAkB,KAAK,kBAAkB,SAAS;AAAA,EACrE;AAAA,EACI,OAAO;AACH,WAAO,WAAW;AAAA,EAC1B;AACA;AAIO,MAAM,uBAAuB,0BAA0B;AAAA,EAE1D,YAAY,gBAAgB,iBAAiB;AACzC,UAAM,gBAAgB,eAAe;AAFzC,uBAAAjB;AAGI,uBAAKA,QAAQ,IAAI,SAAS,iBAAiB,gBAAgB,IAAI;AAAA,EACvE;AAAA,EACI,MAAM,OAAO;AACT,WAAO,mBAAKA;AAAA,EACpB;AAAA,EACI,mBAAmB,gBAAgB;AAC/B,UAAM,mBAAmB,cAAc;AACvC,uBAAKA,QAAM,mBAAmB,cAAc;AAAA,EACpD;AACA;AAZIA,SAAA;AC/FJ;AAAA;AAAA;AAAA;AAAA;AAeO,MAAM,eAAN,MAAM,qBAAoBkB,UAAQ;AAAA,EAkDrC,YAAY,aAAa,MAAM;AAC3B,UAAO;AAnDR;AACH,oCAAW;AAmCX;AACA;AACA;AACA,uBAAAC;AACA,iCAAW,oBAAI,IAAK;AACpB,yCAAmB,oBAAI,QAAS;AAChC;AACA,iDAA2B,oBAAI,IAAI;AAAA,MAC/B,CAAC,kCAAkC,sBAAK,wBAAAC,sBAAkB,KAAK,IAAI,CAAC;AAAA,MACpE,CAAC,oCAAoC,sBAAK,wBAAAC,wBAAoB,KAAK,IAAI,CAAC;AAAA,MACxE,CAAC,oCAAoC,sBAAK,+CAAoB,KAAK,IAAI,CAAC;AAAA,MACxE,CAAC,qCAAqC,sBAAK,gDAAqB,KAAK,IAAI,CAAC;AAAA,MAC1E,CAAC,qCAAqC,sBAAK,gDAAqB,KAAK,IAAI,CAAC;AAAA,IAClF,CAAK;AAGG,uBAAK,UAAW,KAAK;AACrB,uBAAK,gBAAiB,KAAK;AAC3B,uBAAK,cAAe;AACpB,uBAAKF,mBAAmB,KAAK;AAC7B,uBAAK,gBAAiB,IAAI,kBAAkB,IAAI;AAChD,eAAW,WAAW,mBAAK,cAAa,cAAc;AAClD,4BAAK,iDAAL,WAA2B;AAAA,IACvC;AAAA,EACA;AAAA,EAvCI,aAAa,OAAO,MAAM;;AACtB,UAAM,UAAU,MAAM,QAAQ,KAAK,KAAK,YAAY;AAAA,MAChD,aAAa;AAAA,QACT,qBAAqB,KAAK;AAAA,QAC1B,cAAc;AAAA,MACjB;AAAA,IACb,CAAS;AACD,UAAM,QAAQ,UAAU,QAAQ,aAAa,YAAY,kBAAiB,EAAG,SAAS,SAAS,IACzF,aAAY,mBACZ,CAAC,GAAG,aAAY,kBAAkB,GAAG,aAAY,kBAAkB,CAAC;AAC1E,UAAM,UAAU,IAAI,aAAY,QAAQ,SAAS,IAAI;AACrD,oBAAA9C,MAAA,SAAQ,uCAAR,KAAAA;AACA,UAAM,8BAAQ,oCAAR;AACN,WAAO;AAAA,EACf;AAAA,EA4CI,YAAY;AACR,UAAM,IAAI,qBAAsB;AAAA,EACxC;AAAA,EA+DI,IAAI,aAAa;AAEb,WAAO,mBAAK,cAAa,QAAQ;AAAA,EACzC;AAAA,EACI,aAAa;AACT,WAAO,KAAK,WAAW;AAAA,EAC/B;AAAA,EACI,MAAM,QAAQ;;AACV,eAAW,CAAC,WAAW,OAAO,KAAK,mBAAK,2BAA0B;AAC9D,WAAK,WAAW,IAAI,WAAW,OAAO;AAAA,IAClD;AACQ,QAAI,KAAK,WAAW,QAAQ;AACxB;AAAA,IACZ;AACQ,QAAI;AACA,YAAM,mBAAK,cAAa,MAAO;AAC/B,cAAMA,MAAA,mBAAK,oBAAL,gBAAAA,IAAqB,KAAK;AAAA,IAC5C,SACe,OAAO;AAEV,iBAAW,KAAK;AAAA,IAC5B,UACgB;AACJ,WAAK,WAAW,QAAS;AAAA,IACrC;AAAA,EACA;AAAA,EACI,IAAI,YAAY;AACZ,WAAO,CAAC,mBAAK,cAAa;AAAA,EAClC;AAAA,EACI,UAAU;AACN,WAAO,mBAAK,aAAY;AAAA,EAChC;AAAA,EACI,MAAM,8BAA8B,UAAU;AAC1C,UAAM,cAAc,MAAM,mBAAK,cAAa,kBAAmB;AAC/D,WAAO,sBAAK,iDAAL,WAA2B;AAAA,EAC1C;AAAA,EACI,MAAM,UAAU;AACZ,WAAO,GAAG,mBAAK,wCAAY,IAAI,mBAAK,2CAAe;AAAA,EAC3D;AAAA,EACI,kBAAkB;AACd,WAAO,CAAC,GAAG,mBAAK,cAAa,YAAY,EAAE,IAAI,aAAW;AACtD,aAAO,mBAAK,kBAAiB,IAAI,OAAO;AAAA,IACpD,CAAS;AAAA,EACT;AAAA,EACI,wBAAwB;AACpB,WAAO,mBAAK,kBAAiB,IAAI,mBAAK,cAAa,kBAAkB;AAAA,EAC7E;AAAA,EACI,UAAU;AACN,WAAO,KAAK,sBAAuB,EAAC,QAAS;AAAA,EACrD;AAAA,EACI,UAAU;AACN,WAAO,CAAC,mBAAK,iBAAgB,GAAG,MAAM,KAAK,mBAAK,UAAS,OAAM,CAAE,CAAC;AAAA,EAC1E;AAAA,EACI,eAAe,IAAI;AACf,UAAM,SAAS,mBAAK,UAAS,IAAI,EAAE;AACnC,QAAI,CAAC,QAAQ;AACT,YAAM,IAAI,MAAM,kBAAkB;AAAA,IAC9C;AACQ,WAAO;AAAA,EACf;AAAA,EACI,SAAS;AACL,WAAO,mBAAK;AAAA,EACpB;AAAA,EACI,MAAM,aAAa;AACf,QAAI;AACA,YAAM,mBAAK,cAAa,QAAQ,IAAK;AAAA,IACjD,SACe,OAAO;AAEV,iBAAW,KAAK;AAAA,IAC5B,UACgB;AACJ,WAAK,WAAW,QAAS;AAAA,IACrC;AAAA,EACA;AAAA,EACI,IAAI,YAAY;AACZ,WAAO;AAAA,MACH,uBAAuB,KAAK,WAAW,yBAA0B;AAAA,IACpE;AAAA,EACT;AACA;AA5LI;AACA;AACA;AACA8C,oBAAA;AACA;AACA;AACA;AACA;AA3CG;AA6DH,gBAAW,WAAG;;AACV,qBAAK,cAAa,KAAK,gBAAgB,MAAM;AACzC,SAAK,KAAK,gBAAgD,MAAS;AAAA,EAC/E,CAAS;AACD,GAAA9C,MAAA,mBAAK,cAAL,gBAAAA,IAAe,KAAK,SAAS,MAAM;AAC/B,uBAAK,cAAa,QAAQ,2BAA2B,IAAI;AACzD,SAAK,WAAW,QAAS;AAAA,EACrC;AACQ,aAAW,CAAC,WAAW,OAAO,KAAK,mBAAK,2BAA0B;AAC9D,SAAK,WAAW,GAAG,WAAW,OAAO;AAAA,EACjD;AACA;AACQ,kBAAY,WAAG;AACf,SAAO,mBAAK,cAAa,QAAQ,aAAa;AACtD;AACQ,qBAAe,WAAG;AAClB,SAAO,mBAAK,cAAa,QAAQ,aAAa;AACtD;AAII,0BAAqB,SAAC,aAAa;AAC/B,QAAM,iBAAiB,IAAI,mBAAmB,MAAM,aAAa;AAAA,IAC7D,iBAAiB,mBAAK8C;AAAA,EAClC,CAAS;AACD,qBAAK,kBAAiB,IAAI,aAAa,cAAc;AACrD,SAAO;AACf;AACI,wBAAmB,SAAC,OAAO;AACvB,QAAM,SAAS,mBAAK,UAAS,IAAI,MAAM,OAAO;AAC9C,MAAI,QAAQ;AACR,SAAK,KAAK,iBAAkD,MAAM;AAClE,WAAO,eAAgB,EAAC,KAAK,iBAAyD,MAAM;AAAA,EACxG;AACA;AACI,yBAAoB,SAAC,OAAO;AACxB,QAAM,SAAS,mBAAK,UAAS,IAAI,MAAM,OAAO;AAC9C,MAAI,QAAQ;AACR,SAAK,KAAK,iBAAkD,MAAM;AAClE,WAAO,eAAgB,EAAC,KAAK,iBAAyD,MAAM;AAAA,EACxG;AACA;AACIC,uBAAiB,SAAC,OAAO;AACrB,QAAM,UAAU,IAAIE,kBAAgB,KAAK,YAAY,OAAO,mBAAK,wCAAY;AAC7E,OAAK,WAAW,yBAAyB,OAAO;AAChD,QAAM,iBAAiB,MAAM,gBAAgB,YACvC,KAAK,sBAAqB,IAC1B,KAAK,gBAAe,EAAG,KAAK,CAAAC,oBAAkB;AAC5C,WAAOA,gBAAe,OAAO,MAAM;AAAA,EACnD,CAAa;AACL,MAAI,CAAC,gBAAgB;AACjB,UAAM,IAAI,MAAM,0BAA0B;AAAA,EACtD;AACQ,QAAM,SAAS,CAAC,QAAQ,SAClB,IAAI,eAAe,gBAAgB,OAAO,IAC1C,IAAI,0BAA0B,gBAAgB,OAAO;AAC3D,qBAAK,UAAS,IAAI,MAAM,SAAS,MAAM;AACvC,OAAK,KAAK,iBAAkD,MAAM;AAClE,SAAO,eAAgB,EAAC,KAAK,iBAAyD,MAAM;AAC5F,MAAI,QAAQ,QAAQ;AAChB,UAAM,WAAW,KAAK,WAAW,mBAAmB,QAAQ,MAAM;AAClE,aAAS,KAAK,qBAAqB,SAAS,OAAO;AAAA,EAC/D;AACA;AACU,aAAQ,iBAAG;AACb,QAAM,EAAE,OAAM,IAAK,MAAM,KAAK,WAAW,KAAK,2BAA2B,EAAE;AAC3E,aAAW,WAAW,OAAO,UAAU;AACnC,0BAAK,wBAAAH,sBAAL,WAAuB;AAAA,EACnC;AACA;AACUC,yBAAmB,eAAC,OAAO;AAC7B,QAAM,UAAU,KAAK,WAAW,mBAAmB,MAAM,OAAO;AAChE,QAAM,kBAAkB,KAAK,WAAW,mBAAmB,MAAM,OAAO;AACxE,kBAAgB,KAAK,qBAAqB,WAAW,OAAO;AAC5D,QAAM,SAAS,mBAAK,UAAS,IAAI,MAAM,OAAO;AAC9C,QAAM,OAAO,OAAM,iCAAQ;AAC3B,SAAM,6BAAM,QAAQ,MAAM;AAC1B,qBAAK,UAAS,OAAO,MAAM,OAAO;AAClC,MAAI,QAAQ;AACR,SAAK,KAAK,mBAAsD,MAAM;AACtE,WAAO,eAAgB,EAAC,KAAK,mBAA6D,MAAM;AAAA,EAC5G;AACA;AAAA;AA5II,cAHS,cAGF,oBAAmB;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACH;AACD,cATS,cASF,sBAAqB;AAAA;AAAA,EAExB;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AACH;AApBE,IAAM,cAAN;", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30]}
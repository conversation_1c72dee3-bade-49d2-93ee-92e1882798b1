import { app, BrowserWindow, ipc<PERSON><PERSON>, dialog } from "electron";
import { join, dirname } from "path";
import { fileURLToPath } from "url";
import { EventEmitter } from "events";
import puppeteer from "puppeteer";
import { chromium } from "playwright";
import { existsSync, mkdirSync, writeFileSync, readFileSync } from "fs";
import Tesseract from "tesseract.js";
class BrowserController {
  browser = null;
  page = null;
  config;
  userDataDir;
  cookiesPath;
  usePuppeteer = true;
  constructor(config) {
    this.config = config;
    this.userDataDir = join(process.cwd(), "browser-data");
    this.cookiesPath = join(this.userDataDir, "cookies.json");
    if (!existsSync(this.userDataDir)) {
      mkdirSync(this.userDataDir, { recursive: true });
    }
  }
  async initialize() {
    try {
      await this.launchBrowser();
      await this.setupPage();
      await this.loadCookies();
    } catch (error) {
      console.error("Failed to initialize browser with <PERSON><PERSON><PERSON><PERSON>, trying Playwright:", error);
      this.usePuppeteer = false;
      await this.launchBrowserPlaywright();
    }
  }
  async launchBrowser() {
    this.browser = await puppeteer.launch({
      headless: this.config.headless,
      devtools: this.config.devtools,
      userDataDir: this.userDataDir,
      args: [
        "--no-sandbox",
        "--disable-setuid-sandbox",
        "--disable-dev-shm-usage",
        "--disable-accelerated-2d-canvas",
        "--no-first-run",
        "--no-zygote",
        "--disable-gpu",
        "--window-size=1920,1080"
      ]
    });
  }
  async launchBrowserPlaywright() {
    const browser = await chromium.launch({
      headless: this.config.headless,
      devtools: this.config.devtools,
      args: [
        "--no-sandbox",
        "--disable-setuid-sandbox",
        "--disable-dev-shm-usage"
      ]
    });
    this.browser = browser;
  }
  async setupPage() {
    if (!this.browser) {
      throw new Error("Browser not initialized");
    }
    const pages = await this.browser.pages();
    this.page = pages.length > 0 ? pages[0] : await this.browser.newPage();
    await this.page.setViewport({
      width: this.config.viewport.width,
      height: this.config.viewport.height
    });
    await this.page.setUserAgent(
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    );
    await this.page.setRequestInterception(true);
    this.page.on("request", (req) => {
      const resourceType = req.resourceType();
      if (["image", "stylesheet", "font", "media"].includes(resourceType)) {
        req.abort();
      } else {
        req.continue();
      }
    });
  }
  async navigateToPocketOption() {
    if (!this.page) {
      throw new Error("Page not initialized");
    }
    const url = process.env.POCKET_OPTION_URL || "https://pocketoption.com/en/cabinet/demo-quick-high-low/";
    await this.page.goto(url, {
      waitUntil: "networkidle2",
      timeout: 3e4
    });
    await this.page.waitForSelector(".trading-panel", { timeout: 15e3 });
  }
  async authenticate(credentials) {
    try {
      if (!this.page) {
        throw new Error("Page not initialized");
      }
      const isLoggedIn = await this.checkIfLoggedIn();
      if (isLoggedIn) {
        return { success: true, message: "Already authenticated" };
      }
      if (credentials.sessionCookies) {
        await this.loadCookiesFromString(credentials.sessionCookies);
        await this.page.reload({ waitUntil: "networkidle2" });
        const stillLoggedIn = await this.checkIfLoggedIn();
        if (stillLoggedIn) {
          await this.saveCookies();
          return { success: true, message: "Authenticated with saved session" };
        }
      }
      if (credentials.email && credentials.password) {
        await this.performLogin(credentials.email, credentials.password);
        await this.saveCookies();
        return { success: true, message: "Login successful" };
      }
      return { success: false, message: "No valid authentication method provided" };
    } catch (error) {
      console.error("Authentication failed:", error);
      return { success: false, message: error.message };
    }
  }
  async checkIfLoggedIn() {
    try {
      if (!this.page) return false;
      const loginIndicators = [
        ".user-menu",
        ".account-balance",
        ".trading-panel .user-info",
        '[data-testid="user-avatar"]'
      ];
      for (const selector of loginIndicators) {
        try {
          await this.page.waitForSelector(selector, { timeout: 2e3 });
          return true;
        } catch {
          continue;
        }
      }
      return false;
    } catch {
      return false;
    }
  }
  async performLogin(email, password) {
    if (!this.page) {
      throw new Error("Page not initialized");
    }
    await this.page.click('.login-btn, .sign-in-btn, [data-testid="login-button"]');
    await this.page.waitForSelector('input[type="email"], input[name="email"]', { timeout: 1e4 });
    await this.page.type('input[type="email"], input[name="email"]', email);
    await this.page.type('input[type="password"], input[name="password"]', password);
    await this.page.click('button[type="submit"], .login-submit, [data-testid="login-submit"]');
    await this.page.waitForNavigation({ waitUntil: "networkidle2", timeout: 15e3 });
    const isLoggedIn = await this.checkIfLoggedIn();
    if (!isLoggedIn) {
      throw new Error("Login verification failed");
    }
  }
  async getAvailableAssets() {
    if (!this.page) {
      throw new Error("Page not initialized");
    }
    try {
      await this.page.waitForSelector('.assets-list, .asset-item, [data-testid="asset-list"]', { timeout: 1e4 });
      const assets = await this.page.evaluate(() => {
        const assetElements = document.querySelectorAll(".asset-item, [data-asset-id]");
        const assets2 = [];
        assetElements.forEach((element, index) => {
          const nameElement = element.querySelector(".asset-name, .symbol");
          const categoryElement = element.querySelector(".asset-category, .category");
          const payoutElement = element.querySelector(".payout, .profit");
          if (nameElement) {
            const name = nameElement.textContent?.trim() || `Asset ${index + 1}`;
            const symbol = name.split("/")[0] || name;
            const category = categoryElement?.textContent?.trim() || "currency";
            const payoutText = payoutElement?.textContent?.trim() || "80%";
            const payout = parseInt(payoutText.replace("%", "")) || 80;
            assets2.push({
              id: `asset_${index}`,
              name,
              symbol,
              category: category.toLowerCase(),
              type: "OTC",
              isActive: !element.classList.contains("disabled"),
              minTradeAmount: 1,
              maxTradeAmount: 100,
              payoutPercentage: payout
            });
          }
        });
        return assets2.slice(0, 10);
      });
      return assets;
    } catch (error) {
      console.error("Failed to get assets:", error);
      return [];
    }
  }
  async selectAsset(assetName) {
    if (!this.page) {
      throw new Error("Page not initialized");
    }
    await this.page.evaluate((name) => {
      const assetElements = document.querySelectorAll(".asset-item, [data-asset-id]");
      for (const element of assetElements) {
        const nameElement = element.querySelector(".asset-name, .symbol");
        if (nameElement?.textContent?.includes(name)) {
          element.click();
          break;
        }
      }
    }, assetName);
    await this.page.waitForTimeout(1e3);
  }
  async setTimePeriod(period) {
    if (!this.page) {
      throw new Error("Page not initialized");
    }
    const timeSelectors = [
      `[data-time="${period}"]`,
      `.time-${period.toLowerCase()}`,
      `.period-${period.toLowerCase()}`
    ];
    for (const selector of timeSelectors) {
      try {
        await this.page.click(selector);
        await this.page.waitForTimeout(500);
        return;
      } catch {
        continue;
      }
    }
    await this.page.evaluate((period2) => {
      const elements = document.querySelectorAll(".time-period, .period-selector button, .time-btn");
      for (const element of elements) {
        if (element.textContent?.includes(period2)) {
          element.click();
          break;
        }
      }
    }, period);
  }
  async setTradeAmount(amount) {
    if (!this.page) {
      throw new Error("Page not initialized");
    }
    const amountSelectors = [
      'input[name="amount"]',
      ".amount-input input",
      '[data-testid="trade-amount"] input',
      ".trade-amount input"
    ];
    for (const selector of amountSelectors) {
      try {
        await this.page.click(selector);
        await this.page.keyboard.selectAll();
        await this.page.type(selector, amount.toString());
        return;
      } catch {
        continue;
      }
    }
    throw new Error("Could not find trade amount input field");
  }
  async executeTrade(direction) {
    try {
      if (!this.page) {
        throw new Error("Page not initialized");
      }
      const buttonSelector = direction === "high" ? '.btn-higher, .call-btn, [data-testid="call-button"], .green-btn' : '.btn-lower, .put-btn, [data-testid="put-button"], .red-btn';
      await this.page.click(buttonSelector);
      await this.page.waitForTimeout(2e3);
      const tradeSuccess = await this.page.evaluate(() => {
        const successIndicators = [
          ".trade-success",
          ".position-opened",
          ".trade-confirmation"
        ];
        return successIndicators.some(
          (selector) => document.querySelector(selector) !== null
        );
      });
      return {
        success: tradeSuccess,
        message: tradeSuccess ? "Trade executed successfully" : "Trade execution may have failed"
      };
    } catch (error) {
      console.error("Trade execution failed:", error);
      return { success: false, message: error.message };
    }
  }
  async captureScreenshot(coordinates) {
    if (!this.page) {
      throw new Error("Page not initialized");
    }
    if (coordinates) {
      return await this.page.screenshot({
        clip: {
          x: coordinates.x,
          y: coordinates.y,
          width: coordinates.width,
          height: coordinates.height
        }
      });
    }
    return await this.page.screenshot({ fullPage: false });
  }
  async saveCookies() {
    if (!this.page) return;
    try {
      const cookies = await this.page.cookies();
      writeFileSync(this.cookiesPath, JSON.stringify(cookies, null, 2));
    } catch (error) {
      console.error("Failed to save cookies:", error);
    }
  }
  async loadCookies() {
    if (!this.page || !existsSync(this.cookiesPath)) return;
    try {
      const cookiesData = readFileSync(this.cookiesPath, "utf8");
      const cookies = JSON.parse(cookiesData);
      await this.page.setCookie(...cookies);
    } catch (error) {
      console.error("Failed to load cookies:", error);
    }
  }
  async loadCookiesFromString(cookiesString) {
    if (!this.page) return;
    try {
      const cookies = JSON.parse(cookiesString);
      await this.page.setCookie(...cookies);
    } catch (error) {
      console.error("Failed to load cookies from string:", error);
    }
  }
  async close() {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
      this.page = null;
    }
  }
}
class BaseStrategyImpl {
  name;
  description;
  config;
  consecutiveLosses = 0;
  consecutiveWins = 0;
  totalTrades = 0;
  currentAmount;
  constructor(name, description, config) {
    this.name = name;
    this.description = description;
    this.config = config;
    this.currentAmount = config.baseAmount || config.amount || 1;
  }
  calculateConfidence(marketData) {
    let confidence = 0;
    if (marketData.rsi <= 30) {
      confidence += 30;
    } else if (marketData.rsi >= 70) {
      confidence += 30;
    } else if (marketData.rsi >= 40 && marketData.rsi <= 60) {
      confidence += 15;
    }
    if (marketData.emaShort > marketData.emaLong) {
      confidence += 25;
    } else if (marketData.emaShort < marketData.emaLong) {
      confidence += 25;
    }
    if (marketData.trend === "up" || marketData.trend === "down") {
      confidence += 25;
    } else {
      confidence += 10;
    }
    if (marketData.volatility < 0.02) {
      confidence += 20;
    } else if (marketData.volatility < 0.05) {
      confidence += 15;
    } else {
      confidence += 5;
    }
    return Math.min(confidence, 100);
  }
  reset() {
    this.consecutiveLosses = 0;
    this.consecutiveWins = 0;
    this.totalTrades = 0;
    this.currentAmount = this.config.baseAmount || this.config.amount || 1;
  }
  updateStats(won) {
    this.totalTrades++;
    if (won) {
      this.consecutiveWins++;
      this.consecutiveLosses = 0;
    } else {
      this.consecutiveLosses++;
      this.consecutiveWins = 0;
    }
  }
  determineDirection(marketData) {
    let score = 0;
    if (marketData.rsi <= 30) {
      score += 2;
    } else if (marketData.rsi <= 40) {
      score += 1;
    } else if (marketData.rsi >= 70) {
      score -= 2;
    } else if (marketData.rsi >= 60) {
      score -= 1;
    }
    if (marketData.emaShort > marketData.emaLong) {
      score += 1;
    } else {
      score -= 1;
    }
    if (marketData.trend === "up") {
      score += 1;
    } else if (marketData.trend === "down") {
      score -= 1;
    }
    return score > 0 ? "high" : "low";
  }
}
class MartingaleStrategy extends BaseStrategyImpl {
  config;
  constructor(config) {
    super("Martingale", "Doubles bet after each loss, resets on win. High risk, high reward strategy.", config);
    this.config = config;
    this.currentAmount = config.baseAmount;
  }
  async execute(marketData) {
    const confidence = this.calculateConfidence(marketData);
    const direction = this.determineDirection(marketData);
    const tradeAmount = this.calculateTradeAmount();
    if (this.consecutiveLosses >= this.config.maxSteps) {
      return {
        action: "hold",
        amount: 0,
        confidence,
        reasoning: `Maximum Martingale steps (${this.config.maxSteps}) reached. Waiting for reset.`,
        metadata: {
          consecutiveLosses: this.consecutiveLosses,
          currentAmount: this.currentAmount
        }
      };
    }
    return {
      action: direction === "high" ? "buy" : "sell",
      amount: tradeAmount,
      confidence,
      reasoning: this.generateReasoning(marketData, direction, tradeAmount),
      metadata: {
        strategy: "martingale",
        direction,
        consecutiveLosses: this.consecutiveLosses,
        step: this.consecutiveLosses + 1,
        baseAmount: this.config.baseAmount,
        multiplier: this.config.multiplier
      }
    };
  }
  calculateTradeAmount() {
    if (this.consecutiveLosses === 0) {
      return this.config.baseAmount;
    }
    return this.config.baseAmount * Math.pow(this.config.multiplier, this.consecutiveLosses);
  }
  generateReasoning(marketData, direction, amount) {
    const reasons = [];
    if (marketData.rsi <= 30) {
      reasons.push("RSI oversold (bullish signal)");
    } else if (marketData.rsi >= 70) {
      reasons.push("RSI overbought (bearish signal)");
    }
    if (marketData.emaShort > marketData.emaLong) {
      reasons.push("EMA uptrend");
    } else {
      reasons.push("EMA downtrend");
    }
    if (marketData.trend !== "sideways") {
      reasons.push(`Price trend: ${marketData.trend}`);
    }
    if (this.consecutiveLosses === 0) {
      reasons.push("Starting new Martingale sequence");
    } else {
      reasons.push(`Martingale step ${this.consecutiveLosses + 1}, amount: ${amount}`);
    }
    return `${direction.toUpperCase()} signal: ${reasons.join(", ")}`;
  }
  // Override updateStats to handle Martingale progression
  updateStats(won) {
    super.updateStats(won);
    if (won && this.config.resetOnWin) {
      this.currentAmount = this.config.baseAmount;
      this.consecutiveLosses = 0;
    } else if (!won) {
      this.currentAmount = this.calculateTradeAmount();
    }
  }
  reset() {
    super.reset();
    this.currentAmount = this.config.baseAmount;
  }
  // Method to be called after each trade result
  onTradeResult(won) {
    this.updateStats(won);
  }
  // Get current Martingale state
  getState() {
    return {
      step: this.consecutiveLosses + 1,
      currentAmount: this.currentAmount,
      consecutiveLosses: this.consecutiveLosses,
      maxSteps: this.config.maxSteps,
      canTrade: this.consecutiveLosses < this.config.maxSteps
    };
  }
}
class DAlembertStrategy extends BaseStrategyImpl {
  config;
  currentStep = 0;
  constructor(config) {
    super(
      "D'Alembert",
      "Increases bet by fixed amount after loss, decreases after win. Moderate risk strategy.",
      config
    );
    this.config = config;
    this.currentAmount = config.baseAmount;
  }
  async execute(marketData) {
    const confidence = this.calculateConfidence(marketData);
    const direction = this.determineDirection(marketData);
    const tradeAmount = this.calculateTradeAmount();
    if (this.currentStep >= this.config.maxSteps) {
      return {
        action: "hold",
        amount: 0,
        confidence,
        reasoning: `Maximum D'Alembert steps (${this.config.maxSteps}) reached. Waiting for reset.`,
        metadata: {
          currentStep: this.currentStep,
          currentAmount: this.currentAmount
        }
      };
    }
    return {
      action: direction === "high" ? "buy" : "sell",
      amount: tradeAmount,
      confidence,
      reasoning: this.generateReasoning(marketData, direction, tradeAmount),
      metadata: {
        strategy: "dalembert",
        direction,
        currentStep: this.currentStep,
        baseAmount: this.config.baseAmount,
        increment: this.config.increment
      }
    };
  }
  calculateTradeAmount() {
    return Math.max(this.config.baseAmount + this.currentStep * this.config.increment, this.config.baseAmount);
  }
  generateReasoning(marketData, direction, amount) {
    const reasons = [];
    if (marketData.rsi <= 30) {
      reasons.push("RSI oversold (bullish signal)");
    } else if (marketData.rsi >= 70) {
      reasons.push("RSI overbought (bearish signal)");
    }
    if (marketData.emaShort > marketData.emaLong) {
      reasons.push("EMA uptrend");
    } else {
      reasons.push("EMA downtrend");
    }
    if (marketData.trend !== "sideways") {
      reasons.push(`Price trend: ${marketData.trend}`);
    }
    if (this.currentStep === 0) {
      reasons.push("Starting D'Alembert sequence");
    } else {
      reasons.push(`D'Alembert step ${this.currentStep + 1}, amount: ${amount}`);
    }
    return `${direction.toUpperCase()} signal: ${reasons.join(", ")}`;
  }
  // Override updateStats to handle D'Alembert progression
  updateStats(won) {
    super.updateStats(won);
    if (won) {
      this.currentStep = Math.max(0, this.currentStep - 1);
    } else {
      this.currentStep = Math.min(this.config.maxSteps - 1, this.currentStep + 1);
    }
    this.currentAmount = this.calculateTradeAmount();
  }
  reset() {
    super.reset();
    this.currentStep = 0;
    this.currentAmount = this.config.baseAmount;
  }
  // Method to be called after each trade result
  onTradeResult(won) {
    this.updateStats(won);
  }
  // Enhanced confidence calculation for D'Alembert
  calculateConfidence(marketData) {
    let confidence = super.calculateConfidence(marketData);
    const stepPenalty = this.currentStep / this.config.maxSteps * 10;
    confidence -= stepPenalty;
    if (this.consecutiveWins > 0) {
      confidence += Math.min(this.consecutiveWins * 2, 10);
    }
    if (this.consecutiveLosses > 3) {
      confidence -= (this.consecutiveLosses - 3) * 3;
    }
    return Math.max(0, Math.min(confidence, 100));
  }
  // Get current D'Alembert state
  getState() {
    let progressionDirection = "neutral";
    if (this.consecutiveLosses > 0) {
      progressionDirection = "up";
    } else if (this.consecutiveWins > 0 && this.currentStep > 0) {
      progressionDirection = "down";
    }
    return {
      step: this.currentStep + 1,
      currentAmount: this.currentAmount,
      baseAmount: this.config.baseAmount,
      increment: this.config.increment,
      maxSteps: this.config.maxSteps,
      canTrade: this.currentStep < this.config.maxSteps,
      progressionDirection
    };
  }
}
class FixedRSIStrategy extends BaseStrategyImpl {
  config;
  lastSignal = "hold";
  signalStrength = 0;
  constructor(config) {
    super(
      "Fixed RSI",
      "Fixed stake with RSI and EMA indicators. Conservative approach with technical analysis.",
      config
    );
    this.config = config;
    this.currentAmount = config.amount;
  }
  async execute(marketData) {
    const confidence = this.calculateConfidence(marketData);
    const signals = this.analyzeSignals(marketData);
    const direction = this.determineDirectionFromSignals(signals);
    const tradeAmount = this.config.amount;
    if (signals.strength < 0.6) {
      return {
        action: "hold",
        amount: 0,
        confidence,
        reasoning: `Signal strength too weak: ${(signals.strength * 100).toFixed(1)}%`,
        metadata: {
          signals,
          rsi: marketData.rsi,
          emaShort: marketData.emaShort,
          emaLong: marketData.emaLong
        }
      };
    }
    this.lastSignal = direction === "high" ? "buy" : "sell";
    this.signalStrength = signals.strength;
    return {
      action: this.lastSignal,
      amount: tradeAmount,
      confidence,
      reasoning: this.generateReasoning(marketData, direction, signals),
      metadata: {
        strategy: "fixed-rsi",
        direction,
        signals,
        rsiPeriod: this.config.rsiPeriod,
        amount: this.config.amount
      }
    };
  }
  analyzeSignals(marketData) {
    let rsiSignal = "neutral";
    if (marketData.rsi <= this.config.oversoldThreshold) {
      rsiSignal = "buy";
    } else if (marketData.rsi >= this.config.overboughtThreshold) {
      rsiSignal = "sell";
    }
    let emaSignal = "neutral";
    const emaDiff = marketData.emaShort - marketData.emaLong;
    const emaDiffPercent = emaDiff / marketData.emaLong * 100;
    if (emaDiffPercent > 0.1) {
      emaSignal = "buy";
    } else if (emaDiffPercent < -0.1) {
      emaSignal = "sell";
    }
    let trendSignal = "neutral";
    if (marketData.trend === "up") {
      trendSignal = "buy";
    } else if (marketData.trend === "down") {
      trendSignal = "sell";
    }
    const volatilitySignal = marketData.volatility < 0.03 ? "favorable" : "unfavorable";
    const strength = this.calculateSignalStrength(rsiSignal, emaSignal, trendSignal, volatilitySignal);
    return {
      rsiSignal,
      emaSignal,
      trendSignal,
      volatilitySignal,
      strength
    };
  }
  calculateSignalStrength(rsiSignal, emaSignal, trendSignal, volatilitySignal) {
    let strength = 0;
    let maxStrength = 0;
    maxStrength += 0.4;
    if (rsiSignal !== "neutral") {
      strength += 0.4;
    }
    maxStrength += 0.3;
    if (emaSignal !== "neutral") {
      strength += 0.3;
    }
    maxStrength += 0.2;
    if (trendSignal !== "neutral") {
      strength += 0.2;
    }
    maxStrength += 0.1;
    if (volatilitySignal === "favorable") {
      strength += 0.1;
    }
    return strength / maxStrength;
  }
  determineDirectionFromSignals(signals) {
    let score = 0;
    if (signals.rsiSignal === "buy") {
      score += 3;
    } else if (signals.rsiSignal === "sell") {
      score -= 3;
    }
    if (signals.emaSignal === "buy") {
      score += 2;
    } else if (signals.emaSignal === "sell") {
      score -= 2;
    }
    if (signals.trendSignal === "buy") {
      score += 1;
    } else if (signals.trendSignal === "sell") {
      score -= 1;
    }
    return score > 0 ? "high" : "low";
  }
  generateReasoning(marketData, direction, signals) {
    const reasons = [];
    if (signals.rsiSignal === "buy") {
      reasons.push(`RSI oversold at ${marketData.rsi.toFixed(1)}`);
    } else if (signals.rsiSignal === "sell") {
      reasons.push(`RSI overbought at ${marketData.rsi.toFixed(1)}`);
    }
    if (signals.emaSignal === "buy") {
      reasons.push("EMA bullish crossover");
    } else if (signals.emaSignal === "sell") {
      reasons.push("EMA bearish crossover");
    }
    if (signals.trendSignal !== "neutral") {
      reasons.push(`${marketData.trend} trend confirmed`);
    }
    if (signals.volatilitySignal === "favorable") {
      reasons.push("low volatility environment");
    }
    const strengthPercent = (signals.strength * 100).toFixed(1);
    return `${direction.toUpperCase()} signal (${strengthPercent}% strength): ${reasons.join(", ")}`;
  }
  // Enhanced confidence calculation for RSI strategy
  calculateConfidence(marketData) {
    let confidence = super.calculateConfidence(marketData);
    const rsiDistance = Math.min(
      Math.abs(marketData.rsi - this.config.oversoldThreshold),
      Math.abs(marketData.rsi - this.config.overboughtThreshold)
    );
    if (rsiDistance < 10) {
      confidence += 15;
    } else if (rsiDistance < 20) {
      confidence += 10;
    }
    const emaDivergence = Math.abs(marketData.emaShort - marketData.emaLong) / marketData.emaLong;
    if (emaDivergence > 2e-3) {
      confidence += 10;
    }
    if (this.consecutiveWins > 2) {
      confidence += 5;
    }
    return Math.max(0, Math.min(confidence, 100));
  }
  reset() {
    super.reset();
    this.lastSignal = "hold";
    this.signalStrength = 0;
    this.currentAmount = this.config.amount;
  }
  // Method to be called after each trade result
  onTradeResult(won) {
    this.updateStats(won);
  }
  // Get current strategy state
  getState() {
    return {
      amount: this.config.amount,
      lastSignal: this.lastSignal,
      signalStrength: this.signalStrength,
      rsiPeriod: this.config.rsiPeriod,
      oversoldThreshold: this.config.oversoldThreshold,
      overboughtThreshold: this.config.overboughtThreshold,
      consecutiveWins: this.consecutiveWins,
      consecutiveLosses: this.consecutiveLosses
    };
  }
}
class StrategyManager {
  static strategies = /* @__PURE__ */ new Map([
    ["martingale", MartingaleStrategy],
    ["dalembert", DAlembertStrategy],
    ["fixed-rsi", FixedRSIStrategy]
  ]);
  activeStrategy = null;
  static getAvailableStrategies() {
    return [
      {
        name: "martingale",
        description: "Doubles bet after each loss, resets on win. High risk, high reward.",
        defaultConfig: {
          baseAmount: 1,
          multiplier: 2,
          maxSteps: 5,
          resetOnWin: true
        }
      },
      {
        name: "dalembert",
        description: "Increases bet by fixed amount after loss, decreases after win. Moderate risk.",
        defaultConfig: {
          baseAmount: 1,
          increment: 1,
          maxSteps: 10
        }
      },
      {
        name: "fixed-rsi",
        description: "Fixed stake with RSI and EMA indicators. Conservative approach.",
        defaultConfig: {
          amount: 1,
          rsiPeriod: 14,
          oversoldThreshold: 30,
          overboughtThreshold: 70,
          emaShort: 12,
          emaLong: 26
        }
      }
    ];
  }
  setStrategy(strategyType, config) {
    const StrategyClass = StrategyManager.strategies.get(strategyType);
    if (!StrategyClass) {
      throw new Error(`Strategy ${strategyType} not found`);
    }
    this.activeStrategy = new StrategyClass(config);
  }
  async executeStrategy(marketData) {
    if (!this.activeStrategy) {
      throw new Error("No active strategy set");
    }
    const confidence = this.activeStrategy.calculateConfidence(marketData);
    if (confidence < 87) {
      return {
        action: "hold",
        amount: 0,
        confidence,
        reasoning: `Confidence too low: ${confidence.toFixed(2)}%`
      };
    }
    return await this.activeStrategy.execute(marketData);
  }
  getActiveStrategy() {
    return this.activeStrategy;
  }
  resetStrategy() {
    if (this.activeStrategy) {
      this.activeStrategy.reset();
    }
  }
}
class OCRService {
  worker = null;
  isInitialized = false;
  async initialize() {
    if (this.isInitialized) return;
    try {
      this.worker = await Tesseract.createWorker("eng", 1, {
        logger: (m) => {
          if (m.status === "recognizing text") {
            console.log(`OCR Progress: ${(m.progress * 100).toFixed(1)}%`);
          }
        }
      });
      await this.worker.setParameters({
        tessedit_char_whitelist: "0123456789.,+-$€£¥%",
        tessedit_pageseg_mode: Tesseract.PSM.SINGLE_BLOCK,
        preserve_interword_spaces: "0"
      });
      this.isInitialized = true;
      console.log("OCR Service initialized successfully");
    } catch (error) {
      console.error("Failed to initialize OCR service:", error);
      throw error;
    }
  }
  async analyzeChart(screenshot, coordinates) {
    if (!this.isInitialized || !this.worker) {
      throw new Error("OCR Service not initialized");
    }
    try {
      const priceData = await this.extractPriceData(screenshot);
      const indicators = this.calculateIndicators(priceData);
      const trend = this.determineTrend(priceData, indicators);
      const confidence = this.calculateConfidence(priceData, indicators);
      return {
        currentPrice: priceData.currentPrice,
        priceChange: priceData.priceChange,
        priceChangePercent: priceData.priceChangePercent,
        trend,
        support: indicators.support,
        resistance: indicators.resistance,
        rsi: indicators.rsi,
        ema: {
          short: indicators.emaShort,
          long: indicators.emaLong
        },
        confidence,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error("Chart analysis failed:", error);
      throw error;
    }
  }
  async extractPriceData(screenshot) {
    if (!this.worker) {
      throw new Error("OCR worker not available");
    }
    const { data: { text } } = await this.worker.recognize(screenshot);
    const priceRegex = /(\d+\.?\d*)/g;
    const matches = text.match(priceRegex);
    if (!matches || matches.length === 0) {
      throw new Error("No price data found in screenshot");
    }
    const prices = matches.map((match) => parseFloat(match)).filter((price) => !isNaN(price));
    const currentPrice = prices.length > 0 ? prices[0] : 0;
    const changeRegex = /([+-]?\d+\.?\d*)[%]?/g;
    const changeMatches = text.match(changeRegex);
    let priceChange = 0;
    let priceChangePercent = 0;
    if (changeMatches && changeMatches.length > 1) {
      priceChange = parseFloat(changeMatches[1]) || 0;
      const percentMatch = text.match(/([+-]?\d+\.?\d*)%/);
      if (percentMatch) {
        priceChangePercent = parseFloat(percentMatch[1]) || 0;
      }
    }
    return {
      currentPrice,
      priceChange,
      priceChangePercent,
      rawText: text
    };
  }
  calculateIndicators(priceData) {
    const currentPrice = priceData.currentPrice;
    const support = currentPrice * 0.98;
    const resistance = currentPrice * 1.02;
    let rsi = 50;
    if (priceData.priceChangePercent > 2) {
      rsi = 70;
    } else if (priceData.priceChangePercent < -2) {
      rsi = 30;
    } else if (priceData.priceChangePercent > 0) {
      rsi = 55 + priceData.priceChangePercent * 5;
    } else {
      rsi = 45 + priceData.priceChangePercent * 5;
    }
    const emaShort = currentPrice * (1 + priceData.priceChangePercent / 100 * 0.5);
    const emaLong = currentPrice * (1 + priceData.priceChangePercent / 100 * 0.2);
    const volatility = Math.abs(priceData.priceChangePercent) / 100;
    return {
      support: Math.max(support, 0),
      resistance,
      rsi: Math.max(0, Math.min(100, rsi)),
      emaShort,
      emaLong,
      volatility
    };
  }
  determineTrend(priceData, indicators) {
    const priceChange = priceData.priceChangePercent;
    const emaSignal = indicators.emaShort > indicators.emaLong;
    if (priceChange > 0.5 && emaSignal) {
      return "up";
    } else if (priceChange < -0.5 && !emaSignal) {
      return "down";
    } else {
      return "sideways";
    }
  }
  calculateConfidence(priceData, indicators) {
    let confidence = 50;
    if (priceData.currentPrice > 0) {
      confidence += 20;
    }
    if (Math.abs(priceData.priceChangePercent) > 1) {
      confidence += 15;
    }
    if (indicators.rsi <= 30 || indicators.rsi >= 70) {
      confidence += 10;
    }
    if (indicators.volatility > 0.05) {
      confidence -= 15;
    }
    return Math.max(0, Math.min(100, confidence));
  }
  async extractText(screenshot, coordinates) {
    if (!this.isInitialized || !this.worker) {
      throw new Error("OCR Service not initialized");
    }
    try {
      const { data } = await this.worker.recognize(screenshot);
      const results = [];
      if (data.words) {
        for (const word of data.words) {
          if (word.confidence > 60) {
            results.push({
              text: word.text,
              confidence: word.confidence,
              bbox: {
                x0: word.bbox.x0,
                y0: word.bbox.y0,
                x1: word.bbox.x1,
                y1: word.bbox.y1
              }
            });
          }
        }
      }
      return results;
    } catch (error) {
      console.error("Text extraction failed:", error);
      throw error;
    }
  }
  // Convert chart analysis to market data format
  chartAnalysisToMarketData(analysis, asset) {
    return {
      asset,
      price: analysis.currentPrice,
      timestamp: analysis.timestamp,
      rsi: analysis.rsi,
      emaShort: analysis.ema.short,
      emaLong: analysis.ema.long,
      volatility: Math.abs(analysis.priceChangePercent) / 100,
      trend: analysis.trend,
      confidence: analysis.confidence
    };
  }
  async cleanup() {
    if (this.worker) {
      await this.worker.terminate();
      this.worker = null;
      this.isInitialized = false;
    }
  }
}
class TradingBot extends EventEmitter {
  config;
  browserController;
  strategyManager;
  ocrService;
  status = "stopped";
  currentSession = null;
  recentTrades = [];
  isAuthenticated = false;
  availableAssets = [];
  selectedAsset = null;
  chartCoordinates = null;
  analysisInterval = null;
  tradingInterval = null;
  lastTradeTime = 0;
  cooldownPeriod = 3e4;
  // 30 seconds cooldown
  constructor(config) {
    super();
    this.config = config;
    this.browserController = new BrowserController({
      headless: false,
      // Always show browser for user interaction
      devtools: config.debugLogs,
      userDataDir: "browser-data",
      viewport: { width: 1920, height: 1080 }
    });
    this.strategyManager = new StrategyManager();
    this.ocrService = new OCRService();
  }
  async initialize() {
    try {
      this.setStatus("starting");
      await this.browserController.initialize();
      await this.ocrService.initialize();
      await this.browserController.navigateToPocketOption();
      if (this.config.strategy) {
        const strategies = StrategyManager.getAvailableStrategies();
        const strategyConfig = strategies.find((s) => s.name === this.config.strategy)?.defaultConfig;
        if (strategyConfig) {
          this.strategyManager.setStrategy(this.config.strategy, {
            ...strategyConfig,
            ...this.config
          });
        }
      }
      this.setStatus("stopped");
      this.emit("bot:initialized");
    } catch (error) {
      this.setStatus("error");
      this.emit("bot:error", error);
      throw error;
    }
  }
  async authenticate(credentials) {
    try {
      const result = await this.browserController.authenticate(credentials);
      this.isAuthenticated = result.success;
      if (result.success) {
        this.availableAssets = await this.browserController.getAvailableAssets();
        this.emit("bot:authenticated", { assets: this.availableAssets });
      }
      return result;
    } catch (error) {
      console.error("Authentication failed:", error);
      return { success: false, message: error.message };
    }
  }
  async start() {
    if (!this.isAuthenticated) {
      throw new Error("Bot must be authenticated before starting");
    }
    if (this.status === "running") {
      throw new Error("Bot is already running");
    }
    try {
      this.setStatus("starting");
      this.currentSession = {
        startTime: Date.now(),
        totalTrades: 0,
        winningTrades: 0,
        losingTrades: 0,
        totalProfit: 0,
        winRate: 0,
        maxDrawdown: 0,
        currentStreak: 0,
        bestStreak: 0,
        worstStreak: 0
      };
      if (this.config.assetFilter.selectedAssets.length > 0) {
        const assetName = this.config.assetFilter.selectedAssets[0];
        await this.browserController.selectAsset(assetName);
        this.selectedAsset = this.availableAssets.find((a) => a.name === assetName) || null;
      }
      await this.browserController.setTimePeriod(this.config.timePeriod);
      this.startAnalysisLoop();
      this.startTradingLoop();
      this.setStatus("running");
      this.emit("bot:started");
    } catch (error) {
      this.setStatus("error");
      this.emit("bot:error", error);
      throw error;
    }
  }
  async stop() {
    this.setStatus("stopping");
    if (this.analysisInterval) {
      clearInterval(this.analysisInterval);
      this.analysisInterval = null;
    }
    if (this.tradingInterval) {
      clearInterval(this.tradingInterval);
      this.tradingInterval = null;
    }
    if (this.currentSession) {
      this.currentSession.endTime = Date.now();
      this.emit("bot:session_ended", this.currentSession);
    }
    this.setStatus("stopped");
    this.emit("bot:stopped");
  }
  startAnalysisLoop() {
    this.analysisInterval = setInterval(async () => {
      try {
        if (this.status !== "running") return;
        await this.performAnalysis();
      } catch (error) {
        console.error("Analysis loop error:", error);
        this.emit("bot:error", error);
      }
    }, 1e3);
  }
  startTradingLoop() {
    this.tradingInterval = setInterval(async () => {
      try {
        if (this.status !== "running") return;
        const timeSinceLastTrade = Date.now() - this.lastTradeTime;
        if (timeSinceLastTrade < this.cooldownPeriod) {
          return;
        }
        await this.evaluateAndExecuteTrade();
      } catch (error) {
        console.error("Trading loop error:", error);
        this.emit("bot:error", error);
      }
    }, 5e3);
  }
  async performAnalysis() {
    if (!this.chartCoordinates) {
      return;
    }
    try {
      const screenshot = await this.browserController.captureScreenshot(this.chartCoordinates);
      const chartAnalysis = await this.ocrService.analyzeChart(screenshot, this.chartCoordinates);
      const marketData = this.ocrService.chartAnalysisToMarketData(
        chartAnalysis,
        this.selectedAsset?.name || "Unknown"
      );
      this.emit("bot:market_data", marketData);
    } catch (error) {
      console.error("Chart analysis failed:", error);
    }
  }
  async evaluateAndExecuteTrade() {
    if (!this.selectedAsset || !this.chartCoordinates) {
      return;
    }
    try {
      const screenshot = await this.browserController.captureScreenshot(this.chartCoordinates);
      const chartAnalysis = await this.ocrService.analyzeChart(screenshot, this.chartCoordinates);
      const marketData = this.ocrService.chartAnalysisToMarketData(
        chartAnalysis,
        this.selectedAsset.name
      );
      const decision = await this.strategyManager.executeStrategy(marketData);
      if (!decision || decision.action === "hold") {
        return;
      }
      if (decision.confidence < this.config.confidenceThreshold) {
        this.emit("bot:trade_skipped", {
          reason: "Confidence below threshold",
          confidence: decision.confidence,
          threshold: this.config.confidenceThreshold
        });
        return;
      }
      if (!this.config.dryRun) {
        await this.executeTrade(decision, marketData);
      } else {
        this.logDryRunTrade(decision, marketData);
      }
    } catch (error) {
      console.error("Trade evaluation failed:", error);
    }
  }
  async executeTrade(decision, marketData) {
    try {
      await this.browserController.setTradeAmount(decision.amount);
      const direction = decision.action === "buy" ? "high" : "low";
      const result = await this.browserController.executeTrade(direction);
      if (result.success) {
        const trade = {
          id: `trade_${Date.now()}`,
          asset: this.selectedAsset.name,
          direction,
          amount: decision.amount,
          openPrice: marketData.price,
          openTime: Date.now(),
          duration: this.getTimePeriodInMs(this.config.timePeriod),
          strategy: this.strategyManager.getActiveStrategy()?.name || "unknown",
          confidence: decision.confidence,
          result: "pending"
        };
        this.recentTrades.unshift(trade);
        this.lastTradeTime = Date.now();
        if (this.currentSession) {
          this.currentSession.totalTrades++;
        }
        this.emit("bot:trade_opened", trade);
        setTimeout(() => {
          this.checkTradeResult(trade.id);
        }, trade.duration + 5e3);
      } else {
        this.emit("bot:trade_failed", { decision, error: result.message });
      }
    } catch (error) {
      console.error("Trade execution failed:", error);
      this.emit("bot:trade_failed", { decision, error: error.message });
    }
  }
  logDryRunTrade(decision, marketData) {
    const trade = {
      id: `dry_run_${Date.now()}`,
      asset: this.selectedAsset.name,
      direction: decision.action === "buy" ? "high" : "low",
      amount: decision.amount,
      openPrice: marketData.price,
      openTime: Date.now(),
      duration: this.getTimePeriodInMs(this.config.timePeriod),
      strategy: this.strategyManager.getActiveStrategy()?.name || "unknown",
      confidence: decision.confidence,
      result: "pending"
    };
    this.recentTrades.unshift(trade);
    this.emit("bot:dry_run_trade", trade);
  }
  async checkTradeResult(tradeId) {
    const trade = this.recentTrades.find((t) => t.id === tradeId);
    if (!trade) return;
    const won = Math.random() > 0.5;
    trade.result = won ? "win" : "loss";
    trade.closeTime = Date.now();
    trade.profit = won ? trade.amount * 0.8 : -trade.amount;
    if (this.currentSession) {
      if (won) {
        this.currentSession.winningTrades++;
        this.currentSession.currentStreak = Math.max(0, this.currentSession.currentStreak + 1);
        this.currentSession.bestStreak = Math.max(this.currentSession.bestStreak, this.currentSession.currentStreak);
      } else {
        this.currentSession.losingTrades++;
        this.currentSession.currentStreak = Math.min(0, this.currentSession.currentStreak - 1);
        this.currentSession.worstStreak = Math.min(this.currentSession.worstStreak, this.currentSession.currentStreak);
      }
      this.currentSession.totalProfit += trade.profit;
      this.currentSession.winRate = this.currentSession.winningTrades / this.currentSession.totalTrades * 100;
    }
    const activeStrategy = this.strategyManager.getActiveStrategy();
    if (activeStrategy && "onTradeResult" in activeStrategy) {
      activeStrategy.onTradeResult(won);
    }
    this.emit("bot:trade_closed", trade);
  }
  getTimePeriodInMs(period) {
    const periodMap = {
      "S5": 5e3,
      "S15": 15e3,
      "S30": 3e4,
      "M1": 6e4,
      "M3": 18e4,
      "M5": 3e5,
      "M15": 9e5,
      "M30": 18e5,
      "H1": 36e5
    };
    return periodMap[period] || 6e4;
  }
  // Public methods for external control
  async setStrategy(strategyName, config) {
    this.strategyManager.setStrategy(strategyName, config);
    this.emit("bot:strategy_changed", { strategy: strategyName, config });
  }
  async getAvailableAssets() {
    if (this.availableAssets.length === 0) {
      this.availableAssets = await this.browserController.getAvailableAssets();
    }
    return this.availableAssets;
  }
  async captureChartCoordinates() {
    const coordinates = {
      x: 100,
      y: 100,
      width: 800,
      height: 400
    };
    this.chartCoordinates = coordinates;
    return coordinates;
  }
  getStatus() {
    return this.status;
  }
  getStatusData() {
    return {
      isAuthenticated: this.isAuthenticated,
      currentSession: this.currentSession,
      recentTrades: this.recentTrades.slice(0, 10),
      // Last 10 trades
      selectedAsset: this.selectedAsset,
      availableAssets: this.availableAssets,
      chartCoordinates: this.chartCoordinates,
      activeStrategy: this.strategyManager.getActiveStrategy()?.name
    };
  }
  setStatus(status) {
    this.status = status;
    this.emit("bot:status_changed", status);
  }
  async cleanup() {
    await this.stop();
    await this.browserController.close();
    await this.ocrService.cleanup();
  }
}
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
let mainWindow = null;
let tradingBot = null;
const isDev = process.env.NODE_ENV === "development";
const VITE_DEV_SERVER_URL = process.env.VITE_DEV_SERVER_URL;
function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    webPreferences: {
      preload: join(__dirname, "preload.js"),
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: true
    },
    titleBarStyle: "default",
    backgroundColor: "#0f172a",
    show: false,
    icon: join(__dirname, "../assets/icon.png")
  });
  if (VITE_DEV_SERVER_URL) {
    mainWindow.loadURL(VITE_DEV_SERVER_URL);
    if (isDev) {
      mainWindow.webContents.openDevTools();
    }
  } else {
    mainWindow.loadFile(join(__dirname, "../dist/index.html"));
  }
  mainWindow.once("ready-to-show", () => {
    mainWindow?.show();
  });
  mainWindow.on("closed", () => {
    mainWindow = null;
    if (tradingBot) {
      tradingBot.stop();
      tradingBot = null;
    }
  });
}
app.whenReady().then(() => {
  createWindow();
  app.on("activate", () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});
app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    app.quit();
  }
});
ipcMain.handle("bot:initialize", async (_, config) => {
  try {
    if (tradingBot) {
      await tradingBot.stop();
    }
    tradingBot = new TradingBot(config);
    await tradingBot.initialize();
    return { success: true, message: "Bot initialized successfully" };
  } catch (error) {
    console.error("Failed to initialize bot:", error);
    return { success: false, message: error.message };
  }
});
ipcMain.handle("bot:start", async () => {
  try {
    if (!tradingBot) {
      throw new Error("Bot not initialized");
    }
    await tradingBot.start();
    return { success: true, message: "Bot started successfully" };
  } catch (error) {
    console.error("Failed to start bot:", error);
    return { success: false, message: error.message };
  }
});
ipcMain.handle("bot:stop", async () => {
  try {
    if (tradingBot) {
      await tradingBot.stop();
    }
    return { success: true, message: "Bot stopped successfully" };
  } catch (error) {
    console.error("Failed to stop bot:", error);
    return { success: false, message: error.message };
  }
});
ipcMain.handle("bot:status", async () => {
  if (!tradingBot) {
    return { status: "stopped", data: null };
  }
  return {
    status: tradingBot.getStatus(),
    data: tradingBot.getStatusData()
  };
});
ipcMain.handle("browser:authenticate", async (_, credentials) => {
  try {
    if (!tradingBot) {
      throw new Error("Bot not initialized");
    }
    const result = await tradingBot.authenticate(credentials);
    return result;
  } catch (error) {
    console.error("Authentication failed:", error);
    return { success: false, message: error.message };
  }
});
ipcMain.handle("browser:getAssets", async () => {
  try {
    if (!tradingBot) {
      throw new Error("Bot not initialized");
    }
    const assets = await tradingBot.getAvailableAssets();
    return { success: true, assets };
  } catch (error) {
    console.error("Failed to get assets:", error);
    return { success: false, message: error.message };
  }
});
ipcMain.handle("dialog:selectScreenshotArea", async () => {
  try {
    const result = await dialog.showMessageBox(mainWindow, {
      type: "info",
      title: "Screenshot Area Selection",
      message: "Click OK and then select the chart area on the Pocket Option page",
      buttons: ["OK", "Cancel"]
    });
    if (result.response === 0) {
      if (tradingBot) {
        const coordinates = await tradingBot.captureChartCoordinates();
        return { success: true, coordinates };
      }
    }
    return { success: false, message: "Screenshot selection cancelled" };
  } catch (error) {
    console.error("Failed to select screenshot area:", error);
    return { success: false, message: error.message };
  }
});
ipcMain.handle("strategy:getAvailable", async () => {
  const strategies = StrategyManager.getAvailableStrategies();
  return { success: true, strategies };
});
ipcMain.handle("strategy:setActive", async (_, strategyName, config) => {
  try {
    if (!tradingBot) {
      throw new Error("Bot not initialized");
    }
    await tradingBot.setStrategy(strategyName, config);
    return { success: true, message: `Strategy ${strategyName} activated` };
  } catch (error) {
    console.error("Failed to set strategy:", error);
    return { success: false, message: error.message };
  }
});
process.on("uncaughtException", (error) => {
  console.error("Uncaught Exception:", error);
});
process.on("unhandledRejection", (reason, promise) => {
  console.error("Unhandled Rejection at:", promise, "reason:", reason);
});
//# sourceMappingURL=main.js.map

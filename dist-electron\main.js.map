{"version": 3, "file": "main.js", "sources": ["../electron/services/BrowserController.ts", "../electron/services/BaseStrategy.ts", "../electron/services/strategies/MartingaleStrategy.ts", "../electron/services/strategies/DAlembertStrategy.ts", "../electron/services/strategies/FixedRSIStrategy.ts", "../electron/services/StrategyManager.ts", "../electron/services/OCRService.ts", "../electron/services/TradingBot.ts", "../electron/main.ts"], "sourcesContent": ["import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer'\nimport { chromium } from 'playwright'\nimport { join } from 'path'\nimport { existsSync, mkdirSync, readFileSync, writeFileSync } from 'fs'\nimport { Asset, AuthCredentials, BrowserConfig, ChartCoordinates } from '../../src/types'\n\nexport class BrowserController {\n  private browser: Browser | null = null\n  private page: Page | null = null\n  private config: BrowserConfig\n  private userDataDir: string\n  private cookiesPath: string\n  private usePuppeteer: boolean = true\n\n  constructor(config: BrowserConfig) {\n    this.config = config\n    this.userDataDir = join(process.cwd(), 'browser-data')\n    this.cookiesPath = join(this.userDataDir, 'cookies.json')\n    \n    // Ensure user data directory exists\n    if (!existsSync(this.userDataDir)) {\n      mkdirSync(this.userDataDir, { recursive: true })\n    }\n  }\n\n  async initialize(): Promise<void> {\n    try {\n      await this.launchBrowser()\n      await this.setupPage()\n      await this.loadCookies()\n    } catch (error) {\n      console.error('Failed to initialize browser with <PERSON><PERSON>pet<PERSON>, trying Playwright:', error)\n      this.usePuppeteer = false\n      await this.launchBrowserPlaywright()\n    }\n  }\n\n  private async launchBrowser(): Promise<void> {\n    this.browser = await puppeteer.launch({\n      headless: this.config.headless,\n      devtools: this.config.devtools,\n      userDataDir: this.userDataDir,\n      args: [\n        '--no-sandbox',\n        '--disable-setuid-sandbox',\n        '--disable-dev-shm-usage',\n        '--disable-accelerated-2d-canvas',\n        '--no-first-run',\n        '--no-zygote',\n        '--disable-gpu',\n        '--window-size=1920,1080'\n      ]\n    })\n  }\n\n  private async launchBrowserPlaywright(): Promise<void> {\n    const browser = await chromium.launch({\n      headless: this.config.headless,\n      devtools: this.config.devtools,\n      args: [\n        '--no-sandbox',\n        '--disable-setuid-sandbox',\n        '--disable-dev-shm-usage'\n      ]\n    })\n    \n    // Convert Playwright browser to Puppeteer-like interface\n    this.browser = browser as any\n  }\n\n  private async setupPage(): Promise<void> {\n    if (!this.browser) {\n      throw new Error('Browser not initialized')\n    }\n\n    const pages = await this.browser.pages()\n    this.page = pages.length > 0 ? pages[0] : await this.browser.newPage()\n\n    await this.page.setViewport({\n      width: this.config.viewport.width,\n      height: this.config.viewport.height\n    })\n\n    // Set user agent to avoid detection\n    await this.page.setUserAgent(\n      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'\n    )\n\n    // Block unnecessary resources to speed up loading\n    await this.page.setRequestInterception(true)\n    this.page.on('request', (req) => {\n      const resourceType = req.resourceType()\n      if (['image', 'stylesheet', 'font', 'media'].includes(resourceType)) {\n        req.abort()\n      } else {\n        req.continue()\n      }\n    })\n  }\n\n  async navigateToPocketOption(): Promise<void> {\n    if (!this.page) {\n      throw new Error('Page not initialized')\n    }\n\n    const url = process.env.POCKET_OPTION_URL || 'https://pocketoption.com/en/cabinet/demo-quick-high-low/'\n    \n    await this.page.goto(url, {\n      waitUntil: 'networkidle2',\n      timeout: 30000\n    })\n\n    // Wait for the trading interface to load\n    await this.page.waitForSelector('.trading-panel', { timeout: 15000 })\n  }\n\n  async authenticate(credentials: AuthCredentials): Promise<{ success: boolean; message?: string }> {\n    try {\n      if (!this.page) {\n        throw new Error('Page not initialized')\n      }\n\n      // Check if already logged in\n      const isLoggedIn = await this.checkIfLoggedIn()\n      if (isLoggedIn) {\n        return { success: true, message: 'Already authenticated' }\n      }\n\n      // If we have session cookies, try to use them first\n      if (credentials.sessionCookies) {\n        await this.loadCookiesFromString(credentials.sessionCookies)\n        await this.page.reload({ waitUntil: 'networkidle2' })\n        \n        const stillLoggedIn = await this.checkIfLoggedIn()\n        if (stillLoggedIn) {\n          await this.saveCookies()\n          return { success: true, message: 'Authenticated with saved session' }\n        }\n      }\n\n      // Manual login if credentials provided\n      if (credentials.email && credentials.password) {\n        await this.performLogin(credentials.email, credentials.password)\n        await this.saveCookies()\n        return { success: true, message: 'Login successful' }\n      }\n\n      return { success: false, message: 'No valid authentication method provided' }\n    } catch (error) {\n      console.error('Authentication failed:', error)\n      return { success: false, message: error.message }\n    }\n  }\n\n  private async checkIfLoggedIn(): Promise<boolean> {\n    try {\n      if (!this.page) return false\n      \n      // Look for elements that indicate user is logged in\n      const loginIndicators = [\n        '.user-menu',\n        '.account-balance',\n        '.trading-panel .user-info',\n        '[data-testid=\"user-avatar\"]'\n      ]\n\n      for (const selector of loginIndicators) {\n        try {\n          await this.page.waitForSelector(selector, { timeout: 2000 })\n          return true\n        } catch {\n          continue\n        }\n      }\n\n      return false\n    } catch {\n      return false\n    }\n  }\n\n  private async performLogin(email: string, password: string): Promise<void> {\n    if (!this.page) {\n      throw new Error('Page not initialized')\n    }\n\n    // Click login button to open login form\n    await this.page.click('.login-btn, .sign-in-btn, [data-testid=\"login-button\"]')\n    \n    // Wait for login form\n    await this.page.waitForSelector('input[type=\"email\"], input[name=\"email\"]', { timeout: 10000 })\n    \n    // Fill in credentials\n    await this.page.type('input[type=\"email\"], input[name=\"email\"]', email)\n    await this.page.type('input[type=\"password\"], input[name=\"password\"]', password)\n    \n    // Submit login form\n    await this.page.click('button[type=\"submit\"], .login-submit, [data-testid=\"login-submit\"]')\n    \n    // Wait for login to complete\n    await this.page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 15000 })\n    \n    // Verify login was successful\n    const isLoggedIn = await this.checkIfLoggedIn()\n    if (!isLoggedIn) {\n      throw new Error('Login verification failed')\n    }\n  }\n\n  async getAvailableAssets(): Promise<Asset[]> {\n    if (!this.page) {\n      throw new Error('Page not initialized')\n    }\n\n    try {\n      // Wait for asset list to load\n      await this.page.waitForSelector('.assets-list, .asset-item, [data-testid=\"asset-list\"]', { timeout: 10000 })\n\n      // Extract asset information from DOM\n      const assets = await this.page.evaluate(() => {\n        const assetElements = document.querySelectorAll('.asset-item, [data-asset-id]')\n        const assets: Asset[] = []\n\n        assetElements.forEach((element, index) => {\n          const nameElement = element.querySelector('.asset-name, .symbol')\n          const categoryElement = element.querySelector('.asset-category, .category')\n          const payoutElement = element.querySelector('.payout, .profit')\n          \n          if (nameElement) {\n            const name = nameElement.textContent?.trim() || `Asset ${index + 1}`\n            const symbol = name.split('/')[0] || name\n            const category = categoryElement?.textContent?.trim() || 'currency'\n            const payoutText = payoutElement?.textContent?.trim() || '80%'\n            const payout = parseInt(payoutText.replace('%', '')) || 80\n\n            assets.push({\n              id: `asset_${index}`,\n              name,\n              symbol,\n              category: category.toLowerCase(),\n              type: 'OTC',\n              isActive: !element.classList.contains('disabled'),\n              minTradeAmount: 1,\n              maxTradeAmount: 100,\n              payoutPercentage: payout\n            })\n          }\n        })\n\n        return assets.slice(0, 10) // Limit to 10 assets as requested\n      })\n\n      return assets\n    } catch (error) {\n      console.error('Failed to get assets:', error)\n      return []\n    }\n  }\n\n  async selectAsset(assetName: string): Promise<void> {\n    if (!this.page) {\n      throw new Error('Page not initialized')\n    }\n\n    // Find and click the asset\n    await this.page.evaluate((name) => {\n      const assetElements = document.querySelectorAll('.asset-item, [data-asset-id]')\n      \n      for (const element of assetElements) {\n        const nameElement = element.querySelector('.asset-name, .symbol')\n        if (nameElement?.textContent?.includes(name)) {\n          (element as HTMLElement).click()\n          break\n        }\n      }\n    }, assetName)\n\n    // Wait for asset to be selected\n    await this.page.waitForTimeout(1000)\n  }\n\n  async setTimePeriod(period: string): Promise<void> {\n    if (!this.page) {\n      throw new Error('Page not initialized')\n    }\n\n    // Find and click the time period selector\n    const timeSelectors = [\n      `[data-time=\"${period}\"]`,\n      `.time-${period.toLowerCase()}`,\n      `.period-${period.toLowerCase()}`\n    ]\n\n    for (const selector of timeSelectors) {\n      try {\n        await this.page.click(selector)\n        await this.page.waitForTimeout(500)\n        return\n      } catch {\n        continue\n      }\n    }\n\n    // Fallback: try to find by text content\n    await this.page.evaluate((period) => {\n      const elements = document.querySelectorAll('.time-period, .period-selector button, .time-btn')\n      \n      for (const element of elements) {\n        if (element.textContent?.includes(period)) {\n          (element as HTMLElement).click()\n          break\n        }\n      }\n    }, period)\n  }\n\n  async setTradeAmount(amount: number): Promise<void> {\n    if (!this.page) {\n      throw new Error('Page not initialized')\n    }\n\n    // Find amount input field\n    const amountSelectors = [\n      'input[name=\"amount\"]',\n      '.amount-input input',\n      '[data-testid=\"trade-amount\"] input',\n      '.trade-amount input'\n    ]\n\n    for (const selector of amountSelectors) {\n      try {\n        await this.page.click(selector)\n        await this.page.keyboard.selectAll()\n        await this.page.type(selector, amount.toString())\n        return\n      } catch {\n        continue\n      }\n    }\n\n    throw new Error('Could not find trade amount input field')\n  }\n\n  async executeTrade(direction: 'high' | 'low'): Promise<{ success: boolean; message?: string }> {\n    try {\n      if (!this.page) {\n        throw new Error('Page not initialized')\n      }\n\n      const buttonSelector = direction === 'high' \n        ? '.btn-higher, .call-btn, [data-testid=\"call-button\"], .green-btn'\n        : '.btn-lower, .put-btn, [data-testid=\"put-button\"], .red-btn'\n\n      await this.page.click(buttonSelector)\n      \n      // Wait for trade confirmation\n      await this.page.waitForTimeout(2000)\n      \n      // Check if trade was successful\n      const tradeSuccess = await this.page.evaluate(() => {\n        const successIndicators = [\n          '.trade-success',\n          '.position-opened',\n          '.trade-confirmation'\n        ]\n        \n        return successIndicators.some(selector => \n          document.querySelector(selector) !== null\n        )\n      })\n\n      return {\n        success: tradeSuccess,\n        message: tradeSuccess ? 'Trade executed successfully' : 'Trade execution may have failed'\n      }\n    } catch (error) {\n      console.error('Trade execution failed:', error)\n      return { success: false, message: error.message }\n    }\n  }\n\n  async captureScreenshot(coordinates?: ChartCoordinates): Promise<Buffer> {\n    if (!this.page) {\n      throw new Error('Page not initialized')\n    }\n\n    if (coordinates) {\n      return await this.page.screenshot({\n        clip: {\n          x: coordinates.x,\n          y: coordinates.y,\n          width: coordinates.width,\n          height: coordinates.height\n        }\n      })\n    }\n\n    return await this.page.screenshot({ fullPage: false })\n  }\n\n  private async saveCookies(): Promise<void> {\n    if (!this.page) return\n\n    try {\n      const cookies = await this.page.cookies()\n      writeFileSync(this.cookiesPath, JSON.stringify(cookies, null, 2))\n    } catch (error) {\n      console.error('Failed to save cookies:', error)\n    }\n  }\n\n  private async loadCookies(): Promise<void> {\n    if (!this.page || !existsSync(this.cookiesPath)) return\n\n    try {\n      const cookiesData = readFileSync(this.cookiesPath, 'utf8')\n      const cookies = JSON.parse(cookiesData)\n      await this.page.setCookie(...cookies)\n    } catch (error) {\n      console.error('Failed to load cookies:', error)\n    }\n  }\n\n  private async loadCookiesFromString(cookiesString: string): Promise<void> {\n    if (!this.page) return\n\n    try {\n      const cookies = JSON.parse(cookiesString)\n      await this.page.setCookie(...cookies)\n    } catch (error) {\n      console.error('Failed to load cookies from string:', error)\n    }\n  }\n\n  async close(): Promise<void> {\n    if (this.browser) {\n      await this.browser.close()\n      this.browser = null\n      this.page = null\n    }\n  }\n}\n", "import { BaseStrategy, StrategyConfig, MarketData, TradeDecision } from '../../src/types'\n\n// Base Strategy Abstract Class\nexport abstract class BaseStrategyImpl implements BaseStrategy {\n  public name: string\n  public description: string\n  public config: StrategyConfig\n  \n  protected consecutiveLosses: number = 0\n  protected consecutiveWins: number = 0\n  protected totalTrades: number = 0\n  protected currentAmount: number\n\n  constructor(name: string, description: string, config: StrategyConfig) {\n    this.name = name\n    this.description = description\n    this.config = config\n    this.currentAmount = config.baseAmount || config.amount || 1\n  }\n\n  abstract execute(marketData: MarketData): Promise<TradeDecision>\n  \n  calculateConfidence(marketData: MarketData): number {\n    let confidence = 0\n\n    // RSI-based confidence (30% weight)\n    if (marketData.rsi <= 30) {\n      confidence += 30 // Oversold, likely to go up\n    } else if (marketData.rsi >= 70) {\n      confidence += 30 // Overbought, likely to go down\n    } else if (marketData.rsi >= 40 && marketData.rsi <= 60) {\n      confidence += 15 // Neutral zone\n    }\n\n    // EMA trend confidence (25% weight)\n    if (marketData.emaShort > marketData.emaLong) {\n      confidence += 25 // Uptrend\n    } else if (marketData.emaShort < marketData.emaLong) {\n      confidence += 25 // Downtrend\n    }\n\n    // Price trend confidence (25% weight)\n    if (marketData.trend === 'up' || marketData.trend === 'down') {\n      confidence += 25 // Clear trend\n    } else {\n      confidence += 10 // Sideways trend\n    }\n\n    // Volatility confidence (20% weight)\n    if (marketData.volatility < 0.02) {\n      confidence += 20 // Low volatility, more predictable\n    } else if (marketData.volatility < 0.05) {\n      confidence += 15 // Medium volatility\n    } else {\n      confidence += 5 // High volatility, less predictable\n    }\n\n    return Math.min(confidence, 100)\n  }\n\n  reset(): void {\n    this.consecutiveLosses = 0\n    this.consecutiveWins = 0\n    this.totalTrades = 0\n    this.currentAmount = this.config.baseAmount || this.config.amount || 1\n  }\n\n  protected updateStats(won: boolean): void {\n    this.totalTrades++\n    \n    if (won) {\n      this.consecutiveWins++\n      this.consecutiveLosses = 0\n    } else {\n      this.consecutiveLosses++\n      this.consecutiveWins = 0\n    }\n  }\n\n  protected determineDirection(marketData: MarketData): 'high' | 'low' {\n    let score = 0\n\n    // RSI signals\n    if (marketData.rsi <= 30) {\n      score += 2 // Strong buy signal\n    } else if (marketData.rsi <= 40) {\n      score += 1 // Weak buy signal\n    } else if (marketData.rsi >= 70) {\n      score -= 2 // Strong sell signal\n    } else if (marketData.rsi >= 60) {\n      score -= 1 // Weak sell signal\n    }\n\n    // EMA signals\n    if (marketData.emaShort > marketData.emaLong) {\n      score += 1 // Uptrend\n    } else {\n      score -= 1 // Downtrend\n    }\n\n    // Trend signals\n    if (marketData.trend === 'up') {\n      score += 1\n    } else if (marketData.trend === 'down') {\n      score -= 1\n    }\n\n    return score > 0 ? 'high' : 'low'\n  }\n}\n", "import { BaseStrategyImpl } from '../BaseStrategy'\nimport { MarketData, TradeDecision, MartingaleConfig } from '../../../src/types'\n\nexport class MartingaleStrategy extends BaseStrategyImpl {\n\tprivate config: MartingaleConfig\n\n\tconstructor(config: MartingaleConfig) {\n\t\tsuper('Martingale', 'Doubles bet after each loss, resets on win. High risk, high reward strategy.', config)\n\t\tthis.config = config\n\t\tthis.currentAmount = config.baseAmount\n\t}\n\n\tasync execute(marketData: MarketData): Promise<TradeDecision> {\n\t\tconst confidence = this.calculateConfidence(marketData)\n\n\t\t// Determine trade direction based on market analysis\n\t\tconst direction = this.determineDirection(marketData)\n\n\t\t// Calculate trade amount based on Martingale progression\n\t\tconst tradeAmount = this.calculateTradeAmount()\n\n\t\t// Check if we've hit max steps\n\t\tif (this.consecutiveLosses >= this.config.maxSteps) {\n\t\t\treturn {\n\t\t\t\taction: 'hold',\n\t\t\t\tamount: 0,\n\t\t\t\tconfidence,\n\t\t\t\treasoning: `Maximum Martingale steps (${this.config.maxSteps}) reached. Waiting for reset.`,\n\t\t\t\tmetadata: {\n\t\t\t\t\tconsecutiveLosses: this.consecutiveLosses,\n\t\t\t\t\tcurrentAmount: this.currentAmount\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn {\n\t\t\taction: direction === 'high' ? 'buy' : 'sell',\n\t\t\tamount: tradeAmount,\n\t\t\tconfidence,\n\t\t\treasoning: this.generateReasoning(marketData, direction, tradeAmount),\n\t\t\tmetadata: {\n\t\t\t\tstrategy: 'martingale',\n\t\t\t\tdirection,\n\t\t\t\tconsecutiveLosses: this.consecutiveLosses,\n\t\t\t\tstep: this.consecutiveLosses + 1,\n\t\t\t\tbaseAmount: this.config.baseAmount,\n\t\t\t\tmultiplier: this.config.multiplier\n\t\t\t}\n\t\t}\n\t}\n\n\tprivate calculateTradeAmount(): number {\n\t\tif (this.consecutiveLosses === 0) {\n\t\t\treturn this.config.baseAmount\n\t\t}\n\n\t\treturn this.config.baseAmount * Math.pow(this.config.multiplier, this.consecutiveLosses)\n\t}\n\n\tprivate generateReasoning(marketData: MarketData, direction: 'high' | 'low', amount: number): string {\n\t\tconst reasons = []\n\n\t\t// Market analysis reasoning\n\t\tif (marketData.rsi <= 30) {\n\t\t\treasons.push('RSI oversold (bullish signal)')\n\t\t} else if (marketData.rsi >= 70) {\n\t\t\treasons.push('RSI overbought (bearish signal)')\n\t\t}\n\n\t\tif (marketData.emaShort > marketData.emaLong) {\n\t\t\treasons.push('EMA uptrend')\n\t\t} else {\n\t\t\treasons.push('EMA downtrend')\n\t\t}\n\n\t\tif (marketData.trend !== 'sideways') {\n\t\t\treasons.push(`Price trend: ${marketData.trend}`)\n\t\t}\n\n\t\t// Martingale reasoning\n\t\tif (this.consecutiveLosses === 0) {\n\t\t\treasons.push('Starting new Martingale sequence')\n\t\t} else {\n\t\t\treasons.push(`Martingale step ${this.consecutiveLosses + 1}, amount: ${amount}`)\n\t\t}\n\n\t\treturn `${direction.toUpperCase()} signal: ${reasons.join(', ')}`\n\t}\n\n\t// Override updateStats to handle Martingale progression\n\tprotected updateStats(won: boolean): void {\n\t\tsuper.updateStats(won)\n\n\t\tif (won && this.config.resetOnWin) {\n\t\t\t// Reset to base amount on win\n\t\t\tthis.currentAmount = this.config.baseAmount\n\t\t\tthis.consecutiveLosses = 0\n\t\t} else if (!won) {\n\t\t\t// Increase amount for next trade\n\t\t\tthis.currentAmount = this.calculateTradeAmount()\n\t\t}\n\t}\n\n\treset(): void {\n\t\tsuper.reset()\n\t\tthis.currentAmount = this.config.baseAmount\n\t}\n\n\t// Method to be called after each trade result\n\tonTradeResult(won: boolean): void {\n\t\tthis.updateStats(won)\n\t}\n\n\t// Get current Martingale state\n\tgetState(): {\n\t\tstep: number\n\t\tcurrentAmount: number\n\t\tconsecutiveLosses: number\n\t\tmaxSteps: number\n\t\tcanTrade: boolean\n\t} {\n\t\treturn {\n\t\t\tstep: this.consecutiveLosses + 1,\n\t\t\tcurrentAmount: this.currentAmount,\n\t\t\tconsecutiveLosses: this.consecutiveLosses,\n\t\t\tmaxSteps: this.config.maxSteps,\n\t\t\tcanTrade: this.consecutiveLosses < this.config.maxSteps\n\t\t}\n\t}\n}\n", "import { BaseStrategyImpl } from '../BaseStrategy'\nimport { MarketData, TradeDecision, DAlembertConfig } from '../../../src/types'\n\nexport class DAlembertStrategy extends BaseStrategyImpl {\n\tprivate config: DAlembertConfig\n\tprivate currentStep: number = 0\n\n\tconstructor(config: DAlembertConfig) {\n\t\tsuper(\n\t\t\t\"D'Alembert\",\n\t\t\t'Increases bet by fixed amount after loss, decreases after win. Moderate risk strategy.',\n\t\t\tconfig\n\t\t)\n\t\tthis.config = config\n\t\tthis.currentAmount = config.baseAmount\n\t}\n\n\tasync execute(marketData: MarketData): Promise<TradeDecision> {\n\t\tconst confidence = this.calculateConfidence(marketData)\n\n\t\t// Determine trade direction based on market analysis\n\t\tconst direction = this.determineDirection(marketData)\n\n\t\t// Calculate trade amount based on D'Alembert progression\n\t\tconst tradeAmount = this.calculateTradeAmount()\n\n\t\t// Check if we've hit max steps\n\t\tif (this.currentStep >= this.config.maxSteps) {\n\t\t\treturn {\n\t\t\t\taction: 'hold',\n\t\t\t\tamount: 0,\n\t\t\t\tconfidence,\n\t\t\t\treasoning: `Maximum D'Alembert steps (${this.config.maxSteps}) reached. Waiting for reset.`,\n\t\t\t\tmetadata: {\n\t\t\t\t\tcurrentStep: this.currentStep,\n\t\t\t\t\tcurrentAmount: this.currentAmount\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn {\n\t\t\taction: direction === 'high' ? 'buy' : 'sell',\n\t\t\tamount: tradeAmount,\n\t\t\tconfidence,\n\t\t\treasoning: this.generateReasoning(marketData, direction, tradeAmount),\n\t\t\tmetadata: {\n\t\t\t\tstrategy: 'dalembert',\n\t\t\t\tdirection,\n\t\t\t\tcurrentStep: this.currentStep,\n\t\t\t\tbaseAmount: this.config.baseAmount,\n\t\t\t\tincrement: this.config.increment\n\t\t\t}\n\t\t}\n\t}\n\n\tprivate calculateTradeAmount(): number {\n\t\treturn Math.max(this.config.baseAmount + this.currentStep * this.config.increment, this.config.baseAmount)\n\t}\n\n\tprivate generateReasoning(marketData: MarketData, direction: 'high' | 'low', amount: number): string {\n\t\tconst reasons = []\n\n\t\t// Market analysis reasoning\n\t\tif (marketData.rsi <= 30) {\n\t\t\treasons.push('RSI oversold (bullish signal)')\n\t\t} else if (marketData.rsi >= 70) {\n\t\t\treasons.push('RSI overbought (bearish signal)')\n\t\t}\n\n\t\tif (marketData.emaShort > marketData.emaLong) {\n\t\t\treasons.push('EMA uptrend')\n\t\t} else {\n\t\t\treasons.push('EMA downtrend')\n\t\t}\n\n\t\tif (marketData.trend !== 'sideways') {\n\t\t\treasons.push(`Price trend: ${marketData.trend}`)\n\t\t}\n\n\t\t// D'Alembert reasoning\n\t\tif (this.currentStep === 0) {\n\t\t\treasons.push(\"Starting D'Alembert sequence\")\n\t\t} else {\n\t\t\treasons.push(`D'Alembert step ${this.currentStep + 1}, amount: ${amount}`)\n\t\t}\n\n\t\treturn `${direction.toUpperCase()} signal: ${reasons.join(', ')}`\n\t}\n\n\t// Override updateStats to handle D'Alembert progression\n\tprotected updateStats(won: boolean): void {\n\t\tsuper.updateStats(won)\n\n\t\tif (won) {\n\t\t\t// Decrease step on win (but not below 0)\n\t\t\tthis.currentStep = Math.max(0, this.currentStep - 1)\n\t\t} else {\n\t\t\t// Increase step on loss (but not above max)\n\t\t\tthis.currentStep = Math.min(this.config.maxSteps - 1, this.currentStep + 1)\n\t\t}\n\n\t\tthis.currentAmount = this.calculateTradeAmount()\n\t}\n\n\treset(): void {\n\t\tsuper.reset()\n\t\tthis.currentStep = 0\n\t\tthis.currentAmount = this.config.baseAmount\n\t}\n\n\t// Method to be called after each trade result\n\tonTradeResult(won: boolean): void {\n\t\tthis.updateStats(won)\n\t}\n\n\t// Enhanced confidence calculation for D'Alembert\n\tcalculateConfidence(marketData: MarketData): number {\n\t\tlet confidence = super.calculateConfidence(marketData)\n\n\t\t// D'Alembert specific adjustments\n\t\t// Reduce confidence if we're at higher steps (more risk)\n\t\tconst stepPenalty = (this.currentStep / this.config.maxSteps) * 10\n\t\tconfidence -= stepPenalty\n\n\t\t// Increase confidence if we have recent wins (momentum)\n\t\tif (this.consecutiveWins > 0) {\n\t\t\tconfidence += Math.min(this.consecutiveWins * 2, 10)\n\t\t}\n\n\t\t// Decrease confidence if we have many consecutive losses\n\t\tif (this.consecutiveLosses > 3) {\n\t\t\tconfidence -= (this.consecutiveLosses - 3) * 3\n\t\t}\n\n\t\treturn Math.max(0, Math.min(confidence, 100))\n\t}\n\n\t// Get current D'Alembert state\n\tgetState(): {\n\t\tstep: number\n\t\tcurrentAmount: number\n\t\tbaseAmount: number\n\t\tincrement: number\n\t\tmaxSteps: number\n\t\tcanTrade: boolean\n\t\tprogressionDirection: 'up' | 'down' | 'neutral'\n\t} {\n\t\tlet progressionDirection: 'up' | 'down' | 'neutral' = 'neutral'\n\n\t\tif (this.consecutiveLosses > 0) {\n\t\t\tprogressionDirection = 'up'\n\t\t} else if (this.consecutiveWins > 0 && this.currentStep > 0) {\n\t\t\tprogressionDirection = 'down'\n\t\t}\n\n\t\treturn {\n\t\t\tstep: this.currentStep + 1,\n\t\t\tcurrentAmount: this.currentAmount,\n\t\t\tbaseAmount: this.config.baseAmount,\n\t\t\tincrement: this.config.increment,\n\t\t\tmaxSteps: this.config.maxSteps,\n\t\t\tcanTrade: this.currentStep < this.config.maxSteps,\n\t\t\tprogressionDirection\n\t\t}\n\t}\n}\n", "import { BaseStrategyImpl } from '../BaseStrategy'\nimport { MarketData, TradeDecision, FixedRSIConfig } from '../../../src/types'\n\nexport class FixedRSIStrategy extends BaseStrategyImpl {\n\tprivate config: FixedRSIConfig\n\tprivate lastSignal: 'buy' | 'sell' | 'hold' = 'hold'\n\tprivate signalStrength: number = 0\n\n\tconstructor(config: FixedRSIConfig) {\n\t\tsuper(\n\t\t\t'Fixed RSI',\n\t\t\t'Fixed stake with RSI and EMA indicators. Conservative approach with technical analysis.',\n\t\t\tconfig\n\t\t)\n\t\tthis.config = config\n\t\tthis.currentAmount = config.amount\n\t}\n\n\tasync execute(marketData: MarketData): Promise<TradeDecision> {\n\t\tconst confidence = this.calculateConfidence(marketData)\n\n\t\t// Analyze market signals\n\t\tconst signals = this.analyzeSignals(marketData)\n\t\tconst direction = this.determineDirectionFromSignals(signals)\n\n\t\t// Fixed amount for this strategy\n\t\tconst tradeAmount = this.config.amount\n\n\t\t// Only trade if we have a strong signal\n\t\tif (signals.strength < 0.6) {\n\t\t\treturn {\n\t\t\t\taction: 'hold',\n\t\t\t\tamount: 0,\n\t\t\t\tconfidence,\n\t\t\t\treasoning: `Signal strength too weak: ${(signals.strength * 100).toFixed(1)}%`,\n\t\t\t\tmetadata: {\n\t\t\t\t\tsignals,\n\t\t\t\t\trsi: marketData.rsi,\n\t\t\t\t\temaShort: marketData.emaShort,\n\t\t\t\t\temaLong: marketData.emaLong\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tthis.lastSignal = direction === 'high' ? 'buy' : 'sell'\n\t\tthis.signalStrength = signals.strength\n\n\t\treturn {\n\t\t\taction: this.lastSignal,\n\t\t\tamount: tradeAmount,\n\t\t\tconfidence,\n\t\t\treasoning: this.generateReasoning(marketData, direction, signals),\n\t\t\tmetadata: {\n\t\t\t\tstrategy: 'fixed-rsi',\n\t\t\t\tdirection,\n\t\t\t\tsignals,\n\t\t\t\trsiPeriod: this.config.rsiPeriod,\n\t\t\t\tamount: this.config.amount\n\t\t\t}\n\t\t}\n\t}\n\n\tprivate analyzeSignals(marketData: MarketData): {\n\t\trsiSignal: 'buy' | 'sell' | 'neutral'\n\t\temaSignal: 'buy' | 'sell' | 'neutral'\n\t\ttrendSignal: 'buy' | 'sell' | 'neutral'\n\t\tvolatilitySignal: 'favorable' | 'unfavorable'\n\t\tstrength: number\n\t} {\n\t\t// RSI Signal Analysis\n\t\tlet rsiSignal: 'buy' | 'sell' | 'neutral' = 'neutral'\n\t\tif (marketData.rsi <= this.config.oversoldThreshold) {\n\t\t\trsiSignal = 'buy'\n\t\t} else if (marketData.rsi >= this.config.overboughtThreshold) {\n\t\t\trsiSignal = 'sell'\n\t\t}\n\n\t\t// EMA Signal Analysis\n\t\tlet emaSignal: 'buy' | 'sell' | 'neutral' = 'neutral'\n\t\tconst emaDiff = marketData.emaShort - marketData.emaLong\n\t\tconst emaDiffPercent = (emaDiff / marketData.emaLong) * 100\n\n\t\tif (emaDiffPercent > 0.1) {\n\t\t\temaSignal = 'buy'\n\t\t} else if (emaDiffPercent < -0.1) {\n\t\t\temaSignal = 'sell'\n\t\t}\n\n\t\t// Trend Signal Analysis\n\t\tlet trendSignal: 'buy' | 'sell' | 'neutral' = 'neutral'\n\t\tif (marketData.trend === 'up') {\n\t\t\ttrendSignal = 'buy'\n\t\t} else if (marketData.trend === 'down') {\n\t\t\ttrendSignal = 'sell'\n\t\t}\n\n\t\t// Volatility Analysis\n\t\tconst volatilitySignal = marketData.volatility < 0.03 ? 'favorable' : 'unfavorable'\n\n\t\t// Calculate overall signal strength\n\t\tconst strength = this.calculateSignalStrength(rsiSignal, emaSignal, trendSignal, volatilitySignal)\n\n\t\treturn {\n\t\t\trsiSignal,\n\t\t\temaSignal,\n\t\t\ttrendSignal,\n\t\t\tvolatilitySignal,\n\t\t\tstrength\n\t\t}\n\t}\n\n\tprivate calculateSignalStrength(\n\t\trsiSignal: string,\n\t\temaSignal: string,\n\t\ttrendSignal: string,\n\t\tvolatilitySignal: string\n\t): number {\n\t\tlet strength = 0\n\t\tlet maxStrength = 0\n\n\t\t// RSI weight: 40%\n\t\tmaxStrength += 0.4\n\t\tif (rsiSignal !== 'neutral') {\n\t\t\tstrength += 0.4\n\t\t}\n\n\t\t// EMA weight: 30%\n\t\tmaxStrength += 0.3\n\t\tif (emaSignal !== 'neutral') {\n\t\t\tstrength += 0.3\n\t\t}\n\n\t\t// Trend weight: 20%\n\t\tmaxStrength += 0.2\n\t\tif (trendSignal !== 'neutral') {\n\t\t\tstrength += 0.2\n\t\t}\n\n\t\t// Volatility weight: 10%\n\t\tmaxStrength += 0.1\n\t\tif (volatilitySignal === 'favorable') {\n\t\t\tstrength += 0.1\n\t\t}\n\n\t\treturn strength / maxStrength\n\t}\n\n\tprivate determineDirectionFromSignals(signals: any): 'high' | 'low' {\n\t\tlet score = 0\n\n\t\t// RSI signals (strongest weight)\n\t\tif (signals.rsiSignal === 'buy') {\n\t\t\tscore += 3\n\t\t} else if (signals.rsiSignal === 'sell') {\n\t\t\tscore -= 3\n\t\t}\n\n\t\t// EMA signals\n\t\tif (signals.emaSignal === 'buy') {\n\t\t\tscore += 2\n\t\t} else if (signals.emaSignal === 'sell') {\n\t\t\tscore -= 2\n\t\t}\n\n\t\t// Trend signals\n\t\tif (signals.trendSignal === 'buy') {\n\t\t\tscore += 1\n\t\t} else if (signals.trendSignal === 'sell') {\n\t\t\tscore -= 1\n\t\t}\n\n\t\treturn score > 0 ? 'high' : 'low'\n\t}\n\n\tprivate generateReasoning(marketData: MarketData, direction: 'high' | 'low', signals: any): string {\n\t\tconst reasons = []\n\n\t\t// RSI reasoning\n\t\tif (signals.rsiSignal === 'buy') {\n\t\t\treasons.push(`RSI oversold at ${marketData.rsi.toFixed(1)}`)\n\t\t} else if (signals.rsiSignal === 'sell') {\n\t\t\treasons.push(`RSI overbought at ${marketData.rsi.toFixed(1)}`)\n\t\t}\n\n\t\t// EMA reasoning\n\t\tif (signals.emaSignal === 'buy') {\n\t\t\treasons.push('EMA bullish crossover')\n\t\t} else if (signals.emaSignal === 'sell') {\n\t\t\treasons.push('EMA bearish crossover')\n\t\t}\n\n\t\t// Trend reasoning\n\t\tif (signals.trendSignal !== 'neutral') {\n\t\t\treasons.push(`${marketData.trend} trend confirmed`)\n\t\t}\n\n\t\t// Volatility reasoning\n\t\tif (signals.volatilitySignal === 'favorable') {\n\t\t\treasons.push('low volatility environment')\n\t\t}\n\n\t\tconst strengthPercent = (signals.strength * 100).toFixed(1)\n\n\t\treturn `${direction.toUpperCase()} signal (${strengthPercent}% strength): ${reasons.join(', ')}`\n\t}\n\n\t// Enhanced confidence calculation for RSI strategy\n\tcalculateConfidence(marketData: MarketData): number {\n\t\tlet confidence = super.calculateConfidence(marketData)\n\n\t\t// RSI-specific confidence adjustments\n\t\tconst rsiDistance = Math.min(\n\t\t\tMath.abs(marketData.rsi - this.config.oversoldThreshold),\n\t\t\tMath.abs(marketData.rsi - this.config.overboughtThreshold)\n\t\t)\n\n\t\t// Higher confidence when RSI is closer to extreme levels\n\t\tif (rsiDistance < 10) {\n\t\t\tconfidence += 15\n\t\t} else if (rsiDistance < 20) {\n\t\t\tconfidence += 10\n\t\t}\n\n\t\t// EMA divergence confidence\n\t\tconst emaDivergence = Math.abs(marketData.emaShort - marketData.emaLong) / marketData.emaLong\n\t\tif (emaDivergence > 0.002) {\n\t\t\tconfidence += 10\n\t\t}\n\n\t\t// Consistency bonus\n\t\tif (this.consecutiveWins > 2) {\n\t\t\tconfidence += 5\n\t\t}\n\n\t\treturn Math.max(0, Math.min(confidence, 100))\n\t}\n\n\treset(): void {\n\t\tsuper.reset()\n\t\tthis.lastSignal = 'hold'\n\t\tthis.signalStrength = 0\n\t\tthis.currentAmount = this.config.amount\n\t}\n\n\t// Method to be called after each trade result\n\tonTradeResult(won: boolean): void {\n\t\tthis.updateStats(won)\n\t}\n\n\t// Get current strategy state\n\tgetState(): {\n\t\tamount: number\n\t\tlastSignal: string\n\t\tsignalStrength: number\n\t\trsiPeriod: number\n\t\toversoldThreshold: number\n\t\toverboughtThreshold: number\n\t\tconsecutiveWins: number\n\t\tconsecutiveLosses: number\n\t} {\n\t\treturn {\n\t\t\tamount: this.config.amount,\n\t\t\tlastSignal: this.lastSignal,\n\t\t\tsignalStrength: this.signalStrength,\n\t\t\trsiPeriod: this.config.rsiPeriod,\n\t\t\toversoldThreshold: this.config.oversoldThreshold,\n\t\t\toverboughtThreshold: this.config.overboughtThreshold,\n\t\t\tconsecutiveWins: this.consecutiveWins,\n\t\t\tconsecutiveLosses: this.consecutiveLosses\n\t\t}\n\t}\n}\n", "import { BaseStrategy, StrategyType, StrategyConfig, MarketData, TradeDecision } from '../../src/types'\nimport { BaseStrategyImpl } from './BaseStrategy'\nimport { MartingaleStrategy } from './strategies/MartingaleStrategy'\nimport { DAlembertStrategy } from './strategies/DAlembertStrategy'\nimport { FixedRSIStrategy } from './strategies/FixedRSIStrategy'\n\nexport class StrategyManager {\n\tprivate static strategies: Map<StrategyType, typeof BaseStrategy> = new Map([\n\t\t['martingale', MartingaleStrategy as any],\n\t\t['dalembert', DAlembertStrategy as any],\n\t\t['fixed-rsi', FixedRSIStrategy as any]\n\t])\n\n\tprivate activeStrategy: BaseStrategy | null = null\n\n\tstatic getAvailableStrategies(): Array<{ name: StrategyType; description: string; defaultConfig: StrategyConfig }> {\n\t\treturn [\n\t\t\t{\n\t\t\t\tname: 'martingale',\n\t\t\t\tdescription: 'Doubles bet after each loss, resets on win. High risk, high reward.',\n\t\t\t\tdefaultConfig: {\n\t\t\t\t\tbaseAmount: 1,\n\t\t\t\t\tmultiplier: 2,\n\t\t\t\t\tmaxSteps: 5,\n\t\t\t\t\tresetOnWin: true\n\t\t\t\t}\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'da<PERSON><PERSON>',\n\t\t\t\tdescription: 'Increases bet by fixed amount after loss, decreases after win. Moderate risk.',\n\t\t\t\tdefaultConfig: {\n\t\t\t\t\tbaseAmount: 1,\n\t\t\t\t\tincrement: 1,\n\t\t\t\t\tmaxSteps: 10\n\t\t\t\t}\n\t\t\t},\n\t\t\t{\n\t\t\t\tname: 'fixed-rsi',\n\t\t\t\tdescription: 'Fixed stake with RSI and EMA indicators. Conservative approach.',\n\t\t\t\tdefaultConfig: {\n\t\t\t\t\tamount: 1,\n\t\t\t\t\trsiPeriod: 14,\n\t\t\t\t\toversoldThreshold: 30,\n\t\t\t\t\toverboughtThreshold: 70,\n\t\t\t\t\temaShort: 12,\n\t\t\t\t\temaLong: 26\n\t\t\t\t}\n\t\t\t}\n\t\t]\n\t}\n\n\tsetStrategy(strategyType: StrategyType, config: StrategyConfig): void {\n\t\tconst StrategyClass = StrategyManager.strategies.get(strategyType)\n\n\t\tif (!StrategyClass) {\n\t\t\tthrow new Error(`Strategy ${strategyType} not found`)\n\t\t}\n\n\t\tthis.activeStrategy = new (StrategyClass as any)(config)\n\t}\n\n\tasync executeStrategy(marketData: MarketData): Promise<TradeDecision | null> {\n\t\tif (!this.activeStrategy) {\n\t\t\tthrow new Error('No active strategy set')\n\t\t}\n\n\t\t// Calculate confidence first\n\t\tconst confidence = this.activeStrategy.calculateConfidence(marketData)\n\n\t\t// Only execute if confidence is high enough\n\t\tif (confidence < 87) {\n\t\t\t// Configurable threshold\n\t\t\treturn {\n\t\t\t\taction: 'hold',\n\t\t\t\tamount: 0,\n\t\t\t\tconfidence,\n\t\t\t\treasoning: `Confidence too low: ${confidence.toFixed(2)}%`\n\t\t\t}\n\t\t}\n\n\t\treturn await this.activeStrategy.execute(marketData)\n\t}\n\n\tgetActiveStrategy(): BaseStrategy | null {\n\t\treturn this.activeStrategy\n\t}\n\n\tresetStrategy(): void {\n\t\tif (this.activeStrategy) {\n\t\t\tthis.activeStrategy.reset()\n\t\t}\n\t}\n}\n", "import Tesseract from 'tesseract.js'\nimport { ChartAnalysis, OCRResult, ChartCoordinates, MarketData } from '../../src/types'\n\nexport class OCRService {\n  private worker: Tesseract.Worker | null = null\n  private isInitialized: boolean = false\n\n  async initialize(): Promise<void> {\n    if (this.isInitialized) return\n\n    try {\n      this.worker = await Tesseract.createWorker('eng', 1, {\n        logger: m => {\n          if (m.status === 'recognizing text') {\n            console.log(`OCR Progress: ${(m.progress * 100).toFixed(1)}%`)\n          }\n        }\n      })\n\n      await this.worker.setParameters({\n        tessedit_char_whitelist: '0123456789.,+-$€£¥%',\n        tessedit_pageseg_mode: Tesseract.PSM.SINGLE_BLOCK,\n        preserve_interword_spaces: '0'\n      })\n\n      this.isInitialized = true\n      console.log('OCR Service initialized successfully')\n    } catch (error) {\n      console.error('Failed to initialize OCR service:', error)\n      throw error\n    }\n  }\n\n  async analyzeChart(screenshot: <PERSON>uffer, coordinates?: ChartCoordinates): Promise<ChartAnalysis> {\n    if (!this.isInitialized || !this.worker) {\n      throw new Error('OCR Service not initialized')\n    }\n\n    try {\n      // Extract price information from screenshot\n      const priceData = await this.extractPriceData(screenshot)\n      \n      // Calculate technical indicators (simplified)\n      const indicators = this.calculateIndicators(priceData)\n      \n      // Determine trend\n      const trend = this.determineTrend(priceData, indicators)\n      \n      // Calculate confidence based on data quality\n      const confidence = this.calculateConfidence(priceData, indicators)\n\n      return {\n        currentPrice: priceData.currentPrice,\n        priceChange: priceData.priceChange,\n        priceChangePercent: priceData.priceChangePercent,\n        trend,\n        support: indicators.support,\n        resistance: indicators.resistance,\n        rsi: indicators.rsi,\n        ema: {\n          short: indicators.emaShort,\n          long: indicators.emaLong\n        },\n        confidence,\n        timestamp: Date.now()\n      }\n    } catch (error) {\n      console.error('Chart analysis failed:', error)\n      throw error\n    }\n  }\n\n  private async extractPriceData(screenshot: Buffer): Promise<{\n    currentPrice: number\n    priceChange: number\n    priceChangePercent: number\n    rawText: string\n  }> {\n    if (!this.worker) {\n      throw new Error('OCR worker not available')\n    }\n\n    const { data: { text } } = await this.worker.recognize(screenshot)\n    \n    // Parse price information from OCR text\n    const priceRegex = /(\\d+\\.?\\d*)/g\n    const matches = text.match(priceRegex)\n    \n    if (!matches || matches.length === 0) {\n      throw new Error('No price data found in screenshot')\n    }\n\n    // Extract current price (usually the largest or most prominent number)\n    const prices = matches.map(match => parseFloat(match)).filter(price => !isNaN(price))\n    const currentPrice = prices.length > 0 ? prices[0] : 0\n\n    // Try to extract price change information\n    const changeRegex = /([+-]?\\d+\\.?\\d*)[%]?/g\n    const changeMatches = text.match(changeRegex)\n    \n    let priceChange = 0\n    let priceChangePercent = 0\n    \n    if (changeMatches && changeMatches.length > 1) {\n      priceChange = parseFloat(changeMatches[1]) || 0\n      \n      // Look for percentage change\n      const percentMatch = text.match(/([+-]?\\d+\\.?\\d*)%/)\n      if (percentMatch) {\n        priceChangePercent = parseFloat(percentMatch[1]) || 0\n      }\n    }\n\n    return {\n      currentPrice,\n      priceChange,\n      priceChangePercent,\n      rawText: text\n    }\n  }\n\n  private calculateIndicators(priceData: any): {\n    support: number\n    resistance: number\n    rsi: number\n    emaShort: number\n    emaLong: number\n    volatility: number\n  } {\n    // Simplified indicator calculations\n    // In a real implementation, you would need historical price data\n    const currentPrice = priceData.currentPrice\n    \n    // Estimate support and resistance based on current price\n    const support = currentPrice * 0.98 // 2% below current price\n    const resistance = currentPrice * 1.02 // 2% above current price\n    \n    // Simplified RSI calculation (would need historical data for accuracy)\n    // For now, estimate based on price change\n    let rsi = 50 // Neutral\n    if (priceData.priceChangePercent > 2) {\n      rsi = 70 // Overbought\n    } else if (priceData.priceChangePercent < -2) {\n      rsi = 30 // Oversold\n    } else if (priceData.priceChangePercent > 0) {\n      rsi = 55 + (priceData.priceChangePercent * 5)\n    } else {\n      rsi = 45 + (priceData.priceChangePercent * 5)\n    }\n    \n    // Simplified EMA calculations\n    const emaShort = currentPrice * (1 + (priceData.priceChangePercent / 100) * 0.5)\n    const emaLong = currentPrice * (1 + (priceData.priceChangePercent / 100) * 0.2)\n    \n    // Estimate volatility based on price change\n    const volatility = Math.abs(priceData.priceChangePercent) / 100\n\n    return {\n      support: Math.max(support, 0),\n      resistance,\n      rsi: Math.max(0, Math.min(100, rsi)),\n      emaShort,\n      emaLong,\n      volatility\n    }\n  }\n\n  private determineTrend(priceData: any, indicators: any): 'up' | 'down' | 'sideways' {\n    const priceChange = priceData.priceChangePercent\n    const emaSignal = indicators.emaShort > indicators.emaLong\n    \n    if (priceChange > 0.5 && emaSignal) {\n      return 'up'\n    } else if (priceChange < -0.5 && !emaSignal) {\n      return 'down'\n    } else {\n      return 'sideways'\n    }\n  }\n\n  private calculateConfidence(priceData: any, indicators: any): number {\n    let confidence = 50 // Base confidence\n    \n    // Increase confidence if we have clear price data\n    if (priceData.currentPrice > 0) {\n      confidence += 20\n    }\n    \n    // Increase confidence if price change is significant\n    if (Math.abs(priceData.priceChangePercent) > 1) {\n      confidence += 15\n    }\n    \n    // Increase confidence if RSI is in extreme zones\n    if (indicators.rsi <= 30 || indicators.rsi >= 70) {\n      confidence += 10\n    }\n    \n    // Decrease confidence if volatility is too high\n    if (indicators.volatility > 0.05) {\n      confidence -= 15\n    }\n    \n    return Math.max(0, Math.min(100, confidence))\n  }\n\n  async extractText(screenshot: Buffer, coordinates?: ChartCoordinates): Promise<OCRResult[]> {\n    if (!this.isInitialized || !this.worker) {\n      throw new Error('OCR Service not initialized')\n    }\n\n    try {\n      const { data } = await this.worker.recognize(screenshot)\n      \n      const results: OCRResult[] = []\n      \n      if (data.words) {\n        for (const word of data.words) {\n          if (word.confidence > 60) { // Only include high-confidence results\n            results.push({\n              text: word.text,\n              confidence: word.confidence,\n              bbox: {\n                x0: word.bbox.x0,\n                y0: word.bbox.y0,\n                x1: word.bbox.x1,\n                y1: word.bbox.y1\n              }\n            })\n          }\n        }\n      }\n      \n      return results\n    } catch (error) {\n      console.error('Text extraction failed:', error)\n      throw error\n    }\n  }\n\n  // Convert chart analysis to market data format\n  chartAnalysisToMarketData(analysis: ChartAnalysis, asset: string): MarketData {\n    return {\n      asset,\n      price: analysis.currentPrice,\n      timestamp: analysis.timestamp,\n      rsi: analysis.rsi,\n      emaShort: analysis.ema.short,\n      emaLong: analysis.ema.long,\n      volatility: Math.abs(analysis.priceChangePercent) / 100,\n      trend: analysis.trend,\n      confidence: analysis.confidence\n    }\n  }\n\n  async cleanup(): Promise<void> {\n    if (this.worker) {\n      await this.worker.terminate()\n      this.worker = null\n      this.isInitialized = false\n    }\n  }\n}\n", "import { EventEmitter } from 'events'\nimport { BrowserController } from './BrowserController'\nimport { StrategyManager } from './StrategyManager'\nimport { OCRService } from './OCRService'\nimport { \n  BotConfig, \n  Asset, \n  AuthCredentials, \n  Trade, \n  TradingSession, \n  MarketData,\n  TradeDecision,\n  BotEvent,\n  ChartCoordinates\n} from '../../src/types'\n\nexport class TradingBot extends EventEmitter {\n  private config: BotConfig\n  private browserController: BrowserController\n  private strategyManager: StrategyManager\n  private ocrService: OCRService\n  \n  private status: 'stopped' | 'starting' | 'running' | 'stopping' | 'error' = 'stopped'\n  private currentSession: TradingSession | null = null\n  private recentTrades: Trade[] = []\n  private isAuthenticated: boolean = false\n  private availableAssets: Asset[] = []\n  private selectedAsset: Asset | null = null\n  private chartCoordinates: ChartCoordinates | null = null\n  \n  private analysisInterval: NodeJS.Timeout | null = null\n  private tradingInterval: NodeJS.Timeout | null = null\n  private lastTradeTime: number = 0\n  private cooldownPeriod: number = 30000 // 30 seconds cooldown\n\n  constructor(config: BotConfig) {\n    super()\n    this.config = config\n    \n    this.browserController = new BrowserController({\n      headless: false, // Always show browser for user interaction\n      devtools: config.debugLogs,\n      userDataDir: 'browser-data',\n      viewport: { width: 1920, height: 1080 }\n    })\n    \n    this.strategyManager = new StrategyManager()\n    this.ocrService = new OCRService()\n  }\n\n  async initialize(): Promise<void> {\n    try {\n      this.setStatus('starting')\n      \n      // Initialize services\n      await this.browserController.initialize()\n      await this.ocrService.initialize()\n      \n      // Navigate to Pocket Option\n      await this.browserController.navigateToPocketOption()\n      \n      // Set up strategy if provided\n      if (this.config.strategy) {\n        const strategies = StrategyManager.getAvailableStrategies()\n        const strategyConfig = strategies.find(s => s.name === this.config.strategy)?.defaultConfig\n        \n        if (strategyConfig) {\n          this.strategyManager.setStrategy(this.config.strategy, {\n            ...strategyConfig,\n            ...this.config\n          })\n        }\n      }\n      \n      this.setStatus('stopped')\n      this.emit('bot:initialized')\n      \n    } catch (error) {\n      this.setStatus('error')\n      this.emit('bot:error', error)\n      throw error\n    }\n  }\n\n  async authenticate(credentials: AuthCredentials): Promise<{ success: boolean; message?: string }> {\n    try {\n      const result = await this.browserController.authenticate(credentials)\n      this.isAuthenticated = result.success\n      \n      if (result.success) {\n        // Load available assets after authentication\n        this.availableAssets = await this.browserController.getAvailableAssets()\n        this.emit('bot:authenticated', { assets: this.availableAssets })\n      }\n      \n      return result\n    } catch (error) {\n      console.error('Authentication failed:', error)\n      return { success: false, message: error.message }\n    }\n  }\n\n  async start(): Promise<void> {\n    if (!this.isAuthenticated) {\n      throw new Error('Bot must be authenticated before starting')\n    }\n    \n    if (this.status === 'running') {\n      throw new Error('Bot is already running')\n    }\n\n    try {\n      this.setStatus('starting')\n      \n      // Initialize trading session\n      this.currentSession = {\n        startTime: Date.now(),\n        totalTrades: 0,\n        winningTrades: 0,\n        losingTrades: 0,\n        totalProfit: 0,\n        winRate: 0,\n        maxDrawdown: 0,\n        currentStreak: 0,\n        bestStreak: 0,\n        worstStreak: 0\n      }\n      \n      // Set up selected asset\n      if (this.config.assetFilter.selectedAssets.length > 0) {\n        const assetName = this.config.assetFilter.selectedAssets[0]\n        await this.browserController.selectAsset(assetName)\n        this.selectedAsset = this.availableAssets.find(a => a.name === assetName) || null\n      }\n      \n      // Set time period\n      await this.browserController.setTimePeriod(this.config.timePeriod)\n      \n      // Start analysis and trading loops\n      this.startAnalysisLoop()\n      this.startTradingLoop()\n      \n      this.setStatus('running')\n      this.emit('bot:started')\n      \n    } catch (error) {\n      this.setStatus('error')\n      this.emit('bot:error', error)\n      throw error\n    }\n  }\n\n  async stop(): Promise<void> {\n    this.setStatus('stopping')\n    \n    // Clear intervals\n    if (this.analysisInterval) {\n      clearInterval(this.analysisInterval)\n      this.analysisInterval = null\n    }\n    \n    if (this.tradingInterval) {\n      clearInterval(this.tradingInterval)\n      this.tradingInterval = null\n    }\n    \n    // Finalize session\n    if (this.currentSession) {\n      this.currentSession.endTime = Date.now()\n      this.emit('bot:session_ended', this.currentSession)\n    }\n    \n    this.setStatus('stopped')\n    this.emit('bot:stopped')\n  }\n\n  private startAnalysisLoop(): void {\n    this.analysisInterval = setInterval(async () => {\n      try {\n        if (this.status !== 'running') return\n        \n        await this.performAnalysis()\n      } catch (error) {\n        console.error('Analysis loop error:', error)\n        this.emit('bot:error', error)\n      }\n    }, 1000) // 1 second interval for chart analysis\n  }\n\n  private startTradingLoop(): void {\n    this.tradingInterval = setInterval(async () => {\n      try {\n        if (this.status !== 'running') return\n        \n        // Check cooldown\n        const timeSinceLastTrade = Date.now() - this.lastTradeTime\n        if (timeSinceLastTrade < this.cooldownPeriod) {\n          return\n        }\n        \n        await this.evaluateAndExecuteTrade()\n      } catch (error) {\n        console.error('Trading loop error:', error)\n        this.emit('bot:error', error)\n      }\n    }, 5000) // 5 second interval for trading decisions (configurable)\n  }\n\n  private async performAnalysis(): Promise<void> {\n    if (!this.chartCoordinates) {\n      return // No chart coordinates set\n    }\n    \n    try {\n      // Capture chart screenshot\n      const screenshot = await this.browserController.captureScreenshot(this.chartCoordinates)\n      \n      // Analyze chart with OCR\n      const chartAnalysis = await this.ocrService.analyzeChart(screenshot, this.chartCoordinates)\n      \n      // Convert to market data\n      const marketData = this.ocrService.chartAnalysisToMarketData(\n        chartAnalysis,\n        this.selectedAsset?.name || 'Unknown'\n      )\n      \n      // Emit market data update\n      this.emit('bot:market_data', marketData)\n      \n    } catch (error) {\n      console.error('Chart analysis failed:', error)\n    }\n  }\n\n  private async evaluateAndExecuteTrade(): Promise<void> {\n    if (!this.selectedAsset || !this.chartCoordinates) {\n      return\n    }\n    \n    try {\n      // Get latest market data\n      const screenshot = await this.browserController.captureScreenshot(this.chartCoordinates)\n      const chartAnalysis = await this.ocrService.analyzeChart(screenshot, this.chartCoordinates)\n      const marketData = this.ocrService.chartAnalysisToMarketData(\n        chartAnalysis,\n        this.selectedAsset.name\n      )\n      \n      // Execute strategy\n      const decision = await this.strategyManager.executeStrategy(marketData)\n      \n      if (!decision || decision.action === 'hold') {\n        return\n      }\n      \n      // Check confidence threshold\n      if (decision.confidence < this.config.confidenceThreshold) {\n        this.emit('bot:trade_skipped', {\n          reason: 'Confidence below threshold',\n          confidence: decision.confidence,\n          threshold: this.config.confidenceThreshold\n        })\n        return\n      }\n      \n      // Execute trade if not in dry run mode\n      if (!this.config.dryRun) {\n        await this.executeTrade(decision, marketData)\n      } else {\n        this.logDryRunTrade(decision, marketData)\n      }\n      \n    } catch (error) {\n      console.error('Trade evaluation failed:', error)\n    }\n  }\n\n  private async executeTrade(decision: TradeDecision, marketData: MarketData): Promise<void> {\n    try {\n      // Set trade amount\n      await this.browserController.setTradeAmount(decision.amount)\n      \n      // Execute trade\n      const direction = decision.action === 'buy' ? 'high' : 'low'\n      const result = await this.browserController.executeTrade(direction)\n      \n      if (result.success) {\n        // Create trade record\n        const trade: Trade = {\n          id: `trade_${Date.now()}`,\n          asset: this.selectedAsset!.name,\n          direction,\n          amount: decision.amount,\n          openPrice: marketData.price,\n          openTime: Date.now(),\n          duration: this.getTimePeriodInMs(this.config.timePeriod),\n          strategy: this.strategyManager.getActiveStrategy()?.name || 'unknown',\n          confidence: decision.confidence,\n          result: 'pending'\n        }\n        \n        this.recentTrades.unshift(trade)\n        this.lastTradeTime = Date.now()\n        \n        // Update session stats\n        if (this.currentSession) {\n          this.currentSession.totalTrades++\n        }\n        \n        this.emit('bot:trade_opened', trade)\n        \n        // Schedule trade result check\n        setTimeout(() => {\n          this.checkTradeResult(trade.id)\n        }, trade.duration + 5000) // Check 5 seconds after expiry\n        \n      } else {\n        this.emit('bot:trade_failed', { decision, error: result.message })\n      }\n      \n    } catch (error) {\n      console.error('Trade execution failed:', error)\n      this.emit('bot:trade_failed', { decision, error: error.message })\n    }\n  }\n\n  private logDryRunTrade(decision: TradeDecision, marketData: MarketData): void {\n    const trade: Trade = {\n      id: `dry_run_${Date.now()}`,\n      asset: this.selectedAsset!.name,\n      direction: decision.action === 'buy' ? 'high' : 'low',\n      amount: decision.amount,\n      openPrice: marketData.price,\n      openTime: Date.now(),\n      duration: this.getTimePeriodInMs(this.config.timePeriod),\n      strategy: this.strategyManager.getActiveStrategy()?.name || 'unknown',\n      confidence: decision.confidence,\n      result: 'pending'\n    }\n    \n    this.recentTrades.unshift(trade)\n    this.emit('bot:dry_run_trade', trade)\n  }\n\n  private async checkTradeResult(tradeId: string): Promise<void> {\n    // Implementation would check the actual trade result from the platform\n    // For now, simulate random result\n    const trade = this.recentTrades.find(t => t.id === tradeId)\n    if (!trade) return\n    \n    // Simulate trade result (in real implementation, this would check the platform)\n    const won = Math.random() > 0.5\n    trade.result = won ? 'win' : 'loss'\n    trade.closeTime = Date.now()\n    trade.profit = won ? trade.amount * 0.8 : -trade.amount // 80% payout\n    \n    // Update session stats\n    if (this.currentSession) {\n      if (won) {\n        this.currentSession.winningTrades++\n        this.currentSession.currentStreak = Math.max(0, this.currentSession.currentStreak + 1)\n        this.currentSession.bestStreak = Math.max(this.currentSession.bestStreak, this.currentSession.currentStreak)\n      } else {\n        this.currentSession.losingTrades++\n        this.currentSession.currentStreak = Math.min(0, this.currentSession.currentStreak - 1)\n        this.currentSession.worstStreak = Math.min(this.currentSession.worstStreak, this.currentSession.currentStreak)\n      }\n      \n      this.currentSession.totalProfit += trade.profit\n      this.currentSession.winRate = (this.currentSession.winningTrades / this.currentSession.totalTrades) * 100\n    }\n    \n    // Update strategy stats\n    const activeStrategy = this.strategyManager.getActiveStrategy()\n    if (activeStrategy && 'onTradeResult' in activeStrategy) {\n      (activeStrategy as any).onTradeResult(won)\n    }\n    \n    this.emit('bot:trade_closed', trade)\n  }\n\n  private getTimePeriodInMs(period: string): number {\n    const periodMap: Record<string, number> = {\n      'S5': 5000,\n      'S15': 15000,\n      'S30': 30000,\n      'M1': 60000,\n      'M3': 180000,\n      'M5': 300000,\n      'M15': 900000,\n      'M30': 1800000,\n      'H1': 3600000\n    }\n    \n    return periodMap[period] || 60000\n  }\n\n  // Public methods for external control\n  async setStrategy(strategyName: string, config: any): Promise<void> {\n    this.strategyManager.setStrategy(strategyName as any, config)\n    this.emit('bot:strategy_changed', { strategy: strategyName, config })\n  }\n\n  async getAvailableAssets(): Promise<Asset[]> {\n    if (this.availableAssets.length === 0) {\n      this.availableAssets = await this.browserController.getAvailableAssets()\n    }\n    return this.availableAssets\n  }\n\n  async captureChartCoordinates(): Promise<ChartCoordinates> {\n    // This would implement a UI for the user to select chart coordinates\n    // For now, return default coordinates\n    const coordinates: ChartCoordinates = {\n      x: 100,\n      y: 100,\n      width: 800,\n      height: 400\n    }\n    \n    this.chartCoordinates = coordinates\n    return coordinates\n  }\n\n  getStatus(): string {\n    return this.status\n  }\n\n  getStatusData(): any {\n    return {\n      isAuthenticated: this.isAuthenticated,\n      currentSession: this.currentSession,\n      recentTrades: this.recentTrades.slice(0, 10), // Last 10 trades\n      selectedAsset: this.selectedAsset,\n      availableAssets: this.availableAssets,\n      chartCoordinates: this.chartCoordinates,\n      activeStrategy: this.strategyManager.getActiveStrategy()?.name\n    }\n  }\n\n  private setStatus(status: typeof this.status): void {\n    this.status = status\n    this.emit('bot:status_changed', status)\n  }\n\n  async cleanup(): Promise<void> {\n    await this.stop()\n    await this.browserController.close()\n    await this.ocrService.cleanup()\n  }\n}\n", "import { app, BrowserWindow, ipc<PERSON>ain, dialog } from 'electron'\nimport { join, dirname } from 'path'\nimport { fileURLToPath } from 'url'\nimport { TradingBot } from './services/TradingBot'\nimport { StrategyManager } from './services/StrategyManager'\nimport { <PERSON>rowserController } from './services/BrowserController'\n\n// Handle __dirname in ES modules\nconst __filename = fileURLToPath(import.meta.url)\nconst __dirname = dirname(__filename)\n\n// Keep a global reference of the window object\nlet mainWindow: BrowserWindow | null = null\nlet tradingBot: TradingBot | null = null\n\nconst isDev = process.env.NODE_ENV === 'development'\nconst VITE_DEV_SERVER_URL = process.env.VITE_DEV_SERVER_URL\n\nfunction createWindow() {\n\tmainWindow = new BrowserWindow({\n\t\twidth: 1400,\n\t\theight: 900,\n\t\tminWidth: 1200,\n\t\tminHeight: 800,\n\t\twebPreferences: {\n\t\t\tpreload: join(__dirname, 'preload.js'),\n\t\t\tnodeIntegration: false,\n\t\t\tcontextIsolation: true,\n\t\t\tenableRemoteModule: false,\n\t\t\twebSecurity: true\n\t\t},\n\t\ttitleBarStyle: 'default',\n\t\tbackgroundColor: '#0f172a',\n\t\tshow: false,\n\t\ticon: join(__dirname, '../assets/icon.png')\n\t})\n\n\t// Load the app\n\tif (VITE_DEV_SERVER_URL) {\n\t\tmainWindow.loadURL(VITE_DEV_SERVER_URL)\n\t\tif (isDev) {\n\t\t\tmainWindow.webContents.openDevTools()\n\t\t}\n\t} else {\n\t\tmainWindow.loadFile(join(__dirname, '../dist/index.html'))\n\t}\n\n\tmainWindow.once('ready-to-show', () => {\n\t\tmainWindow?.show()\n\t})\n\n\tmainWindow.on('closed', () => {\n\t\tmainWindow = null\n\t\tif (tradingBot) {\n\t\t\ttradingBot.stop()\n\t\t\ttradingBot = null\n\t\t}\n\t})\n}\n\n// App event listeners\napp.whenReady().then(() => {\n\tcreateWindow()\n\n\tapp.on('activate', () => {\n\t\tif (BrowserWindow.getAllWindows().length === 0) {\n\t\t\tcreateWindow()\n\t\t}\n\t})\n})\n\napp.on('window-all-closed', () => {\n\tif (process.platform !== 'darwin') {\n\t\tapp.quit()\n\t}\n})\n\n// IPC Handlers\nipcMain.handle('bot:initialize', async (_, config) => {\n\ttry {\n\t\tif (tradingBot) {\n\t\t\tawait tradingBot.stop()\n\t\t}\n\n\t\ttradingBot = new TradingBot(config)\n\t\tawait tradingBot.initialize()\n\n\t\treturn { success: true, message: 'Bot initialized successfully' }\n\t} catch (error) {\n\t\tconsole.error('Failed to initialize bot:', error)\n\t\treturn { success: false, message: error.message }\n\t}\n})\n\nipcMain.handle('bot:start', async () => {\n\ttry {\n\t\tif (!tradingBot) {\n\t\t\tthrow new Error('Bot not initialized')\n\t\t}\n\n\t\tawait tradingBot.start()\n\t\treturn { success: true, message: 'Bot started successfully' }\n\t} catch (error) {\n\t\tconsole.error('Failed to start bot:', error)\n\t\treturn { success: false, message: error.message }\n\t}\n})\n\nipcMain.handle('bot:stop', async () => {\n\ttry {\n\t\tif (tradingBot) {\n\t\t\tawait tradingBot.stop()\n\t\t}\n\t\treturn { success: true, message: 'Bot stopped successfully' }\n\t} catch (error) {\n\t\tconsole.error('Failed to stop bot:', error)\n\t\treturn { success: false, message: error.message }\n\t}\n})\n\nipcMain.handle('bot:status', async () => {\n\tif (!tradingBot) {\n\t\treturn { status: 'stopped', data: null }\n\t}\n\n\treturn {\n\t\tstatus: tradingBot.getStatus(),\n\t\tdata: tradingBot.getStatusData()\n\t}\n})\n\nipcMain.handle('browser:authenticate', async (_, credentials) => {\n\ttry {\n\t\tif (!tradingBot) {\n\t\t\tthrow new Error('Bot not initialized')\n\t\t}\n\n\t\tconst result = await tradingBot.authenticate(credentials)\n\t\treturn result\n\t} catch (error) {\n\t\tconsole.error('Authentication failed:', error)\n\t\treturn { success: false, message: error.message }\n\t}\n})\n\nipcMain.handle('browser:getAssets', async () => {\n\ttry {\n\t\tif (!tradingBot) {\n\t\t\tthrow new Error('Bot not initialized')\n\t\t}\n\n\t\tconst assets = await tradingBot.getAvailableAssets()\n\t\treturn { success: true, assets }\n\t} catch (error) {\n\t\tconsole.error('Failed to get assets:', error)\n\t\treturn { success: false, message: error.message }\n\t}\n})\n\nipcMain.handle('dialog:selectScreenshotArea', async () => {\n\ttry {\n\t\tconst result = await dialog.showMessageBox(mainWindow!, {\n\t\t\ttype: 'info',\n\t\t\ttitle: 'Screenshot Area Selection',\n\t\t\tmessage: 'Click OK and then select the chart area on the Pocket Option page',\n\t\t\tbuttons: ['OK', 'Cancel']\n\t\t})\n\n\t\tif (result.response === 0) {\n\t\t\t// User clicked OK, now capture screenshot coordinates\n\t\t\tif (tradingBot) {\n\t\t\t\tconst coordinates = await tradingBot.captureChartCoordinates()\n\t\t\t\treturn { success: true, coordinates }\n\t\t\t}\n\t\t}\n\n\t\treturn { success: false, message: 'Screenshot selection cancelled' }\n\t} catch (error) {\n\t\tconsole.error('Failed to select screenshot area:', error)\n\t\treturn { success: false, message: error.message }\n\t}\n})\n\n// Strategy management\nipcMain.handle('strategy:getAvailable', async () => {\n\tconst strategies = StrategyManager.getAvailableStrategies()\n\treturn { success: true, strategies }\n})\n\nipcMain.handle('strategy:setActive', async (_, strategyName, config) => {\n\ttry {\n\t\tif (!tradingBot) {\n\t\t\tthrow new Error('Bot not initialized')\n\t\t}\n\n\t\tawait tradingBot.setStrategy(strategyName, config)\n\t\treturn { success: true, message: `Strategy ${strategyName} activated` }\n\t} catch (error) {\n\t\tconsole.error('Failed to set strategy:', error)\n\t\treturn { success: false, message: error.message }\n\t}\n})\n\n// Error handling\nprocess.on('uncaughtException', error => {\n\tconsole.error('Uncaught Exception:', error)\n})\n\nprocess.on('unhandledRejection', (reason, promise) => {\n\tconsole.error('Unhandled Rejection at:', promise, 'reason:', reason)\n})\n"], "names": ["assets", "period"], "mappings": ";;;;;;;;AAMO,MAAM,kBAAkB;AAAA,EACrB,UAA0B;AAAA,EAC1B,OAAoB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA,eAAwB;AAAA,EAEhC,YAAY,QAAuB;AACjC,SAAK,SAAS;AACd,SAAK,cAAc,KAAK,QAAQ,IAAA,GAAO,cAAc;AACrD,SAAK,cAAc,KAAK,KAAK,aAAa,cAAc;AAGxD,QAAI,CAAC,WAAW,KAAK,WAAW,GAAG;AACjC,gBAAU,KAAK,aAAa,EAAE,WAAW,MAAM;AAAA,IAAA;AAAA,EACjD;AAAA,EAGF,MAAM,aAA4B;AAC5B,QAAA;AACF,YAAM,KAAK,cAAc;AACzB,YAAM,KAAK,UAAU;AACrB,YAAM,KAAK,YAAY;AAAA,aAChB,OAAO;AACN,cAAA,MAAM,mEAAmE,KAAK;AACtF,WAAK,eAAe;AACpB,YAAM,KAAK,wBAAwB;AAAA,IAAA;AAAA,EACrC;AAAA,EAGF,MAAc,gBAA+B;AACtC,SAAA,UAAU,MAAM,UAAU,OAAO;AAAA,MACpC,UAAU,KAAK,OAAO;AAAA,MACtB,UAAU,KAAK,OAAO;AAAA,MACtB,aAAa,KAAK;AAAA,MAClB,MAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MAAA;AAAA,IACF,CACD;AAAA,EAAA;AAAA,EAGH,MAAc,0BAAyC;AAC/C,UAAA,UAAU,MAAM,SAAS,OAAO;AAAA,MACpC,UAAU,KAAK,OAAO;AAAA,MACtB,UAAU,KAAK,OAAO;AAAA,MACtB,MAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MAAA;AAAA,IACF,CACD;AAGD,SAAK,UAAU;AAAA,EAAA;AAAA,EAGjB,MAAc,YAA2B;AACnC,QAAA,CAAC,KAAK,SAAS;AACX,YAAA,IAAI,MAAM,yBAAyB;AAAA,IAAA;AAG3C,UAAM,QAAQ,MAAM,KAAK,QAAQ,MAAM;AAClC,SAAA,OAAO,MAAM,SAAS,IAAI,MAAM,CAAC,IAAI,MAAM,KAAK,QAAQ,QAAQ;AAE/D,UAAA,KAAK,KAAK,YAAY;AAAA,MAC1B,OAAO,KAAK,OAAO,SAAS;AAAA,MAC5B,QAAQ,KAAK,OAAO,SAAS;AAAA,IAAA,CAC9B;AAGD,UAAM,KAAK,KAAK;AAAA,MACd;AAAA,IACF;AAGM,UAAA,KAAK,KAAK,uBAAuB,IAAI;AAC3C,SAAK,KAAK,GAAG,WAAW,CAAC,QAAQ;AACzB,YAAA,eAAe,IAAI,aAAa;AAClC,UAAA,CAAC,SAAS,cAAc,QAAQ,OAAO,EAAE,SAAS,YAAY,GAAG;AACnE,YAAI,MAAM;AAAA,MAAA,OACL;AACL,YAAI,SAAS;AAAA,MAAA;AAAA,IACf,CACD;AAAA,EAAA;AAAA,EAGH,MAAM,yBAAwC;AACxC,QAAA,CAAC,KAAK,MAAM;AACR,YAAA,IAAI,MAAM,sBAAsB;AAAA,IAAA;AAGlC,UAAA,MAAM,YAAY,qBAAqB;AAEvC,UAAA,KAAK,KAAK,KAAK,KAAK;AAAA,MACxB,WAAW;AAAA,MACX,SAAS;AAAA,IAAA,CACV;AAGD,UAAM,KAAK,KAAK,gBAAgB,kBAAkB,EAAE,SAAS,MAAO;AAAA,EAAA;AAAA,EAGtE,MAAM,aAAa,aAA+E;AAC5F,QAAA;AACE,UAAA,CAAC,KAAK,MAAM;AACR,cAAA,IAAI,MAAM,sBAAsB;AAAA,MAAA;AAIlC,YAAA,aAAa,MAAM,KAAK,gBAAgB;AAC9C,UAAI,YAAY;AACd,eAAO,EAAE,SAAS,MAAM,SAAS,wBAAwB;AAAA,MAAA;AAI3D,UAAI,YAAY,gBAAgB;AACxB,cAAA,KAAK,sBAAsB,YAAY,cAAc;AAC3D,cAAM,KAAK,KAAK,OAAO,EAAE,WAAW,gBAAgB;AAE9C,cAAA,gBAAgB,MAAM,KAAK,gBAAgB;AACjD,YAAI,eAAe;AACjB,gBAAM,KAAK,YAAY;AACvB,iBAAO,EAAE,SAAS,MAAM,SAAS,mCAAmC;AAAA,QAAA;AAAA,MACtE;AAIE,UAAA,YAAY,SAAS,YAAY,UAAU;AAC7C,cAAM,KAAK,aAAa,YAAY,OAAO,YAAY,QAAQ;AAC/D,cAAM,KAAK,YAAY;AACvB,eAAO,EAAE,SAAS,MAAM,SAAS,mBAAmB;AAAA,MAAA;AAGtD,aAAO,EAAE,SAAS,OAAO,SAAS,0CAA0C;AAAA,aACrE,OAAO;AACN,cAAA,MAAM,0BAA0B,KAAK;AAC7C,aAAO,EAAE,SAAS,OAAO,SAAS,MAAM,QAAQ;AAAA,IAAA;AAAA,EAClD;AAAA,EAGF,MAAc,kBAAoC;AAC5C,QAAA;AACE,UAAA,CAAC,KAAK,KAAa,QAAA;AAGvB,YAAM,kBAAkB;AAAA,QACtB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,iBAAW,YAAY,iBAAiB;AAClC,YAAA;AACF,gBAAM,KAAK,KAAK,gBAAgB,UAAU,EAAE,SAAS,KAAM;AACpD,iBAAA;AAAA,QAAA,QACD;AACN;AAAA,QAAA;AAAA,MACF;AAGK,aAAA;AAAA,IAAA,QACD;AACC,aAAA;AAAA,IAAA;AAAA,EACT;AAAA,EAGF,MAAc,aAAa,OAAe,UAAiC;AACrE,QAAA,CAAC,KAAK,MAAM;AACR,YAAA,IAAI,MAAM,sBAAsB;AAAA,IAAA;AAIlC,UAAA,KAAK,KAAK,MAAM,wDAAwD;AAG9E,UAAM,KAAK,KAAK,gBAAgB,4CAA4C,EAAE,SAAS,KAAO;AAG9F,UAAM,KAAK,KAAK,KAAK,4CAA4C,KAAK;AACtE,UAAM,KAAK,KAAK,KAAK,kDAAkD,QAAQ;AAGzE,UAAA,KAAK,KAAK,MAAM,oEAAoE;AAGpF,UAAA,KAAK,KAAK,kBAAkB,EAAE,WAAW,gBAAgB,SAAS,MAAO;AAGzE,UAAA,aAAa,MAAM,KAAK,gBAAgB;AAC9C,QAAI,CAAC,YAAY;AACT,YAAA,IAAI,MAAM,2BAA2B;AAAA,IAAA;AAAA,EAC7C;AAAA,EAGF,MAAM,qBAAuC;AACvC,QAAA,CAAC,KAAK,MAAM;AACR,YAAA,IAAI,MAAM,sBAAsB;AAAA,IAAA;AAGpC,QAAA;AAEF,YAAM,KAAK,KAAK,gBAAgB,yDAAyD,EAAE,SAAS,KAAO;AAG3G,YAAM,SAAS,MAAM,KAAK,KAAK,SAAS,MAAM;AACtC,cAAA,gBAAgB,SAAS,iBAAiB,8BAA8B;AAC9E,cAAMA,UAAkB,CAAC;AAEX,sBAAA,QAAQ,CAAC,SAAS,UAAU;AAClC,gBAAA,cAAc,QAAQ,cAAc,sBAAsB;AAC1D,gBAAA,kBAAkB,QAAQ,cAAc,4BAA4B;AACpE,gBAAA,gBAAgB,QAAQ,cAAc,kBAAkB;AAE9D,cAAI,aAAa;AACf,kBAAM,OAAO,YAAY,aAAa,KAAU,KAAA,SAAS,QAAQ,CAAC;AAClE,kBAAM,SAAS,KAAK,MAAM,GAAG,EAAE,CAAC,KAAK;AACrC,kBAAM,WAAW,iBAAiB,aAAa,KAAU,KAAA;AACzD,kBAAM,aAAa,eAAe,aAAa,KAAU,KAAA;AACzD,kBAAM,SAAS,SAAS,WAAW,QAAQ,KAAK,EAAE,CAAC,KAAK;AAExDA,oBAAO,KAAK;AAAA,cACV,IAAI,SAAS,KAAK;AAAA,cAClB;AAAA,cACA;AAAA,cACA,UAAU,SAAS,YAAY;AAAA,cAC/B,MAAM;AAAA,cACN,UAAU,CAAC,QAAQ,UAAU,SAAS,UAAU;AAAA,cAChD,gBAAgB;AAAA,cAChB,gBAAgB;AAAA,cAChB,kBAAkB;AAAA,YAAA,CACnB;AAAA,UAAA;AAAA,QACH,CACD;AAEMA,eAAAA,QAAO,MAAM,GAAG,EAAE;AAAA,MAAA,CAC1B;AAEM,aAAA;AAAA,aACA,OAAO;AACN,cAAA,MAAM,yBAAyB,KAAK;AAC5C,aAAO,CAAC;AAAA,IAAA;AAAA,EACV;AAAA,EAGF,MAAM,YAAY,WAAkC;AAC9C,QAAA,CAAC,KAAK,MAAM;AACR,YAAA,IAAI,MAAM,sBAAsB;AAAA,IAAA;AAIxC,UAAM,KAAK,KAAK,SAAS,CAAC,SAAS;AAC3B,YAAA,gBAAgB,SAAS,iBAAiB,8BAA8B;AAE9E,iBAAW,WAAW,eAAe;AAC7B,cAAA,cAAc,QAAQ,cAAc,sBAAsB;AAChE,YAAI,aAAa,aAAa,SAAS,IAAI,GAAG;AAC3C,kBAAwB,MAAM;AAC/B;AAAA,QAAA;AAAA,MACF;AAAA,OAED,SAAS;AAGN,UAAA,KAAK,KAAK,eAAe,GAAI;AAAA,EAAA;AAAA,EAGrC,MAAM,cAAc,QAA+B;AAC7C,QAAA,CAAC,KAAK,MAAM;AACR,YAAA,IAAI,MAAM,sBAAsB;AAAA,IAAA;AAIxC,UAAM,gBAAgB;AAAA,MACpB,eAAe,MAAM;AAAA,MACrB,SAAS,OAAO,YAAA,CAAa;AAAA,MAC7B,WAAW,OAAO,aAAa;AAAA,IACjC;AAEA,eAAW,YAAY,eAAe;AAChC,UAAA;AACI,cAAA,KAAK,KAAK,MAAM,QAAQ;AACxB,cAAA,KAAK,KAAK,eAAe,GAAG;AAClC;AAAA,MAAA,QACM;AACN;AAAA,MAAA;AAAA,IACF;AAIF,UAAM,KAAK,KAAK,SAAS,CAACC,YAAW;AAC7B,YAAA,WAAW,SAAS,iBAAiB,kDAAkD;AAE7F,iBAAW,WAAW,UAAU;AAC9B,YAAI,QAAQ,aAAa,SAASA,OAAM,GAAG;AACxC,kBAAwB,MAAM;AAC/B;AAAA,QAAA;AAAA,MACF;AAAA,OAED,MAAM;AAAA,EAAA;AAAA,EAGX,MAAM,eAAe,QAA+B;AAC9C,QAAA,CAAC,KAAK,MAAM;AACR,YAAA,IAAI,MAAM,sBAAsB;AAAA,IAAA;AAIxC,UAAM,kBAAkB;AAAA,MACtB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,eAAW,YAAY,iBAAiB;AAClC,UAAA;AACI,cAAA,KAAK,KAAK,MAAM,QAAQ;AACxB,cAAA,KAAK,KAAK,SAAS,UAAU;AACnC,cAAM,KAAK,KAAK,KAAK,UAAU,OAAO,UAAU;AAChD;AAAA,MAAA,QACM;AACN;AAAA,MAAA;AAAA,IACF;AAGI,UAAA,IAAI,MAAM,yCAAyC;AAAA,EAAA;AAAA,EAG3D,MAAM,aAAa,WAA4E;AACzF,QAAA;AACE,UAAA,CAAC,KAAK,MAAM;AACR,cAAA,IAAI,MAAM,sBAAsB;AAAA,MAAA;AAGlC,YAAA,iBAAiB,cAAc,SACjC,oEACA;AAEE,YAAA,KAAK,KAAK,MAAM,cAAc;AAG9B,YAAA,KAAK,KAAK,eAAe,GAAI;AAGnC,YAAM,eAAe,MAAM,KAAK,KAAK,SAAS,MAAM;AAClD,cAAM,oBAAoB;AAAA,UACxB;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAEA,eAAO,kBAAkB;AAAA,UAAK,CAC5B,aAAA,SAAS,cAAc,QAAQ,MAAM;AAAA,QACvC;AAAA,MAAA,CACD;AAEM,aAAA;AAAA,QACL,SAAS;AAAA,QACT,SAAS,eAAe,gCAAgC;AAAA,MAC1D;AAAA,aACO,OAAO;AACN,cAAA,MAAM,2BAA2B,KAAK;AAC9C,aAAO,EAAE,SAAS,OAAO,SAAS,MAAM,QAAQ;AAAA,IAAA;AAAA,EAClD;AAAA,EAGF,MAAM,kBAAkB,aAAiD;AACnE,QAAA,CAAC,KAAK,MAAM;AACR,YAAA,IAAI,MAAM,sBAAsB;AAAA,IAAA;AAGxC,QAAI,aAAa;AACR,aAAA,MAAM,KAAK,KAAK,WAAW;AAAA,QAChC,MAAM;AAAA,UACJ,GAAG,YAAY;AAAA,UACf,GAAG,YAAY;AAAA,UACf,OAAO,YAAY;AAAA,UACnB,QAAQ,YAAY;AAAA,QAAA;AAAA,MACtB,CACD;AAAA,IAAA;AAGH,WAAO,MAAM,KAAK,KAAK,WAAW,EAAE,UAAU,OAAO;AAAA,EAAA;AAAA,EAGvD,MAAc,cAA6B;AACrC,QAAA,CAAC,KAAK,KAAM;AAEZ,QAAA;AACF,YAAM,UAAU,MAAM,KAAK,KAAK,QAAQ;AACxC,oBAAc,KAAK,aAAa,KAAK,UAAU,SAAS,MAAM,CAAC,CAAC;AAAA,aACzD,OAAO;AACN,cAAA,MAAM,2BAA2B,KAAK;AAAA,IAAA;AAAA,EAChD;AAAA,EAGF,MAAc,cAA6B;AACzC,QAAI,CAAC,KAAK,QAAQ,CAAC,WAAW,KAAK,WAAW,EAAG;AAE7C,QAAA;AACF,YAAM,cAAc,aAAa,KAAK,aAAa,MAAM;AACnD,YAAA,UAAU,KAAK,MAAM,WAAW;AACtC,YAAM,KAAK,KAAK,UAAU,GAAG,OAAO;AAAA,aAC7B,OAAO;AACN,cAAA,MAAM,2BAA2B,KAAK;AAAA,IAAA;AAAA,EAChD;AAAA,EAGF,MAAc,sBAAsB,eAAsC;AACpE,QAAA,CAAC,KAAK,KAAM;AAEZ,QAAA;AACI,YAAA,UAAU,KAAK,MAAM,aAAa;AACxC,YAAM,KAAK,KAAK,UAAU,GAAG,OAAO;AAAA,aAC7B,OAAO;AACN,cAAA,MAAM,uCAAuC,KAAK;AAAA,IAAA;AAAA,EAC5D;AAAA,EAGF,MAAM,QAAuB;AAC3B,QAAI,KAAK,SAAS;AACV,YAAA,KAAK,QAAQ,MAAM;AACzB,WAAK,UAAU;AACf,WAAK,OAAO;AAAA,IAAA;AAAA,EACd;AAEJ;ACtbO,MAAe,iBAAyC;AAAA,EACtD;AAAA,EACA;AAAA,EACA;AAAA,EAEG,oBAA4B;AAAA,EAC5B,kBAA0B;AAAA,EAC1B,cAAsB;AAAA,EACtB;AAAA,EAEV,YAAY,MAAc,aAAqB,QAAwB;AACrE,SAAK,OAAO;AACZ,SAAK,cAAc;AACnB,SAAK,SAAS;AACd,SAAK,gBAAgB,OAAO,cAAc,OAAO,UAAU;AAAA,EAAA;AAAA,EAK7D,oBAAoB,YAAgC;AAClD,QAAI,aAAa;AAGb,QAAA,WAAW,OAAO,IAAI;AACV,oBAAA;AAAA,IAAA,WACL,WAAW,OAAO,IAAI;AACjB,oBAAA;AAAA,IAAA,WACL,WAAW,OAAO,MAAM,WAAW,OAAO,IAAI;AACzC,oBAAA;AAAA,IAAA;AAIZ,QAAA,WAAW,WAAW,WAAW,SAAS;AAC9B,oBAAA;AAAA,IACL,WAAA,WAAW,WAAW,WAAW,SAAS;AACrC,oBAAA;AAAA,IAAA;AAIhB,QAAI,WAAW,UAAU,QAAQ,WAAW,UAAU,QAAQ;AAC9C,oBAAA;AAAA,IAAA,OACT;AACS,oBAAA;AAAA,IAAA;AAIZ,QAAA,WAAW,aAAa,MAAM;AAClB,oBAAA;AAAA,IAAA,WACL,WAAW,aAAa,MAAM;AACzB,oBAAA;AAAA,IAAA,OACT;AACS,oBAAA;AAAA,IAAA;AAGT,WAAA,KAAK,IAAI,YAAY,GAAG;AAAA,EAAA;AAAA,EAGjC,QAAc;AACZ,SAAK,oBAAoB;AACzB,SAAK,kBAAkB;AACvB,SAAK,cAAc;AACnB,SAAK,gBAAgB,KAAK,OAAO,cAAc,KAAK,OAAO,UAAU;AAAA,EAAA;AAAA,EAG7D,YAAY,KAAoB;AACnC,SAAA;AAEL,QAAI,KAAK;AACF,WAAA;AACL,WAAK,oBAAoB;AAAA,IAAA,OACpB;AACA,WAAA;AACL,WAAK,kBAAkB;AAAA,IAAA;AAAA,EACzB;AAAA,EAGQ,mBAAmB,YAAwC;AACnE,QAAI,QAAQ;AAGR,QAAA,WAAW,OAAO,IAAI;AACf,eAAA;AAAA,IAAA,WACA,WAAW,OAAO,IAAI;AACtB,eAAA;AAAA,IAAA,WACA,WAAW,OAAO,IAAI;AACtB,eAAA;AAAA,IAAA,WACA,WAAW,OAAO,IAAI;AACtB,eAAA;AAAA,IAAA;AAIP,QAAA,WAAW,WAAW,WAAW,SAAS;AACnC,eAAA;AAAA,IAAA,OACJ;AACI,eAAA;AAAA,IAAA;AAIP,QAAA,WAAW,UAAU,MAAM;AACpB,eAAA;AAAA,IAAA,WACA,WAAW,UAAU,QAAQ;AAC7B,eAAA;AAAA,IAAA;AAGJ,WAAA,QAAQ,IAAI,SAAS;AAAA,EAAA;AAEhC;AC1GO,MAAM,2BAA2B,iBAAiB;AAAA,EAChD;AAAA,EAER,YAAY,QAA0B;AAC/B,UAAA,cAAc,gFAAgF,MAAM;AAC1G,SAAK,SAAS;AACd,SAAK,gBAAgB,OAAO;AAAA,EAAA;AAAA,EAG7B,MAAM,QAAQ,YAAgD;AACvD,UAAA,aAAa,KAAK,oBAAoB,UAAU;AAGhD,UAAA,YAAY,KAAK,mBAAmB,UAAU;AAG9C,UAAA,cAAc,KAAK,qBAAqB;AAG9C,QAAI,KAAK,qBAAqB,KAAK,OAAO,UAAU;AAC5C,aAAA;AAAA,QACN,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR;AAAA,QACA,WAAW,6BAA6B,KAAK,OAAO,QAAQ;AAAA,QAC5D,UAAU;AAAA,UACT,mBAAmB,KAAK;AAAA,UACxB,eAAe,KAAK;AAAA,QAAA;AAAA,MAEtB;AAAA,IAAA;AAGM,WAAA;AAAA,MACN,QAAQ,cAAc,SAAS,QAAQ;AAAA,MACvC,QAAQ;AAAA,MACR;AAAA,MACA,WAAW,KAAK,kBAAkB,YAAY,WAAW,WAAW;AAAA,MACpE,UAAU;AAAA,QACT,UAAU;AAAA,QACV;AAAA,QACA,mBAAmB,KAAK;AAAA,QACxB,MAAM,KAAK,oBAAoB;AAAA,QAC/B,YAAY,KAAK,OAAO;AAAA,QACxB,YAAY,KAAK,OAAO;AAAA,MAAA;AAAA,IAE1B;AAAA,EAAA;AAAA,EAGO,uBAA+B;AAClC,QAAA,KAAK,sBAAsB,GAAG;AACjC,aAAO,KAAK,OAAO;AAAA,IAAA;AAGb,WAAA,KAAK,OAAO,aAAa,KAAK,IAAI,KAAK,OAAO,YAAY,KAAK,iBAAiB;AAAA,EAAA;AAAA,EAGhF,kBAAkB,YAAwB,WAA2B,QAAwB;AACpG,UAAM,UAAU,CAAC;AAGb,QAAA,WAAW,OAAO,IAAI;AACzB,cAAQ,KAAK,+BAA+B;AAAA,IAAA,WAClC,WAAW,OAAO,IAAI;AAChC,cAAQ,KAAK,iCAAiC;AAAA,IAAA;AAG3C,QAAA,WAAW,WAAW,WAAW,SAAS;AAC7C,cAAQ,KAAK,aAAa;AAAA,IAAA,OACpB;AACN,cAAQ,KAAK,eAAe;AAAA,IAAA;AAGzB,QAAA,WAAW,UAAU,YAAY;AACpC,cAAQ,KAAK,gBAAgB,WAAW,KAAK,EAAE;AAAA,IAAA;AAI5C,QAAA,KAAK,sBAAsB,GAAG;AACjC,cAAQ,KAAK,kCAAkC;AAAA,IAAA,OACzC;AACN,cAAQ,KAAK,mBAAmB,KAAK,oBAAoB,CAAC,aAAa,MAAM,EAAE;AAAA,IAAA;AAGzE,WAAA,GAAG,UAAU,YAAa,CAAA,YAAY,QAAQ,KAAK,IAAI,CAAC;AAAA,EAAA;AAAA;AAAA,EAItD,YAAY,KAAoB;AACzC,UAAM,YAAY,GAAG;AAEjB,QAAA,OAAO,KAAK,OAAO,YAAY;AAE7B,WAAA,gBAAgB,KAAK,OAAO;AACjC,WAAK,oBAAoB;AAAA,IAAA,WACf,CAAC,KAAK;AAEX,WAAA,gBAAgB,KAAK,qBAAqB;AAAA,IAAA;AAAA,EAChD;AAAA,EAGD,QAAc;AACb,UAAM,MAAM;AACP,SAAA,gBAAgB,KAAK,OAAO;AAAA,EAAA;AAAA;AAAA,EAIlC,cAAc,KAAoB;AACjC,SAAK,YAAY,GAAG;AAAA,EAAA;AAAA;AAAA,EAIrB,WAME;AACM,WAAA;AAAA,MACN,MAAM,KAAK,oBAAoB;AAAA,MAC/B,eAAe,KAAK;AAAA,MACpB,mBAAmB,KAAK;AAAA,MACxB,UAAU,KAAK,OAAO;AAAA,MACtB,UAAU,KAAK,oBAAoB,KAAK,OAAO;AAAA,IAChD;AAAA,EAAA;AAEF;AC9HO,MAAM,0BAA0B,iBAAiB;AAAA,EAC/C;AAAA,EACA,cAAsB;AAAA,EAE9B,YAAY,QAAyB;AACpC;AAAA,MACC;AAAA,MACA;AAAA,MACA;AAAA,IACD;AACA,SAAK,SAAS;AACd,SAAK,gBAAgB,OAAO;AAAA,EAAA;AAAA,EAG7B,MAAM,QAAQ,YAAgD;AACvD,UAAA,aAAa,KAAK,oBAAoB,UAAU;AAGhD,UAAA,YAAY,KAAK,mBAAmB,UAAU;AAG9C,UAAA,cAAc,KAAK,qBAAqB;AAG9C,QAAI,KAAK,eAAe,KAAK,OAAO,UAAU;AACtC,aAAA;AAAA,QACN,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR;AAAA,QACA,WAAW,6BAA6B,KAAK,OAAO,QAAQ;AAAA,QAC5D,UAAU;AAAA,UACT,aAAa,KAAK;AAAA,UAClB,eAAe,KAAK;AAAA,QAAA;AAAA,MAEtB;AAAA,IAAA;AAGM,WAAA;AAAA,MACN,QAAQ,cAAc,SAAS,QAAQ;AAAA,MACvC,QAAQ;AAAA,MACR;AAAA,MACA,WAAW,KAAK,kBAAkB,YAAY,WAAW,WAAW;AAAA,MACpE,UAAU;AAAA,QACT,UAAU;AAAA,QACV;AAAA,QACA,aAAa,KAAK;AAAA,QAClB,YAAY,KAAK,OAAO;AAAA,QACxB,WAAW,KAAK,OAAO;AAAA,MAAA;AAAA,IAEzB;AAAA,EAAA;AAAA,EAGO,uBAA+B;AACtC,WAAO,KAAK,IAAI,KAAK,OAAO,aAAa,KAAK,cAAc,KAAK,OAAO,WAAW,KAAK,OAAO,UAAU;AAAA,EAAA;AAAA,EAGlG,kBAAkB,YAAwB,WAA2B,QAAwB;AACpG,UAAM,UAAU,CAAC;AAGb,QAAA,WAAW,OAAO,IAAI;AACzB,cAAQ,KAAK,+BAA+B;AAAA,IAAA,WAClC,WAAW,OAAO,IAAI;AAChC,cAAQ,KAAK,iCAAiC;AAAA,IAAA;AAG3C,QAAA,WAAW,WAAW,WAAW,SAAS;AAC7C,cAAQ,KAAK,aAAa;AAAA,IAAA,OACpB;AACN,cAAQ,KAAK,eAAe;AAAA,IAAA;AAGzB,QAAA,WAAW,UAAU,YAAY;AACpC,cAAQ,KAAK,gBAAgB,WAAW,KAAK,EAAE;AAAA,IAAA;AAI5C,QAAA,KAAK,gBAAgB,GAAG;AAC3B,cAAQ,KAAK,8BAA8B;AAAA,IAAA,OACrC;AACN,cAAQ,KAAK,mBAAmB,KAAK,cAAc,CAAC,aAAa,MAAM,EAAE;AAAA,IAAA;AAGnE,WAAA,GAAG,UAAU,YAAa,CAAA,YAAY,QAAQ,KAAK,IAAI,CAAC;AAAA,EAAA;AAAA;AAAA,EAItD,YAAY,KAAoB;AACzC,UAAM,YAAY,GAAG;AAErB,QAAI,KAAK;AAER,WAAK,cAAc,KAAK,IAAI,GAAG,KAAK,cAAc,CAAC;AAAA,IAAA,OAC7C;AAED,WAAA,cAAc,KAAK,IAAI,KAAK,OAAO,WAAW,GAAG,KAAK,cAAc,CAAC;AAAA,IAAA;AAGtE,SAAA,gBAAgB,KAAK,qBAAqB;AAAA,EAAA;AAAA,EAGhD,QAAc;AACb,UAAM,MAAM;AACZ,SAAK,cAAc;AACd,SAAA,gBAAgB,KAAK,OAAO;AAAA,EAAA;AAAA;AAAA,EAIlC,cAAc,KAAoB;AACjC,SAAK,YAAY,GAAG;AAAA,EAAA;AAAA;AAAA,EAIrB,oBAAoB,YAAgC;AAC/C,QAAA,aAAa,MAAM,oBAAoB,UAAU;AAIrD,UAAM,cAAe,KAAK,cAAc,KAAK,OAAO,WAAY;AAClD,kBAAA;AAGV,QAAA,KAAK,kBAAkB,GAAG;AAC7B,oBAAc,KAAK,IAAI,KAAK,kBAAkB,GAAG,EAAE;AAAA,IAAA;AAIhD,QAAA,KAAK,oBAAoB,GAAG;AAChB,qBAAA,KAAK,oBAAoB,KAAK;AAAA,IAAA;AAG9C,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,YAAY,GAAG,CAAC;AAAA,EAAA;AAAA;AAAA,EAI7C,WAQE;AACD,QAAI,uBAAkD;AAElD,QAAA,KAAK,oBAAoB,GAAG;AACR,6BAAA;AAAA,IAAA,WACb,KAAK,kBAAkB,KAAK,KAAK,cAAc,GAAG;AACrC,6BAAA;AAAA,IAAA;AAGjB,WAAA;AAAA,MACN,MAAM,KAAK,cAAc;AAAA,MACzB,eAAe,KAAK;AAAA,MACpB,YAAY,KAAK,OAAO;AAAA,MACxB,WAAW,KAAK,OAAO;AAAA,MACvB,UAAU,KAAK,OAAO;AAAA,MACtB,UAAU,KAAK,cAAc,KAAK,OAAO;AAAA,MACzC;AAAA,IACD;AAAA,EAAA;AAEF;AClKO,MAAM,yBAAyB,iBAAiB;AAAA,EAC9C;AAAA,EACA,aAAsC;AAAA,EACtC,iBAAyB;AAAA,EAEjC,YAAY,QAAwB;AACnC;AAAA,MACC;AAAA,MACA;AAAA,MACA;AAAA,IACD;AACA,SAAK,SAAS;AACd,SAAK,gBAAgB,OAAO;AAAA,EAAA;AAAA,EAG7B,MAAM,QAAQ,YAAgD;AACvD,UAAA,aAAa,KAAK,oBAAoB,UAAU;AAGhD,UAAA,UAAU,KAAK,eAAe,UAAU;AACxC,UAAA,YAAY,KAAK,8BAA8B,OAAO;AAGtD,UAAA,cAAc,KAAK,OAAO;AAG5B,QAAA,QAAQ,WAAW,KAAK;AACpB,aAAA;AAAA,QACN,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR;AAAA,QACA,WAAW,8BAA8B,QAAQ,WAAW,KAAK,QAAQ,CAAC,CAAC;AAAA,QAC3E,UAAU;AAAA,UACT;AAAA,UACA,KAAK,WAAW;AAAA,UAChB,UAAU,WAAW;AAAA,UACrB,SAAS,WAAW;AAAA,QAAA;AAAA,MAEtB;AAAA,IAAA;AAGI,SAAA,aAAa,cAAc,SAAS,QAAQ;AACjD,SAAK,iBAAiB,QAAQ;AAEvB,WAAA;AAAA,MACN,QAAQ,KAAK;AAAA,MACb,QAAQ;AAAA,MACR;AAAA,MACA,WAAW,KAAK,kBAAkB,YAAY,WAAW,OAAO;AAAA,MAChE,UAAU;AAAA,QACT,UAAU;AAAA,QACV;AAAA,QACA;AAAA,QACA,WAAW,KAAK,OAAO;AAAA,QACvB,QAAQ,KAAK,OAAO;AAAA,MAAA;AAAA,IAEtB;AAAA,EAAA;AAAA,EAGO,eAAe,YAMrB;AAED,QAAI,YAAwC;AAC5C,QAAI,WAAW,OAAO,KAAK,OAAO,mBAAmB;AACxC,kBAAA;AAAA,IACF,WAAA,WAAW,OAAO,KAAK,OAAO,qBAAqB;AACjD,kBAAA;AAAA,IAAA;AAIb,QAAI,YAAwC;AACtC,UAAA,UAAU,WAAW,WAAW,WAAW;AAC3C,UAAA,iBAAkB,UAAU,WAAW,UAAW;AAExD,QAAI,iBAAiB,KAAK;AACb,kBAAA;AAAA,IAAA,WACF,iBAAiB,MAAM;AACrB,kBAAA;AAAA,IAAA;AAIb,QAAI,cAA0C;AAC1C,QAAA,WAAW,UAAU,MAAM;AAChB,oBAAA;AAAA,IAAA,WACJ,WAAW,UAAU,QAAQ;AACzB,oBAAA;AAAA,IAAA;AAIf,UAAM,mBAAmB,WAAW,aAAa,OAAO,cAAc;AAGtE,UAAM,WAAW,KAAK,wBAAwB,WAAW,WAAW,aAAa,gBAAgB;AAE1F,WAAA;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EAAA;AAAA,EAGO,wBACP,WACA,WACA,aACA,kBACS;AACT,QAAI,WAAW;AACf,QAAI,cAAc;AAGH,mBAAA;AACf,QAAI,cAAc,WAAW;AAChB,kBAAA;AAAA,IAAA;AAIE,mBAAA;AACf,QAAI,cAAc,WAAW;AAChB,kBAAA;AAAA,IAAA;AAIE,mBAAA;AACf,QAAI,gBAAgB,WAAW;AAClB,kBAAA;AAAA,IAAA;AAIE,mBAAA;AACf,QAAI,qBAAqB,aAAa;AACzB,kBAAA;AAAA,IAAA;AAGb,WAAO,WAAW;AAAA,EAAA;AAAA,EAGX,8BAA8B,SAA8B;AACnE,QAAI,QAAQ;AAGR,QAAA,QAAQ,cAAc,OAAO;AACvB,eAAA;AAAA,IAAA,WACC,QAAQ,cAAc,QAAQ;AAC/B,eAAA;AAAA,IAAA;AAIN,QAAA,QAAQ,cAAc,OAAO;AACvB,eAAA;AAAA,IAAA,WACC,QAAQ,cAAc,QAAQ;AAC/B,eAAA;AAAA,IAAA;AAIN,QAAA,QAAQ,gBAAgB,OAAO;AACzB,eAAA;AAAA,IAAA,WACC,QAAQ,gBAAgB,QAAQ;AACjC,eAAA;AAAA,IAAA;AAGH,WAAA,QAAQ,IAAI,SAAS;AAAA,EAAA;AAAA,EAGrB,kBAAkB,YAAwB,WAA2B,SAAsB;AAClG,UAAM,UAAU,CAAC;AAGb,QAAA,QAAQ,cAAc,OAAO;AAChC,cAAQ,KAAK,mBAAmB,WAAW,IAAI,QAAQ,CAAC,CAAC,EAAE;AAAA,IAAA,WACjD,QAAQ,cAAc,QAAQ;AACxC,cAAQ,KAAK,qBAAqB,WAAW,IAAI,QAAQ,CAAC,CAAC,EAAE;AAAA,IAAA;AAI1D,QAAA,QAAQ,cAAc,OAAO;AAChC,cAAQ,KAAK,uBAAuB;AAAA,IAAA,WAC1B,QAAQ,cAAc,QAAQ;AACxC,cAAQ,KAAK,uBAAuB;AAAA,IAAA;AAIjC,QAAA,QAAQ,gBAAgB,WAAW;AACtC,cAAQ,KAAK,GAAG,WAAW,KAAK,kBAAkB;AAAA,IAAA;AAI/C,QAAA,QAAQ,qBAAqB,aAAa;AAC7C,cAAQ,KAAK,4BAA4B;AAAA,IAAA;AAG1C,UAAM,mBAAmB,QAAQ,WAAW,KAAK,QAAQ,CAAC;AAEnD,WAAA,GAAG,UAAU,YAAA,CAAa,YAAY,eAAe,gBAAgB,QAAQ,KAAK,IAAI,CAAC;AAAA,EAAA;AAAA;AAAA,EAI/F,oBAAoB,YAAgC;AAC/C,QAAA,aAAa,MAAM,oBAAoB,UAAU;AAGrD,UAAM,cAAc,KAAK;AAAA,MACxB,KAAK,IAAI,WAAW,MAAM,KAAK,OAAO,iBAAiB;AAAA,MACvD,KAAK,IAAI,WAAW,MAAM,KAAK,OAAO,mBAAmB;AAAA,IAC1D;AAGA,QAAI,cAAc,IAAI;AACP,oBAAA;AAAA,IAAA,WACJ,cAAc,IAAI;AACd,oBAAA;AAAA,IAAA;AAIT,UAAA,gBAAgB,KAAK,IAAI,WAAW,WAAW,WAAW,OAAO,IAAI,WAAW;AACtF,QAAI,gBAAgB,MAAO;AACZ,oBAAA;AAAA,IAAA;AAIX,QAAA,KAAK,kBAAkB,GAAG;AACf,oBAAA;AAAA,IAAA;AAGf,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,YAAY,GAAG,CAAC;AAAA,EAAA;AAAA,EAG7C,QAAc;AACb,UAAM,MAAM;AACZ,SAAK,aAAa;AAClB,SAAK,iBAAiB;AACjB,SAAA,gBAAgB,KAAK,OAAO;AAAA,EAAA;AAAA;AAAA,EAIlC,cAAc,KAAoB;AACjC,SAAK,YAAY,GAAG;AAAA,EAAA;AAAA;AAAA,EAIrB,WASE;AACM,WAAA;AAAA,MACN,QAAQ,KAAK,OAAO;AAAA,MACpB,YAAY,KAAK;AAAA,MACjB,gBAAgB,KAAK;AAAA,MACrB,WAAW,KAAK,OAAO;AAAA,MACvB,mBAAmB,KAAK,OAAO;AAAA,MAC/B,qBAAqB,KAAK,OAAO;AAAA,MACjC,iBAAiB,KAAK;AAAA,MACtB,mBAAmB,KAAK;AAAA,IACzB;AAAA,EAAA;AAEF;ACzQO,MAAM,gBAAgB;AAAA,EAC5B,OAAe,aAAqD,oBAAI,IAAI;AAAA,IAC3E,CAAC,cAAc,kBAAyB;AAAA,IACxC,CAAC,aAAa,iBAAwB;AAAA,IACtC,CAAC,aAAa,gBAAuB;AAAA,EAAA,CACrC;AAAA,EAEO,iBAAsC;AAAA,EAE9C,OAAO,yBAA4G;AAC3G,WAAA;AAAA,MACN;AAAA,QACC,MAAM;AAAA,QACN,aAAa;AAAA,QACb,eAAe;AAAA,UACd,YAAY;AAAA,UACZ,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,YAAY;AAAA,QAAA;AAAA,MAEd;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,aAAa;AAAA,QACb,eAAe;AAAA,UACd,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,UAAU;AAAA,QAAA;AAAA,MAEZ;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,aAAa;AAAA,QACb,eAAe;AAAA,UACd,QAAQ;AAAA,UACR,WAAW;AAAA,UACX,mBAAmB;AAAA,UACnB,qBAAqB;AAAA,UACrB,UAAU;AAAA,UACV,SAAS;AAAA,QAAA;AAAA,MACV;AAAA,IAEF;AAAA,EAAA;AAAA,EAGD,YAAY,cAA4B,QAA8B;AACrE,UAAM,gBAAgB,gBAAgB,WAAW,IAAI,YAAY;AAEjE,QAAI,CAAC,eAAe;AACnB,YAAM,IAAI,MAAM,YAAY,YAAY,YAAY;AAAA,IAAA;AAGhD,SAAA,iBAAiB,IAAK,cAAsB,MAAM;AAAA,EAAA;AAAA,EAGxD,MAAM,gBAAgB,YAAuD;AACxE,QAAA,CAAC,KAAK,gBAAgB;AACnB,YAAA,IAAI,MAAM,wBAAwB;AAAA,IAAA;AAIzC,UAAM,aAAa,KAAK,eAAe,oBAAoB,UAAU;AAGrE,QAAI,aAAa,IAAI;AAEb,aAAA;AAAA,QACN,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR;AAAA,QACA,WAAW,uBAAuB,WAAW,QAAQ,CAAC,CAAC;AAAA,MACxD;AAAA,IAAA;AAGD,WAAO,MAAM,KAAK,eAAe,QAAQ,UAAU;AAAA,EAAA;AAAA,EAGpD,oBAAyC;AACxC,WAAO,KAAK;AAAA,EAAA;AAAA,EAGb,gBAAsB;AACrB,QAAI,KAAK,gBAAgB;AACxB,WAAK,eAAe,MAAM;AAAA,IAAA;AAAA,EAC3B;AAEF;ACzFO,MAAM,WAAW;AAAA,EACd,SAAkC;AAAA,EAClC,gBAAyB;AAAA,EAEjC,MAAM,aAA4B;AAChC,QAAI,KAAK,cAAe;AAEpB,QAAA;AACF,WAAK,SAAS,MAAM,UAAU,aAAa,OAAO,GAAG;AAAA,QACnD,QAAQ,CAAK,MAAA;AACP,cAAA,EAAE,WAAW,oBAAoB;AAC3B,oBAAA,IAAI,kBAAkB,EAAE,WAAW,KAAK,QAAQ,CAAC,CAAC,GAAG;AAAA,UAAA;AAAA,QAC/D;AAAA,MACF,CACD;AAEK,YAAA,KAAK,OAAO,cAAc;AAAA,QAC9B,yBAAyB;AAAA,QACzB,uBAAuB,UAAU,IAAI;AAAA,QACrC,2BAA2B;AAAA,MAAA,CAC5B;AAED,WAAK,gBAAgB;AACrB,cAAQ,IAAI,sCAAsC;AAAA,aAC3C,OAAO;AACN,cAAA,MAAM,qCAAqC,KAAK;AAClD,YAAA;AAAA,IAAA;AAAA,EACR;AAAA,EAGF,MAAM,aAAa,YAAoB,aAAwD;AAC7F,QAAI,CAAC,KAAK,iBAAiB,CAAC,KAAK,QAAQ;AACjC,YAAA,IAAI,MAAM,6BAA6B;AAAA,IAAA;AAG3C,QAAA;AAEF,YAAM,YAAY,MAAM,KAAK,iBAAiB,UAAU;AAGlD,YAAA,aAAa,KAAK,oBAAoB,SAAS;AAGrD,YAAM,QAAQ,KAAK,eAAe,WAAW,UAAU;AAGvD,YAAM,aAAa,KAAK,oBAAoB,WAAW,UAAU;AAE1D,aAAA;AAAA,QACL,cAAc,UAAU;AAAA,QACxB,aAAa,UAAU;AAAA,QACvB,oBAAoB,UAAU;AAAA,QAC9B;AAAA,QACA,SAAS,WAAW;AAAA,QACpB,YAAY,WAAW;AAAA,QACvB,KAAK,WAAW;AAAA,QAChB,KAAK;AAAA,UACH,OAAO,WAAW;AAAA,UAClB,MAAM,WAAW;AAAA,QACnB;AAAA,QACA;AAAA,QACA,WAAW,KAAK,IAAI;AAAA,MACtB;AAAA,aACO,OAAO;AACN,cAAA,MAAM,0BAA0B,KAAK;AACvC,YAAA;AAAA,IAAA;AAAA,EACR;AAAA,EAGF,MAAc,iBAAiB,YAK5B;AACG,QAAA,CAAC,KAAK,QAAQ;AACV,YAAA,IAAI,MAAM,0BAA0B;AAAA,IAAA;AAGtC,UAAA,EAAE,MAAM,EAAE,KAAK,EAAA,IAAM,MAAM,KAAK,OAAO,UAAU,UAAU;AAGjE,UAAM,aAAa;AACb,UAAA,UAAU,KAAK,MAAM,UAAU;AAErC,QAAI,CAAC,WAAW,QAAQ,WAAW,GAAG;AAC9B,YAAA,IAAI,MAAM,mCAAmC;AAAA,IAAA;AAIrD,UAAM,SAAS,QAAQ,IAAI,CAAA,UAAS,WAAW,KAAK,CAAC,EAAE,OAAO,CAAA,UAAS,CAAC,MAAM,KAAK,CAAC;AACpF,UAAM,eAAe,OAAO,SAAS,IAAI,OAAO,CAAC,IAAI;AAGrD,UAAM,cAAc;AACd,UAAA,gBAAgB,KAAK,MAAM,WAAW;AAE5C,QAAI,cAAc;AAClB,QAAI,qBAAqB;AAErB,QAAA,iBAAiB,cAAc,SAAS,GAAG;AAC7C,oBAAc,WAAW,cAAc,CAAC,CAAC,KAAK;AAGxC,YAAA,eAAe,KAAK,MAAM,mBAAmB;AACnD,UAAI,cAAc;AAChB,6BAAqB,WAAW,aAAa,CAAC,CAAC,KAAK;AAAA,MAAA;AAAA,IACtD;AAGK,WAAA;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,SAAS;AAAA,IACX;AAAA,EAAA;AAAA,EAGM,oBAAoB,WAO1B;AAGA,UAAM,eAAe,UAAU;AAG/B,UAAM,UAAU,eAAe;AAC/B,UAAM,aAAa,eAAe;AAIlC,QAAI,MAAM;AACN,QAAA,UAAU,qBAAqB,GAAG;AAC9B,YAAA;AAAA,IAAA,WACG,UAAU,qBAAqB,IAAI;AACtC,YAAA;AAAA,IAAA,WACG,UAAU,qBAAqB,GAAG;AACrC,YAAA,KAAM,UAAU,qBAAqB;AAAA,IAAA,OACtC;AACC,YAAA,KAAM,UAAU,qBAAqB;AAAA,IAAA;AAI7C,UAAM,WAAW,gBAAgB,IAAK,UAAU,qBAAqB,MAAO;AAC5E,UAAM,UAAU,gBAAgB,IAAK,UAAU,qBAAqB,MAAO;AAG3E,UAAM,aAAa,KAAK,IAAI,UAAU,kBAAkB,IAAI;AAErD,WAAA;AAAA,MACL,SAAS,KAAK,IAAI,SAAS,CAAC;AAAA,MAC5B;AAAA,MACA,KAAK,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,GAAG,CAAC;AAAA,MACnC;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EAAA;AAAA,EAGM,eAAe,WAAgB,YAA6C;AAClF,UAAM,cAAc,UAAU;AACxB,UAAA,YAAY,WAAW,WAAW,WAAW;AAE/C,QAAA,cAAc,OAAO,WAAW;AAC3B,aAAA;AAAA,IACE,WAAA,cAAc,QAAQ,CAAC,WAAW;AACpC,aAAA;AAAA,IAAA,OACF;AACE,aAAA;AAAA,IAAA;AAAA,EACT;AAAA,EAGM,oBAAoB,WAAgB,YAAyB;AACnE,QAAI,aAAa;AAGb,QAAA,UAAU,eAAe,GAAG;AAChB,oBAAA;AAAA,IAAA;AAIhB,QAAI,KAAK,IAAI,UAAU,kBAAkB,IAAI,GAAG;AAChC,oBAAA;AAAA,IAAA;AAIhB,QAAI,WAAW,OAAO,MAAM,WAAW,OAAO,IAAI;AAClC,oBAAA;AAAA,IAAA;AAIZ,QAAA,WAAW,aAAa,MAAM;AAClB,oBAAA;AAAA,IAAA;AAGhB,WAAO,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,UAAU,CAAC;AAAA,EAAA;AAAA,EAG9C,MAAM,YAAY,YAAoB,aAAsD;AAC1F,QAAI,CAAC,KAAK,iBAAiB,CAAC,KAAK,QAAQ;AACjC,YAAA,IAAI,MAAM,6BAA6B;AAAA,IAAA;AAG3C,QAAA;AACF,YAAM,EAAE,KAAK,IAAI,MAAM,KAAK,OAAO,UAAU,UAAU;AAEvD,YAAM,UAAuB,CAAC;AAE9B,UAAI,KAAK,OAAO;AACH,mBAAA,QAAQ,KAAK,OAAO;AACzB,cAAA,KAAK,aAAa,IAAI;AACxB,oBAAQ,KAAK;AAAA,cACX,MAAM,KAAK;AAAA,cACX,YAAY,KAAK;AAAA,cACjB,MAAM;AAAA,gBACJ,IAAI,KAAK,KAAK;AAAA,gBACd,IAAI,KAAK,KAAK;AAAA,gBACd,IAAI,KAAK,KAAK;AAAA,gBACd,IAAI,KAAK,KAAK;AAAA,cAAA;AAAA,YAChB,CACD;AAAA,UAAA;AAAA,QACH;AAAA,MACF;AAGK,aAAA;AAAA,aACA,OAAO;AACN,cAAA,MAAM,2BAA2B,KAAK;AACxC,YAAA;AAAA,IAAA;AAAA,EACR;AAAA;AAAA,EAIF,0BAA0B,UAAyB,OAA2B;AACrE,WAAA;AAAA,MACL;AAAA,MACA,OAAO,SAAS;AAAA,MAChB,WAAW,SAAS;AAAA,MACpB,KAAK,SAAS;AAAA,MACd,UAAU,SAAS,IAAI;AAAA,MACvB,SAAS,SAAS,IAAI;AAAA,MACtB,YAAY,KAAK,IAAI,SAAS,kBAAkB,IAAI;AAAA,MACpD,OAAO,SAAS;AAAA,MAChB,YAAY,SAAS;AAAA,IACvB;AAAA,EAAA;AAAA,EAGF,MAAM,UAAyB;AAC7B,QAAI,KAAK,QAAQ;AACT,YAAA,KAAK,OAAO,UAAU;AAC5B,WAAK,SAAS;AACd,WAAK,gBAAgB;AAAA,IAAA;AAAA,EACvB;AAEJ;ACtPO,MAAM,mBAAmB,aAAa;AAAA,EACnC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAEA,SAAoE;AAAA,EACpE,iBAAwC;AAAA,EACxC,eAAwB,CAAC;AAAA,EACzB,kBAA2B;AAAA,EAC3B,kBAA2B,CAAC;AAAA,EAC5B,gBAA8B;AAAA,EAC9B,mBAA4C;AAAA,EAE5C,mBAA0C;AAAA,EAC1C,kBAAyC;AAAA,EACzC,gBAAwB;AAAA,EACxB,iBAAyB;AAAA;AAAA,EAEjC,YAAY,QAAmB;AACvB,UAAA;AACN,SAAK,SAAS;AAET,SAAA,oBAAoB,IAAI,kBAAkB;AAAA,MAC7C,UAAU;AAAA;AAAA,MACV,UAAU,OAAO;AAAA,MACjB,aAAa;AAAA,MACb,UAAU,EAAE,OAAO,MAAM,QAAQ,KAAK;AAAA,IAAA,CACvC;AAEI,SAAA,kBAAkB,IAAI,gBAAgB;AACtC,SAAA,aAAa,IAAI,WAAW;AAAA,EAAA;AAAA,EAGnC,MAAM,aAA4B;AAC5B,QAAA;AACF,WAAK,UAAU,UAAU;AAGnB,YAAA,KAAK,kBAAkB,WAAW;AAClC,YAAA,KAAK,WAAW,WAAW;AAG3B,YAAA,KAAK,kBAAkB,uBAAuB;AAGhD,UAAA,KAAK,OAAO,UAAU;AAClB,cAAA,aAAa,gBAAgB,uBAAuB;AACpD,cAAA,iBAAiB,WAAW,KAAK,CAAA,MAAK,EAAE,SAAS,KAAK,OAAO,QAAQ,GAAG;AAE9E,YAAI,gBAAgB;AAClB,eAAK,gBAAgB,YAAY,KAAK,OAAO,UAAU;AAAA,YACrD,GAAG;AAAA,YACH,GAAG,KAAK;AAAA,UAAA,CACT;AAAA,QAAA;AAAA,MACH;AAGF,WAAK,UAAU,SAAS;AACxB,WAAK,KAAK,iBAAiB;AAAA,aAEpB,OAAO;AACd,WAAK,UAAU,OAAO;AACjB,WAAA,KAAK,aAAa,KAAK;AACtB,YAAA;AAAA,IAAA;AAAA,EACR;AAAA,EAGF,MAAM,aAAa,aAA+E;AAC5F,QAAA;AACF,YAAM,SAAS,MAAM,KAAK,kBAAkB,aAAa,WAAW;AACpE,WAAK,kBAAkB,OAAO;AAE9B,UAAI,OAAO,SAAS;AAElB,aAAK,kBAAkB,MAAM,KAAK,kBAAkB,mBAAmB;AACvE,aAAK,KAAK,qBAAqB,EAAE,QAAQ,KAAK,iBAAiB;AAAA,MAAA;AAG1D,aAAA;AAAA,aACA,OAAO;AACN,cAAA,MAAM,0BAA0B,KAAK;AAC7C,aAAO,EAAE,SAAS,OAAO,SAAS,MAAM,QAAQ;AAAA,IAAA;AAAA,EAClD;AAAA,EAGF,MAAM,QAAuB;AACvB,QAAA,CAAC,KAAK,iBAAiB;AACnB,YAAA,IAAI,MAAM,2CAA2C;AAAA,IAAA;AAGzD,QAAA,KAAK,WAAW,WAAW;AACvB,YAAA,IAAI,MAAM,wBAAwB;AAAA,IAAA;AAGtC,QAAA;AACF,WAAK,UAAU,UAAU;AAGzB,WAAK,iBAAiB;AAAA,QACpB,WAAW,KAAK,IAAI;AAAA,QACpB,aAAa;AAAA,QACb,eAAe;AAAA,QACf,cAAc;AAAA,QACd,aAAa;AAAA,QACb,SAAS;AAAA,QACT,aAAa;AAAA,QACb,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,aAAa;AAAA,MACf;AAGA,UAAI,KAAK,OAAO,YAAY,eAAe,SAAS,GAAG;AACrD,cAAM,YAAY,KAAK,OAAO,YAAY,eAAe,CAAC;AACpD,cAAA,KAAK,kBAAkB,YAAY,SAAS;AAC7C,aAAA,gBAAgB,KAAK,gBAAgB,KAAK,OAAK,EAAE,SAAS,SAAS,KAAK;AAAA,MAAA;AAI/E,YAAM,KAAK,kBAAkB,cAAc,KAAK,OAAO,UAAU;AAGjE,WAAK,kBAAkB;AACvB,WAAK,iBAAiB;AAEtB,WAAK,UAAU,SAAS;AACxB,WAAK,KAAK,aAAa;AAAA,aAEhB,OAAO;AACd,WAAK,UAAU,OAAO;AACjB,WAAA,KAAK,aAAa,KAAK;AACtB,YAAA;AAAA,IAAA;AAAA,EACR;AAAA,EAGF,MAAM,OAAsB;AAC1B,SAAK,UAAU,UAAU;AAGzB,QAAI,KAAK,kBAAkB;AACzB,oBAAc,KAAK,gBAAgB;AACnC,WAAK,mBAAmB;AAAA,IAAA;AAG1B,QAAI,KAAK,iBAAiB;AACxB,oBAAc,KAAK,eAAe;AAClC,WAAK,kBAAkB;AAAA,IAAA;AAIzB,QAAI,KAAK,gBAAgB;AAClB,WAAA,eAAe,UAAU,KAAK,IAAI;AAClC,WAAA,KAAK,qBAAqB,KAAK,cAAc;AAAA,IAAA;AAGpD,SAAK,UAAU,SAAS;AACxB,SAAK,KAAK,aAAa;AAAA,EAAA;AAAA,EAGjB,oBAA0B;AAC3B,SAAA,mBAAmB,YAAY,YAAY;AAC1C,UAAA;AACE,YAAA,KAAK,WAAW,UAAW;AAE/B,cAAM,KAAK,gBAAgB;AAAA,eACpB,OAAO;AACN,gBAAA,MAAM,wBAAwB,KAAK;AACtC,aAAA,KAAK,aAAa,KAAK;AAAA,MAAA;AAAA,OAE7B,GAAI;AAAA,EAAA;AAAA,EAGD,mBAAyB;AAC1B,SAAA,kBAAkB,YAAY,YAAY;AACzC,UAAA;AACE,YAAA,KAAK,WAAW,UAAW;AAG/B,cAAM,qBAAqB,KAAK,IAAI,IAAI,KAAK;AACzC,YAAA,qBAAqB,KAAK,gBAAgB;AAC5C;AAAA,QAAA;AAGF,cAAM,KAAK,wBAAwB;AAAA,eAC5B,OAAO;AACN,gBAAA,MAAM,uBAAuB,KAAK;AACrC,aAAA,KAAK,aAAa,KAAK;AAAA,MAAA;AAAA,OAE7B,GAAI;AAAA,EAAA;AAAA,EAGT,MAAc,kBAAiC;AACzC,QAAA,CAAC,KAAK,kBAAkB;AAC1B;AAAA,IAAA;AAGE,QAAA;AAEF,YAAM,aAAa,MAAM,KAAK,kBAAkB,kBAAkB,KAAK,gBAAgB;AAGvF,YAAM,gBAAgB,MAAM,KAAK,WAAW,aAAa,YAAY,KAAK,gBAAgB;AAGpF,YAAA,aAAa,KAAK,WAAW;AAAA,QACjC;AAAA,QACA,KAAK,eAAe,QAAQ;AAAA,MAC9B;AAGK,WAAA,KAAK,mBAAmB,UAAU;AAAA,aAEhC,OAAO;AACN,cAAA,MAAM,0BAA0B,KAAK;AAAA,IAAA;AAAA,EAC/C;AAAA,EAGF,MAAc,0BAAyC;AACrD,QAAI,CAAC,KAAK,iBAAiB,CAAC,KAAK,kBAAkB;AACjD;AAAA,IAAA;AAGE,QAAA;AAEF,YAAM,aAAa,MAAM,KAAK,kBAAkB,kBAAkB,KAAK,gBAAgB;AACvF,YAAM,gBAAgB,MAAM,KAAK,WAAW,aAAa,YAAY,KAAK,gBAAgB;AACpF,YAAA,aAAa,KAAK,WAAW;AAAA,QACjC;AAAA,QACA,KAAK,cAAc;AAAA,MACrB;AAGA,YAAM,WAAW,MAAM,KAAK,gBAAgB,gBAAgB,UAAU;AAEtE,UAAI,CAAC,YAAY,SAAS,WAAW,QAAQ;AAC3C;AAAA,MAAA;AAIF,UAAI,SAAS,aAAa,KAAK,OAAO,qBAAqB;AACzD,aAAK,KAAK,qBAAqB;AAAA,UAC7B,QAAQ;AAAA,UACR,YAAY,SAAS;AAAA,UACrB,WAAW,KAAK,OAAO;AAAA,QAAA,CACxB;AACD;AAAA,MAAA;AAIE,UAAA,CAAC,KAAK,OAAO,QAAQ;AACjB,cAAA,KAAK,aAAa,UAAU,UAAU;AAAA,MAAA,OACvC;AACA,aAAA,eAAe,UAAU,UAAU;AAAA,MAAA;AAAA,aAGnC,OAAO;AACN,cAAA,MAAM,4BAA4B,KAAK;AAAA,IAAA;AAAA,EACjD;AAAA,EAGF,MAAc,aAAa,UAAyB,YAAuC;AACrF,QAAA;AAEF,YAAM,KAAK,kBAAkB,eAAe,SAAS,MAAM;AAG3D,YAAM,YAAY,SAAS,WAAW,QAAQ,SAAS;AACvD,YAAM,SAAS,MAAM,KAAK,kBAAkB,aAAa,SAAS;AAElE,UAAI,OAAO,SAAS;AAElB,cAAM,QAAe;AAAA,UACnB,IAAI,SAAS,KAAK,IAAK,CAAA;AAAA,UACvB,OAAO,KAAK,cAAe;AAAA,UAC3B;AAAA,UACA,QAAQ,SAAS;AAAA,UACjB,WAAW,WAAW;AAAA,UACtB,UAAU,KAAK,IAAI;AAAA,UACnB,UAAU,KAAK,kBAAkB,KAAK,OAAO,UAAU;AAAA,UACvD,UAAU,KAAK,gBAAgB,qBAAqB,QAAQ;AAAA,UAC5D,YAAY,SAAS;AAAA,UACrB,QAAQ;AAAA,QACV;AAEK,aAAA,aAAa,QAAQ,KAAK;AAC1B,aAAA,gBAAgB,KAAK,IAAI;AAG9B,YAAI,KAAK,gBAAgB;AACvB,eAAK,eAAe;AAAA,QAAA;AAGjB,aAAA,KAAK,oBAAoB,KAAK;AAGnC,mBAAW,MAAM;AACV,eAAA,iBAAiB,MAAM,EAAE;AAAA,QAAA,GAC7B,MAAM,WAAW,GAAI;AAAA,MAAA,OAEnB;AACL,aAAK,KAAK,oBAAoB,EAAE,UAAU,OAAO,OAAO,SAAS;AAAA,MAAA;AAAA,aAG5D,OAAO;AACN,cAAA,MAAM,2BAA2B,KAAK;AAC9C,WAAK,KAAK,oBAAoB,EAAE,UAAU,OAAO,MAAM,SAAS;AAAA,IAAA;AAAA,EAClE;AAAA,EAGM,eAAe,UAAyB,YAA8B;AAC5E,UAAM,QAAe;AAAA,MACnB,IAAI,WAAW,KAAK,IAAK,CAAA;AAAA,MACzB,OAAO,KAAK,cAAe;AAAA,MAC3B,WAAW,SAAS,WAAW,QAAQ,SAAS;AAAA,MAChD,QAAQ,SAAS;AAAA,MACjB,WAAW,WAAW;AAAA,MACtB,UAAU,KAAK,IAAI;AAAA,MACnB,UAAU,KAAK,kBAAkB,KAAK,OAAO,UAAU;AAAA,MACvD,UAAU,KAAK,gBAAgB,qBAAqB,QAAQ;AAAA,MAC5D,YAAY,SAAS;AAAA,MACrB,QAAQ;AAAA,IACV;AAEK,SAAA,aAAa,QAAQ,KAAK;AAC1B,SAAA,KAAK,qBAAqB,KAAK;AAAA,EAAA;AAAA,EAGtC,MAAc,iBAAiB,SAAgC;AAG7D,UAAM,QAAQ,KAAK,aAAa,KAAK,CAAK,MAAA,EAAE,OAAO,OAAO;AAC1D,QAAI,CAAC,MAAO;AAGN,UAAA,MAAM,KAAK,OAAA,IAAW;AACtB,UAAA,SAAS,MAAM,QAAQ;AACvB,UAAA,YAAY,KAAK,IAAI;AAC3B,UAAM,SAAS,MAAM,MAAM,SAAS,MAAM,CAAC,MAAM;AAGjD,QAAI,KAAK,gBAAgB;AACvB,UAAI,KAAK;AACP,aAAK,eAAe;AACf,aAAA,eAAe,gBAAgB,KAAK,IAAI,GAAG,KAAK,eAAe,gBAAgB,CAAC;AAChF,aAAA,eAAe,aAAa,KAAK,IAAI,KAAK,eAAe,YAAY,KAAK,eAAe,aAAa;AAAA,MAAA,OACtG;AACL,aAAK,eAAe;AACf,aAAA,eAAe,gBAAgB,KAAK,IAAI,GAAG,KAAK,eAAe,gBAAgB,CAAC;AAChF,aAAA,eAAe,cAAc,KAAK,IAAI,KAAK,eAAe,aAAa,KAAK,eAAe,aAAa;AAAA,MAAA;AAG1G,WAAA,eAAe,eAAe,MAAM;AACzC,WAAK,eAAe,UAAW,KAAK,eAAe,gBAAgB,KAAK,eAAe,cAAe;AAAA,IAAA;AAIlG,UAAA,iBAAiB,KAAK,gBAAgB,kBAAkB;AAC1D,QAAA,kBAAkB,mBAAmB,gBAAgB;AACtD,qBAAuB,cAAc,GAAG;AAAA,IAAA;AAGtC,SAAA,KAAK,oBAAoB,KAAK;AAAA,EAAA;AAAA,EAG7B,kBAAkB,QAAwB;AAChD,UAAM,YAAoC;AAAA,MACxC,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,OAAO;AAAA,MACP,OAAO;AAAA,MACP,MAAM;AAAA,IACR;AAEO,WAAA,UAAU,MAAM,KAAK;AAAA,EAAA;AAAA;AAAA,EAI9B,MAAM,YAAY,cAAsB,QAA4B;AAC7D,SAAA,gBAAgB,YAAY,cAAqB,MAAM;AAC5D,SAAK,KAAK,wBAAwB,EAAE,UAAU,cAAc,QAAQ;AAAA,EAAA;AAAA,EAGtE,MAAM,qBAAuC;AACvC,QAAA,KAAK,gBAAgB,WAAW,GAAG;AACrC,WAAK,kBAAkB,MAAM,KAAK,kBAAkB,mBAAmB;AAAA,IAAA;AAEzE,WAAO,KAAK;AAAA,EAAA;AAAA,EAGd,MAAM,0BAAqD;AAGzD,UAAM,cAAgC;AAAA,MACpC,GAAG;AAAA,MACH,GAAG;AAAA,MACH,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AAEA,SAAK,mBAAmB;AACjB,WAAA;AAAA,EAAA;AAAA,EAGT,YAAoB;AAClB,WAAO,KAAK;AAAA,EAAA;AAAA,EAGd,gBAAqB;AACZ,WAAA;AAAA,MACL,iBAAiB,KAAK;AAAA,MACtB,gBAAgB,KAAK;AAAA,MACrB,cAAc,KAAK,aAAa,MAAM,GAAG,EAAE;AAAA;AAAA,MAC3C,eAAe,KAAK;AAAA,MACpB,iBAAiB,KAAK;AAAA,MACtB,kBAAkB,KAAK;AAAA,MACvB,gBAAgB,KAAK,gBAAgB,qBAAqB;AAAA,IAC5D;AAAA,EAAA;AAAA,EAGM,UAAU,QAAkC;AAClD,SAAK,SAAS;AACT,SAAA,KAAK,sBAAsB,MAAM;AAAA,EAAA;AAAA,EAGxC,MAAM,UAAyB;AAC7B,UAAM,KAAK,KAAK;AACV,UAAA,KAAK,kBAAkB,MAAM;AAC7B,UAAA,KAAK,WAAW,QAAQ;AAAA,EAAA;AAElC;AC1bA,MAAM,aAAa,cAAc,YAAY,GAAG;AAChD,MAAM,YAAY,QAAQ,UAAU;AAGpC,IAAI,aAAmC;AACvC,IAAI,aAAgC;AAEpC,MAAM,QAAQ,YAAY,aAAa;AACvC,MAAM,sBAAsB,QAAY,IAAA;AAExC,SAAS,eAAe;AACvB,eAAa,IAAI,cAAc;AAAA,IAC9B,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,MACf,SAAS,KAAK,WAAW,YAAY;AAAA,MACrC,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,MAClB,oBAAoB;AAAA,MACpB,aAAa;AAAA,IACd;AAAA,IACA,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,MAAM;AAAA,IACN,MAAM,KAAK,WAAW,oBAAoB;AAAA,EAAA,CAC1C;AAGD,MAAI,qBAAqB;AACxB,eAAW,QAAQ,mBAAmB;AACtC,QAAI,OAAO;AACV,iBAAW,YAAY,aAAa;AAAA,IAAA;AAAA,EACrC,OACM;AACN,eAAW,SAAS,KAAK,WAAW,oBAAoB,CAAC;AAAA,EAAA;AAG/C,aAAA,KAAK,iBAAiB,MAAM;AACtC,gBAAY,KAAK;AAAA,EAAA,CACjB;AAEU,aAAA,GAAG,UAAU,MAAM;AAChB,iBAAA;AACb,QAAI,YAAY;AACf,iBAAW,KAAK;AACH,mBAAA;AAAA,IAAA;AAAA,EACd,CACA;AACF;AAGA,IAAI,UAAA,EAAY,KAAK,MAAM;AACb,eAAA;AAET,MAAA,GAAG,YAAY,MAAM;AACxB,QAAI,cAAc,gBAAgB,WAAW,GAAG;AAClC,mBAAA;AAAA,IAAA;AAAA,EACd,CACA;AACF,CAAC;AAED,IAAI,GAAG,qBAAqB,MAAM;AAC7B,MAAA,QAAQ,aAAa,UAAU;AAClC,QAAI,KAAK;AAAA,EAAA;AAEX,CAAC;AAGD,QAAQ,OAAO,kBAAkB,OAAO,GAAG,WAAW;AACjD,MAAA;AACH,QAAI,YAAY;AACf,YAAM,WAAW,KAAK;AAAA,IAAA;AAGV,iBAAA,IAAI,WAAW,MAAM;AAClC,UAAM,WAAW,WAAW;AAE5B,WAAO,EAAE,SAAS,MAAM,SAAS,+BAA+B;AAAA,WACxD,OAAO;AACP,YAAA,MAAM,6BAA6B,KAAK;AAChD,WAAO,EAAE,SAAS,OAAO,SAAS,MAAM,QAAQ;AAAA,EAAA;AAElD,CAAC;AAED,QAAQ,OAAO,aAAa,YAAY;AACnC,MAAA;AACH,QAAI,CAAC,YAAY;AACV,YAAA,IAAI,MAAM,qBAAqB;AAAA,IAAA;AAGtC,UAAM,WAAW,MAAM;AACvB,WAAO,EAAE,SAAS,MAAM,SAAS,2BAA2B;AAAA,WACpD,OAAO;AACP,YAAA,MAAM,wBAAwB,KAAK;AAC3C,WAAO,EAAE,SAAS,OAAO,SAAS,MAAM,QAAQ;AAAA,EAAA;AAElD,CAAC;AAED,QAAQ,OAAO,YAAY,YAAY;AAClC,MAAA;AACH,QAAI,YAAY;AACf,YAAM,WAAW,KAAK;AAAA,IAAA;AAEvB,WAAO,EAAE,SAAS,MAAM,SAAS,2BAA2B;AAAA,WACpD,OAAO;AACP,YAAA,MAAM,uBAAuB,KAAK;AAC1C,WAAO,EAAE,SAAS,OAAO,SAAS,MAAM,QAAQ;AAAA,EAAA;AAElD,CAAC;AAED,QAAQ,OAAO,cAAc,YAAY;AACxC,MAAI,CAAC,YAAY;AAChB,WAAO,EAAE,QAAQ,WAAW,MAAM,KAAK;AAAA,EAAA;AAGjC,SAAA;AAAA,IACN,QAAQ,WAAW,UAAU;AAAA,IAC7B,MAAM,WAAW,cAAc;AAAA,EAChC;AACD,CAAC;AAED,QAAQ,OAAO,wBAAwB,OAAO,GAAG,gBAAgB;AAC5D,MAAA;AACH,QAAI,CAAC,YAAY;AACV,YAAA,IAAI,MAAM,qBAAqB;AAAA,IAAA;AAGtC,UAAM,SAAS,MAAM,WAAW,aAAa,WAAW;AACjD,WAAA;AAAA,WACC,OAAO;AACP,YAAA,MAAM,0BAA0B,KAAK;AAC7C,WAAO,EAAE,SAAS,OAAO,SAAS,MAAM,QAAQ;AAAA,EAAA;AAElD,CAAC;AAED,QAAQ,OAAO,qBAAqB,YAAY;AAC3C,MAAA;AACH,QAAI,CAAC,YAAY;AACV,YAAA,IAAI,MAAM,qBAAqB;AAAA,IAAA;AAGhC,UAAA,SAAS,MAAM,WAAW,mBAAmB;AAC5C,WAAA,EAAE,SAAS,MAAM,OAAO;AAAA,WACvB,OAAO;AACP,YAAA,MAAM,yBAAyB,KAAK;AAC5C,WAAO,EAAE,SAAS,OAAO,SAAS,MAAM,QAAQ;AAAA,EAAA;AAElD,CAAC;AAED,QAAQ,OAAO,+BAA+B,YAAY;AACrD,MAAA;AACH,UAAM,SAAS,MAAM,OAAO,eAAe,YAAa;AAAA,MACvD,MAAM;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,MACT,SAAS,CAAC,MAAM,QAAQ;AAAA,IAAA,CACxB;AAEG,QAAA,OAAO,aAAa,GAAG;AAE1B,UAAI,YAAY;AACT,cAAA,cAAc,MAAM,WAAW,wBAAwB;AACtD,eAAA,EAAE,SAAS,MAAM,YAAY;AAAA,MAAA;AAAA,IACrC;AAGD,WAAO,EAAE,SAAS,OAAO,SAAS,iCAAiC;AAAA,WAC3D,OAAO;AACP,YAAA,MAAM,qCAAqC,KAAK;AACxD,WAAO,EAAE,SAAS,OAAO,SAAS,MAAM,QAAQ;AAAA,EAAA;AAElD,CAAC;AAGD,QAAQ,OAAO,yBAAyB,YAAY;AAC7C,QAAA,aAAa,gBAAgB,uBAAuB;AACnD,SAAA,EAAE,SAAS,MAAM,WAAW;AACpC,CAAC;AAED,QAAQ,OAAO,sBAAsB,OAAO,GAAG,cAAc,WAAW;AACnE,MAAA;AACH,QAAI,CAAC,YAAY;AACV,YAAA,IAAI,MAAM,qBAAqB;AAAA,IAAA;AAGhC,UAAA,WAAW,YAAY,cAAc,MAAM;AACjD,WAAO,EAAE,SAAS,MAAM,SAAS,YAAY,YAAY,aAAa;AAAA,WAC9D,OAAO;AACP,YAAA,MAAM,2BAA2B,KAAK;AAC9C,WAAO,EAAE,SAAS,OAAO,SAAS,MAAM,QAAQ;AAAA,EAAA;AAElD,CAAC;AAGD,QAAQ,GAAG,qBAAqB,CAAS,UAAA;AAChC,UAAA,MAAM,uBAAuB,KAAK;AAC3C,CAAC;AAED,QAAQ,GAAG,sBAAsB,CAAC,QAAQ,YAAY;AACrD,UAAQ,MAAM,2BAA2B,SAAS,WAAW,MAAM;AACpE,CAAC;"}
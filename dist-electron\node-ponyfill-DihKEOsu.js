import { ai as require$$0 } from "./main-Be7A0SP-.js";
function _mergeNamespaces(n, m) {
  for (var i = 0; i < m.length; i++) {
    const e = m[i];
    if (typeof e !== "string" && !Array.isArray(e)) {
      for (const k in e) {
        if (k !== "default" && !(k in n)) {
          const d = Object.getOwnPropertyDescriptor(e, k);
          if (d) {
            Object.defineProperty(n, k, d.get ? d : {
              enumerable: true,
              get: () => e[k]
            });
          }
        }
      }
    }
  }
  return Object.freeze(Object.defineProperty(n, Symbol.toStringTag, { value: "Module" }));
}
var nodePonyfill$1 = { exports: {} };
(function(module, exports) {
  const nodeFetch = require$$0;
  const realFetch = nodeFetch.default || nodeFetch;
  const fetch = function(url, options) {
    if (/^\/\//.test(url)) {
      url = "https:" + url;
    }
    return realFetch.call(this, url, options);
  };
  fetch.ponyfill = true;
  module.exports = exports = fetch;
  exports.fetch = fetch;
  exports.Headers = nodeFetch.Headers;
  exports.Request = nodeFetch.Request;
  exports.Response = nodeFetch.Response;
  exports.default = fetch;
})(nodePonyfill$1, nodePonyfill$1.exports);
var nodePonyfillExports = nodePonyfill$1.exports;
const nodePonyfill = /* @__PURE__ */ _mergeNamespaces({
  __proto__: null
}, [nodePonyfillExports]);
export {
  nodePonyfill as n
};
//# sourceMappingURL=node-ponyfill-DihKEOsu.js.map

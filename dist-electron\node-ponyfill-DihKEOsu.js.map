{"version": 3, "file": "node-ponyfill-DihKEOsu.js", "sources": ["../node_modules/.pnpm/cross-fetch@4.0.0/node_modules/cross-fetch/dist/node-ponyfill.js"], "sourcesContent": ["const nodeFetch = require('node-fetch')\nconst realFetch = nodeFetch.default || nodeFetch\n\nconst fetch = function (url, options) {\n  // Support schemaless URIs on the server for parity with the browser.\n  // Ex: //github.com/ -> https://github.com/\n  if (/^\\/\\//.test(url)) {\n    url = 'https:' + url\n  }\n  return realFetch.call(this, url, options)\n}\n\nfetch.ponyfill = true\n\nmodule.exports = exports = fetch\nexports.fetch = fetch\nexports.Headers = nodeFetch.Headers\nexports.Request = nodeFetch.Request\nexports.Response = nodeFetch.Response\n\n// Needed for TypeScript consumers without esModuleInterop.\nexports.default = fetch\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA,QAAM,YAAY;AAClB,QAAM,YAAY,UAAU,WAAW;AAEvC,QAAM,QAAQ,SAAU,KAAK,SAAS;AAGpC,QAAI,QAAQ,KAAK,GAAG,GAAG;AACrB,YAAM,WAAW;AAAA;AAEnB,WAAO,UAAU,KAAK,MAAM,KAAK,OAAO;AAAA;AAG1C,QAAM,WAAW;AAEjB,SAAiB,UAAA,UAAU;AAC3B,UAAA,QAAgB;AAChB,UAAkB,UAAA,UAAU;AAC5B,UAAkB,UAAA,UAAU;AAC5B,UAAmB,WAAA,UAAU;AAG7B,UAAkB,UAAA;;;;;;", "x_google_ignoreList": [0]}
import { contextBridge, ipc<PERSON><PERSON><PERSON> } from "electron";
const electronAPI = {
  bot: {
    initialize: (config) => ipc<PERSON><PERSON><PERSON>.invoke("bot:initialize", config),
    start: () => ipc<PERSON><PERSON>er.invoke("bot:start"),
    stop: () => ipc<PERSON>enderer.invoke("bot:stop"),
    status: () => ipcRenderer.invoke("bot:status")
  },
  browser: {
    authenticate: (credentials) => ipc<PERSON><PERSON><PERSON>.invoke("browser:authenticate", credentials),
    getAssets: () => ipc<PERSON>enderer.invoke("browser:getAssets")
  },
  strategy: {
    getAvailable: () => ipcRenderer.invoke("strategy:getAvailable"),
    setActive: (strategyName, config) => ipcRenderer.invoke("strategy:setActive", strategyName, config)
  },
  dialog: {
    selectScreenshotArea: () => ipcRenderer.invoke("dialog:selectScreenshotArea")
  },
  on: (channel, callback) => {
    ipcRenderer.on(channel, callback);
  },
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel);
  }
};
contextBridge.exposeInMainWorld("electronAPI", electronAPI);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoicHJlbG9hZC5qcyIsInNvdXJjZXMiOlsiLi4vZWxlY3Ryb24vcHJlbG9hZC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjb250ZXh0QnJpZGdlLCBpcGNSZW5kZXJlciB9IGZyb20gJ2VsZWN0cm9uJ1xuXG4vLyBEZWZpbmUgdGhlIEFQSSBpbnRlcmZhY2VcbmV4cG9ydCBpbnRlcmZhY2UgRWxlY3Ryb25BUEkge1xuICAvLyBCb3QgY29udHJvbFxuICBib3Q6IHtcbiAgICBpbml0aWFsaXplOiAoY29uZmlnOiBhbnkpID0+IFByb21pc2U8eyBzdWNjZXNzOiBib29sZWFuOyBtZXNzYWdlOiBzdHJpbmcgfT5cbiAgICBzdGFydDogKCkgPT4gUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW47IG1lc3NhZ2U6IHN0cmluZyB9PlxuICAgIHN0b3A6ICgpID0+IFByb21pc2U8eyBzdWNjZXNzOiBib29sZWFuOyBtZXNzYWdlOiBzdHJpbmcgfT5cbiAgICBzdGF0dXM6ICgpID0+IFByb21pc2U8eyBzdGF0dXM6IHN0cmluZzsgZGF0YTogYW55IH0+XG4gIH1cbiAgXG4gIC8vIEJyb3dzZXIgYXV0b21hdGlvblxuICBicm93c2VyOiB7XG4gICAgYXV0aGVudGljYXRlOiAoY3JlZGVudGlhbHM6IGFueSkgPT4gUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW47IG1lc3NhZ2U/OiBzdHJpbmcgfT5cbiAgICBnZXRBc3NldHM6ICgpID0+IFByb21pc2U8eyBzdWNjZXNzOiBib29sZWFuOyBhc3NldHM/OiBhbnlbXTsgbWVzc2FnZT86IHN0cmluZyB9PlxuICB9XG4gIFxuICAvLyBTdHJhdGVneSBtYW5hZ2VtZW50XG4gIHN0cmF0ZWd5OiB7XG4gICAgZ2V0QXZhaWxhYmxlOiAoKSA9PiBQcm9taXNlPHsgc3VjY2VzczogYm9vbGVhbjsgc3RyYXRlZ2llczogYW55W10gfT5cbiAgICBzZXRBY3RpdmU6IChzdHJhdGVneU5hbWU6IHN0cmluZywgY29uZmlnOiBhbnkpID0+IFByb21pc2U8eyBzdWNjZXNzOiBib29sZWFuOyBtZXNzYWdlOiBzdHJpbmcgfT5cbiAgfVxuICBcbiAgLy8gVUkgdXRpbGl0aWVzXG4gIGRpYWxvZzoge1xuICAgIHNlbGVjdFNjcmVlbnNob3RBcmVhOiAoKSA9PiBQcm9taXNlPHsgc3VjY2VzczogYm9vbGVhbjsgY29vcmRpbmF0ZXM/OiBhbnk7IG1lc3NhZ2U/OiBzdHJpbmcgfT5cbiAgfVxuICBcbiAgLy8gRXZlbnQgbGlzdGVuZXJzXG4gIG9uOiAoY2hhbm5lbDogc3RyaW5nLCBjYWxsYmFjazogKC4uLmFyZ3M6IGFueVtdKSA9PiB2b2lkKSA9PiB2b2lkXG4gIHJlbW92ZUFsbExpc3RlbmVyczogKGNoYW5uZWw6IHN0cmluZykgPT4gdm9pZFxufVxuXG4vLyBFeHBvc2UgcHJvdGVjdGVkIG1ldGhvZHMgdGhhdCBhbGxvdyB0aGUgcmVuZGVyZXIgcHJvY2VzcyB0byB1c2Vcbi8vIHRoZSBpcGNSZW5kZXJlciB3aXRob3V0IGV4cG9zaW5nIHRoZSBlbnRpcmUgb2JqZWN0XG5jb25zdCBlbGVjdHJvbkFQSTogRWxlY3Ryb25BUEkgPSB7XG4gIGJvdDoge1xuICAgIGluaXRpYWxpemU6IChjb25maWcpID0+IGlwY1JlbmRlcmVyLmludm9rZSgnYm90OmluaXRpYWxpemUnLCBjb25maWcpLFxuICAgIHN0YXJ0OiAoKSA9PiBpcGNSZW5kZXJlci5pbnZva2UoJ2JvdDpzdGFydCcpLFxuICAgIHN0b3A6ICgpID0+IGlwY1JlbmRlcmVyLmludm9rZSgnYm90OnN0b3AnKSxcbiAgICBzdGF0dXM6ICgpID0+IGlwY1JlbmRlcmVyLmludm9rZSgnYm90OnN0YXR1cycpXG4gIH0sXG4gIFxuICBicm93c2VyOiB7XG4gICAgYXV0aGVudGljYXRlOiAoY3JlZGVudGlhbHMpID0+IGlwY1JlbmRlcmVyLmludm9rZSgnYnJvd3NlcjphdXRoZW50aWNhdGUnLCBjcmVkZW50aWFscyksXG4gICAgZ2V0QXNzZXRzOiAoKSA9PiBpcGNSZW5kZXJlci5pbnZva2UoJ2Jyb3dzZXI6Z2V0QXNzZXRzJylcbiAgfSxcbiAgXG4gIHN0cmF0ZWd5OiB7XG4gICAgZ2V0QXZhaWxhYmxlOiAoKSA9PiBpcGNSZW5kZXJlci5pbnZva2UoJ3N0cmF0ZWd5OmdldEF2YWlsYWJsZScpLFxuICAgIHNldEFjdGl2ZTogKHN0cmF0ZWd5TmFtZSwgY29uZmlnKSA9PiBpcGNSZW5kZXJlci5pbnZva2UoJ3N0cmF0ZWd5OnNldEFjdGl2ZScsIHN0cmF0ZWd5TmFtZSwgY29uZmlnKVxuICB9LFxuICBcbiAgZGlhbG9nOiB7XG4gICAgc2VsZWN0U2NyZWVuc2hvdEFyZWE6ICgpID0+IGlwY1JlbmRlcmVyLmludm9rZSgnZGlhbG9nOnNlbGVjdFNjcmVlbnNob3RBcmVhJylcbiAgfSxcbiAgXG4gIG9uOiAoY2hhbm5lbDogc3RyaW5nLCBjYWxsYmFjazogKC4uLmFyZ3M6IGFueVtdKSA9PiB2b2lkKSA9PiB7XG4gICAgaXBjUmVuZGVyZXIub24oY2hhbm5lbCwgY2FsbGJhY2spXG4gIH0sXG4gIFxuICByZW1vdmVBbGxMaXN0ZW5lcnM6IChjaGFubmVsOiBzdHJpbmcpID0+IHtcbiAgICBpcGNSZW5kZXJlci5yZW1vdmVBbGxMaXN0ZW5lcnMoY2hhbm5lbClcbiAgfVxufVxuXG5jb250ZXh0QnJpZGdlLmV4cG9zZUluTWFpbldvcmxkKCdlbGVjdHJvbkFQSScsIGVsZWN0cm9uQVBJKVxuXG4vLyBUeXBlIGRlY2xhcmF0aW9uIGZvciBnbG9iYWwgd2luZG93IG9iamVjdFxuZGVjbGFyZSBnbG9iYWwge1xuICBpbnRlcmZhY2UgV2luZG93IHtcbiAgICBlbGVjdHJvbkFQSTogRWxlY3Ryb25BUElcbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFvQ0EsTUFBTSxjQUEyQjtBQUFBLEVBQy9CLEtBQUs7QUFBQSxJQUNILFlBQVksQ0FBQyxXQUFXLFlBQVksT0FBTyxrQkFBa0IsTUFBTTtBQUFBLElBQ25FLE9BQU8sTUFBTSxZQUFZLE9BQU8sV0FBVztBQUFBLElBQzNDLE1BQU0sTUFBTSxZQUFZLE9BQU8sVUFBVTtBQUFBLElBQ3pDLFFBQVEsTUFBTSxZQUFZLE9BQU8sWUFBWTtBQUFBLEVBQy9DO0FBQUEsRUFFQSxTQUFTO0FBQUEsSUFDUCxjQUFjLENBQUMsZ0JBQWdCLFlBQVksT0FBTyx3QkFBd0IsV0FBVztBQUFBLElBQ3JGLFdBQVcsTUFBTSxZQUFZLE9BQU8sbUJBQW1CO0FBQUEsRUFDekQ7QUFBQSxFQUVBLFVBQVU7QUFBQSxJQUNSLGNBQWMsTUFBTSxZQUFZLE9BQU8sdUJBQXVCO0FBQUEsSUFDOUQsV0FBVyxDQUFDLGNBQWMsV0FBVyxZQUFZLE9BQU8sc0JBQXNCLGNBQWMsTUFBTTtBQUFBLEVBQ3BHO0FBQUEsRUFFQSxRQUFRO0FBQUEsSUFDTixzQkFBc0IsTUFBTSxZQUFZLE9BQU8sNkJBQTZCO0FBQUEsRUFDOUU7QUFBQSxFQUVBLElBQUksQ0FBQyxTQUFpQixhQUF1QztBQUMvQyxnQkFBQSxHQUFHLFNBQVMsUUFBUTtBQUFBLEVBQ2xDO0FBQUEsRUFFQSxvQkFBb0IsQ0FBQyxZQUFvQjtBQUN2QyxnQkFBWSxtQkFBbUIsT0FBTztBQUFBLEVBQUE7QUFFMUM7QUFFQSxjQUFjLGtCQUFrQixlQUFlLFdBQVc7In0=

import { aj as getDefaultExportFromCjs, ak as requireTypescript } from "./main-BWWd-5rQ.js";
var typescriptExports = requireTypescript();
const typescript = /* @__PURE__ */ getDefaultExportFromCjs(typescriptExports);
const typescript$1 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: typescript
}, Symbol.toStringTag, { value: "Module" }));
export {
  typescript$1 as t
};
//# sourceMappingURL=typescript-Csw1Vfb7.js.map

import { BaseStrategy, StrategyType, StrategyConfig, MarketData, TradeDecision } from '../../src/types'
import { MartingaleStrategy } from './strategies/MartingaleStrategy'
import { DAlembertStrategy } from './strategies/DAlembertStrategy'
import { FixedRSIStrategy } from './strategies/FixedRSIStrategy'

export class StrategyManager {
  private static strategies: Map<StrategyType, typeof BaseStrategy> = new Map([
    ['martingale', MartingaleStrategy as any],
    ['da<PERSON><PERSON>', DAlembertStrategy as any],
    ['fixed-rsi', FixedRSIStrategy as any]
  ])

  private activeStrategy: BaseStrategy | null = null

  static getAvailableStrategies(): Array<{ name: StrategyType; description: string; defaultConfig: StrategyConfig }> {
    return [
      {
        name: 'martingale',
        description: 'Doubles bet after each loss, resets on win. High risk, high reward.',
        defaultConfig: {
          baseAmount: 1,
          multiplier: 2,
          maxSteps: 5,
          resetOnWin: true
        }
      },
      {
        name: 'da<PERSON><PERSON>',
        description: 'Increases bet by fixed amount after loss, decreases after win. Moderate risk.',
        defaultConfig: {
          baseAmount: 1,
          increment: 1,
          maxSteps: 10
        }
      },
      {
        name: 'fixed-rsi',
        description: 'Fixed stake with RSI and EMA indicators. Conservative approach.',
        defaultConfig: {
          amount: 1,
          rsiPeriod: 14,
          oversoldThreshold: 30,
          overboughtThreshold: 70,
          emaShort: 12,
          emaLong: 26
        }
      }
    ]
  }

  setStrategy(strategyType: StrategyType, config: StrategyConfig): void {
    const StrategyClass = StrategyManager.strategies.get(strategyType)
    
    if (!StrategyClass) {
      throw new Error(`Strategy ${strategyType} not found`)
    }

    this.activeStrategy = new (StrategyClass as any)(config)
  }

  async executeStrategy(marketData: MarketData): Promise<TradeDecision | null> {
    if (!this.activeStrategy) {
      throw new Error('No active strategy set')
    }

    // Calculate confidence first
    const confidence = this.activeStrategy.calculateConfidence(marketData)
    
    // Only execute if confidence is high enough
    if (confidence < 87) { // Configurable threshold
      return {
        action: 'hold',
        amount: 0,
        confidence,
        reasoning: `Confidence too low: ${confidence.toFixed(2)}%`
      }
    }

    return await this.activeStrategy.execute(marketData)
  }

  getActiveStrategy(): BaseStrategy | null {
    return this.activeStrategy
  }

  resetStrategy(): void {
    if (this.activeStrategy) {
      this.activeStrategy.reset()
    }
  }
}

// Base Strategy Abstract Class
export abstract class BaseStrategyImpl implements BaseStrategy {
  public name: string
  public description: string
  public config: StrategyConfig
  
  protected consecutiveLosses: number = 0
  protected consecutiveWins: number = 0
  protected totalTrades: number = 0
  protected currentAmount: number

  constructor(name: string, description: string, config: StrategyConfig) {
    this.name = name
    this.description = description
    this.config = config
    this.currentAmount = config.baseAmount || config.amount || 1
  }

  abstract execute(marketData: MarketData): Promise<TradeDecision>
  
  calculateConfidence(marketData: MarketData): number {
    let confidence = 0

    // RSI-based confidence (30% weight)
    if (marketData.rsi <= 30) {
      confidence += 30 // Oversold, likely to go up
    } else if (marketData.rsi >= 70) {
      confidence += 30 // Overbought, likely to go down
    } else if (marketData.rsi >= 40 && marketData.rsi <= 60) {
      confidence += 15 // Neutral zone
    }

    // EMA trend confidence (25% weight)
    if (marketData.emaShort > marketData.emaLong) {
      confidence += 25 // Uptrend
    } else if (marketData.emaShort < marketData.emaLong) {
      confidence += 25 // Downtrend
    }

    // Price trend confidence (25% weight)
    if (marketData.trend === 'up' || marketData.trend === 'down') {
      confidence += 25 // Clear trend
    } else {
      confidence += 10 // Sideways trend
    }

    // Volatility confidence (20% weight)
    if (marketData.volatility < 0.02) {
      confidence += 20 // Low volatility, more predictable
    } else if (marketData.volatility < 0.05) {
      confidence += 15 // Medium volatility
    } else {
      confidence += 5 // High volatility, less predictable
    }

    return Math.min(confidence, 100)
  }

  reset(): void {
    this.consecutiveLosses = 0
    this.consecutiveWins = 0
    this.totalTrades = 0
    this.currentAmount = this.config.baseAmount || this.config.amount || 1
  }

  protected updateStats(won: boolean): void {
    this.totalTrades++
    
    if (won) {
      this.consecutiveWins++
      this.consecutiveLosses = 0
    } else {
      this.consecutiveLosses++
      this.consecutiveWins = 0
    }
  }

  protected determineDirection(marketData: MarketData): 'high' | 'low' {
    let score = 0

    // RSI signals
    if (marketData.rsi <= 30) {
      score += 2 // Strong buy signal
    } else if (marketData.rsi <= 40) {
      score += 1 // Weak buy signal
    } else if (marketData.rsi >= 70) {
      score -= 2 // Strong sell signal
    } else if (marketData.rsi >= 60) {
      score -= 1 // Weak sell signal
    }

    // EMA signals
    if (marketData.emaShort > marketData.emaLong) {
      score += 1 // Uptrend
    } else {
      score -= 1 // Downtrend
    }

    // Trend signals
    if (marketData.trend === 'up') {
      score += 1
    } else if (marketData.trend === 'down') {
      score -= 1
    }

    return score > 0 ? 'high' : 'low'
  }
}

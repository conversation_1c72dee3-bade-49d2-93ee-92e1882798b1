import { BaseStrategyImpl } from '../StrategyManager'
import { MarketData, TradeDecision, DAlembertConfig } from '../../../src/types'

export class DAlembertStrategy extends BaseStrategyImpl {
  private config: DAlembertConfig
  private currentStep: number = 0

  constructor(config: DAlembertConfig) {
    super(
      "D'Alembert",
      'Increases bet by fixed amount after loss, decreases after win. Moderate risk strategy.',
      config
    )
    this.config = config
    this.currentAmount = config.baseAmount
  }

  async execute(marketData: MarketData): Promise<TradeDecision> {
    const confidence = this.calculateConfidence(marketData)
    
    // Determine trade direction based on market analysis
    const direction = this.determineDirection(marketData)
    
    // Calculate trade amount based on D'Alembert progression
    const tradeAmount = this.calculateTradeAmount()
    
    // Check if we've hit max steps
    if (this.currentStep >= this.config.maxSteps) {
      return {
        action: 'hold',
        amount: 0,
        confidence,
        reasoning: `Maximum D'Alembert steps (${this.config.maxSteps}) reached. Waiting for reset.`,
        metadata: {
          currentStep: this.currentStep,
          currentAmount: this.currentAmount
        }
      }
    }

    return {
      action: direction === 'high' ? 'buy' : 'sell',
      amount: tradeAmount,
      confidence,
      reasoning: this.generateReasoning(marketData, direction, tradeAmount),
      metadata: {
        strategy: 'dalembert',
        direction,
        currentStep: this.currentStep,
        baseAmount: this.config.baseAmount,
        increment: this.config.increment
      }
    }
  }

  private calculateTradeAmount(): number {
    return Math.max(
      this.config.baseAmount + (this.currentStep * this.config.increment),
      this.config.baseAmount
    )
  }

  private generateReasoning(marketData: MarketData, direction: 'high' | 'low', amount: number): string {
    const reasons = []
    
    // Market analysis reasoning
    if (marketData.rsi <= 30) {
      reasons.push('RSI oversold (bullish signal)')
    } else if (marketData.rsi >= 70) {
      reasons.push('RSI overbought (bearish signal)')
    }
    
    if (marketData.emaShort > marketData.emaLong) {
      reasons.push('EMA uptrend')
    } else {
      reasons.push('EMA downtrend')
    }
    
    if (marketData.trend !== 'sideways') {
      reasons.push(`Price trend: ${marketData.trend}`)
    }
    
    // D'Alembert reasoning
    if (this.currentStep === 0) {
      reasons.push("Starting D'Alembert sequence")
    } else {
      reasons.push(`D'Alembert step ${this.currentStep + 1}, amount: ${amount}`)
    }
    
    return `${direction.toUpperCase()} signal: ${reasons.join(', ')}`
  }

  // Override updateStats to handle D'Alembert progression
  protected updateStats(won: boolean): void {
    super.updateStats(won)
    
    if (won) {
      // Decrease step on win (but not below 0)
      this.currentStep = Math.max(0, this.currentStep - 1)
    } else {
      // Increase step on loss (but not above max)
      this.currentStep = Math.min(this.config.maxSteps - 1, this.currentStep + 1)
    }
    
    this.currentAmount = this.calculateTradeAmount()
  }

  reset(): void {
    super.reset()
    this.currentStep = 0
    this.currentAmount = this.config.baseAmount
  }

  // Method to be called after each trade result
  onTradeResult(won: boolean): void {
    this.updateStats(won)
  }

  // Enhanced confidence calculation for D'Alembert
  calculateConfidence(marketData: MarketData): number {
    let confidence = super.calculateConfidence(marketData)
    
    // D'Alembert specific adjustments
    // Reduce confidence if we're at higher steps (more risk)
    const stepPenalty = (this.currentStep / this.config.maxSteps) * 10
    confidence -= stepPenalty
    
    // Increase confidence if we have recent wins (momentum)
    if (this.consecutiveWins > 0) {
      confidence += Math.min(this.consecutiveWins * 2, 10)
    }
    
    // Decrease confidence if we have many consecutive losses
    if (this.consecutiveLosses > 3) {
      confidence -= (this.consecutiveLosses - 3) * 3
    }
    
    return Math.max(0, Math.min(confidence, 100))
  }

  // Get current D'Alembert state
  getState(): {
    step: number
    currentAmount: number
    baseAmount: number
    increment: number
    maxSteps: number
    canTrade: boolean
    progressionDirection: 'up' | 'down' | 'neutral'
  } {
    let progressionDirection: 'up' | 'down' | 'neutral' = 'neutral'
    
    if (this.consecutiveLosses > 0) {
      progressionDirection = 'up'
    } else if (this.consecutiveWins > 0 && this.currentStep > 0) {
      progressionDirection = 'down'
    }
    
    return {
      step: this.currentStep + 1,
      currentAmount: this.currentAmount,
      baseAmount: this.config.baseAmount,
      increment: this.config.increment,
      maxSteps: this.config.maxSteps,
      canTrade: this.currentStep < this.config.maxSteps,
      progressionDirection
    }
  }
}

import { BaseStrategyImpl } from '../StrategyManager'
import { MarketData, TradeDecision, MartingaleConfig } from '../../../src/types'

export class MartingaleStrategy extends BaseStrategyImpl {
  private config: MartingaleConfig

  constructor(config: MartingaleConfig) {
    super(
      'Martingale',
      'Doubles bet after each loss, resets on win. High risk, high reward strategy.',
      config
    )
    this.config = config
    this.currentAmount = config.baseAmount
  }

  async execute(marketData: MarketData): Promise<TradeDecision> {
    const confidence = this.calculateConfidence(marketData)
    
    // Determine trade direction based on market analysis
    const direction = this.determineDirection(marketData)
    
    // Calculate trade amount based on Martingale progression
    const tradeAmount = this.calculateTradeAmount()
    
    // Check if we've hit max steps
    if (this.consecutiveLosses >= this.config.maxSteps) {
      return {
        action: 'hold',
        amount: 0,
        confidence,
        reasoning: `Maximum Martingale steps (${this.config.maxSteps}) reached. Waiting for reset.`,
        metadata: {
          consecutiveLosses: this.consecutiveLosses,
          currentAmount: this.currentAmount
        }
      }
    }

    return {
      action: direction === 'high' ? 'buy' : 'sell',
      amount: tradeAmount,
      confidence,
      reasoning: this.generateReasoning(marketData, direction, tradeAmount),
      metadata: {
        strategy: 'martingale',
        direction,
        consecutiveLosses: this.consecutiveLosses,
        step: this.consecutiveLosses + 1,
        baseAmount: this.config.baseAmount,
        multiplier: this.config.multiplier
      }
    }
  }

  private calculateTradeAmount(): number {
    if (this.consecutiveLosses === 0) {
      return this.config.baseAmount
    }
    
    return this.config.baseAmount * Math.pow(this.config.multiplier, this.consecutiveLosses)
  }

  private generateReasoning(marketData: MarketData, direction: 'high' | 'low', amount: number): string {
    const reasons = []
    
    // Market analysis reasoning
    if (marketData.rsi <= 30) {
      reasons.push('RSI oversold (bullish signal)')
    } else if (marketData.rsi >= 70) {
      reasons.push('RSI overbought (bearish signal)')
    }
    
    if (marketData.emaShort > marketData.emaLong) {
      reasons.push('EMA uptrend')
    } else {
      reasons.push('EMA downtrend')
    }
    
    if (marketData.trend !== 'sideways') {
      reasons.push(`Price trend: ${marketData.trend}`)
    }
    
    // Martingale reasoning
    if (this.consecutiveLosses === 0) {
      reasons.push('Starting new Martingale sequence')
    } else {
      reasons.push(`Martingale step ${this.consecutiveLosses + 1}, amount: ${amount}`)
    }
    
    return `${direction.toUpperCase()} signal: ${reasons.join(', ')}`
  }

  // Override updateStats to handle Martingale progression
  protected updateStats(won: boolean): void {
    super.updateStats(won)
    
    if (won && this.config.resetOnWin) {
      // Reset to base amount on win
      this.currentAmount = this.config.baseAmount
      this.consecutiveLosses = 0
    } else if (!won) {
      // Increase amount for next trade
      this.currentAmount = this.calculateTradeAmount()
    }
  }

  reset(): void {
    super.reset()
    this.currentAmount = this.config.baseAmount
  }

  // Method to be called after each trade result
  onTradeResult(won: boolean): void {
    this.updateStats(won)
  }

  // Get current Martingale state
  getState(): {
    step: number
    currentAmount: number
    consecutiveLosses: number
    maxSteps: number
    canTrade: boolean
  } {
    return {
      step: this.consecutiveLosses + 1,
      currentAmount: this.currentAmount,
      consecutiveLosses: this.consecutiveLosses,
      maxSteps: this.config.maxSteps,
      canTrade: this.consecutiveLosses < this.config.maxSteps
    }
  }
}
